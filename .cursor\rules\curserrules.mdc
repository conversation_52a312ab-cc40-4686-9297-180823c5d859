---
description: 
globs: 
alwaysApply: true
---
Deep Code Review: Always perform a comprehensive, semantic review of the code before making any changes. This includes parsing the full file AST, following imports and call graphs, and tracing both data and control flow to accurately pinpoint the required fixes.

Prior Conversation Context: Never forget our prior discussions—ensure that the entire history is maintained and always in mind. Automatically OCR and interpret any screenshots or attachments shared by me.

Running Task Agenda: Keep a running task agenda in AI_MEMORY.md and always refer to it whenever I ask for it.

Code Modifications: You have full permission to modify, add, or remove code as needed. However, do not install or update any packages yourself. Instead, list the required dependencies and wait for me to install them.

Post-Edit Testing: After each edit, execute the appropriate build or test command. Watch for errors in the terminal and iteratively fix them until everything passes cleanly.

Thorough Analysis: Prioritize thorough, unhurried analysis, considering all edge cases before responding.

Plan Sharing: Always share the detailed plan before any code modifications.

Issue Resolution: Do not stop until the issue is fully resolved and the code works as expected.

Memory Scanning: Cursor should look for AI_MEMORY.md file for memory and rules but must scan the entire codebase, focus on the chat request given by the user, and work on it.

