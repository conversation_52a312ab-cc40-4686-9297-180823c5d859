# Autonomous Agent Backend

This directory contains the Python backend for the Autonomous AI Software Development Agent.

## Architecture

The backend follows a modular architecture with the following components:

### Core Components

1. **Agent System**
   - `agent.py`: Main agent coordinator that orchestrates the workflow
   - `agents/planner.py`: Breaks down high-level instructions into steps
   - `agents/researcher.py`: Identifies information needs and gathers data
   - `agents/coder.py`: Generates code based on plans and research
   - `agents/documenter.py`: Creates documentation for projects

2. **LLM Integration**
   - `llm/llm.py`: Base class for LLM integration
   - `llm/openai_client.py`: Integration with OpenAI API
   - `llm/ollama_client.py`: Integration with Ollama for local models
   - `llm/lm_studio_client.py`: Integration with LM Studio

3. **Project Management**
   - `project.py`: Manages project state and files
   - `filesystem/`: Handles file operations

4. **API Layer**
   - `main.py`: FastAPI application with routes
   - `socket_instance.py`: Socket.IO for real-time communication

### Dependencies

- FastAPI: Web framework for the API
- Socket.IO: Real-time communication
- Jinja2: Template rendering for prompts
- Playwright: Web browsing capabilities (optional)

## Setup

1. Create a virtual environment
2. Install dependencies
3. Configure API keys (user-provided)
4. Start the server

## API Endpoints

- `/api/projects`: Project management
- `/api/generate`: Code generation
- `/api/settings`: Configuration settings
- Socket events for real-time updates
