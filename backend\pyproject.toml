[tool.poetry]
name = "autonomous-agent-backend"
version = "0.1.0"
description = "Backend for the Autonomous AI Software Development Agent"
authors = ["Your Name <<EMAIL>>"]
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "^0.104.0"
uvicorn = "^0.23.2"
python-socketio = "^5.9.0"
jinja2 = "^3.1.2"
openai = "^1.1.1"
python-dotenv = "^1.0.0"
pydantic = "^2.4.2"
httpx = "^0.25.0"
markdown = "^3.5"
xhtml2pdf = "^0.2.11"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.2"
black = "^23.10.0"
isort = "^5.12.0"
flake8 = "^6.1.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
