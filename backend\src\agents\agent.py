"""
Main Agent class that coordinates the workflow between specialized agents.
"""
import os
import json
import asyncio
import time
from typing import Dict, List, Any, Optional, Callable
import logging
from agents.shell_executor import ShellExecutor
from agents.automation_executor import AutomationExecutor
from agents.project_executor import ProjectExecutor
from feedback.feedback_store import FeedbackStore
from socket_instance import emit_agent_status, emit_agent_message, emit_plan, emit_terminal_command, emit_code_generation_message, emit_cursor_message, emit_agent_file_update

from llm.llm import LLM
from agents.planner import Planner
from agents.researcher import Researcher
from agents.coder import Coder
from agents.documenter import Documenter
from project import ProjectManager
from state import AgentState
from llm.searxng_client import searxng_search, searxng_search_structured
import inspect
import re
from datetime import datetime
import traceback

logger = logging.getLogger(__name__)

class Agent:
    """
    Main Agent class that coordinates the workflow between specialized agents.
    """
    def __init__(self, openai_model_id: str = None, local_llm_model_id: str = None):
        """
        Initialize the Agent with model IDs.
        """
        # Try to load default model configuration from config.json
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config.json")
        default_primary = "deepseek/deepseek-chat"  # DeepSeek V3 as default
        default_backup = "deepseek/deepseek-coder"
        self.max_retries = 100  # Default value if not found in config
        
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    if "models" in config:
                        default_primary = config["models"].get("default_primary", default_primary)
                        default_backup = config["models"].get("default_backup", default_backup)
                    
                    # Load max_retries from config
                    if "max_retries" in config:
                        self.max_retries = config["max_retries"]
                        logger.info(f"Loaded max_retries from config: {self.max_retries}")
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")
        
        self.primary_model_id = openai_model_id or default_primary
        self.local_llm_model_id = local_llm_model_id
        self.project_manager = ProjectManager()

        logger.info(f"Agent initialized with primary model: {self.primary_model_id}")
        if self.local_llm_model_id:
            logger.info(f"Local LLM model: {self.local_llm_model_id}")

        # Track currently active models
        self.active_models = set()

        # Initialize state
        self.agent_state = AgentState()

        # Initialize agent components
        self.planner = Planner()
        self.researcher = Researcher(self.primary_model_id)
        self.coder = Coder(self.primary_model_id)
        self.documenter = Documenter(self.primary_model_id)

        # Track the current project memory between operations
        self.project_memory = {}

    def _get_recommended_model(self, task_type: str) -> str:
        """
        Get recommended model for this task type based on feedback.
        
        Args:
            task_type: Type of task to get recommendation for
            
        Returns:
            Recommended model ID or default primary model ID
        """
        try:
            feedback_store = FeedbackStore()
            recommended_model = feedback_store.get_model_recommendation(task_type)
            
            # Fall back to default if no recommendation available
            if not recommended_model:
                return self.primary_model_id
            
            logger.info(f"[Agent] Using recommended model {recommended_model} for task type {task_type}")
            return recommended_model
        except Exception as e:
            logger.error(f"[Agent] Error getting model recommendation: {e}")
            return self.primary_model_id

    def assign_models_to_subtasks(self, subtasks: list) -> list:
        """
        Assign models to subtasks based on task complexity and feedback history.
        """
        assignments = []
        for i, subtask in enumerate(subtasks):
            # Always use browser for research-heavy tasks
            subtask['force_web'] = subtask.get('type') in ['research', 'planning', 'analysis']
            
            # Determine best model based on feedback and task complexity
            task_type = subtask.get('type', 'unknown')
            task_complexity = self._estimate_task_complexity(subtask)
            recommended_model = self._get_recommended_model(task_type)
            
            # Select models based on complexity
            models_to_use = []
            
            # Split the recommended model ID
            primary_provider = recommended_model.split("/")[0] if "/" in recommended_model else "unknown"
            backup_provider = self.backup_model_id.split("/")[0] if "/" in self.backup_model_id else "openai"
            
            # For high-complexity tasks, always use the DeepSeek and GPT-4 models
            if task_complexity == "high":
                # Primary model should be DeepSeek
                if "deepseek" in self.primary_model_id:
                    models_to_use.append(("deepseek", self.primary_model_id))
                else:
                    models_to_use.append(("deepseek", "deepseek/deepseek-coder"))
                
                # Add backup model (typically GPT-4 variant)
                models_to_use.append((backup_provider, self.backup_model_id))
            else:
                # Add the recommended model as primary
                models_to_use.append((primary_provider, recommended_model))
                
                # Add the backup model if different from the recommended one
                if recommended_model != self.backup_model_id:
                    models_to_use.append((backup_provider, self.backup_model_id))
            
            # Only add local LLM if specified
            if self.local_llm_model_id and "lm-studio" in self.local_llm_model_id:
                if not any(model_id == self.local_llm_model_id for _, model_id in models_to_use):
                    models_to_use.append(("local", self.local_llm_model_id))
            
            assignments.append({
                "subtask": subtask,
                "models": models_to_use
            })
        return assignments
        
    def _estimate_task_complexity(self, subtask: dict) -> str:
        """
        Estimate task complexity based on its description and requirements.
        
        Args:
            subtask: The subtask to analyze
            
        Returns:
            Complexity level: "low", "medium", or "high"
        """
        # Default to medium complexity
        complexity = "medium"
        
        description = subtask.get('description', '').lower()
        prompt = subtask.get('prompt', '').lower()
        subtask_type = subtask.get('type', '').lower()
        
        # High complexity indicators
        high_complexity_keywords = [
            "complex", "sophisticated", "advanced", 
            "optimize", "refactor", "architecture",
            "security", "performance", "scalable"
        ]
        
        # Low complexity indicators
        low_complexity_keywords = [
            "simple", "basic", "trivial",
            "update text", "change color", "rename",
            "add comment", "documentation"
        ]
        
        # Check for high complexity indicators
        for keyword in high_complexity_keywords:
            if keyword in description or keyword in prompt:
                complexity = "high"
                break
                
        # Check for low complexity indicators (only if not already marked as high)
        if complexity != "high":
            for keyword in low_complexity_keywords:
                if keyword in description or keyword in prompt:
                    complexity = "low"
                    break
        
        # Task types that typically require high complexity
        if subtask_type in ["planning", "architecture", "analysis", "optimization", "security"]:
            complexity = "high"
            
        # Task types that are typically low complexity
        if subtask_type in ["documentation", "comment", "format"]:
            complexity = "low"
            
        # File operations are often high complexity unless they're documentation
        if subtask_type == "file" and not any(doc_type in description.lower() for doc_type in ["readme", "documentation", ".md", "comment"]):
            if subtask.get("file_path", "").endswith((".ts", ".js", ".py", ".java", ".cpp")):
                complexity = "high"
                
        logger.debug(f"[Agent] Estimated complexity for task '{description[:30]}...' is {complexity}")
        return complexity

    async def execute_subtask(self, subtask: dict, models: list, project_name: str, progress_callback=None, subtask_idx=None, total_subtasks=None, retry_count=0) -> dict:
        """
        Execute a single subtask using the specified models.
        Will retry with fallback models if the primary model fails.
        
        Args:
            subtask: The subtask to execute
            models: List of model IDs to try
            project_name: The name of the project
            progress_callback: Callback function for progress updates
            subtask_idx: Index of this subtask (for progress reporting)
            total_subtasks: Total number of subtasks (for progress reporting)
            retry_count: Current retry count
            
        Returns:
            Result dictionary with task output and metadata
        """
        if retry_count >= self.max_retries:
            logger.error(f"[Agent] Maximum retry limit reached ({self.max_retries}) for subtask: {subtask['task']}")
            return {
                "success": False,
                "error": f"Maximum retry limit reached ({self.max_retries})",
                "retry_count": retry_count,
                "subtask": subtask
            }
        
        # Special case for long-running tasks
        is_long_task = subtask.get('task_type') in ['code_generation', 'implementation', 'planning']
        
        # For progress reporting
        task_name = subtask.get('task', 'Unknown task')
        task_type = subtask.get('task_type', 'unknown')
        
        # Update progress if callback provided
        async def safe_progress_callback(payload):
            if progress_callback:
                try:
                    if asyncio.iscoroutinefunction(progress_callback):
                        await progress_callback(payload)
                    else:
                        progress_callback(payload)
                except Exception as e:
                    logger.error(f"[Agent] Error in progress callback: {e}")
                
        # Progress reporting
        if subtask_idx is not None and total_subtasks is not None:
            await safe_progress_callback({
                "stage": "execution",
                "status": "running",
                "message": f"Executing {task_type} task ({subtask_idx+1}/{total_subtasks}): {task_name[:50]}{'...' if len(task_name) > 50 else ''}",
                "completed": subtask_idx,
                "total": total_subtasks,
                "current_task": {
                    "name": task_name,
                    "type": task_type,
                    "retry_count": retry_count
                }
            })
        
        # Get the prompt for this subtask
        prompt = subtask.get('prompt')
        if not prompt:
            logger.error(f"[Agent] No prompt provided for subtask: {subtask}")
            return {
                "success": False,
                "error": "No prompt provided for subtask",
                "subtask": subtask
            }
        
        # Additional prompt context from project memory and previous interactions
        context_data = await self._load_project_context(project_name)
        if context_data:
            # Enhance the prompt with context
            prompt = self._enhance_prompt_with_context(prompt, context_data)
        
        # Prepare result structure
        result = {
            "success": False,
            "error": None,
            "content": None,
            "subtask": subtask,
            "retry_count": retry_count,
            "model_id": None
        }
        
        # Try each model in the list until one succeeds
        for model_id in models:
            # Determine model provider (openai, deepseek, etc.)
            model_parts = model_id.split('/')
            model_type = model_parts[0] if len(model_parts) > 1 else "deepseek"
            
            # Add extra metadata for telemetry
            model_info = {
                "model_id": model_id,
                "type": model_type,
                "start_time": time.time()
            }
            
            logger.info(f"[Agent] Executing subtask using model {model_id}: {task_name[:100]}")
            
            try:
                # For long-running tasks, use a longer timeout
                timeout = 180 if is_long_task else 60
                
                # Execute the subtask with the current model
                response = await self._call_model_with_timeout(
                    model_type=model_type, 
                    model_id=model_id, 
                    prompt=prompt, 
                    project_name=project_name,
                    subtask_type=task_type,
                    timeout=timeout
                )
                
                # Track model usage and end time
                model_info["end_time"] = time.time()
                model_info["duration"] = model_info["end_time"] - model_info["start_time"]
                
                # Check for valid response
                if response and "content" in response:
                    # Calculate confidence score based on response quality
                    quality_score = self._calculate_quality_score(response["content"], subtask)
                    model_info["quality_score"] = quality_score
                    
                    # Only process if quality is acceptable (score above 0.5)
                    if quality_score > 0.5:
                        result["success"] = True
                        result["content"] = response["content"]
                        result["model_id"] = model_id
                        result["model_info"] = model_info
                        
                        # Break the loop since this model was successful
                        break
                    else:
                        # Quality too low, try next model
                        logger.warning(f"[Agent] Low quality response ({quality_score}) from {model_id}, trying next model")
                        continue
                else:
                    # Invalid response, try next model
                    logger.warning(f"[Agent] Invalid response from {model_id}, trying next model")
                    continue
                    
            except Exception as e:
                logger.error(f"[Agent] Error executing subtask with model {model_id}: {e}")
                model_info["error"] = str(e)
                model_info["end_time"] = time.time()
                model_info["duration"] = model_info["end_time"] - model_info["start_time"]
                
                # Add error info to result
                if "error" not in result or not result["error"]:
                    result["error"] = str(e)
                    
                # Keep track of model usage even when it fails
                if "models_tried" not in result:
                    result["models_tried"] = []
                result["models_tried"].append(model_info)
                
                # Try next model in the list
                continue
        
        # If all models failed, retry with the same models but after a short delay
        if not result["success"]:
            # Check if we still have retry attempts available
            if retry_count < self.max_retries - 1:  # leave one retry for final attempt
                # Exponential backoff for retries - wait longer with each retry
                retry_delay = min(2 ** retry_count, 30)  # Cap at 30 seconds
                
                logger.warning(f"[Agent] All models failed for subtask. Retrying after {retry_delay}s delay. Retry {retry_count+1}/{self.max_retries}")
                
                # Wait before retrying
                await asyncio.sleep(retry_delay)
                
                # For progress reporting during retry
                await safe_progress_callback({
                    "stage": "execution",
                    "status": "retrying",
                    "message": f"Retrying {task_type} task ({retry_count+1}/{self.max_retries}): {task_name[:50]}{'...' if len(task_name) > 50 else ''}",
                    "completed": subtask_idx if subtask_idx is not None else 0,
                    "total": total_subtasks if total_subtasks is not None else 1,
                    "current_task": {
                        "name": task_name,
                        "type": task_type,
                        "retry_count": retry_count + 1
                    }
                })
                
                # Adjust the prompt for retry to improve chances of success
                if retry_count > 0:
                    # Make the prompt more explicit for retries
                    retry_prompt = f"""Previous attempts to complete this task have failed. This is retry #{retry_count+1} of {self.max_retries}.
                    
Problems encountered: {result.get('error', 'Unknown error')}

Please be very careful and provide a different approach to solve this task.

ORIGINAL TASK:
{prompt}

Focus on producing a correct and well-formatted response. Do not include unnecessary explanations.
"""
                    subtask["prompt"] = retry_prompt
                
                # Recursive call to retry
                return await self.execute_subtask(
                    subtask=subtask,
                    models=models,
                    project_name=project_name,
                    progress_callback=progress_callback,
                    subtask_idx=subtask_idx,
                    total_subtasks=total_subtasks,
                    retry_count=retry_count + 1
                )
        
        # Report success or final failure
        if result["success"]:
            logger.info(f"[Agent] Successfully executed subtask using {result['model_id']}")
            
            # Update progress if callback provided
            if subtask_idx is not None and total_subtasks is not None:
                await safe_progress_callback({
                    "stage": "execution",
                    "status": "completed",
                    "message": f"Completed {task_type} task: {task_name[:50]}{'...' if len(task_name) > 50 else ''}",
                    "completed": subtask_idx + 1,
                    "total": total_subtasks
                })
        else:
            logger.error(f"[Agent] Failed to execute subtask after {retry_count+1} attempts: {result.get('error', 'Unknown error')}")
            
            # Update progress if callback provided
            if subtask_idx is not None and total_subtasks is not None:
                await safe_progress_callback({
                    "stage": "execution",
                    "status": "failed",
                    "message": f"Failed {task_type} task: {task_name[:50]}{'...' if len(task_name) > 50 else ''}",
                    "completed": subtask_idx,
                    "total": total_subtasks,
                    "error": result.get("error", "Task failed")
                })
        
        return result

    async def _call_model_with_timeout(self, model_type, model_id, prompt, project_name, subtask_type, timeout=120):
        """
        Call a model with a timeout to prevent hanging on slow responses.
        
        Args:
            model_type: Type of model (openai, local, etc.)
            model_id: ID of the model
            prompt: The prompt to send
            project_name: Name of the project
            subtask_type: Type of subtask being executed
            timeout: Timeout in seconds
            
        Returns:
            Dictionary with response and timing information, or exception
        """
        try:
            # Create a task with timeout
            return await asyncio.wait_for(
                self._call_model(model_type, model_id, prompt, project_name, subtask_type),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"[Agent] Model {model_id} timed out after {timeout} seconds")
            return Exception(f"Model {model_id} timed out after {timeout} seconds")
        except Exception as e:
            logger.error(f"[Agent] Error calling model {model_id}: {e}")
            return e
        
    async def _call_model(self, model_type, model_id, prompt, project_name, subtask_type):
        """
        Call a model and measure execution time.
        
        Args:
            model_type: Type of model (openai, local, etc.)
            model_id: ID of the model
            prompt: The prompt to send
            project_name: Name of the project
            subtask_type: Type of subtask being executed
            
        Returns:
            Dictionary with response and timing information
        """
        from llm.llm import LLM
        
        try:
            start_time = time.time()
            
            # Skip Ollama models for certain tasks
            if "ollama" in model_id.lower() and subtask_type in ['planning', 'architecture', 'analysis']:
                logger.warning(f"[Agent] Skipping Ollama model {model_id} for complex task type {subtask_type}")
                return {"response": "", "error": "Skipped Ollama for complex task", "execution_time": 0}
                
            llm = LLM.create(model_id)
            logger.info(f"[Agent] Calling {model_type} model ({model_id}) for project {project_name}")
            
            response = await llm.generate(prompt, project_name)
            execution_time = time.time() - start_time
            
            logger.info(f"[Agent] {model_type} model ({model_id}) responded in {execution_time:.2f}s")
            
            return {
                "response": response,
                "execution_time": execution_time
            }
        except Exception as e:
            logger.error(f"[Agent] Error calling {model_type} model ({model_id}): {e}")
            raise

    def _create_search_query(self, subtask: dict, project_name: str) -> str:
        """
        Create a focused search query based on the subtask.
        
        Args:
            subtask: The subtask to create a query for
            project_name: Name of the project
            
        Returns:
            A focused search query
        """
        description = subtask.get('description', '')
        prompt = subtask.get('prompt', '')
        subtask_type = subtask.get('type', '')
        
        # Extract key terms from the description and prompt
        key_terms = self.extract_keywords(description + " " + prompt)
        
        # Build the query based on the task type
        if subtask_type == 'file':
            file_path = subtask.get('file_path', '')
            file_ext = os.path.splitext(file_path)[1] if file_path else ''
            
            if 'component' in file_path.lower():
                framework = 'Angular' if '.ts' in file_path else 'React' if '.jsx' in file_path else ''
                return f"{framework} component {os.path.basename(file_path)} implementation example {' '.join(key_terms[:3])}"
            elif 'service' in file_path.lower():
                return f"backend service implementation {file_ext} example {' '.join(key_terms[:3])}"
            else:
                return f"code example {file_ext} {os.path.basename(file_path)} {' '.join(key_terms[:3])}"
        
        elif subtask_type in ['research', 'planning', 'analysis']:
            return f"{' '.join(key_terms[:5])} {project_name} best practices"
        
        else:
            # Generic query using key terms
            return f"{' '.join(key_terms[:5])} {project_name}"
            
    def _calculate_quality_score(self, response: str, subtask: dict) -> float:
        """
        Calculate a quality score for a model response.
        
        Args:
            response: The model's response
            subtask: The subtask being executed
            
        Returns:
            Quality score as a float
        """
        if not response:
            return 0.0
            
        score = 0.0
        subtask_type = subtask.get('type', '')
        
        # Base score based on length (up to a point)
        length = len(response)
        score += min(length / 1000, 5)  # Cap at 5 points for length
        
        # Check for code blocks in appropriate tasks
        if subtask_type == 'file' or 'code' in subtask.get('description', '').lower():
            code_blocks = re.findall(r'```(?:\w+)?\s*([\s\S]+?)\s*```', response)
            if code_blocks:
                score += 3
                
                # Check for imports/includes in code
                if any('import' in block or 'include' in block or '#include' in block for block in code_blocks):
                    score += 2
                    
                # Check for function/class definitions
                if any('function' in block or 'class' in block or 'def ' in block for block in code_blocks):
                    score += 2
        
        # For research tasks, reward citations and references
        if subtask_type == 'research' or 'research' in subtask.get('description', '').lower():
            # Check for URLs
            urls = re.findall(r'https?://\S+', response)
            score += min(len(urls), 3)
            
            # Check for citation patterns [1], (Smith, 2020), etc.
            citations = re.findall(r'\[\d+\]|\([A-Za-z]+,?\s+\d{4}\)', response)
            score += min(len(citations), 2)
        
        # For planning tasks, reward structured content
        if subtask_type == 'planning' or 'plan' in subtask.get('description', '').lower():
            # Check for numbered lists/steps
            numbered_items = re.findall(r'\n\s*\d+\.\s+', response)
            score += min(len(numbered_items), 3)
            
            # Check for headers/sections
            headers = re.findall(r'\n\s*#+\s+\w+', response)
            score += min(len(headers), 2)
        
        logger.debug(f"[Agent] Quality score for response: {score:.2f}")
        return score

    async def generate_automation_plan(self, prompt: str, project_name: str) -> Dict[str, Any]:
        """
        Phase 1: Generate a complete, actionable automation plan (but do NOT execute it).
        Returns the plan and plan details for user review/confirmation.
        """
        logger.info(f"[Agent] Generating automation plan for project: {project_name}")
        self.project_manager.create_project_if_not_exists(project_name)
        plan = await self.planner.execute(prompt, project_name)
        plan_details = self.planner.parse_response(plan)
        return {
            "plan_details": plan_details,
            "raw_plan": plan
        }

    def convert_plan_to_steps(self, plan_details: dict) -> list:
        """
        Convert the descriptive plan to a structured list of actionable steps for execution.
        
        Args:
            plan_details: Dictionary with plan details including project name, description, and steps
            
        Returns:
            List of structured steps (commands, file operations, etc.)
        """
        logger.info(f"[Agent] Converting plan to actionable steps")
        
        steps = []
        step_map = plan_details.get('steps_desc', {})
        project_name = plan_details.get('project_name', 'default_project')
        
        # First, check if there are actionable steps in the plan_details
        if 'actionable_steps' in plan_details and isinstance(plan_details['actionable_steps'], list):
            logger.info(f"[Agent] Using pre-parsed actionable steps from plan: {len(plan_details['actionable_steps'])} steps")
            return plan_details['actionable_steps']
        
        # Add Angular project initialization
        steps.append({
            "type": "command", 
            "command": f"ng new {project_name} --routing --style=scss --skip-git",
            "description": "Initialize Angular project"
        })
        
        # Add dependency installation
        steps.append({
            "type": "command", 
            "command": "npm install",
            "description": "Install dependencies"
        })
        
        # Extract components and other elements from the plan
        for num in sorted(step_map.keys()):
            desc = step_map[num]['description'].lower()
            
            # Check for component generation
            if 'generate' in desc and 'component' in desc:
                import re
                match = re.search(r'component\s+(\w+)', desc)
                comp = match.group(1) if match else 'app'
                steps.append({
                    "type": "command", 
                    "command": f"ng generate component {comp}",
                    "description": f"Generate {comp} component"
                })
                
                # Add placeholder files for this component
                steps.append({
                    "type": "file",
                    "file_path": f"src/app/{comp}/{comp}.component.ts",
                    "content": self._generate_component_ts(comp, project_name),
                    "description": f"Create {comp} component TypeScript file"
                })
                
                steps.append({
                    "type": "file",
                    "file_path": f"src/app/{comp}/{comp}.component.html",
                    "content": self._generate_component_html(comp, project_name),
                    "description": f"Create {comp} component HTML template"
                })
                
                steps.append({
                    "type": "file",
                    "file_path": f"src/app/{comp}/{comp}.component.scss",
                    "content": self._generate_component_scss(comp),
                    "description": f"Create {comp} component SCSS styles"
                })
            
            # Check for service generation
            elif 'generate' in desc and 'service' in desc:
                import re
                match = re.search(r'service\s+(\w+)', desc)
                service = match.group(1) if match else 'data'
                steps.append({
                    "type": "command", 
                    "command": f"ng generate service {service}",
                    "description": f"Generate {service} service"
                })
                
                # Add placeholder file for this service
                steps.append({
                    "type": "file",
                    "file_path": f"src/app/services/{service}.service.ts",
                    "content": self._generate_service_ts(service),
                    "description": f"Create {service} service TypeScript file"
                })
            
            # Check for folder creation
            elif 'create folder' in desc or 'create directory' in desc or 'mkdir' in desc:
                folder = desc.split('folder')[-1].strip() if 'folder' in desc else desc.split('directory')[-1].strip()
                steps.append({
                    "type": "folder", 
                    "path": folder,
                    "description": f"Create folder: {folder}"
                })
            
            # Check for README generation
            elif 'readme' in desc:
                steps.append({
                    "type": "file",
                    "file_path": "README.md", 
                    "content": self._generate_readme(project_name),
                    "description": "Create README.md file"
                })
        
        # Add a default app component files if not already included
        if not any('app.component.html' in step.get('file_path', '') for step in steps if step.get('type') == 'file'):
            steps.append({
                "type": "file",
                "file_path": "src/app/app.component.html",
                "content": self._generate_app_component_html(project_name),
                "description": "Create app.component.html file"
            })
        
        if not any('app.component.ts' in step.get('file_path', '') for step in steps if step.get('type') == 'file'):
            steps.append({
                "type": "file",
                "file_path": "src/app/app.component.ts",
                "content": self._generate_app_component_ts(project_name),
                "description": "Create app.component.ts file"
            })
            
        if not any('app.component.scss' in step.get('file_path', '') for step in steps if step.get('type') == 'file'):
            steps.append({
                "type": "file",
                "file_path": "src/app/app.component.scss",
                "content": self._generate_app_component_scss(),
                "description": "Create app.component.scss file"
            })
        
        # Add steps to open tools
        steps.append({
            "type": "open_vscode",
            "description": "Open VS Code editor"
        })
        
        steps.append({
            "type": "open_terminal",
            "description": "Open terminal"
        })
        
        logger.info(f"[Agent] Generated {len(steps)} actionable steps")
        return steps
    
    def _generate_component_ts(self, component_name: str, project_name: str) -> str:
        """Generate TypeScript code for a component"""
        pascal_name = self._to_pascal_case(component_name)
        return f"""import {{ Component, OnInit }} from '@angular/core';

@Component({{
  selector: 'app-{component_name}',
  templateUrl: './{component_name}.component.html',
  styleUrls: ['./{component_name}.component.scss']
}})
export class {pascal_name}Component implements OnInit {{
  title = '{project_name}';
  
  constructor() {{ }}
  
  ngOnInit(): void {{
    // Initialize component
  }}
}}
"""
    
    def _generate_component_html(self, component_name: str, project_name: str) -> str:
        """Generate HTML template for a component"""
        return f"""<div class="{component_name}-container">
  <h2>{self._to_display_name(component_name)} Component</h2>
  <p>Welcome to the {component_name} component of {project_name}</p>
</div>
"""
    
    def _generate_component_scss(self, component_name: str) -> str:
        """Generate SCSS styles for a component"""
        return f""".{component_name}-container {{
  padding: 20px;
  margin: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  
  h2 {{
    color: #333;
    margin-bottom: 15px;
  }}
  
  p {{
    color: #666;
  }}
}}
"""
    
    def _generate_service_ts(self, service_name: str) -> str:
        """Generate TypeScript code for a service"""
        pascal_name = self._to_pascal_case(service_name)
        return f"""import {{ Injectable }} from '@angular/core';
import {{ HttpClient }} from '@angular/common/http';
import {{ Observable, throwError }} from 'rxjs';
import {{ catchError }} from 'rxjs/operators';

@Injectable({{
  providedIn: 'root'
}})
export class {pascal_name}Service {{
  private apiUrl = 'api/{service_name}';
  
  constructor(private http: HttpClient) {{ }}
  
  getData(): Observable<any[]> {{
    return this.http.get<any[]>(this.apiUrl)
      .pipe(
        catchError(this.handleError)
      );
  }}
  
  private handleError(error: any) {{
    console.error('An error occurred', error);
    return throwError(() => error);
  }}
}}
"""
    
    def _generate_readme(self, project_name: str) -> str:
        """Generate content for README.md"""
        return f"""# {project_name}

This project was generated with Angular.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via Karma.

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice.

## Further help

To get more help on the Angular CLI use `ng help` or check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
"""
    
    def _generate_app_component_html(self, project_name: str) -> str:
        """Generate content for app.component.html"""
        return f"""<div class="app-container">
  <header class="app-header">
    <h1>{self._to_display_name(project_name)}</h1>
    <nav>
      <ul>
        <li><a href="#">Home</a></li>
        <li><a href="#">About</a></li>
        <li><a href="#">Contact</a></li>
      </ul>
    </nav>
  </header>
  
  <main class="app-content">
    <router-outlet></router-outlet>
  </main>
  
  <footer class="app-footer">
    <p>&copy; {datetime.now().year} {self._to_display_name(project_name)}. All rights reserved.</p>
  </footer>
</div>
"""
    
    def _generate_app_component_ts(self, project_name: str) -> str:
        """Generate content for app.component.ts"""
        return f"""import {{ Component }} from '@angular/core';

@Component({{
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
}})
export class AppComponent {{
  title = '{project_name}';
}}
"""
    
    def _generate_app_component_scss(self) -> str:
        """Generate content for app.component.scss"""
        return """.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-header {
  background-color: #3f51b5;
  color: white;
  padding: 1rem;
  
  h1 {
    margin: 0;
    font-size: 1.8rem;
  }
  
  nav {
    margin-top: 1rem;
    
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      gap: 1rem;
      
      li a {
        color: white;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.app-content {
  flex: 1;
  padding: 1rem;
}

.app-footer {
  background-color: #f5f5f5;
  padding: 1rem;
  text-align: center;
  
  p {
    margin: 0;
    color: #666;
  }
}
"""
    
    def _to_pascal_case(self, name: str) -> str:
        """Convert a name to PascalCase"""
        return ''.join(word.capitalize() for word in name.split('-'))
    
    def _to_display_name(self, name: str) -> str:
        """Convert a name to display format (e.g., 'my-app' to 'My App')"""
        return ' '.join(word.capitalize() for word in re.sub(r'[-_]', ' ', name).split())

    def run_automation_plan(self, plan: list, project_name: str, progress_callback=None) -> Dict[str, Any]:
        """
        Phase 3: Execute the automation plan after user confirmation.
        Uses AutomationExecutor to run all steps, with progress and error reporting.
        """
        logger.info(f"[Agent] Running automation plan for project: {project_name}")
        executor = AutomationExecutor(project_name, self.project_manager.projects_dir)
        errors = []
        steps_executed = []
        for idx, step in enumerate(plan):
            result = executor.run_plan([step])
            steps_executed.append({'step': step, 'result': result})
            if progress_callback:
                progress_callback(idx+1, len(plan), step, result)
            if not result['success']:
                errors.extend(result['errors'])
                break
        return {
            'success': len(errors) == 0,
            'errors': errors,
            'steps_executed': steps_executed
        }

    async def execute(self, prompt: str, project_name: str, progress_callback=None) -> Dict[str, Any]:
        """
        Execute the agent workflow.
        
        Args:
            prompt: User prompt
            project_name: Name of the project
            progress_callback: Callback for progress updates
            
        Returns:
            Dictionary with response and results
        """
        logger.info("[Agent] Starting execution")
        thinking_process = []  # Track agent's thinking process for debugging/transparency
        
        try:
            # Extract keywords from the prompt
            self.context_keywords = self.extract_keywords(prompt)
            thinking_process.append(f"Extracted keywords: {', '.join(self.context_keywords)}")
            
            # Start by generating a plan
            thinking_process.append("Generating automation plan...")
            plan_details = await self.generate_automation_plan(prompt, project_name)
            thinking_process.append(f"Generated plan with {len(plan_details.get('steps', []))} steps")
            
            # Convert the plan to executable steps
            subtasks = self.convert_plan_to_steps(plan_details)
            thinking_process.append(f"Converted plan to {len(subtasks)} executable steps")
            
            # Ensure steps include file content implementation when needed
            subtasks = self.ensure_code_implementation_steps(subtasks, prompt, project_name)
            thinking_process.append(f"Validated code implementation steps, now have {len(subtasks)} tasks")
            
            # Execute the plan and return the results
            results = self.run_automation_plan(subtasks, project_name, progress_callback)
            thinking_process.append(f"Executed plan with {len(results.get('results', []))} results")
            
            # Generate a response for the user
            if len(results.get('results', [])) > 0:
                response = f"I've completed the requested task. Here's what I did:\n\n"
                for i, result in enumerate(results.get('results', [])):
                    description = result.get('description', 'Unknown step')
                    status = result.get('status', 'unknown')
                    if status == 'success':
                        response += f"✅ Step {i+1}: {description}\n"
                    elif status == 'error':
                        response += f"❌ Step {i+1}: {description} - Error: {result.get('error', 'Unknown error')}\n"
                    else:
                        response += f"⏳ Step {i+1}: {description} - Status: {status}\n"
            else:
                response = "I've analyzed your request. Here's what I understand:\n\n"
                if 'description' in plan_details:
                    response += f"{plan_details['description']}\n\n"
                response += "I don't have any specific actions to take at this time. Please provide more details about what you'd like me to do."
            
            logger.info("[Agent] Execution completed successfully")
            return {
                "success": True,
                "response": response,
                "results": results.get('results', []),
                "thinking": "\n".join(thinking_process)
            }
        except Exception as e:
            logger.error(f"[Agent] Error during execution: {e}")
            return {
                "success": False,
                "error": f"Error during execution: {e}",
                "results": [],
                "thinking": "\n".join(thinking_process + [f"Error: {str(e)}"])
            }

    async def execute_with_streaming(self, prompt: str, project_name: str, stream_callback=None) -> Dict[str, Any]:
        """
        Execute the agent workflow with streaming response.
        
        Args:
            prompt: User prompt
            project_name: Name of the project
            stream_callback: Callback for streaming tokens
            
        Returns:
            Dictionary with response and results
        """
        logger.info("[Agent] Starting execution with streaming")
        thinking_process = []  # Track agent's thinking process for debugging/transparency
        full_response = ""
        
        try:
            # If stream_callback is provided, send an initial message
            if stream_callback:
                await stream_callback("I'm analyzing your request...\n\n")
                full_response += "I'm analyzing your request...\n\n"
            
            # Extract keywords from the prompt
            self.context_keywords = self.extract_keywords(prompt)
            thinking_process.append(f"Extracted keywords: {', '.join(self.context_keywords)}")
            
            # Start by generating a plan
            thinking_process.append("Generating automation plan...")
            plan_details = await self.generate_automation_plan(prompt, project_name)
            thinking_process.append(f"Generated plan with {len(plan_details.get('steps', []))} steps")
            
            # Stream plan summary if callback exists
            if stream_callback and 'description' in plan_details:
                plan_msg = f"Based on your request, here's my plan:\n{plan_details['description']}\n\n"
                await stream_callback(plan_msg)
                full_response += plan_msg
            
            # Convert the plan to executable steps
            subtasks = self.convert_plan_to_steps(plan_details)
            thinking_process.append(f"Converted plan to {len(subtasks)} executable steps")
            
            # Ensure steps include file content implementation when needed
            subtasks = self.ensure_code_implementation_steps(subtasks, prompt, project_name)
            thinking_process.append(f"Validated code implementation steps, now have {len(subtasks)} tasks")
            
            # Stream the starting execution message if callback exists
            if stream_callback and subtasks:
                start_msg = f"Starting execution with {len(subtasks)} steps...\n\n"
                await stream_callback(start_msg)
                full_response += start_msg
            
            # Create a progress callback that streams updates
            async def streaming_progress_callback(data):
                nonlocal full_response
                if stream_callback:
                    if data.get('status') == 'in_progress':
                        msg = f"Working on step {data.get('step_number')} of {data.get('total_steps')}: {data.get('description', '')}\n"
                        await stream_callback(msg)
                        full_response += msg
                    elif data.get('status') == 'complete':
                        msg = f"✅ Completed step {data.get('step_number')} of {data.get('total_steps')}: {data.get('description', '')}\n"
                        await stream_callback(msg)
                        full_response += msg
                    elif data.get('status') == 'error':
                        msg = f"❌ Error in step {data.get('step_number')} of {data.get('total_steps')}: {data.get('description', '')} - {data.get('error', '')}\n"
                        await stream_callback(msg)
                        full_response += msg
            
            # Execute the plan with streaming updates
            results = self.run_automation_plan(subtasks, project_name, streaming_progress_callback)
            thinking_process.append(f"Executed plan with {len(results.get('results', []))} results")
            
            # Stream a summary of the results
            if stream_callback:
                summary_msg = "\nSummary of execution:\n"
                success_count = sum(1 for r in results.get('results', []) if r.get('status') == 'success')
                error_count = sum(1 for r in results.get('results', []) if r.get('status') == 'error')
                
                summary_msg += f"- Successfully completed: {success_count} steps\n"
                if error_count > 0:
                    summary_msg += f"- Encountered errors: {error_count} steps\n"
                
                await stream_callback(summary_msg)
                full_response += summary_msg
                
                # Final conclusion
                conclusion = "\nI've completed the requested task. Let me know if you need any adjustments or have questions!"
                await stream_callback(conclusion)
                full_response += conclusion
            
            logger.info("[Agent] Streaming execution completed successfully")
            return {
                "success": True,
                "response": full_response,
                "results": results.get('results', []),
                "thinking": "\n".join(thinking_process)
            }
        except Exception as e:
            logger.error(f"[Agent] Error during streaming execution: {e}")
            error_message = f"I encountered an error: {str(e)}. Please try again or refine your request."
            
            if stream_callback:
                await stream_callback(f"\n\n❌ {error_message}")
                full_response += f"\n\n❌ {error_message}"
            
            return {
                "success": False,
                "error": error_message,
                "results": [],
                "thinking": "\n".join(thinking_process + [f"Error: {str(e)}"])
            }

    def ensure_code_implementation_steps(self, subtasks: list, prompt: str, project_name: str) -> list:
        """
        Ensure the plan includes necessary implementation steps.
        
        This ensures the plan has steps to create basic project structure, components,
        and routes based on the project requirements.
        
        Args:
            subtasks: The existing subtasks
            prompt: The original prompt
            project_name: The name of the project
            
        Returns:
            Updated subtasks with necessary implementation steps
        """
        # Check if this is a web application based on keywords in the prompt
        is_web_app = any(keyword in prompt.lower() for keyword in [
            "web ", "website", "frontend", "ui", "interface", "app", "application",
            "angular", "react", "vue", "javascript", "typescript", "html", "css"
        ])
        
        has_init_steps = any(step.get('type') == 'command' and 'new' in step.get('command', '') 
                            for step in subtasks)
        
        if not has_init_steps:
            # If no initialization steps, add project creation based on type
            if "angular" in prompt.lower():
                subtasks.insert(0, {
                    "type": "command",
                    "command": f"ng new {project_name} --routing --style=scss --skip-git",
                    "description": "Initialize Angular project with routing"
                })
                
                # Next step: cd into project
                subtasks.insert(1, {
                    "type": "command",
                    "command": f"cd {project_name}",
                    "description": "Navigate to project directory"
                })
            elif "react" in prompt.lower():
                subtasks.insert(0, {
                    "type": "command",
                    "command": f"npx create-react-app {project_name}",
                    "description": "Initialize React project"
                })
                
                # Next step: cd into project
                subtasks.insert(1, {
                    "type": "command",
                    "command": f"cd {project_name}",
                    "description": "Navigate to project directory"
                })
            elif "vue" in prompt.lower():
                subtasks.insert(0, {
                    "type": "command",
                    "command": f"npm init vue@latest {project_name} -- --ts --router",
                    "description": "Initialize Vue project with TypeScript and routing"
                })
                
                # Next step: cd into project
                subtasks.insert(1, {
                    "type": "command",
                    "command": f"cd {project_name}",
                    "description": "Navigate to project directory"
                })
                
                # Extra step for Vue.js: npm install
                subtasks.insert(2, {
                    "type": "command",
                    "command": "npm install",
                    "description": "Install dependencies"
                })
        
        # Ensure there are component creation steps for web apps
        if is_web_app:
            # Extract component names from the prompt
            component_names = self._extract_components_from_prompt(prompt)
            
            # Check if we need to add components
            has_component_steps = any("component" in step.get('description', '').lower() for step in subtasks)
            if not has_component_steps and component_names:
                # Add component generation steps after installation
                install_index = next((i for i, step in enumerate(subtasks) 
                                     if step.get('type') == 'command' and 'install' in step.get('command', '').lower()), 2)
                
                for i, component in enumerate(component_names):
                    if "angular" in prompt.lower():
                        subtasks.insert(install_index + i + 1, {
                            "type": "command",
                            "command": f"ng generate component components/{component}",
                            "description": f"Generate Angular component: {component}"
                        })
                    elif "react" in prompt.lower():
                        subtasks.insert(install_index + i + 1, {
                "type": "file",
                            "file_path": f"src/components/{component}/{component}.jsx",
                            "description": f"Create React component: {component}"
                        })
                    elif "vue" in prompt.lower():
                        subtasks.insert(install_index + i + 1, {
                "type": "file",
                            "file_path": f"src/components/{component}.vue",
                            "description": f"Create Vue component: {component}"
                        })
        
        return subtasks
        
    def _extract_components_from_prompt(self, prompt: str) -> list:
        """
        Extract component names from the prompt using NLP techniques.
        
        Args:
            prompt: The project prompt
            
        Returns:
            List of component names
        """
        components = []
        
        # Look for common UI component patterns
        component_patterns = [
            r'(?:page|component|screen) called (\w+)',
            r'(?:page|component|screen) for (\w+)',
            r'(\w+) (?:page|component|screen)',
            r'(?:implement|create|add) (?:a|the) (\w+) (?:page|component|screen|feature|section)',
            r'(?:implement|create|add) (?:a|the) (\w+)',
        ]
        
        for pattern in component_patterns:
            matches = re.finditer(pattern, prompt, re.IGNORECASE)
            for match in matches:
                component = match.group(1).lower()
                if (len(component) > 2 and 
                    component not in ['the', 'and', 'for', 'with', 'page', 'component', 'screen']):
                    components.append(component)
        
        # Make components unique
        return list(set(components))

    def extract_keywords(self, text: str) -> List[str]:
        """
        Extract keywords from text for research context.
        
        Args:
            text: The text to extract keywords from
            
        Returns:
            A list of keywords
        """
        words = text.lower().split()
        keywords = [word for word in words if len(word) > 3 and word.isalnum()]
        return list(set(keywords))

    def determine_project_type(self, prompt: str) -> str:
        """
        Determine the type of project based on the user prompt.
        Uses semantic analysis to identify project categories.
        """
        prompt_lower = prompt.lower()
        
        # Web application types
        if any(web_app in prompt_lower for web_app in ["web app", "website", "web application", "angular app", "react app", "vue app", "spa", "single page application"]):
            return "web_app"
        
        # Mobile application types    
        elif any(mobile_app in prompt_lower for mobile_app in ["mobile app", "android app", "ios app", "flutter app", "react native"]):
            return "mobile_app"
        
        # Backend/API types
        elif any(backend in prompt_lower for backend in ["api", "backend", "server", "rest api", "graphql", "microservice"]):
            return "backend_api"
        
        # Data-focused applications
        elif any(data_app in prompt_lower for data_app in ["dashboard", "data visualization", "analytics", "chart", "report"]):
            return "data_dashboard"
        
        # General utility applications
        elif any(utility in prompt_lower for utility in ["cli", "command line", "utility", "tool"]):
            return "utility"
        
        # Default for any other type
        else:
            return "default"

    async def fully_automated_execute(self, prompt: str, project_name: str, callbacks=None) -> Dict[str, Any]:
        """
        Execute a project build request in fully automated mode.
        
        Args:
            prompt: The user's prompt
            project_name: The name of the project
            callbacks: Optional dictionary of callbacks for different events
            
        Returns:
            Result dictionary
        """
        start_time = time.time()
        logger.info(f"[Agent] Starting fully automated execution for prompt: {prompt[:100]}...")
        
        # Ensure project directory exists
        project_dir = os.path.join(self.project_manager.get_projects_dir(), project_name)
        
        # Check if this is a continuation of existing development
        is_continuation = os.path.exists(project_dir) and len(os.listdir(project_dir)) > 0
        previous_context = None
        
        if is_continuation:
            logger.info(f"[Agent] This appears to be a continuation of an existing project. Loading context...")
            # Get the existing project context to maintain continuity
            previous_context = await self._load_project_context(project_name)
            # Update the prompt with project context
            prompt = self._enhance_prompt_with_context(prompt, previous_context)
        
        # Ensure callbacks exist
        if not callbacks:
            callbacks = {}
            
        # Helper for safe callbacks
        async def safe_callback(callback, payload):
            if callback:
                try:
                    if inspect.iscoroutinefunction(callback):
                        await callback(payload)
                    else:
                        callback(payload)
                except Exception as cb_err:
                    logger.error(f"[Agent] Error in callback: {cb_err}")
                    
        # Send initial status
        await safe_callback(callbacks.get('status'), {
            "status": "starting",
            "message": "Starting automation planning...",
            "progress": 0,
            "is_continuation": is_continuation
        })

    async def _analyze_implementation_against_requirements(self, project_dir: str, requirements: str, project_type: str, build_issues: List[str] = None, model_id: str = None, implementation_iterations: int = 0) -> Dict[str, Any]:
        """
        Analyze how well the implementation meets the specified requirements.
        
        Args:
            project_dir: Path to the project directory
            requirements: String describing the project requirements
            project_type: Type of project (web_app, CLI, API, etc.)
            build_issues: List of build issues detected
            model_id: Model ID to use for analysis
            implementation_iterations: Number of implementation iterations performed so far
            
        Returns:
            Dictionary with analysis results and suggested improvements
        """
        try:
            # Always proceed with analysis, even if build was successful
            # to ensure all components are implemented completely
            force_analyze = True
            
            # Get file info for analysis
            file_info = await self._get_project_files_info(project_dir)
            
            # Get a list of all existing components
            existing_components = await self._find_all_components(project_dir)
            logger.info(f"Found existing components: {existing_components}")
        except Exception as e:
            logger.error(f"Error preparing for analysis: {e}")
            return {
                "error": str(e),
                "analysis": f"Error preparing for analysis: {e}",
                "missing_files": [],
                "missing_components": [],
                "missing_features": [],
                "missing_component_files": {},
                "needs_implementation": False
            }
        
        try:
            # Prepare prompt for analysis
            if project_type == "angular":
                project_structure = self._get_directory_tree(project_dir, max_depth=4)
                
                prompt = f"""
                You are an expert Angular developer performing a gap analysis between project requirements and implementation.
                
                Project Requirements:
                {requirements}
                
                Current Project Structure:
                {project_structure}

                Existing Components: {', '.join(existing_components) if existing_components else 'None'}
                
                File Counts by Type:
                {file_info}
                
                Build Issues: {build_issues if build_issues else 'None reported'}
                
                Please analyze if the current implementation meets the requirements and identify:
                1. Missing required components or features
                2. Incomplete implementations of existing components
                3. Major architectural issues
                4. Missing dependencies or configuration
                
                Required Format:
                1. First provide a summary assessment of how well the implementation meets requirements (1-2 paragraphs)
                2. For each identified gap, clearly specify:
                   - Issue type: "missing_component", "incomplete_implementation", "architectural_issue", or "missing_dependency"
                   - Component or file path affected
                   - Description of what needs to be implemented or fixed
                   - Suggested files to create or modify (with relative paths)
                3. Finally, provide a sorted list of MISSING FILES, strictly in the following JSON format:
                ```json
                {
                  "missing_files": [
                    {"path": "relative/path/to/file1.ext", "component": "component-name", "priority": "high"},
                    {"path": "relative/path/to/file2.ext", "component": "component-name", "priority": "medium"}
                  ]
                }
                ```
                Only include actually missing files, not files that need modifications.
                
                Keep responses practical, specific, and focused on technical implementation details.
                """
            else:
                project_structure = self._get_directory_tree(project_dir, max_depth=4)
                
                prompt = f"""
                You are an expert developer performing a gap analysis between project requirements and implementation.
                
                Project Requirements:
                {requirements}
                
                Current Project Structure:
                {project_structure}
                
                File Counts by Type:
                {file_info}
                
                Build Issues: {build_issues if build_issues else 'None reported'}
                
                Please analyze if the current implementation meets the requirements and identify:
                1. Missing components or features
                2. Incomplete implementations
                3. Major architectural issues
                4. Missing dependencies or configuration
                
                Required Format:
                1. First provide a summary assessment of how well the implementation meets requirements (1-2 paragraphs)
                2. For each identified gap, clearly specify:
                   - Issue type: "missing_feature", "incomplete_implementation", "architectural_issue", or "missing_dependency"
                   - File or area affected
                   - Description of what needs to be implemented or fixed
                   - Suggested files to create or modify (with relative paths)
                3. Finally, provide a sorted list of MISSING FILES, strictly in the following JSON format:
                ```json
                {
                  "missing_files": [
                    {"path": "relative/path/to/file1.ext", "component": "feature-name", "priority": "high"},
                    {"path": "relative/path/to/file2.ext", "component": "feature-name", "priority": "medium"}
                  ]
                }
                ```
                Only include actually missing files, not files that need modifications.
                
                Keep responses practical, specific, and focused on technical implementation details.
                """
            
            # Get model for analysis
            analysis_model = model_id or self._get_large_context_model(self.reasoning_model_id)
            
            # Perform the analysis
            llm = LLM.create(analysis_model)
            analysis_response = await llm.generate(prompt, project_name=os.path.basename(project_dir))
            
            # Extract missing files from JSON block in the response
            missing_files = []
            missing_features = []
            missing_components = []
            
            # Log the analysis response for debugging
            logger.debug(f"Analysis response: {analysis_response}")
            
            # Extract JSON block for missing files
            json_match = re.search(r'```json\s*([\s\S]+?)\s*```', analysis_response)
            if json_match:
                try:
                    json_data = json.loads(json_match.group(1))
                    if "missing_files" in json_data:
                        missing_files = json_data["missing_files"]
                        logger.info(f"Extracted {len(missing_files)} missing files from analysis")
                        
                        # Extract distinct components from missing files
                        for file in missing_files:
                            component = file.get("component", "")
                            if component and component not in missing_components:
                                missing_components.append(component)
                                
                except Exception as e:
                    logger.error(f"Error parsing JSON in analysis response: {e}")
            
            # Extract missing features from text - these might be components or functionality
            feature_patterns = [
                r'missing_(?:component|feature)[\s\S]*?Component or file affected:[\s\S]*?([^\n]+)',
                r'missing component:?\s*([^\n]+)',
                r'missing feature:?\s*([^\n]+)',
                r'should implement\s*([^\n.]+)',
                r'needs to implement\s*([^\n.]+)'
            ]
            
            for pattern in feature_patterns:
                for match in re.finditer(pattern, analysis_response, re.IGNORECASE):
                    feature = match.group(1).strip()
                    # Only add non-trivial and unique features
                    if feature and len(feature) > 3 and feature not in missing_features:
                        missing_features.append(feature)
            
            # Convert missing features to missing components if they look like component names
            for feature in missing_features:
                if (
                    '-component' in feature.lower() or
                    '-service' in feature.lower() or
                    '-page' in feature.lower() or
                    feature.lower().endswith(' component') or
                    feature.lower().endswith(' service') or
                    feature.lower().endswith(' page')
                ):
                    # Extract the core component name
                    component_name = self._extract_component_name(feature)
                    if component_name and component_name not in missing_components:
                        missing_components.append(component_name)
            
            # Convert missing files to missing components if they seem related
            missing_component_files = {}
            for file in missing_files:
                file_path = file.get("path", "")
                component = file.get("component", "")
                
                # If component is specified in the file info, use it
                if component and component not in missing_component_files:
                    missing_component_files[component] = []
                
                # Group files by component
                if component and file_path:
                    # Initialize the list if not already done
                    if component not in missing_component_files:
                        missing_component_files[component] = []
                    
                    missing_component_files[component].append(file_path)
                    
                # If no component specified but the path suggests a component
                elif not component and file_path:
                    # Try to identify component from file path
                    match = re.search(r'/([^/]+)/[^/]+\.(?:component|service|directive)\.\w+$', file_path)
                    if match:
                        extracted_component = match.group(1)
                        if extracted_component not in missing_component_files:
                            missing_component_files[extracted_component] = []
                        
                        missing_component_files[extracted_component].append(file_path)
            
            # Add component names from grouped files if they're not already in missing_components
            for component in missing_component_files.keys():
                if component not in missing_components:
                    missing_components.append(component)
            
            # Determine if we need to implement additional components based on requirements analysis
            needs_implementation = (
                bool(missing_components) or 
                bool(missing_files) or 
                bool(missing_features) or
                force_analyze
            )
            
            # Create a markdown report with the analysis results
            markdown_report = f"""# Implementation Analysis

## Summary

        
        except Exception as e:

        
            logger.error(f"Error in operation: {e}")

        
            raise{analysis_response.split('1.', 1)[0].strip() if '1.' in analysis_response else "Analysis performed based on project requirements."}

## Missing Components
{', '.join(missing_components) if missing_components else 'None identified'}

## Missing Features
{', '.join(missing_features) if missing_features else 'None identified'}

## Missing Files
{json.dumps(missing_files, indent=2) if missing_files else 'None identified'}

## Full Analysis
{analysis_response}
"""
            
            # Return the analysis results
            return {
                "analysis": analysis_response,
                "markdown_report": markdown_report,
                "missing_files": missing_files,
                "missing_components": missing_components,
                "missing_features": missing_features,
                "missing_component_files": missing_component_files,
                "needs_implementation": needs_implementation
            }
        except Exception as e:
            logger.error(f"Error analyzing implementation: {e}")
            return {
                "error": str(e),
                "analysis": f"Error performing analysis: {e}",
                "missing_files": [],
                "missing_components": [],
                "missing_features": [],
                "missing_component_files": {},
                "needs_implementation": False
            }

    def _get_large_context_model(self, original_model_id: str) -> str:
        """
        Get a large context model based on the original model ID.
        This is used for fallback when we need to process large amounts of code.
        
        Args:
            original_model_id: The original model ID
            
        Returns:
            ID of a large context model
        """
        # Model mappings - when we need larger context
        model_mappings = {
            "deepseek/deepseek-coder": "deepseek/deepseek-reasoner",  # Fallback to reasoner for code analysis
            "deepseek/deepseek-chat": "deepseek/deepseek-reasoner",
            "openai/gpt-3.5-turbo": "openai/gpt-4o",
            "openai/gpt-4o-mini": "openai/gpt-4o"
        }
        
        # Default fallback for any unknown model
        default_large_model = "deepseek/deepseek-reasoner"
        
        # Check if we have a direct mapping
        if original_model_id in model_mappings:
            return model_mappings[original_model_id]
            
        # Check for partial matches
        for model_prefix, large_model in model_mappings.items():
            if original_model_id.startswith(model_prefix.split('/')[0]):
                return large_model
        
        # Return default if no mapping found
        logger.info(f"[Agent] No large context model mapping found for {original_model_id}, using default: {default_large_model}")
        return default_large_model

    def _extract_component_name(self, text: str) -> str:
        """
        Extract component name from text description or command.
        
        Args:
            text: The text to extract component name from
            
        Returns:
            Extracted component name or empty string if not found
        """
        # Common patterns for component names in command output
        patterns = [
            r"generate\s+component[s]?\s+(\w+[-\w]*)",
            r"g\s+c\s+(\w+[-\w]*)",
            r"component[s]?\s+(\w+[-\w]*)",
            r"create\s+(\w+[-\w]*)\s+component",
            r"implement\s+(\w+[-\w]*)\s+component"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        # For Angular CLI generate commands: ng g c component-name
        ng_g_c_pattern = r"ng\s+g\s+c\s+(\w+[-\w]*)"
        match = re.search(ng_g_c_pattern, text, re.IGNORECASE)
        if match:
            return match.group(1).strip()
            
        # If no match found but text is a simple word, use it as is
        if re.match(r"^\w+[-\w]*$", text.strip()):
            return text.strip()
            
        return ""

    def _extract_dependencies(self, text: str) -> List[str]:
        """
        Extract a list of dependencies from text.
        
        Args:
            text: The text to extract dependencies from
            
        Returns:
            List of dependencies
        """
        dependencies = []
        
        # Look for common patterns for dependencies
        patterns = [
            r'npm install ([\w\-@/]+)',
            r'npm i ([\w\-@/]+)',
            r'yarn add ([\w\-@/]+)',
            r'requires? ([\w\-@/]+)',
            r'depends? on ([\w\-@/]+)',
            r'using ([\w\-@/]+) package',
            r'import .* from [\'"]([\w\-@/]+)[\'"]'
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                dep = match.group(1).strip()
                # Remove version specifiers if present
                dep = re.sub(r'@\d+\.\d+.*$', '', dep)
                if dep and dep not in dependencies:
                    dependencies.append(dep)
        
        return dependencies
        
    async def _find_all_components(self, project_dir: str) -> List[str]:
        """
        Scan the project directory to find all components.
        
        Args:
            project_dir: Path to the project directory
            
        Returns:
            List of component names found in the project
        """
        components = []
        try:
            # For Angular projects
            angular_component_pattern = r'.*\.component\.ts$'
            # For React projects
            react_component_pattern = r'.*\.(jsx|tsx)$'
            # For Vue projects
            vue_component_pattern = r'.*\.vue$'
            
            # Walk through the project directory
            for root, _, files in os.walk(project_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, project_dir)
                    
                    # Skip node_modules and other common directories
                    if any(skip_dir in rel_path for skip_dir in ['node_modules', 'dist', '.git']):
                        continue
                    
                    # Check if file matches component patterns
                    if re.match(angular_component_pattern, file):
                        # Extract component name from Angular component
                        component_name = re.sub(r'\.component\.ts$', '', file)
                        components.append(component_name)
                    elif re.match(react_component_pattern, file):
                        # Extract component name from React component
                        component_name = re.sub(r'\.(jsx|tsx)$', '', file)
                        components.append(component_name)
                    elif re.match(vue_component_pattern, file):
                        # Extract component name from Vue component
                        component_name = re.sub(r'\.vue$', '', file)
                        components.append(component_name)
            
            # Remove duplicates
            components = list(set(components))
            logger.info(f"[Agent] Found {len(components)} components in project")
            
        except Exception as e:
            logger.error(f"[Agent] Error finding components: {str(e)}")
        
        return components

    async def _generate_implementation_improvement_tasks(self, prompt: str, project_dir: str, project_type: str, implementation_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate tasks to improve the implementation based on analysis.
        
        Args:
            prompt: The original project requirements
            project_dir: Path to the project directory
            project_type: Type of project (angular, react, etc.)
            implementation_analysis: Analysis results from _analyze_implementation_against_requirements
            
        Returns:
            List of tasks to improve the implementation
        """
        improvement_tasks = []
        missing_features = implementation_analysis.get("missing_features", [])
        incomplete_components = implementation_analysis.get("incomplete_components", [])
        build_issues = implementation_analysis.get("build_issues", [])
        
        # Add tasks for missing features
        for feature in missing_features:
            # Determine which component might be responsible for this feature
            component_files = {}
            for comp in incomplete_components:
                component_files[comp.get('name', '')] = comp.get('path', '')
            
            # If we have specific component information, create targeted task
            best_component = self._find_best_component_for_feature(feature, component_files)
            
            if best_component:
                improvement_tasks.append({
                    "type": "implementation",
                    "title": f"Implement {feature}",
                    "description": f"Implement missing feature: {feature} in component {best_component}",
                    "component": best_component,
                    "feature": feature,
                    "prompt": f"Implement the missing feature '{feature}' in the {best_component} component according to these requirements: {prompt}"
                })
            else:
                # Create a general implementation task if we can't determine the specific component
                improvement_tasks.append({
                    "type": "implementation",
                    "title": f"Implement {feature}",
                    "description": f"Implement missing feature: {feature}",
                    "feature": feature,
                    "prompt": f"Implement the missing feature '{feature}' according to these requirements: {prompt}"
                })
        
        # Add tasks for incomplete components
        for comp in incomplete_components:
            component_name = comp.get('name', '')
            component_path = comp.get('path', '')
            missing_functionality = comp.get('missing', [])
            
            if component_name and component_path:
                # Read current component implementation if available
                component_content = ""
                try:
                    with open(os.path.join(project_dir, component_path), 'r', encoding='utf-8') as f:
                        component_content = f.read()
                except Exception as e:
                    logger.warning(f"[Agent] Could not read component file {component_path}: {e}")
                
                # Create task for improving the component
                missing_desc = ", ".join(missing_functionality) if missing_functionality else "required functionality"
                improvement_tasks.append({
                    "type": "component_improvement",
                    "title": f"Improve {component_name} component",
                    "description": f"Implement missing functionality in {component_name}: {missing_desc}",
                    "component": component_name,
                    "file_path": component_path,
                    "current_content": component_content,
                    "missing": missing_functionality,
                    "prompt": f"Improve the {component_name} component by implementing: {missing_desc}. Make sure it meets these requirements: {prompt}"
                })
        
        # Add tasks for fixing build issues
        if build_issues:
            build_issues_text = "\n".join(build_issues)
            improvement_tasks.append({
                "type": "build_fix",
                "title": "Fix build issues",
                "description": f"Fix build issues preventing successful compilation",
                "issues": build_issues,
                "prompt": f"Fix the following build issues:\n{build_issues_text}\nMake sure the implementation meets these requirements: {prompt}"
            })
        
        # Always include a general code review task if there are other tasks
        if improvement_tasks:
            improvement_tasks.append({
                "type": "code_review",
                "title": "Code quality review",
                "description": "Review and improve code quality, ensuring all components work together",
                "prompt": f"Review the implementation for code quality and ensure all components work together properly to meet these requirements: {prompt}"
            })
        
        return improvement_tasks
    
    def _find_best_component_for_feature(self, feature: str, component_files: Dict[str, str]) -> str:
        """
        Find the best component to implement a given feature.
        
        Args:
            feature: The feature to implement
            component_files: Dictionary of component names to file paths
            
        Returns:
            Name of the best component for this feature, or empty string if none found
        """
        if not component_files:
            return ""
            
        # Convert feature to lowercase for matching
        feature_lower = feature.lower()
        
        # Keywords that might indicate a relationship between feature and component
        feature_keywords = self.extract_keywords(feature_lower)
        
        # Score components based on keyword matches
        component_scores = {}
        for comp_name, comp_path in component_files.items():
            score = 0
            comp_name_lower = comp_name.lower()
            
            # Direct name match is a strong indicator
            for keyword in feature_keywords:
                if keyword in comp_name_lower:
                    score += 3
            
            # Special case scoring for common component types
            if ("player" in feature_lower or "playback" in feature_lower) and ("player" in comp_name_lower or "audio" in comp_name_lower):
                score += 5
            elif ("list" in feature_lower or "display" in feature_lower) and ("list" in comp_name_lower or "display" in comp_name_lower):
                score += 5
            elif ("search" in feature_lower) and ("search" in comp_name_lower):
                score += 5
            elif ("auth" in feature_lower or "login" in feature_lower) and ("auth" in comp_name_lower or "login" in comp_name_lower):
                score += 5
            
            component_scores[comp_name] = score
        
        # Find the component with the highest score
        best_component = max(component_scores.items(), key=lambda x: x[1], default=("",-1))
        
        # Only return if score is above threshold
        if best_component[1] > 0:
            return best_component[0]
            
        # Fallback to first component if no good match
        return next(iter(component_files)) if component_files else ""

    def _extract_key_features_from_requirements(self, requirements: str) -> List[str]:
        """
        Extract key features from requirements text.
        This is a simplified extraction that looks for common patterns indicating features.
        
        Args:
            requirements: The requirements text
            
        Returns:
            List of extracted features
        """
        features = []
        
        # Split the requirements into lines for processing
        lines = requirements.split('\n')
        
        # Look for bullet points or numbered lists which often indicate features
        for line in lines:
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
                
            # Check for bullet points or numbered lists
            if line.startswith(('- ', '• ', '* ', '· ')) or re.match(r'^\d+\.?\s+', line):
                # Remove the bullet or number
                feature = re.sub(r'^[-•*·]\s+', '', line)
                feature = re.sub(r'^\d+\.?\s+', '', feature)
                
                # Only add if it's substantive (more than just a few words)
                if len(feature.split()) >= 3:
                    features.append(feature)
                    
        # If no bullet points found, try to extract based on common phrases
        if not features:
            # Look for phrases that often indicate features
            feature_patterns = [
                r'should (?:have|include|provide|support|implement) (.*?)[\.;]',
                r'must (?:have|include|provide|support|implement) (.*?)[\.;]',
                r'needs? to (?:have|include|provide|support|implement) (.*?)[\.;]',
                r'requires? (.*?)[\.;]',
                r'featuring (.*?)[\.;]',
                r'with (.*?) (?:functionality|capabilities|features)[\.;]'
            ]
            
            for pattern in feature_patterns:
                matches = re.finditer(pattern, requirements, re.IGNORECASE)
                for match in matches:
                    feature = match.group(1).strip()
                    
                    # Only add if it's substantive
                    if len(feature.split()) >= 3 and feature not in features:
                        features.append(feature)
        
        # If still no features found, just split based on common sentence delimiters
        if not features:
            sentences = re.split(r'[.;:!?]\s+', requirements)
            for sentence in sentences:
                sentence = sentence.strip()
                
                # Only consider substantive sentences that might describe features
                if (len(sentence.split()) >= 5 and
                    any(keyword in sentence.lower() for keyword in 
                        ['feature', 'function', 'should', 'must', 'need', 'require', 'implement'])):
                    features.append(sentence)
        
        return features
    
    async def execute_step(self, step: Dict[str, Any], project_executor: ProjectExecutor, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute a single step in the workflow.
        
        Args:
            step: Dictionary with step information
            project_executor: ProjectExecutor instance
            context: Optional context for step execution
            
        Returns:
            Dictionary with execution results
        """
        try:
            step_type = step.get("type", "").lower()
            step_args = step.get("args", {})
            
            # Check if we have a handler for this step type
            if step_type == "shell_command":
                # Execute shell command
                command = step_args.get("command", "")
                if not command:
                    return {"success": False, "error": "No command specified for shell_command step"}
                
                result = await self.shell_executor.execute_command(command)
                return result
                
            elif step_type == "component_generation":
                # Generate component
                component_spec = step_args.get("component", {})
                requirements = step_args.get("requirements", "")
                
                if not component_spec:
                    return {"success": False, "error": "No component specification provided for component_generation step"}
                
                result = await project_executor.generate_component(component_spec, requirements)
                return result
                
            elif step_type == "component_implementation":
                # Implement component functionality
                component_name = step_args.get("component", "")
                requirements = step_args.get("requirements", "")
                
                if not component_name:
                    return {"success": False, "error": "No component name specified for component_implementation step"}
                
                result = await project_executor.implement_component_functionality(component_name, requirements)
                return result
                
            elif step_type == "service_implementation":
                # Implement service functionality
                service_name = step_args.get("service", "")
                requirements = step_args.get("requirements", "")
                
                if not service_name:
                    return {"success": False, "error": "No service name specified for service_implementation step"}
                
                # Use component implementation with service type
                service_spec = {
                    "name": service_name,
                    "type": "service"
                }
                result = await project_executor.implement_component_functionality(service_spec, requirements)
                return result
                
            elif step_type == "file_modification":
                # Modify existing file
                file_path = step_args.get("file_path", "")
                content = step_args.get("content", "")
                
                if not file_path:
                    return {"success": False, "error": "No file path specified for file_modification step"}
                
                if not content:
                    return {"success": False, "error": "No content specified for file_modification step"}
                
                result = await project_executor.modify_file(file_path, content)
                return result
                
            elif step_type == "dependency_installation":
                # Install dependencies
                dependencies = step_args.get("dependencies", [])
                
                if not dependencies:
                    return {"success": False, "error": "No dependencies specified for dependency_installation step"}
                
                result = await project_executor.install_dependencies(dependencies)
                return result
                
            elif step_type == "build":
                # Build the project
                build_command = step_args.get("command", "")
                
                result = await project_executor.build_project(build_command)
                return result
                
            elif step_type == "create_file":
                # Create a new file with the specified content
                file_path = step_args.get("file_path", "")
                content = step_args.get("content", "")
                
                if not file_path:
                    return {"success": False, "error": "No file path specified for create_file step"}
                
                result = await project_executor.create_file(file_path, content)
                return result
                
            # IMPORTANT: Also handle file step type which is used in some places
            elif step_type == "file":
                # Handle file creation/modification
                file_path = step.get("file_path", "")
                content = step.get("content", "")
                
                if not file_path:
                    return {"success": False, "error": "No file path specified for file step"}
                
                # This is essentially the same as create_file
                result = await project_executor.create_file(file_path, content)
                return result
                
            else:
                logger.warning(f"[Agent] Step type '{step_type}' not recognized for automated execution")
                return {
                    "success": False,
                    "error": f"Step type '{step_type}' not recognized for automated execution"
                }
        except Exception as e:
            logger.error(f"[Agent] Error executing step: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _apply_project_wide_fixes(self, response, project_dir, requirements):
        """
        Apply project-wide fixes suggested by the model.
        
        Args:
            response: The model's response containing potential fixes
            project_dir: The project directory
            requirements: Original project requirements
            
        Returns:
            Boolean indicating whether any fixes were applied
        """
        try:
            # Look for JSON blocks with fixes
            json_pattern = r'```json\s*([\s\S]+?)\s*```'
            fixes_applied = False
            
            for json_match in re.finditer(json_pattern, response):
                try:
                    # Extract the JSON content
                    json_content = json_match.group(1).strip()
                    
                    # Parse JSON content safely
                    fixes_data = json.loads(json_content)
                    
                    # Apply each fix
                    if isinstance(fixes_data, dict):
                        for file_path, fix_content in fixes_data.items():
                            # Ensure path is within project
                            full_path = os.path.join(project_dir, file_path)
                            if not os.path.normpath(full_path).startswith(os.path.normpath(project_dir)):
                                logger.warning(f"Attempted to modify file outside project directory: {file_path}")
                                continue
                                
                            # Ensure the directory exists
                            os.makedirs(os.path.dirname(full_path), exist_ok=True)
                            
                            # Write the content to the file
                            with open(full_path, 'w', encoding='utf-8') as f:
                                # Ensure fix_content is a string
                                if isinstance(fix_content, dict):
                                    fix_content = json.dumps(fix_content, indent=2)
                                elif isinstance(fix_content, list):
                                    fix_content = json.dumps(fix_content, indent=2)
                                elif not isinstance(fix_content, str):
                                    fix_content = str(fix_content)
                                
                                # Double-check that we're writing a string
                                if not isinstance(fix_content, str):
                                    logger.warning(f"Converting non-string content for {file_path} to string")
                                    fix_content = str(fix_content)
                                
                                f.write(fix_content)
                                
                            logger.info(f"Applied fix to {file_path}")
                            fixes_applied = True
                    elif isinstance(fixes_data, list):
                        # Handle list format
                        for fix in fixes_data:
                            if isinstance(fix, dict) and 'file_path' in fix and 'content' in fix:
                                file_path = fix['file_path']
                                fix_content = fix['content']
                                
                                # Ensure path is within project
                                full_path = os.path.join(project_dir, file_path)
                                if not os.path.normpath(full_path).startswith(os.path.normpath(project_dir)):
                                    logger.warning(f"Attempted to modify file outside project directory: {file_path}")
                                    continue
                                    
                                # Ensure the directory exists
                                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                                
                                # Write the content to the file
                                with open(full_path, 'w', encoding='utf-8') as f:
                                    # Ensure fix_content is a string
                                    if isinstance(fix_content, dict):
                                        fix_content = json.dumps(fix_content, indent=2)
                                    elif isinstance(fix_content, list):
                                        fix_content = json.dumps(fix_content, indent=2)
                                    elif not isinstance(fix_content, str):
                                        fix_content = str(fix_content)
                                    
                                    # Double-check that we're writing a string
                                    if not isinstance(fix_content, str):
                                        logger.warning(f"Converting non-string content for {file_path} to string")
                                        fix_content = str(fix_content)
                                    
                                    f.write(fix_content)
                                    
                                logger.info(f"Applied fix to {file_path}")
                                fixes_applied = True
                
                except json.JSONDecodeError as json_err:
                    logger.error(f"Error parsing JSON in project-wide fixes: {json_err}")
                    # Try to extract individual file fixes using regex
                    self._apply_non_json_fixes(json_match.group(1), project_dir)
                    
                except Exception as e:
                    logger.error(f"Error in project-wide fixes: {e}")
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    
            return fixes_applied
            
        except Exception as e:
            logger.error(f"Error in project-wide fixes: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def _apply_non_json_fixes(self, content, project_dir):
        """
        Fallback method to apply fixes when JSON parsing fails.
        
        Args:
            content: Text content with potential file fixes
            project_dir: The project directory
        """
        try:
            # Look for file path and content patterns
            file_pattern = r'File:\s*[\'"]?([\w\-\.\/]+)[\'"]?[\s\n]+```(?:\w+)?\s*([\s\S]+?)\s*```'
            
            for match in re.finditer(file_pattern, content):
                file_path = match.group(1).strip()
                file_content = match.group(2).strip()
                
                # Ensure path is within project
                full_path = os.path.join(project_dir, file_path)
                if not os.path.normpath(full_path).startswith(os.path.normpath(project_dir)):
                    logger.warning(f"Attempted to modify file outside project directory: {file_path}")
                    continue
                    
                # Ensure the directory exists
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                
                # Write the content to the file
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(file_content)
                    
                logger.info(f"Applied non-JSON fix to {file_path}")
                
        except Exception as e:
            logger.error(f"Error applying non-JSON fixes: {e}")

        except Exception as e:

            logger.error(f"Error in operation: {e}")

            raise

    async def _save_project_context(self, project_name: str, context_data: Dict[str, Any]) -> bool:
        """
        Save project context information to maintain continuity between sessions.
        
        Args:
            project_name: The name of the project
            context_data: Dictionary containing context data like file structure, 
                         requirements, development phase, etc.
            
        Returns:
            Success status
        """
        try:
            project_dir = os.path.join(self.project_manager.get_projects_dir(), project_name)
            context_file = os.path.join(project_dir, ".project_context.json")
            
            # Make sure we have a valid directory
            os.makedirs(project_dir, exist_ok=True)
            
            # Add timestamp
            context_data["last_updated"] = time.time()
            
            with open(context_file, 'w') as f:
                json.dump(context_data, f, indent=2)
                
            logger.info(f"[Agent] Saved project context for {project_name}")
            return True
        except Exception as e:
            logger.error(f"[Agent] Error saving project context: {e}")
            return False
            
    async def _load_project_context(self, project_name: str) -> Optional[Dict[str, Any]]:
        """
        Load project context information to maintain continuity between sessions.
        
        Args:
            project_name: The name of the project
            
        Returns:
            Dictionary containing context data if found, None otherwise
        """
        try:
            project_dir = os.path.join(self.project_manager.get_projects_dir(), project_name)
            context_file = os.path.join(project_dir, ".project_context.json")
            
            if os.path.exists(context_file):
                with open(context_file, 'r') as f:
                    context_data = json.load(f)
                logger.info(f"[Agent] Loaded project context for {project_name}")
                return context_data
            else:
                logger.info(f"[Agent] No project context found for {project_name}")
                return None
        except Exception as e:
            logger.error(f"[Agent] Error loading project context: {e}")
            return None
            
    def _enhance_prompt_with_context(self, prompt: str, context: Optional[Dict[str, Any]]) -> str:
        """
        Enhance a prompt with existing project context for better continuity.
        
        Args:
            prompt: The original user prompt
            context: The project context dictionary
            
        Returns:
            Enhanced prompt with context information
        """
        if not context:
            return prompt
            
        # Extract relevant context information
        last_phase = context.get("development_phase", "unknown")
        requirements = context.get("requirements", "")
        file_structure = context.get("file_structure", {})
        components = context.get("components", [])
        
        # Build context prefix
        context_prefix = f"""
This is a continuation of an existing project with the following context:
- Last development phase: {last_phase}
- Project requirements: {requirements}
- Existing components: {', '.join(components) if components else 'None'}

The project already has the following structure:
{json.dumps(file_structure, indent=2) if file_structure else 'Empty project'}

IMPORTANT: This is a CONTINUATION of development. Do NOT recreate existing files or start from scratch.
Continue development by enhancing the existing codebase with the new requirements:

"""
        
        # Combine with the original prompt
        enhanced_prompt = context_prefix + prompt
        
        logger.info(f"[Agent] Enhanced prompt with context. New length: {len(enhanced_prompt)}")
        return enhanced_prompt
    
    # Temporary fix for broken fully_automated_execute method
    async def _save_context_after_execution(self, project_name: str, prompt: str, results: Dict[str, Any]) -> None:
        """
        Save the project context after execution for session continuity.
        This should be called at the end of fully_automated_execute.
        
        Args:
            project_name: The name of the project
            prompt: The original prompt
            results: The execution results
            
        Returns:
            None
        """
        try:
            # Get project directory
            project_dir = os.path.join(self.project_manager.get_projects_dir(), project_name)
            
            # Get file structure for context
            file_structure = {}
            if os.path.exists(project_dir):
                file_structure = self._get_directory_tree(project_dir, max_depth=5)
            
            # Get existing components
            components = []
            try:
                components = await self._find_all_components(project_dir)
            except Exception as e:
                logger.warning(f"Could not find components: {e}")
            
            # Build context data
            context_data = {
                "project_name": project_name,
                "requirements": prompt,
                "development_phase": "implementation",
                "file_structure": file_structure,
                "components": components,
                "last_execution_results": {
                    "success": results.get("success", False),
                    "error": results.get("error", None),
                    "has_build_issues": bool(results.get("build_issues", [])),
                }
            }
            
            # Save the context
            await self._save_project_context(project_name, context_data)
            logger.info(f"[Agent] Saved project context for {project_name} to maintain session continuity")
            
        except Exception as e:
            logger.error(f"[Agent] Error saving project context after execution: {e}")
            logger.error(traceback.format_exc())
            
    # Add a call to this method at the end of your fully_automated_execute method, before returning results
    # Example:
    # await self._save_context_after_execution(project_name, prompt, results)
    # return results

    async def _load_project_context(self, project_name: str) -> Optional[Dict[str, Any]]:
        """
        Load project context from disk.
        
        Args:
            project_name: The name of the project
            
        Returns:
            Project context data or None if not found
        """
        try:
            if not self.project_manager:
                logger.error("[Agent] Project manager not initialized")
                return None
                
            # Get the project directory
            project = self.project_manager.get_project(project_name)
            if not project:
                logger.error(f"[Agent] Project {project_name} not found")
                return None
                
            project_dir = project.get("path")
            if not project_dir:
                logger.error(f"[Agent] No path found for project {project_name}")
                return None
                
            # Check for the main project memory file
            memory_file = os.path.join(project_dir, "project_memory.json")
            if not os.path.exists(memory_file):
                # Also check for memory file in subdirectory structure (to handle previous bugs)
                nested_memory_file = os.path.join(project_dir, project_name, "project_memory.json")
                if os.path.exists(nested_memory_file):
                    memory_file = nested_memory_file
                    logger.warning(f"[Agent] Using nested memory file: {nested_memory_file}")
                else:
                    logger.info(f"[Agent] No project memory file found for {project_name}")
                    return {}
                    
            with open(memory_file, "r", encoding="utf-8") as f:
                context_data = json.load(f)
                
            # Add metadata about when this context was loaded
            context_data["_last_loaded"] = time.time()
            context_data["_load_count"] = context_data.get("_load_count", 0) + 1
                
            logger.info(f"[Agent] Loaded project context for {project_name}: {len(str(context_data))} bytes")
            
            # Detect project framework if not already set
            if "framework" not in context_data or not context_data.get("framework"):
                framework_info = self._detect_framework(project_dir)
                if framework_info:
                    context_data["framework"] = framework_info.get("framework", "unknown")
                    context_data["framework_details"] = framework_info
                    logger.info(f"[Agent] Detected framework: {context_data['framework']}")
            
            # Load the list of components/files that have been created
            if "components" not in context_data:
                context_data["components"] = await self._find_all_components(project_dir)
                logger.info(f"[Agent] Found {len(context_data['components'])} components in project")
            
            # Add important project files 
            context_data["important_files"] = self._get_important_files(project_dir)
            
            # Save the updated context back to disk
            await self._save_project_context(project_name, context_data)
            
            return context_data
        except Exception as e:
            logger.error(f"[Agent] Error loading project context: {e}")
            return {}

    async def _save_project_context(self, project_name: str, context_data: Dict[str, Any]) -> bool:
        """
        Save project context to disk.
        
        Args:
            project_name: The name of the project
            context_data: Context data to save
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            if not self.project_manager:
                logger.error("[Agent] Project manager not initialized")
                return False
                
            # Get the project directory
            project = self.project_manager.get_project(project_name)
            if not project:
                logger.error(f"[Agent] Project {project_name} not found")
                return False
                
            project_dir = project.get("path")
            if not project_dir:
                logger.error(f"[Agent] No path found for project {project_name}")
                return False
                
            # Update metadata before saving
            context_data["_last_saved"] = time.time()
            context_data["_save_count"] = context_data.get("_save_count", 0) + 1
            
            # Ensure we don't store excessively large data in context
            # Clean up any potentially large items
            clean_context = context_data.copy()
            for key in list(clean_context.keys()):
                # Skip metadata fields
                if key.startswith("_"):
                    continue
                    
                value = clean_context[key]
                # Check if value is a string that's too long
                if isinstance(value, str) and len(value) > 10000:
                    clean_context[key] = value[:5000] + "... [truncated]"
                    logger.info(f"[Agent] Truncated large string in context: {key}")
                
                # Check if value is a list that's too long
                elif isinstance(value, list) and len(value) > 100:
                    clean_context[key] = value[:50]
                    logger.info(f"[Agent] Truncated large list in context: {key}")
                
                # Check if value is a dict with deeply nested content
                elif isinstance(value, dict) and len(json.dumps(value)) > 10000:
                    # Keep only top-level keys
                    clean_context[key] = {k: "..." for k in value.keys()}
                    logger.info(f"[Agent] Simplified large dict in context: {key}")
            
            # Save to the memory file
            memory_file = os.path.join(project_dir, "project_memory.json")
            with open(memory_file, "w", encoding="utf-8") as f:
                json.dump(clean_context, f, indent=2)
                
            logger.info(f"[Agent] Saved project context for {project_name}: {len(str(clean_context))} bytes")
            return True
        except Exception as e:
            logger.error(f"[Agent] Error saving project context: {e}", exc_info=True)
            return False

    def _detect_framework(self, project_dir: str) -> Dict[str, bool]:
        """
        Detect the framework used in a project directory.
        
        Args:
            project_dir: Path to the project directory
            
        Returns:
            Dictionary of framework detection flags
        """
        framework_detection = {
            "is_angular": False,
            "is_react": False,
            "is_vue": False,
            "is_node": False,
            "is_python": False,
            "framework": "unknown"
        }
        
        try:
            # Check for package.json
            package_json_path = os.path.join(project_dir, "package.json")
            if os.path.exists(package_json_path):
                try:
                    with open(package_json_path, "r") as f:
                        package_data = json.load(f)
                        
                    # Get dependencies to detect framework
                    dependencies = {
                        **package_data.get("dependencies", {}),
                        **package_data.get("devDependencies", {})
                    }
                    
                    # Check for Angular
                    if "@angular/core" in dependencies:
                        framework_detection["is_angular"] = True
                        framework_detection["framework"] = "angular"
                    
                    # Check for React
                    elif "react" in dependencies:
                        framework_detection["is_react"] = True
                        framework_detection["framework"] = "react"
                    
                    # Check for Vue
                    elif "vue" in dependencies:
                        framework_detection["is_vue"] = True
                        framework_detection["framework"] = "vue"
                    
                    # Generic Node.js
                    else:
                        framework_detection["is_node"] = True
                        framework_detection["framework"] = "node"
                except Exception as e:
                    logger.error(f"[Agent] Error parsing package.json: {e}")
            
            # Check for requirements.txt (Python)
            elif os.path.exists(os.path.join(project_dir, "requirements.txt")):
                framework_detection["is_python"] = True
                framework_detection["framework"] = "python"
                
                # Check for specific Python frameworks
                with open(os.path.join(project_dir, "requirements.txt"), "r") as f:
                    requirements = f.read().lower()
                    if "django" in requirements:
                        framework_detection["framework"] = "django"
                    elif "flask" in requirements:
                        framework_detection["framework"] = "flask"
                    elif "fastapi" in requirements:
                        framework_detection["framework"] = "fastapi"
        except Exception as e:
            logger.error(f"[Agent] Error in _detect_framework: {e}")
        
        return framework_detection

    def _get_important_files(self, project_dir: str) -> List[Dict[str, Any]]:
        """
        Get a list of important files in the project.
        
        Args:
            project_dir: Path to the project directory
            
        Returns:
            List of important files with metadata
        """
        important_files = []
        
        try:
            # Common important files by framework
            framework_files = {
                "angular": [
                    "angular.json",
                    "tsconfig.json",
                    "src/app/app.module.ts",
                    "src/app/app-routing.module.ts",
                    "src/app/app.component.ts",
                    "src/app/app.component.html"
                ],
                "react": [
                    "package.json",
                    "tsconfig.json",
                    "src/App.js",
                    "src/App.tsx",
                    "src/index.js",
                    "src/index.tsx"
                ],
                "vue": [
                    "package.json",
                    "vue.config.js",
                    "src/main.js",
                    "src/App.vue"
                ],
                "node": [
                    "package.json",
                    "index.js",
                    "server.js",
                    "app.js"
                ],
                "python": [
                    "requirements.txt",
                    "main.py",
                    "app.py",
                    "setup.py"
                ]
            }
            
            # Detect framework
            framework = self._detect_framework(project_dir).get("framework", "unknown")
            
            # Get framework-specific files
            files_to_check = framework_files.get(framework, []) + [
                "README.md",
                ".gitignore",
                "package.json",
                "tsconfig.json"
            ]
            
            # Check and add each file if it exists
            for file_path in files_to_check:
                full_path = os.path.join(project_dir, file_path)
                if os.path.exists(full_path) and os.path.isfile(full_path):
                    try:
                        with open(full_path, "r", encoding="utf-8") as f:
                            content = f.read()
                        
                        important_files.append({
                            "path": file_path,
                            "content": content,
                            "size": os.path.getsize(full_path),
                            "modified_at": datetime.fromtimestamp(os.path.getmtime(full_path)).isoformat()
                        })
                    except Exception as e:
                        logger.error(f"[Agent] Error reading file {file_path}: {e}")
        except Exception as e:
            logger.error(f"[Agent] Error in _get_important_files: {e}")
        
        return important_files