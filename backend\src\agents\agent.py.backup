"""
Main Agent class that coordinates the workflow between specialized agents.
"""
import os
import json
import asyncio
import time
from typing import Dict, List, Any, Optional, Callable
import logging
from src.agents.shell_executor import ShellExecutor
from src.agents.automation_executor import AutomationExecutor
from src.agents.project_executor import ProjectExecutor
from src.feedback.feedback_store import FeedbackStore
from src.socket_instance import emit_agent_status, emit_agent_message

from src.llm.llm import LLM
from src.agents.planner import Planner
from src.agents.researcher import Researcher
from src.agents.coder import Coder
from src.agents.documenter import Documenter
from src.project import ProjectManager
from src.state import AgentState
from src.llm.searxng_client import searxng_search, searxng_search_structured
import inspect
import re
from datetime import datetime

logger = logging.getLogger(__name__)

class Agent:
    """
    Main Agent class that coordinates the workflow between specialized agents.
    """
    def __init__(self, openai_model_id: str = None, local_llm_model_id: str = None):
        """
        Initialize the Agent with model IDs.
        """
        # Try to load default model configuration from config.json
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config.json")
        default_primary = "deepseek/deepseek-coder"
        default_backup = "openai/gpt-4o-mini"
        
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    if "models" in config:
                        default_primary = config["models"].get("default_primary", default_primary)
                        default_backup = config["models"].get("default_backup", default_backup)
                        logger.info(f"[Agent] Loaded model configuration from config.json: primary={default_primary}, backup={default_backup}")
            except Exception as e:
                logger.error(f"[Agent] Error loading model configuration from config.json: {e}")
        
        # Use provided models or defaults
        self.primary_model_id = openai_model_id or default_primary
        self.backup_model_id = default_backup
        self.local_llm_model_id = local_llm_model_id
        
        logger.info(f"[Agent] Initialized with primary model: {self.primary_model_id}, backup: {self.backup_model_id}")
        
        # Initialize specialized agents with the primary model
        self.planner = Planner(model_id=self.primary_model_id)
        self.researcher = Researcher(model_id=self.primary_model_id)
        self.coder = Coder(model_id=self.primary_model_id)
        self.documenter = Documenter(model_id=self.primary_model_id)
        
        self.project_manager = ProjectManager()
        self.agent_state = AgentState()
        self.context_keywords = []
        self.execution_stats = {
            "subtasks_completed": 0,
            "subtasks_failed": 0,
            "total_execution_time": 0,
            "model_usage": {}
        }
        # Maximum number of retries for failed subtasks
        self.max_retries = 2

    def _get_recommended_model(self, task_type: str) -> str:
        """
        Get recommended model for this task type based on feedback.
        
        Args:
            task_type: Type of task to get recommendation for
            
        Returns:
            Recommended model ID or default primary model ID
        """
        try:
            feedback_store = FeedbackStore()
            recommended_model = feedback_store.get_model_recommendation(task_type)
            
            # Fall back to default if no recommendation available
            if not recommended_model:
                return self.primary_model_id
            
            logger.info(f"[Agent] Using recommended model {recommended_model} for task type {task_type}")
            return recommended_model
        except Exception as e:
            logger.error(f"[Agent] Error getting model recommendation: {e}")
            return self.primary_model_id

    def assign_models_to_subtasks(self, subtasks: list) -> list:
        """
        Assign models to subtasks based on task complexity and feedback history.
        """
        assignments = []
        for i, subtask in enumerate(subtasks):
            # Always use browser for research-heavy tasks
            subtask['force_web'] = subtask.get('type') in ['research', 'planning', 'analysis']
            
            # Determine best model based on feedback and task complexity
            task_type = subtask.get('type', 'unknown')
            task_complexity = self._estimate_task_complexity(subtask)
            recommended_model = self._get_recommended_model(task_type)
            
            # Select models based on complexity
            models_to_use = []
            
            # Split the recommended model ID
            primary_provider = recommended_model.split("/")[0] if "/" in recommended_model else "unknown"
            backup_provider = self.backup_model_id.split("/")[0] if "/" in self.backup_model_id else "openai"
            
            # For high-complexity tasks, always use the DeepSeek and GPT-4 models
            if task_complexity == "high":
                # Primary model should be DeepSeek
                if "deepseek" in self.primary_model_id:
                    models_to_use.append(("deepseek", self.primary_model_id))
                else:
                    models_to_use.append(("deepseek", "deepseek/deepseek-coder"))
                
                # Add backup model (typically GPT-4 variant)
                models_to_use.append((backup_provider, self.backup_model_id))
            else:
                # Add the recommended model as primary
                models_to_use.append((primary_provider, recommended_model))
                
                # Add the backup model if different from the recommended one
                if recommended_model != self.backup_model_id:
                    models_to_use.append((backup_provider, self.backup_model_id))
            
            # Only add local LLM if specified
            if self.local_llm_model_id and "lm-studio" in self.local_llm_model_id:
                if not any(model_id == self.local_llm_model_id for _, model_id in models_to_use):
                    models_to_use.append(("local", self.local_llm_model_id))
            
            assignments.append({
                "subtask": subtask,
                "models": models_to_use
            })
        return assignments
        
    def _estimate_task_complexity(self, subtask: dict) -> str:
        """
        Estimate task complexity based on its description and requirements.
        
        Args:
            subtask: The subtask to analyze
            
        Returns:
            Complexity level: "low", "medium", or "high"
        """
        # Default to medium complexity
        complexity = "medium"
        
        description = subtask.get('description', '').lower()
        prompt = subtask.get('prompt', '').lower()
        subtask_type = subtask.get('type', '').lower()
        
        # High complexity indicators
        high_complexity_keywords = [
            "complex", "sophisticated", "advanced", 
            "optimize", "refactor", "architecture",
            "security", "performance", "scalable"
        ]
        
        # Low complexity indicators
        low_complexity_keywords = [
            "simple", "basic", "trivial",
            "update text", "change color", "rename",
            "add comment", "documentation"
        ]
        
        # Check for high complexity indicators
        for keyword in high_complexity_keywords:
            if keyword in description or keyword in prompt:
                complexity = "high"
                break
                
        # Check for low complexity indicators (only if not already marked as high)
        if complexity != "high":
            for keyword in low_complexity_keywords:
                if keyword in description or keyword in prompt:
                    complexity = "low"
                    break
        
        # Task types that typically require high complexity
        if subtask_type in ["planning", "architecture", "analysis", "optimization", "security"]:
            complexity = "high"
            
        # Task types that are typically low complexity
        if subtask_type in ["documentation", "comment", "format"]:
            complexity = "low"
            
        # File operations are often high complexity unless they're documentation
        if subtask_type == "file" and not any(doc_type in description.lower() for doc_type in ["readme", "documentation", ".md", "comment"]):
            if subtask.get("file_path", "").endswith((".ts", ".js", ".py", ".java", ".cpp")):
                complexity = "high"
                
        logger.debug(f"[Agent] Estimated complexity for task '{description[:30]}...' is {complexity}")
        return complexity

    async def execute_subtask(self, subtask: dict, models: list, project_name: str, progress_callback=None, subtask_idx=None, total_subtasks=None, retry_count=0) -> dict:
        """
        Execute a subtask using the assigned models with retry capability.
        """
        from src.llm.llm import LLM
        from src.llm.searxng_client import searxng_search
        
        start_time = time.time()
        prompt = subtask.get("prompt") or json.dumps(subtask)
        subtask_desc = subtask.get('description', str(subtask))
        subtask_type = subtask.get('type', 'unknown')
        timing = {}
        status = 'started'
        error = None
        
        # Log the start of the subtask execution
        logger.info(f"[Agent] Starting subtask {subtask_idx}/{total_subtasks}: {subtask_desc} (Retry: {retry_count})")
        
        # Helper to safely call the async progress callback
        async def safe_progress_callback(payload):
            if progress_callback:
                try:
                    if inspect.iscoroutinefunction(progress_callback):
                        await progress_callback(payload)
                    else:
                        progress_callback(payload)
                except Exception as cb_err:
                    logger.error(f"[Agent] Error in progress_callback: {cb_err}")
                    
        await safe_progress_callback({
            'event': 'subtask_started',
            'subtask_idx': subtask_idx,
            'total_subtasks': total_subtasks,
            'subtask': subtask_desc,
            'status': status,
            'retry_count': retry_count
        })
        
        # Special handling for file content generation steps
        if subtask.get('type') == 'file' and not subtask.get('content'):
            logger.info(f"[Agent] File creation step detected without content. Generating content for {subtask.get('file_path', 'unknown file')}")
            # Create a content generation prompt based on the file path and project context
            file_path = subtask.get('file_path', '')
            file_ext = os.path.splitext(file_path)[1] if file_path else ''
            
            # Determine the file type and create a specialized prompt
            if 'component.ts' in file_path:
                content_prompt = f"Create a fully functional Angular component in TypeScript for file {file_path} for project {project_name}. Include all necessary imports, decorators, and implementation. This component should be feature-complete and ready to use."
            elif 'component.html' in file_path:
                content_prompt = f"Create the complete HTML template for Angular component {file_path} for project {project_name}. Include all necessary elements, bindings, and structural directives."
            elif 'component.scss' in file_path or 'component.css' in file_path:
                content_prompt = f"Create complete SCSS/CSS styling for Angular component {file_path} for project {project_name}. Include all necessary styles to make the component visually appealing and functional."
            elif 'service.ts' in file_path:
                content_prompt = f"Create a fully functional Angular service in TypeScript for file {file_path} for project {project_name}. Include all necessary imports, decorators, methods with implementation, and error handling."
            elif 'module.ts' in file_path:
                content_prompt = f"Create a complete Angular module in TypeScript for file {file_path} for project {project_name}. Include all necessary imports, declarations, and providers."
            elif '.ts' in file_path:
                content_prompt = f"Create a fully functional TypeScript file for {file_path} for project {project_name}. Include all necessary imports and complete implementation."
            elif '.html' in file_path:
                content_prompt = f"Create complete HTML content for {file_path} for project {project_name}. Include all necessary elements and structure."
            elif '.css' in file_path or '.scss' in file_path:
                content_prompt = f"Create complete CSS/SCSS styling for {file_path} for project {project_name}. Include all necessary styles to make the page visually appealing and functional."
            elif '.js' in file_path:
                content_prompt = f"Create a fully functional JavaScript file for {file_path} for project {project_name}. Include all necessary imports and complete implementation."
            elif '.json' in file_path:
                content_prompt = f"Create a complete, valid JSON file for {file_path} for project {project_name}."
            elif '.md' in file_path:
                content_prompt = f"Create a comprehensive Markdown document for {file_path} for project {project_name}."
            else:
                content_prompt = f"Create complete content for file {file_path} for project {project_name}. This should be fully implemented and ready to use."
            
            # SearxNG research for content generation
            try:
                web_results = await searxng_search(f"implementation {file_path} code example")
                content_prompt += f"\n\nWeb search results for reference:\n{web_results}"
            except Exception as e:
                logger.error(f"[Agent] SearxNG error for content research: {e}")
            
            # Generate content using both models
            content_results = []
            for model_type, model_id in models:
                try:
                    # Skip Ollama models entirely
                    if "ollama" in model_id.lower():
                        logger.warning(f"[Agent] Skipping Ollama model {model_id} for content generation as it's not available")
                        continue
                        
                    llm = LLM.create(model_id)
                    logger.info(f"[Agent] Generating content using {model_type} model for {file_path}")
                    content = await llm.generate(content_prompt, project_name)
                    
                    # Clean up the content - extract from code blocks if necessary
                    code_block_match = re.search(r'```(?:\w+)?\s*([\s\S]+?)\s*```', content)
                    if code_block_match:
                        clean_content = code_block_match.group(1).strip()
                    else:
                        clean_content = content.strip()
                    
                    # Quality score - simple heuristic based on content length and specific indicators
                    quality_score = len(clean_content.split('\n'))
                    if "import" in clean_content and file_ext in ['.ts', '.js']:
                        quality_score += 10
                    if "class" in clean_content and file_ext in ['.ts', '.js']:
                        quality_score += 5
                    if "function" in clean_content and file_ext in ['.ts', '.js']:
                        quality_score += 5
                    if "<div" in clean_content and file_ext in ['.html']:
                        quality_score += 5
                    
                    content_results.append({
                        "model_type": model_type,
                        "model_id": model_id,
                        "content": clean_content,
                        "quality_score": quality_score
                    })
                    
                except Exception as e:
                    logger.error(f"[Agent] Error generating content with {model_type} model: {e}")
                    content_results.append({
                        "model_type": model_type,
                        "model_id": model_id,
                        "content": f"// Error generating content: {str(e)}",
                        "quality_score": 0
                    })
            
            # Select the best content based on quality score
            if content_results:
                best_content = max(content_results, key=lambda x: x.get('quality_score', 0))
                status = "failed"
        
        # For research-intensive tasks, perform web search
        web_results = ""
        if subtask.get('force_web', False) or any(keyword in prompt.lower() for keyword in ['search', 'find', 'latest', 'research', 'information']):
            try:
                # Create a focused search query based on the subtask
                search_query = self._create_search_query(subtask, project_name)
                logger.info(f"[Agent] Performing web search for: {search_query}")
                web_results = await searxng_search(search_query)
                logger.info(f"[Agent] Web search completed with {len(web_results.split())} words of results")
            except Exception as e:
                logger.error(f"[Agent] Web search error: {e}")
                web_results = f"Error performing web search: {str(e)}"
        
        # Add web search results to the prompt if available
        if web_results:
            prompt += f"\n\nRecent web search results:\n{web_results}"
        
        # Execute the subtask with all assigned models in parallel
        model_results = []
        tasks = []
        
        # Set up tasks for parallel execution
        for model_type, model_id in models:
            tasks.append(self._call_model_with_timeout(model_type, model_id, prompt, project_name, subtask_type))
        
        # Execute all model tasks in parallel
        start_models_time = time.time()
        completed_tasks = await asyncio.gather(*tasks, return_exceptions=True)
        model_execution_time = time.time() - start_models_time
        
        # Process results from all models
        for i, result in enumerate(completed_tasks):
            model_type, model_id = models[i]
            
            # Handle successful results
            if isinstance(result, dict) and not isinstance(result, Exception):
                model_results.append({
                    "model_type": model_type,
                    "model_id": model_id,
                    "response": result.get("response", ""),
                    "error": None,
                    "execution_time": result.get("execution_time", 0),
                    "quality_score": self._calculate_quality_score(result.get("response", ""), subtask)
                })
                
                # Update model usage statistics
                if model_id not in self.execution_stats["model_usage"]:
                    self.execution_stats["model_usage"][model_id] = {
                        "success_count": 0,
                        "error_count": 0,
                        "total_time": 0
                    }
                self.execution_stats["model_usage"][model_id]["success_count"] += 1
                self.execution_stats["model_usage"][model_id]["total_time"] += result.get("execution_time", 0)
                
            # Handle errors
            else:
                error_message = str(result) if isinstance(result, Exception) else "Unknown error"
                model_results.append({
                    "model_type": model_type,
                    "model_id": model_id,
                    "response": "",
                    "error": error_message,
                    "execution_time": 0,
                    "quality_score": 0
                })
                
                # Update model usage statistics for errors
                if model_id not in self.execution_stats["model_usage"]:
                    self.execution_stats["model_usage"][model_id] = {
                        "success_count": 0,
                        "error_count": 0,
                        "total_time": 0
                    }
                self.execution_stats["model_usage"][model_id]["error_count"] += 1
        
        # Select the best result based on quality score
        valid_results = [r for r in model_results if r["error"] is None and r["response"]]
        if valid_results:
            best_result = max(valid_results, key=lambda x: x["quality_score"])
            response = best_result["response"]
            status = "completed"
            self.execution_stats["subtasks_completed"] += 1
        else:
            # All models failed, check if retry is possible
            if retry_count < self.max_retries:
                logger.warning(f"[Agent] All models failed for subtask {subtask_idx}. Retrying ({retry_count + 1}/{self.max_retries})...")
                return await self.execute_subtask(
                    subtask, models, project_name, progress_callback, 
                    subtask_idx, total_subtasks, retry_count + 1
                )
            
            # Max retries exceeded, mark as failed
            error = "All models failed to generate a valid response"
            response = "Error: " + error
            status = "failed"
            self.execution_stats["subtasks_failed"] += 1
        
        # Calculate execution time
        execution_time = time.time() - start_time
        timing["total_execution_time"] = execution_time
        timing["model_execution_time"] = model_execution_time
        
        # Update progress
        await safe_progress_callback({
            'event': 'subtask_completed',
            'subtask_idx': subtask_idx,
            'total_subtasks': total_subtasks,
            'subtask': subtask_desc,
            'status': status,
            'execution_time': execution_time
        })
        
        # Handle the response based on subtask type
        if subtask.get('type') == 'file' and not subtask.get('content'):
            subtask['content'] = response
            
            # Create or update the file
            try:
                self.project_manager.create_or_update_file(
                    project_name,
                    subtask.get('file_path', ''),
                    subtask['content']
                )
            except Exception as e:
                logger.error(f"[Agent] Error creating file {subtask.get('file_path', '')}: {e}")
                error = str(e)
                status = "failed"
        
        return {
            "status": status,
            "subtask": subtask,
            "model_results": model_results,
            "timing": timing,
            "error": error,
            "response": response
        }
        
    async def _call_model_with_timeout(self, model_type, model_id, prompt, project_name, subtask_type, timeout=120):
        """
        Call a model with a timeout to prevent hanging on slow responses.
        
        Args:
            model_type: Type of model (openai, local, etc.)
            model_id: ID of the model
            prompt: The prompt to send
            project_name: Name of the project
            subtask_type: Type of subtask being executed
            timeout: Timeout in seconds
            
        Returns:
            Dictionary with response and timing information, or exception
        """
        try:
            # Create a task with timeout
            return await asyncio.wait_for(
                self._call_model(model_type, model_id, prompt, project_name, subtask_type),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"[Agent] Model {model_id} timed out after {timeout} seconds")
            return Exception(f"Model {model_id} timed out after {timeout} seconds")
        except Exception as e:
            logger.error(f"[Agent] Error calling model {model_id}: {e}")
            return e
        
    async def _call_model(self, model_type, model_id, prompt, project_name, subtask_type):
        """
        Call a model and measure execution time.
        
        Args:
            model_type: Type of model (openai, local, etc.)
            model_id: ID of the model
            prompt: The prompt to send
            project_name: Name of the project
            subtask_type: Type of subtask being executed
            
        Returns:
            Dictionary with response and timing information
        """
        from src.llm.llm import LLM
        
        try:
            start_time = time.time()
            
            # Skip Ollama models for certain tasks
            if "ollama" in model_id.lower() and subtask_type in ['planning', 'architecture', 'analysis']:
                logger.warning(f"[Agent] Skipping Ollama model {model_id} for complex task type {subtask_type}")
                return {"response": "", "error": "Skipped Ollama for complex task", "execution_time": 0}
                
            llm = LLM.create(model_id)
            logger.info(f"[Agent] Calling {model_type} model ({model_id}) for project {project_name}")
            
            response = await llm.generate(prompt, project_name)
            execution_time = time.time() - start_time
            
            logger.info(f"[Agent] {model_type} model ({model_id}) responded in {execution_time:.2f}s")
            
            return {
                "response": response,
                "execution_time": execution_time
            }
        except Exception as e:
            logger.error(f"[Agent] Error calling {model_type} model ({model_id}): {e}")
            raise

    def _create_search_query(self, subtask: dict, project_name: str) -> str:
        """
        Create a focused search query based on the subtask.
        
        Args:
            subtask: The subtask to create a query for
            project_name: Name of the project
            
        Returns:
            A focused search query
        """
        description = subtask.get('description', '')
        prompt = subtask.get('prompt', '')
        subtask_type = subtask.get('type', '')
        
        # Extract key terms from the description and prompt
        key_terms = self.extract_keywords(description + " " + prompt)
        
        # Build the query based on the task type
        if subtask_type == 'file':
            file_path = subtask.get('file_path', '')
            file_ext = os.path.splitext(file_path)[1] if file_path else ''
            
            if 'component' in file_path.lower():
                framework = 'Angular' if '.ts' in file_path else 'React' if '.jsx' in file_path else ''
                return f"{framework} component {os.path.basename(file_path)} implementation example {' '.join(key_terms[:3])}"
            elif 'service' in file_path.lower():
                return f"backend service implementation {file_ext} example {' '.join(key_terms[:3])}"
            else:
                return f"code example {file_ext} {os.path.basename(file_path)} {' '.join(key_terms[:3])}"
        
        elif subtask_type in ['research', 'planning', 'analysis']:
            return f"{' '.join(key_terms[:5])} {project_name} best practices"
        
        else:
            # Generic query using key terms
            return f"{' '.join(key_terms[:5])} {project_name}"
            
    def _calculate_quality_score(self, response: str, subtask: dict) -> float:
        """
        Calculate a quality score for a model response.
        
        Args:
            response: The model's response
            subtask: The subtask being executed
            
        Returns:
            Quality score as a float
        """
        if not response:
            return 0.0
            
        score = 0.0
        subtask_type = subtask.get('type', '')
        
        # Base score based on length (up to a point)
        length = len(response)
        score += min(length / 1000, 5)  # Cap at 5 points for length
        
        # Check for code blocks in appropriate tasks
        if subtask_type == 'file' or 'code' in subtask.get('description', '').lower():
            code_blocks = re.findall(r'```(?:\w+)?\s*([\s\S]+?)\s*```', response)
            if code_blocks:
                score += 3
                
                # Check for imports/includes in code
                if any('import' in block or 'include' in block or '#include' in block for block in code_blocks):
                    score += 2
                    
                # Check for function/class definitions
                if any('function' in block or 'class' in block or 'def ' in block for block in code_blocks):
                    score += 2
        
        # For research tasks, reward citations and references
        if subtask_type == 'research' or 'research' in subtask.get('description', '').lower():
            # Check for URLs
            urls = re.findall(r'https?://\S+', response)
            score += min(len(urls), 3)
            
            # Check for citation patterns [1], (Smith, 2020), etc.
            citations = re.findall(r'\[\d+\]|\([A-Za-z]+,?\s+\d{4}\)', response)
            score += min(len(citations), 2)
        
        # For planning tasks, reward structured content
        if subtask_type == 'planning' or 'plan' in subtask.get('description', '').lower():
            # Check for numbered lists/steps
            numbered_items = re.findall(r'\n\s*\d+\.\s+', response)
            score += min(len(numbered_items), 3)
            
            # Check for headers/sections
            headers = re.findall(r'\n\s*#+\s+\w+', response)
            score += min(len(headers), 2)
        
        logger.debug(f"[Agent] Quality score for response: {score:.2f}")
        return score

    async def generate_automation_plan(self, prompt: str, project_name: str) -> Dict[str, Any]:
        """
        Phase 1: Generate a complete, actionable automation plan (but do NOT execute it).
        Returns the plan and plan details for user review/confirmation.
        """
        logger.info(f"[Agent] Generating automation plan for project: {project_name}")
        self.project_manager.create_project_if_not_exists(project_name)
        plan = await self.planner.execute(prompt, project_name)
        plan_details = self.planner.parse_response(plan)
        return {
            "plan_details": plan_details,
            "raw_plan": plan
        }

    def convert_plan_to_steps(self, plan_details: dict) -> list:
        """
        Convert the descriptive plan to a structured list of actionable steps for execution.
        
        Args:
            plan_details: Dictionary with plan details including project name, description, and steps
            
        Returns:
            List of structured steps (commands, file operations, etc.)
        """
        logger.info(f"[Agent] Converting plan to actionable steps")
        
        steps = []
        step_map = plan_details.get('steps_desc', {})
        project_name = plan_details.get('project_name', 'default_project')
        
        # First, check if there are actionable steps in the plan_details
        if 'actionable_steps' in plan_details and isinstance(plan_details['actionable_steps'], list):
            logger.info(f"[Agent] Using pre-parsed actionable steps from plan: {len(plan_details['actionable_steps'])} steps")
            return plan_details['actionable_steps']
        
        # Add Angular project initialization
        steps.append({
            "type": "command", 
            "command": f"ng new {project_name} --routing --style=scss --skip-git",
            "description": "Initialize Angular project"
        })
        
        # Add dependency installation
        steps.append({
            "type": "command", 
            "command": "npm install",
            "description": "Install dependencies"
        })
        
        # Extract components and other elements from the plan
        for num in sorted(step_map.keys()):
            desc = step_map[num]['description'].lower()
            
            # Check for component generation
            if 'generate' in desc and 'component' in desc:
                import re
                match = re.search(r'component\s+(\w+)', desc)
                comp = match.group(1) if match else 'app'
                steps.append({
                    "type": "command", 
                    "command": f"ng generate component {comp}",
                    "description": f"Generate {comp} component"
                })
                
                # Add placeholder files for this component
                steps.append({
                    "type": "file",
                    "file_path": f"src/app/{comp}/{comp}.component.ts",
                    "content": self._generate_component_ts(comp, project_name),
                    "description": f"Create {comp} component TypeScript file"
                })
                
                steps.append({
                    "type": "file",
                    "file_path": f"src/app/{comp}/{comp}.component.html",
                    "content": self._generate_component_html(comp, project_name),
                    "description": f"Create {comp} component HTML template"
                })
                
                steps.append({
                    "type": "file",
                    "file_path": f"src/app/{comp}/{comp}.component.scss",
                    "content": self._generate_component_scss(comp),
                    "description": f"Create {comp} component SCSS styles"
                })
            
            # Check for service generation
            elif 'generate' in desc and 'service' in desc:
                import re
                match = re.search(r'service\s+(\w+)', desc)
                service = match.group(1) if match else 'data'
                steps.append({
                    "type": "command", 
                    "command": f"ng generate service {service}",
                    "description": f"Generate {service} service"
                })
                
                # Add placeholder file for this service
                steps.append({
                    "type": "file",
                    "file_path": f"src/app/services/{service}.service.ts",
                    "content": self._generate_service_ts(service),
                    "description": f"Create {service} service TypeScript file"
                })
            
            # Check for folder creation
            elif 'create folder' in desc or 'create directory' in desc or 'mkdir' in desc:
                folder = desc.split('folder')[-1].strip() if 'folder' in desc else desc.split('directory')[-1].strip()
                steps.append({
                    "type": "folder", 
                    "path": folder,
                    "description": f"Create folder: {folder}"
                })
            
            # Check for README generation
            elif 'readme' in desc:
                steps.append({
                    "type": "file",
                    "file_path": "README.md", 
                    "content": self._generate_readme(project_name),
                    "description": "Create README.md file"
                })
        
        # Add a default app component files if not already included
        if not any('app.component.html' in step.get('file_path', '') for step in steps if step.get('type') == 'file'):
            steps.append({
                "type": "file",
                "file_path": "src/app/app.component.html",
                "content": self._generate_app_component_html(project_name),
                "description": "Create app.component.html file"
            })
        
        if not any('app.component.ts' in step.get('file_path', '') for step in steps if step.get('type') == 'file'):
            steps.append({
                "type": "file",
                "file_path": "src/app/app.component.ts",
                "content": self._generate_app_component_ts(project_name),
                "description": "Create app.component.ts file"
            })
            
        if not any('app.component.scss' in step.get('file_path', '') for step in steps if step.get('type') == 'file'):
            steps.append({
                "type": "file",
                "file_path": "src/app/app.component.scss",
                "content": self._generate_app_component_scss(),
                "description": "Create app.component.scss file"
            })
        
        # Add steps to open tools
        steps.append({
            "type": "open_vscode",
            "description": "Open VS Code editor"
        })
        
        steps.append({
            "type": "open_terminal",
            "description": "Open terminal"
        })
        
        logger.info(f"[Agent] Generated {len(steps)} actionable steps")
        return steps
    
    def _generate_component_ts(self, component_name: str, project_name: str) -> str:
        """Generate TypeScript code for a component"""
        pascal_name = self._to_pascal_case(component_name)
        return f"""import {{ Component, OnInit }} from '@angular/core';

@Component({{
  selector: 'app-{component_name}',
  templateUrl: './{component_name}.component.html',
  styleUrls: ['./{component_name}.component.scss']
}})
export class {pascal_name}Component implements OnInit {{
  title = '{project_name}';
  
  constructor() {{ }}
  
  ngOnInit(): void {{
    // Initialize component
  }}
}}
"""
    
    def _generate_component_html(self, component_name: str, project_name: str) -> str:
        """Generate HTML template for a component"""
        return f"""<div class="{component_name}-container">
  <h2>{self._to_display_name(component_name)} Component</h2>
  <p>Welcome to the {component_name} component of {project_name}</p>
</div>
"""
    
    def _generate_component_scss(self, component_name: str) -> str:
        """Generate SCSS styles for a component"""
        return f""".{component_name}-container {{
  padding: 20px;
  margin: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  
  h2 {{
    color: #333;
    margin-bottom: 15px;
  }}
  
  p {{
    color: #666;
  }}
}}
"""
    
    def _generate_service_ts(self, service_name: str) -> str:
        """Generate TypeScript code for a service"""
        pascal_name = self._to_pascal_case(service_name)
        return f"""import {{ Injectable }} from '@angular/core';
import {{ HttpClient }} from '@angular/common/http';
import {{ Observable, throwError }} from 'rxjs';
import {{ catchError }} from 'rxjs/operators';

@Injectable({{
  providedIn: 'root'
}})
export class {pascal_name}Service {{
  private apiUrl = 'api/{service_name}';
  
  constructor(private http: HttpClient) {{ }}
  
  getData(): Observable<any[]> {{
    return this.http.get<any[]>(this.apiUrl)
      .pipe(
        catchError(this.handleError)
      );
  }}
  
  private handleError(error: any) {{
    console.error('An error occurred', error);
    return throwError(() => error);
  }}
}}
"""
    
    def _generate_readme(self, project_name: str) -> str:
        """Generate content for README.md"""
        return f"""# {project_name}

This project was generated with Angular.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via Karma.

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice.

## Further help

To get more help on the Angular CLI use `ng help` or check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
"""
    
    def _generate_app_component_html(self, project_name: str) -> str:
        """Generate content for app.component.html"""
        return f"""<div class="app-container">
  <header class="app-header">
    <h1>{self._to_display_name(project_name)}</h1>
    <nav>
      <ul>
        <li><a href="#">Home</a></li>
        <li><a href="#">About</a></li>
        <li><a href="#">Contact</a></li>
      </ul>
    </nav>
  </header>
  
  <main class="app-content">
    <router-outlet></router-outlet>
  </main>
  
  <footer class="app-footer">
    <p>&copy; {datetime.now().year} {self._to_display_name(project_name)}. All rights reserved.</p>
  </footer>
</div>
"""
    
    def _generate_app_component_ts(self, project_name: str) -> str:
        """Generate content for app.component.ts"""
        return f"""import {{ Component }} from '@angular/core';

@Component({{
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
}})
export class AppComponent {{
  title = '{project_name}';
}}
"""
    
    def _generate_app_component_scss(self) -> str:
        """Generate content for app.component.scss"""
        return """.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-header {
  background-color: #3f51b5;
  color: white;
  padding: 1rem;
  
  h1 {
    margin: 0;
    font-size: 1.8rem;
  }
  
  nav {
    margin-top: 1rem;
    
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      gap: 1rem;
      
      li a {
        color: white;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.app-content {
  flex: 1;
  padding: 1rem;
}

.app-footer {
  background-color: #f5f5f5;
  padding: 1rem;
  text-align: center;
  
  p {
    margin: 0;
    color: #666;
  }
}
"""
    
    def _to_pascal_case(self, name: str) -> str:
        """Convert a name to PascalCase"""
        return ''.join(word.capitalize() for word in name.split('-'))
    
    def _to_display_name(self, name: str) -> str:
        """Convert a name to display format (e.g., 'my-app' to 'My App')"""
        return ' '.join(word.capitalize() for word in re.sub(r'[-_]', ' ', name).split())

    def run_automation_plan(self, plan: list, project_name: str, progress_callback=None) -> Dict[str, Any]:
        """
        Phase 3: Execute the automation plan after user confirmation.
        Uses AutomationExecutor to run all steps, with progress and error reporting.
        """
        logger.info(f"[Agent] Running automation plan for project: {project_name}")
        project_dir = os.path.join(self.project_manager.projects_dir, project_name)
        executor = AutomationExecutor(project_dir=project_dir, project_name=project_name)
        errors = []
        steps_executed = []
        for idx, step in enumerate(plan):
            result = executor.run_plan([step])
            steps_executed.append({'step': step, 'result': result})
            if progress_callback:
                progress_callback(idx+1, len(plan), step, result)
            if not result['success']:
                errors.extend(result['errors'])
                break
        return {
            'success': len(errors) == 0,
            'errors': errors,
            'steps_executed': steps_executed
        }

    async def execute(self, prompt: str, project_name: str, progress_callback=None) -> Dict[str, Any]:
        """
        Execute the agent workflow.
        
        Args:
            prompt: User prompt
            project_name: Name of the project
            progress_callback: Callback for progress updates
            
        Returns:
            Dictionary with response and results
        """
        logger.info("[Agent] Starting execution")
        thinking_process = []  # Track agent's thinking process for debugging/transparency
        
        try:
            # Extract keywords from the prompt
            self.context_keywords = self.extract_keywords(prompt)
            thinking_process.append(f"Extracted keywords: {', '.join(self.context_keywords)}")
            
            # Start by generating a plan
            thinking_process.append("Generating automation plan...")
            plan_details = await self.generate_automation_plan(prompt, project_name)
            thinking_process.append(f"Generated plan with {len(plan_details.get('steps', []))} steps")
            
            # Convert the plan to executable steps
            subtasks = self.convert_plan_to_steps(plan_details)
            thinking_process.append(f"Converted plan to {len(subtasks)} executable steps")
            
            # Ensure steps include file content implementation when needed
            subtasks = self.ensure_code_implementation_steps(subtasks, prompt, project_name)
            thinking_process.append(f"Validated code implementation steps, now have {len(subtasks)} tasks")
            
            # Execute the plan and return the results
            results = self.run_automation_plan(subtasks, project_name, progress_callback)
            thinking_process.append(f"Executed plan with {len(results.get('results', []))} results")
            
            # Generate a response for the user
            if len(results.get('results', [])) > 0:
                response = f"I've completed the requested task. Here's what I did:\n\n"
                for i, result in enumerate(results.get('results', [])):
                    description = result.get('description', 'Unknown step')
                    status = result.get('status', 'unknown')
                    if status == 'success':
                        response += f"✅ Step {i+1}: {description}\n"
                    elif status == 'error':
                        response += f"❌ Step {i+1}: {description} - Error: {result.get('error', 'Unknown error')}\n"
                    else:
                        response += f"⏳ Step {i+1}: {description} - Status: {status}\n"
            else:
                response = "I've analyzed your request. Here's what I understand:\n\n"
                if 'description' in plan_details:
                    response += f"{plan_details['description']}\n\n"
                response += "I don't have any specific actions to take at this time. Please provide more details about what you'd like me to do."
            
            logger.info("[Agent] Execution completed successfully")
            return {
                "response": response,
                "results": results.get('results', []),
                "thinking": "\n".join(thinking_process)
            }
            
        except Exception as e:
            logger.error(f"[Agent] Error during execution: {e}")
            return {
                "response": f"I encountered an error: {str(e)}. Please try again or refine your request.",
                "results": [],
                "thinking": "\n".join(thinking_process + [f"Error: {str(e)}"])
            }

    async def execute_with_streaming(self, prompt: str, project_name: str, stream_callback=None) -> Dict[str, Any]:
        """
        Execute the agent workflow with streaming response.
        
        Args:
            prompt: User prompt
            project_name: Name of the project
            stream_callback: Callback for streaming tokens
            
        Returns:
            Dictionary with response and results
        """
        logger.info("[Agent] Starting execution with streaming")
        thinking_process = []  # Track agent's thinking process for debugging/transparency
        full_response = ""
        
        try:
            # If stream_callback is provided, send an initial message
            if stream_callback:
                await stream_callback("I'm analyzing your request...\n\n")
                full_response += "I'm analyzing your request...\n\n"
            
            # Extract keywords from the prompt
            self.context_keywords = self.extract_keywords(prompt)
            thinking_process.append(f"Extracted keywords: {', '.join(self.context_keywords)}")
            
            # Start by generating a plan
            thinking_process.append("Generating automation plan...")
            plan_details = await self.generate_automation_plan(prompt, project_name)
            thinking_process.append(f"Generated plan with {len(plan_details.get('steps', []))} steps")
            
            # Stream plan summary if callback exists
            if stream_callback and 'description' in plan_details:
                plan_msg = f"Based on your request, here's my plan:\n{plan_details['description']}\n\n"
                await stream_callback(plan_msg)
                full_response += plan_msg
            
            # Convert the plan to executable steps
            subtasks = self.convert_plan_to_steps(plan_details)
            thinking_process.append(f"Converted plan to {len(subtasks)} executable steps")
            
            # Ensure steps include file content implementation when needed
            subtasks = self.ensure_code_implementation_steps(subtasks, prompt, project_name)
            thinking_process.append(f"Validated code implementation steps, now have {len(subtasks)} tasks")
            
            # Stream the starting execution message if callback exists
            if stream_callback and subtasks:
                start_msg = f"Starting execution with {len(subtasks)} steps...\n\n"
                await stream_callback(start_msg)
                full_response += start_msg
            
            # Create a progress callback that streams updates
            async def streaming_progress_callback(data):
                nonlocal full_response
                if stream_callback:
                    if data.get('status') == 'in_progress':
                        msg = f"Working on step {data.get('step_number')} of {data.get('total_steps')}: {data.get('description', '')}\n"
                        await stream_callback(msg)
                        full_response += msg
                    elif data.get('status') == 'complete':
                        msg = f"✅ Completed step {data.get('step_number')} of {data.get('total_steps')}: {data.get('description', '')}\n"
                        await stream_callback(msg)
                        full_response += msg
                    elif data.get('status') == 'error':
                        msg = f"❌ Error in step {data.get('step_number')} of {data.get('total_steps')}: {data.get('description', '')} - {data.get('error', '')}\n"
                        await stream_callback(msg)
                        full_response += msg
            
            # Execute the plan with streaming updates
            results = self.run_automation_plan(subtasks, project_name, streaming_progress_callback)
            thinking_process.append(f"Executed plan with {len(results.get('results', []))} results")
            
            # Stream a summary of the results
            if stream_callback:
                summary_msg = "\nSummary of execution:\n"
                success_count = sum(1 for r in results.get('results', []) if r.get('status') == 'success')
                error_count = sum(1 for r in results.get('results', []) if r.get('status') == 'error')
                
                summary_msg += f"- Successfully completed: {success_count} steps\n"
                if error_count > 0:
                    summary_msg += f"- Encountered errors: {error_count} steps\n"
                
                await stream_callback(summary_msg)
                full_response += summary_msg
                
                # Final conclusion
                conclusion = "\nI've completed the requested task. Let me know if you need any adjustments or have questions!"
                await stream_callback(conclusion)
                full_response += conclusion
            
            logger.info("[Agent] Streaming execution completed successfully")
            return {
                "response": full_response,
                "results": results.get('results', []),
                "thinking": "\n".join(thinking_process)
            }
            
        except Exception as e:
            logger.error(f"[Agent] Error during streaming execution: {e}")
            error_message = f"I encountered an error: {str(e)}. Please try again or refine your request."
            
            if stream_callback:
                await stream_callback(f"\n\n❌ {error_message}")
                full_response += f"\n\n❌ {error_message}"
            
            return {
                "response": full_response or error_message,
                "results": [],
                "thinking": "\n".join(thinking_process + [f"Error: {str(e)}"])
            }

    def ensure_code_implementation_steps(self, subtasks: list, prompt: str, project_name: str) -> list:
        """
        Ensure the plan includes necessary implementation steps.
        
        This ensures the plan has steps to create basic project structure, components,
        and routes based on the project requirements.
        
        Args:
            subtasks: The existing subtasks
            prompt: The original prompt
            project_name: The name of the project
            
        Returns:
            Updated subtasks with necessary implementation steps
        """
        # Check if this is a web application based on keywords in the prompt
        is_web_app = any(keyword in prompt.lower() for keyword in [
            "web ", "website", "frontend", "ui", "interface", "app", "application",
            "angular", "react", "vue", "javascript", "typescript", "html", "css"
        ])
        
        has_init_steps = any(step.get('type') == 'command' and 'new' in step.get('command', '') 
                            for step in subtasks)
        
        if not has_init_steps:
            # If no initialization steps, add project creation based on type
            if "angular" in prompt.lower():
                subtasks.insert(0, {
                    "type": "command",
                    "command": f"ng new {project_name} --routing --style=scss --skip-git",
                    "description": "Initialize Angular project with routing"
                })
                
                # Next step: cd into project
                subtasks.insert(1, {
                    "type": "command",
                    "command": f"cd {project_name}",
                    "description": "Navigate to project directory"
                })
            elif "react" in prompt.lower():
                subtasks.insert(0, {
                    "type": "command",
                    "command": f"npx create-react-app {project_name}",
                    "description": "Initialize React project"
                })
                
                # Next step: cd into project
                subtasks.insert(1, {
                    "type": "command",
                    "command": f"cd {project_name}",
                    "description": "Navigate to project directory"
                })
            elif "vue" in prompt.lower():
                subtasks.insert(0, {
                    "type": "command",
                    "command": f"npm init vue@latest {project_name} -- --ts --router",
                    "description": "Initialize Vue project with TypeScript and routing"
                })
                
                # Next step: cd into project
                subtasks.insert(1, {
                    "type": "command",
                    "command": f"cd {project_name}",
                    "description": "Navigate to project directory"
                })
                
                # Extra step for Vue.js: npm install
                subtasks.insert(2, {
                    "type": "command",
                    "command": "npm install",
                    "description": "Install dependencies"
                })
        
        # Ensure there are component creation steps for web apps
        if is_web_app:
            # Extract component names from the prompt
            component_names = self._extract_components_from_prompt(prompt)
            
            # Check if we need to add components
            has_component_steps = any("component" in step.get('description', '').lower() for step in subtasks)
            if not has_component_steps and component_names:
                # Add component generation steps after installation
                install_index = next((i for i, step in enumerate(subtasks) 
                                     if step.get('type') == 'command' and 'install' in step.get('command', '').lower()), 2)
                
                for i, component in enumerate(component_names):
                    if "angular" in prompt.lower():
                        subtasks.insert(install_index + i + 1, {
                            "type": "command",
                            "command": f"ng generate component components/{component}",
                            "description": f"Generate Angular component: {component}"
                        })
                    elif "react" in prompt.lower():
                        subtasks.insert(install_index + i + 1, {
                "type": "file",
                            "file_path": f"src/components/{component}/{component}.jsx",
                            "description": f"Create React component: {component}"
                        })
                    elif "vue" in prompt.lower():
                        subtasks.insert(install_index + i + 1, {
                "type": "file",
                            "file_path": f"src/components/{component}.vue",
                            "description": f"Create Vue component: {component}"
                        })
        
        return subtasks
        
    def _extract_components_from_prompt(self, prompt: str) -> list:
        """
        Extract component names from the prompt using NLP techniques.
        
        Args:
            prompt: The project prompt
            
        Returns:
            List of component names
        """
        components = []
        
        # Look for common UI component patterns
        component_patterns = [
            r'(?:page|component|screen) called (\w+)',
            r'(?:page|component|screen) for (\w+)',
            r'(\w+) (?:page|component|screen)',
            r'(?:implement|create|add) (?:a|the) (\w+) (?:page|component|screen|feature|section)',
            r'(?:implement|create|add) (?:a|the) (\w+)',
        ]
        
        for pattern in component_patterns:
            matches = re.finditer(pattern, prompt, re.IGNORECASE)
            for match in matches:
                component = match.group(1).lower()
                if (len(component) > 2 and 
                    component not in ['the', 'and', 'for', 'with', 'page', 'component', 'screen']):
                    components.append(component)
        
        # Make components unique
        return list(set(components))

    def extract_keywords(self, text: str) -> List[str]:
        """
        Extract keywords from text for research context.
        
        Args:
            text: The text to extract keywords from
            
        Returns:
            A list of keywords
        """
        words = text.lower().split()
        keywords = [word for word in words if len(word) > 3 and word.isalnum()]
        return list(set(keywords))

    def determine_project_type(self, prompt: str) -> str:
        """
        Determine the type of project based on the user prompt.
        Uses semantic analysis to identify project categories.
        """
        prompt_lower = prompt.lower()
        
        # Web application types
        if any(web_app in prompt_lower for web_app in ["web app", "website", "web application", "angular app", "react app", "vue app", "spa", "single page application"]):
            return "web_app"
        
        # Mobile application types    
        elif any(mobile_app in prompt_lower for mobile_app in ["mobile app", "android app", "ios app", "flutter app", "react native"]):
            return "mobile_app"
        
        # Backend/API types
        elif any(backend in prompt_lower for backend in ["api", "backend", "server", "rest api", "graphql", "microservice"]):
            return "backend_api"
        
        # Data-focused applications
        elif any(data_app in prompt_lower for data_app in ["dashboard", "data visualization", "analytics", "chart", "report"]):
            return "data_dashboard"
        
        # General utility applications
        elif any(utility in prompt_lower for utility in ["cli", "command line", "utility", "tool"]):
            return "utility"
        
        # Default for any other type
        else:
            return "default"

    async def fully_automated_execute(self, prompt: str, project_name: str, callbacks=None) -> Dict[str, Any]:
        """
        Execute a fully automated workflow for end-to-end project creation.
        
        This method performs the complete workflow from research to final validation:
        1. Research phase to gather relevant information
        2. Planning phase with architecture and components
        3. Implementation phase including code generation
        4. Testing and validation
        
        Args:
            prompt: The project description/prompt
            project_name: Name of the project to create
            callbacks: Optional callbacks for: 
                - progress: To report progress updates
                - stream: To stream partial outputs
                - completion: Called when execution completes
                       
        Returns:
            Results dictionary with project details
        """
        # Make sure we have a valid project directory
        project_dir_path = os.path.join(self.project_manager.projects_dir, project_name)
        # Create projects folder
        os.makedirs(project_dir_path, exist_ok=True)
        
        # Log the project creation
        logger.info(f"[Agent] Starting fully automated project creation: {project_name}")
        logger.info(f"[Agent] Project directory: {project_dir_path}")
        
        # Initialize and emit agent workflow status
        workflow_stages = {
            'setup': {'status': 'pending', 'progress': 0},
            'research': {'status': 'pending', 'progress': 0},
            'planning': {'status': 'pending', 'progress': 0},
            'implementation': {'status': 'pending', 'progress': 0},
            'testing': {'status': 'pending', 'progress': 0},
            'validation': {'status': 'pending', 'progress': 0}
        }
        
        # Send initial agent status
        await emit_agent_status(
            project_name=project_name,
            active=True,
            stage="setup",
            progress=0,
            message="Initializing project workflow",
            stages=workflow_stages
        )
        
        # Track metrics
        metrics = {
            "total_execution_time": 0,
            "research_time": 0,
            "planning_time": 0,
            "coding_time": 0,
            "execution_time": 0,
            "testing_time": 0,
            "error_recovery_time": 0,
            "subtasks_completed": 0,
            "subtasks_failed": 0,
            "errors_recovered": 0,
            "ui_test_count": 0,
            "accessibility_violations": 0
        }
        
        # Set up callbacks or create empty placeholder functions
        progress_callback = callbacks.get('progress') if callbacks else None
        stream_callback = callbacks.get('stream') if callbacks else None
        completion_callback = callbacks.get('completion') if callbacks else None
        
        # Helper function for safe callback execution
        async def safe_callback(callback, payload):
            if callback:
                try:
                    if inspect.iscoroutinefunction(callback):
                        await callback(payload)
                    else:
                        callback(payload)
                except Exception as e:
                    logger.error(f"[AutoAgent] Error in callback: {e}")
        
        # Track key artifacts and decisions
        artifacts = {
            "research_results": [],
            "project_plan": {},
            "code_files": {},
            "execution_results": {},
            "test_results": {},
            "error_recovery_actions": [],
            "final_validation": {}
        }
        
        logger.info(f"[AutoAgent] Starting fully automated execution for project: {project_name}")
        await safe_callback(stream_callback, "Initializing autonomous project creation process...\n\n")
        
        # Initialize start_time for total execution time tracking
        start_time = time.time()
        
        try:
            # 1. Project Environment Setup
            logger.info(f"[AutoAgent] Setting up project environment for: {project_name}")
            await safe_callback(stream_callback, f"Setting up project environment for: {project_name}...\n")
            
            # Create project directory if it doesn't exist
            self.project_manager.create_project_if_not_exists(project_name)
            project_dir = os.path.join(self.project_manager.projects_dir, project_name)
            
            # Initialize the project executor for automated project creation
            project_executor = ProjectExecutor(project_name, self.project_manager.projects_dir)
            await project_executor.initialize()
            
            # Initialize shell executor for command execution
            shell_executor = ShellExecutor(project_dir=project_dir)
            
            await safe_callback(progress_callback, {
                'stage': 'setup',
                'status': 'completed',
                'message': f"Project environment set up at {project_dir}"
            })
            
            # 2. Research Phase
            logger.info(f"[AutoAgent] Starting research phase for: {project_name}")
            await safe_callback(stream_callback, "Starting research phase to gather relevant information...\n")
            
            # Update agent workflow status for research phase
            workflow_stages['setup'] = {'status': 'completed', 'progress': 100}
            workflow_stages['research'] = {'status': 'in_progress', 'progress': 10}
            await emit_agent_status(
                project_name=project_name,
                active=True,
                stage="research",
                progress=15,
                message="Researching requirements and technologies for your project",
                stages=workflow_stages
            )
            
            research_start = time.time()
            
            # Keywords extraction for focused research
            keywords = self.extract_keywords(prompt)
            
            # Perform enhanced web research on the project topic
            try:
                # Multiple research queries for broader coverage
                research_queries = [
                    f"{project_name} latest technologies and best practices",
                    f"{' '.join(keywords[:5])} implementation guide",
                    f"{project_name} architecture patterns",
                    f"{project_name} common challenges and solutions",
                    f"how to implement {project_name} step by step",
                    f"{project_name} code examples"
                ]
                
                for query in research_queries:
                    try:
                        # Use structured search for better results
                        research_result = await searxng_search_structured(query, max_results=5)
                        artifacts["research_results"].append({
                            "query": query,
                            "result": research_result
                        })
                        await safe_callback(stream_callback, f"Researching: {query}\n")
                    except Exception as e:
                        logger.warning(f"[AutoAgent] Research query failed: {query} - {e}")
                
                # Enhanced prompt with research results
                enhanced_prompt = prompt + "\n\nResearch findings:\n"
                for result in artifacts["research_results"]:
                    # Format search results for inclusion in the prompt
                    query = result['result'].get('query', 'Unknown query')
                    
                    # Extract and format web results
                    web_results = result['result'].get('results', [])
                    result_text = ""
                    for i, web_result in enumerate(web_results[:3]):  # Limit to top 3 results
                        title = web_result.get('title', 'No title')
                        content = web_result.get('content', 'No content')
                        url = web_result.get('url', 'No URL')
                        result_text += f"\n{i+1}. {title}\n   {content}\n   Source: {url}\n"
                    
                    enhanced_prompt += f"\n--- Research for: {query} ---\n{result_text}\n"
                
                metrics["research_time"] = time.time() - research_start
                await safe_callback(progress_callback, {
                    'stage': 'research',
                    'status': 'completed',
                    'message': f"Research completed with {len(artifacts['research_results'])} queries"
                })
                await safe_callback(stream_callback, f"Research phase completed. Found information from {len(artifacts['research_results'])} different sources.\n\n")
                
            except Exception as e:
                logger.error(f"[AutoAgent] Research phase error: {e}")
                # Continue with original prompt if research fails
                enhanced_prompt = prompt
                await safe_callback(stream_callback, "Research phase encountered issues, proceeding with available information.\n")
            
            # 3. Planning Phase - Generate a comprehensive project plan
            logger.info(f"[AutoAgent] Starting planning phase for: {project_name}")
            await safe_callback(stream_callback, "Generating comprehensive project plan...\n")
            
            # Update agent workflow status for planning phase
            workflow_stages['research'] = {'status': 'completed', 'progress': 100}
            workflow_stages['planning'] = {'status': 'in_progress', 'progress': 10}
            await emit_agent_status(
                project_name=project_name,
                active=True,
                stage="planning",
                progress=30,
                message="Planning project architecture and component structure",
                stages=workflow_stages
            )
            
            planning_start = time.time()
            try:
                # Generate plan with enhanced prompt
                plan_result = await self.generate_automation_plan(enhanced_prompt, project_name)
                artifacts["project_plan"] = plan_result["plan_details"]
                
                # Convert to executable steps
                subtasks = self.convert_plan_to_steps(artifacts["project_plan"])
                
                # Ensure the plan includes concrete implementation steps
                subtasks = self.ensure_code_implementation_steps(subtasks, enhanced_prompt, project_name)
                
                metrics["planning_time"] = time.time() - planning_start
                
                plan_description = artifacts["project_plan"].get("description", "")
                await safe_callback(progress_callback, {
                    'stage': 'planning',
                    'status': 'completed',
                    'message': f"Project plan generated with {len(subtasks)} actionable steps"
                })
                
                plan_summary = f"Project Plan: {plan_description}\n\n" if plan_description else ""
                plan_summary += f"Generated {len(subtasks)} actionable steps to execute. Proceeding with implementation.\n\n"
                await safe_callback(stream_callback, plan_summary)
                
            except Exception as e:
                logger.error(f"[AutoAgent] Planning phase error: {e}")
                raise Exception(f"Critical error in planning phase: {e}")
            
            # 4. Implementation Phase - Execute the plan with automated project setup and error recovery
            logger.info(f"[AutoAgent] Starting implementation phase with {len(subtasks)} steps")
            await safe_callback(stream_callback, "Beginning project implementation with automated execution...\n")
            
            # Update agent workflow status for implementation phase
            workflow_stages['planning'] = {'status': 'completed', 'progress': 100}
            workflow_stages['implementation'] = {'status': 'in_progress', 'progress': 0}
            await emit_agent_status(
                project_name=project_name,
                active=True,
                stage="implementation",
                progress=45,
                message=f"Implementing project with {len(subtasks)} steps",
                stages=workflow_stages
            )
            
            coding_start = time.time()
            execution_start = time.time()
            
            # Initialize the project executor for automated project creation
            project_executor = ProjectExecutor(project_name, self.project_manager.projects_dir)
            await project_executor.initialize()
            
            # Update agent workflow status for component generation phase
            workflow_stages['implementation'] = {'status': 'in_progress', 'progress': 50}
            await emit_agent_status(
                project_name=project_name,
                active=True,
                stage="implementation",
                progress=60,
                message="Generating components"
            )
            
            # Automated project initialization with the project executor
            try:
                project_type = self.determine_project_type(enhanced_prompt)
                logger.info(f"[AutoAgent] Determined project type: {project_type}")
                await safe_callback(stream_callback, f"Project type detected: {project_type}\n")
                await safe_callback(stream_callback, f"Initializing {project_type} project structure...\n")
                
                # Create appropriate project based on the determined type
                project_options = {}
                if project_type.lower() == "angular":
                    project_options = {"style": "scss", "routing": True, "strict": True}
                elif project_type.lower() == "react":
                    project_options = {"typescript": True, "vite": True}
                elif project_type.lower() == "vue":
                    project_options = {"typescript": True, "create_vue": True}
                elif project_type.lower() in ["python", "flask", "django", "fastapi"]:
                    project_options = {"create_venv": True}
                    if "flask" in project_type.lower():
                        project_options["flask"] = True
                    elif "django" in project_type.lower():
                        project_options["django"] = True
                    elif "fastapi" in project_type.lower():
                        project_options["fastapi"] = True
                
                # Initialize project structure
                init_result = await project_executor.create_project_structure(project_type, project_options)
                artifacts["execution_results"] = artifacts.get("execution_results", {})
                artifacts["execution_results"]["project_initialization"] = init_result
                
                if init_result.get("success", False):
                    await safe_callback(stream_callback, f"✅ Successfully created {project_type} project structure!\n")
                else:
                    await safe_callback(stream_callback, f"⚠️ Warning: Automated project initialization encountered issues: {init_result.get('error', 'Unknown error')}\n")
                    await safe_callback(stream_callback, "Continuing with fallback approach...\n")
            except Exception as e:
                logger.error(f"[AutoAgent] Error initializing project structure: {e}")
                await safe_callback(stream_callback, f"⚠️ Warning: Project initialization error: {str(e)}\nContinuing with fallback implementation approach...\n")
            
            # Set up model assignments for subtasks
            subtask_assignments = self.assign_models_to_subtasks(subtasks)
            
            # Progress tracking
            total_subtasks = len(subtask_assignments)
            completed_subtasks = 0
            failed_subtasks = 0
            
            # Track components that need to be generated
            components_to_generate = []
            dependencies_to_install = set()
            
            # Execute subtasks with built-in error recovery and automated code generation
            for subtask_idx, assignment in enumerate(subtask_assignments):
                subtask = assignment["subtask"]
                models = assignment["models"]
                
                subtask_type = subtask.get("type", "").lower()
                subtask_desc = subtask.get("description", "").lower()
                
                logger.info(f"[AutoAgent] Executing subtask {subtask_idx+1}/{total_subtasks}: {subtask.get('description', '')}")
                await safe_callback(stream_callback, f"Step {subtask_idx+1}/{total_subtasks}: {subtask.get('description', '')}\n")
                
                # Check if this is a component generation or dependency task that can be handled directly
                is_component_generation = False
                is_dependency_installation = False
                
                # Extract components from any subtask that mentions component or service generation
                # This is more aggressive about detecting component generation requests
                if ("component" in subtask_desc or "service" in subtask_desc or 
                    any(kw in subtask_desc for kw in ["ui", "interface", "view", "page", "screen"])):
                    is_component_generation = True
                    
                    # Determine component type based on subtask description and project type
                    if project_type.lower() == "angular":
                        component_type = "angular-component" if "service" not in subtask_desc else "angular-service"
                    elif project_type.lower() == "react":
                        component_type = "react-component"
                    else:
                        component_type = "component"
                        
                    # Extract component name from subtask
                    component_name = subtask.get("name", "").strip() or self._extract_component_name(subtask_desc)
                    
                    # Add component to generation queue if we have a name
                    if component_name:
                        # Check if this component is already in the queue to avoid duplicates
                        existing_component_names = [c.get("name") for c in components_to_generate]
                        if component_name not in existing_component_names:
                            components_to_generate.append({
                                "type": component_type,
                                "name": component_name,
                                "options": {"skipTests": subtask.get("skip_tests", False)}
                            })
                            logger.info(f"[Agent] Added {component_type} {component_name} to generation queue")
                            await safe_callback(stream_callback, f"✅ Added {component_type} {component_name} to generation queue\n")
                            
                        success = True
                        completed_subtasks += 1
                        metrics["subtasks_completed"] += 1
                        continue
                
                if "install" in subtask_desc and ("dependenc" in subtask_desc or "package" in subtask_desc):
                    is_dependency_installation = True
                    dependency_names = self._extract_dependencies(subtask_desc)
                    if dependency_names:
                        dependencies_to_install.update(dependency_names)
                        success = True
                        completed_subtasks += 1
                        metrics["subtasks_completed"] += 1
                        await safe_callback(stream_callback, f"✅ Added dependencies to installation queue: {', '.join(dependency_names)}\n")
                        continue
                
                # Track retries and errors for this subtask
                retry_count = 0
                max_retries = 3  # More aggressive retry for autonomous execution
                success = False
                
                # Execute regular subtasks (non-component generation, non-dependency)
                while not success and retry_count <= max_retries:
                    try:
                        # Execute the subtask
                        result = await self.execute_subtask(
                            subtask, models, project_name, 
                            progress_callback=progress_callback,
                            subtask_idx=subtask_idx+1, 
                            total_subtasks=total_subtasks,
                            retry_count=retry_count
                        )
                        
                        if result["status"] == "completed":
                            success = True
                            completed_subtasks += 1
                            metrics["subtasks_completed"] += 1
                            
                            # Store generated code files
                            if subtask.get('type') == 'file' and 'file_path' in subtask:
                                artifacts["code_files"][subtask['file_path']] = subtask.get('content', '')
                            
                            await safe_callback(progress_callback, {
                                'stage': 'implementation',
                                'status': 'in_progress',
                                'message': f"Completed step {subtask_idx+1}/{total_subtasks}",
                                'completed': completed_subtasks,
                                'total': total_subtasks
                            })
                            
                            await safe_callback(stream_callback, f"✅ Completed: {subtask.get('description', '')}\n")
                            
                        else:
                            # Task failed, prepare for retry or adaptive recovery
                            logger.warning(f"[AutoAgent] Subtask failed: {result.get('error', 'Unknown error')}")
                            
                            if retry_count < max_retries:
                                # Try adaptive recovery based on error type
                                recovery_start = time.time()
                                
                                error_msg = result.get('error', '')
                                recovery_action = None
                                
                                # Analyze error and attempt recovery
                                if 'file exists' in error_msg.lower():
                                    # Handle file already exists
                                    if subtask.get('type') == 'file':
                                        # Modify the task to update existing file instead
                                        subtask['update_existing'] = True
                                        recovery_action = "Switching to update existing file mode"
                                        
                                elif 'permission' in error_msg.lower():
                                    # Permission error recovery
                                    if subtask.get('type') == 'command':
                                        # Try with elevated permissions if appropriate
                                        cmd = subtask.get('command', '')
                                        if not cmd.startswith('sudo ') and os.name != 'nt':
                                            subtask['command'] = 'sudo ' + cmd
                                            recovery_action = "Retrying command with elevated permissions"
                                            
                                elif 'not found' in error_msg.lower() and subtask.get('type') == 'command':
                                    # Missing dependency or command - use our project executor
                                    cmd = subtask.get('command', '')
                                    
                                    if 'npm' in cmd and not any(x in cmd for x in ['install', 'i ']):
                                        # Extract dependencies from the task description and add to installation queue
                                        dep_names = self._extract_dependencies(subtask_desc)
                                        if dep_names:
                                            dependencies_to_install.update(dep_names)
                                            recovery_action = f"Added {len(dep_names)} dependencies to installation queue"
                                        else:
                                            # Generic npm install
                                            await project_executor.install_dependencies([""])
                                            recovery_action = "Installing npm dependencies and retrying"
                                        
                                    elif 'ng' in cmd and 'g' in cmd:
                                        # Angular CLI generate command - use our component generator
                                        component_name = self._extract_component_name(cmd)
                                        if component_name:
                                            components_to_generate.append({
                                                "type": "angular-component",
                                                "name": component_name,
                                                "options": {}
                                            })
                                            recovery_action = f"Added component {component_name} to generation queue"
                                
                                # Record recovery action and time
                                if recovery_action:
                                    logger.info(f"[AutoAgent] Recovery action: {recovery_action}")
                                    artifacts["error_recovery_actions"].append({
                                        "subtask": subtask.get('description', ''),
                                        "error": error_msg,
                                        "action": recovery_action
                                    })
                                    metrics["errors_recovered"] += 1
                                    metrics["error_recovery_time"] += time.time() - recovery_start
                                    await safe_callback(stream_callback, f"🔄 Recovery: {recovery_action}\n")
                                
                                # Increment retry counter
                                retry_count += 1
                                await safe_callback(stream_callback, f"⚠️ Retrying step ({retry_count}/{max_retries})...\n")
                                
                            else:
                                # Max retries exceeded
                                failed_subtasks += 1
                                metrics["subtasks_failed"] += 1
                                logger.error(f"[AutoAgent] Subtask failed after {max_retries} retries: {subtask.get('description', '')}")
                                await safe_callback(stream_callback, f"❌ Failed: {subtask.get('description', '')} - Moving to next step\n")
                                break
                    
                    except Exception as e:
                        logger.error(f"[AutoAgent] Error executing subtask: {e}")
                        if retry_count < max_retries:
                            retry_count += 1
                            await safe_callback(stream_callback, f"⚠️ Error: {str(e)}\nRetrying step ({retry_count}/{max_retries})...\n")
                        else:
                            failed_subtasks += 1
                            metrics["subtasks_failed"] += 1
                            await safe_callback(stream_callback, f"❌ Failed: {subtask.get('description', '')} - Moving to next step\n")
                            break
            
            # Add music player specific components if not already in the queue
            music_player_required = "music player" in prompt.lower() or "audio player" in prompt.lower()
            if music_player_required and project_type.lower() in ["angular", "web_app"]:
                music_components = [
                    {"type": "angular-component", "name": "player", "options": {}},
                    {"type": "angular-component", "name": "player-controls", "options": {}},
                    {"type": "angular-component", "name": "playlist", "options": {}},
                    {"type": "angular-service", "name": "audio", "options": {}}
                ]
                
                # Check if components already exist in the queue
                existing_component_names = [comp.get("name") for comp in components_to_generate]
                for component in music_components:
                    if component.get("name") not in existing_component_names:
                        components_to_generate.append(component)
                        await safe_callback(stream_callback, f"✅ Added required music player component: {component.get('name')}\n")
            
            # Process any queued component generations
            if components_to_generate:
                await safe_callback(stream_callback, f"Generating {len(components_to_generate)} components...\n")
                try:
                    # Update agent workflow status for component implementation
                    workflow_stages['implementation'] = {'status': 'in_progress', 'progress': 60}
                    await emit_agent_status(
                        project_name=project_name,
                        active=True,
                        stage="implementation",
                        progress=65,
                        message="Generating components"
                    )
                    
                    # Generate all components first
                    component_result = await project_executor.generate_components(components_to_generate, prompt)
                    if component_result.get("success", False):
                        await safe_callback(stream_callback, f"✅ Successfully generated {len(components_to_generate)} components\n")
                        
                        # Now implement functionality in each component
                        await safe_callback(stream_callback, f"🔧 Adding required functionality to components...\n")
                        for comp in components_to_generate:
                            comp_name = comp.get("name")
                            try:
                                # Try to find the component file and implement functionality
                                logger.info(f"[Agent] Implementing component: {comp_name}")
                                impl_result = await project_executor.implement_component_functionality(comp_name, prompt)
                                if impl_result.get("success", False):
                                    await safe_callback(stream_callback, f"✅ Successfully implemented {comp_name}!\n")
                            except Exception as comp_err:
                                logger.error(f"[Agent] Error implementing component {comp.get('name')}: {comp_err}")
                                await safe_callback(stream_callback, f"⚠️ Error implementing {comp.get('name')}: {str(comp_err)}\n")
                except Exception as e:
                    logger.error(f"[Agent] Error during component generation and implementation: {e}")
                    await safe_callback(stream_callback, f"⚠️ Error during component operations: {str(e)}\n")
                
                # Set model ID for LLM-based generation
                project_executor.model_id = self.primary_model_id
                
                # GUARANTEED COMPONENT IMPLEMENTATION
                # Always generate and implement components after dependencies are installed
                # This is a critical fix to ensure functional implementation always happens
                # Only skip if we've already completed component implementation
                if not hasattr(project_executor, 'component_implementation_done') or not project_executor.component_implementation_done:
                    logger.info(f"[Agent] GUARANTEED COMPONENT IMPLEMENTATION - Forcing component generation and implementation")
            
            # ===== START: Iterative Implementation Refinement Loop =====
            # This loop repeatedly checks, validates, and improves the implementation
            # until it meets the requirements or reaches max iterations
            await safe_callback(stream_callback, "\n\ud83d\udd04 Starting iterative implementation refinement process...\n")
            
            # Initialize refinement state
            implementation_iterations = 0
            implementation_complete = False
            implementation_issues = []
            previous_issue_count = float('inf')  # Track progress between iterations
            
            # Ensure we always have component generation configured before entering the loop
            if not components_to_generate:
                logger.warning(f"[Agent] WARNING: No components created during planning or initial phases. Adding fallback components.")
                main_component_name = project_name.replace("-", "").replace("_", "")
                # Fallback to at least one component
                components_to_generate = [
                    {"type": "component", "name": main_component_name, "options": {}}
                ]
                await safe_callback(stream_callback, f"\n⚠️ CRITICAL: No components created. Adding mandatory fallback component.\n")
            
            # Continue iterations until implementation is complete or no progress is being made
            while not implementation_complete:
                implementation_iterations += 1
                await safe_callback(stream_callback, f"\n\ud83d\udd27 Implementation refinement iteration {implementation_iterations}...\n")
                
                # Update agent workflow status for implementation refinement iteration
                progress_percentage = min(45 + (implementation_iterations * 10), 85)
                workflow_stages['implementation'] = {'status': 'in_progress', 'progress': min((implementation_iterations * 25), 90)}
                await emit_agent_status(
                    project_name=project_name,
                    active=True,
                    stage="implementation",
                    progress=progress_percentage,
                    message=f"Implementation refinement iteration {implementation_iterations}",
                    stages=workflow_stages
                )
                
                # ALWAYS force at least one full implementation round
                # This ensures we implement functionality even if initial build succeeds
                force_implementation = implementation_iterations <= 1
            
                # Step 1: Build the project to check for compilation issues
                await safe_callback(stream_callback, "Building project to validate implementation...\n")
                build_success = False
                try:
                    # Detect project type and use appropriate build command
                    build_cmd = 'npm run build'
                    if project_type.lower() in ['angular', 'angular-app', 'web_app']:
                        # For Angular, ensure we've installed dependencies first
                        if not os.path.exists(os.path.join(project_dir, 'node_modules')):
                            await shell_executor.execute_command('npm install', cwd=project_dir, timeout=180)
                    elif project_type.lower() == 'python':
                        # For Python projects, just check syntax
                        build_cmd = 'python -m compileall .'
                    
                    # Run the build command
                    build_result = await shell_executor.execute_command(build_cmd, cwd=project_dir, timeout=180)
                    
                    # Check for errors in the output
                    build_success = not any(error_term in build_result.upper() for error_term in ['ERROR', 'EXCEPTION', 'FAILED'])
                    
                    if build_success:
                        await safe_callback(stream_callback, "\u2705 Build successful! Checking implementation completeness...\n")
                        
                        # Check if implementation meets requirements
                        implementation_analysis = await self._analyze_implementation_against_requirements(
                            project_dir, prompt, project_type, [], model_id=self.primary_model_id
                        )
                        
                        missing_features = implementation_analysis.get("missing_features", [])
                        incomplete_components = implementation_analysis.get("incomplete_components", [])
                        
                        # Only mark implementation as done when:
                        # 1. Build succeeds
                        # 2. No missing features
                        # 3. No incomplete components
                        # 4. Required components are all found
                        if (build_success and 
                            len(missing_features) == 0 and 
                            len(incomplete_components) == 0 and
                            implementation_iterations >= 1):
                            logger.info("[Agent] All components successfully implemented and build successful")
                            project_executor.component_implementation_done = True
                            await safe_callback(stream_callback, "🎉 All required components successfully implemented!\n")
                    else:
                        await safe_callback(stream_callback, f"\u26a0\ufe0f Build failed. Identifying and fixing issues...\n")
                        # Extract error information from build output
                        error_lines = [line.strip() for line in build_result.split('\n') 
                                     if any(error_term in line.upper() for error_term in ['ERROR', 'EXCEPTION', 'FAILED'])]
                        
                        # Filter out duplicate or irrelevant error messages
                        filtered_errors = []
                        for error in error_lines:
                            # Only add substantive error messages
                            if len(error) > 10 and not any(e in error for e in filtered_errors):
                                filtered_errors.append(error)
                        
                        await safe_callback(stream_callback, f"Found {len(filtered_errors)} build errors to fix.\n")
                        implementation_issues = filtered_errors[:5]  # Limit to first 5 errors
                except Exception as e:
                    logger.error(f"[AutoAgent] Build check error: {e}")
                    await safe_callback(stream_callback, f"\u26a0\ufe0f Build check error: {str(e)}\n")
                    implementation_issues = [str(e)]
                
                # Step 2: Analyze code implementation against requirements in detail
                await safe_callback(stream_callback, "Performing deep analysis of code implementation against requirements...\n")
                
                # Get the detailed implementation analysis from models that can understand code and requirements
                # This approach uses the LLM to evaluate code implementation quality and completeness
                implementation_analysis = await self._analyze_implementation_against_requirements(
                    project_dir, prompt, project_type, implementation_issues,
                    model_id=self.primary_model_id  # Use the primary model for deep code analysis
                )
                
                # ALWAYS check for missing features and components that need implementation
                missing_features = implementation_analysis.get("missing_features", [])
                incomplete_components = implementation_analysis.get("incomplete_components", [])
                
                # For the first iteration, ALWAYS generate improvements and implement components
                # even if no explicit missing features are detected by the analysis
                # This ensures at least one round of detailed implementation occurs
                if implementation_iterations <= 1 or missing_features or incomplete_components or force_implementation:
                    await safe_callback(stream_callback, f"Found {len(missing_features)} missing or incomplete features:\n")
                    for idx, feature in enumerate(missing_features[:3]):  # Show top 3 missing features
                        await safe_callback(stream_callback, f"  {idx+1}. {feature}\n")
                    
                    # Step 3: Implement missing features and fix issues
                    await safe_callback(stream_callback, "Implementing missing features and fixing issues...\n")
                    
                    # Generate improvement tasks
                    improvement_tasks = await self._generate_implementation_improvement_tasks(
                        prompt, project_dir, project_type, implementation_analysis
                    )
                    
                    # Execute improvement tasks
                    improvement_success_count = 0
                    for idx, task in enumerate(improvement_tasks):
                        await safe_callback(stream_callback, f"Implementing improvement {idx+1}/{len(improvement_tasks)}: {task.get('description', '')}\n")
                        
                        # Execute the improvement task
                        result = await self.execute_subtask(
                            task, 
                            {"primary": self.primary_model_id, "backup": self.backup_model_id}, 
                            project_name,
                            subtask_idx=idx+1, 
                            total_subtasks=len(improvement_tasks)
                        )
                        
                        if result.get("status") == "completed":
                            improvement_success_count += 1
                            await safe_callback(stream_callback, "\u2705 Successfully implemented improvement.\n")
                        else:
                            await safe_callback(stream_callback, f"\u26a0\ufe0f Failed to implement improvement: {result.get('error', 'Unknown error')}\n")
                    
                    # Update metrics for task tracking
                    completed_subtasks += improvement_success_count
                    metrics["subtasks_completed"] += improvement_success_count
                    
                    await safe_callback(stream_callback, f"Completed {improvement_success_count}/{len(improvement_tasks)} implementation improvements.\n")
                else:
                    # No missing features found
                    # Step 3: Analyze the implementation against requirements
                    await safe_callback(stream_callback, "Analyzing implementation against requirements...\n")
                    
                    analysis_results = await self._analyze_implementation_against_requirements(
                        project_dir, prompt, project_type, filtered_errors if not build_success else []
                    )
                    
                    # Track issues
                    implementation_issues = analysis_results.get("missing_features", []) + \
                                          analysis_results.get("incomplete_components", []) + \
                                          analysis_results.get("build_issues", [])
                    
                    issue_count = len(implementation_issues)
                    
                    # Check if the implementation has met the requirements
                    # Always do at least one implementation iteration, regardless of build success
                    if issue_count == 0 and build_success and implementation_iterations >= 1 and not force_implementation:
                        implementation_complete = True
                        await safe_callback(stream_callback, "\u2705 Implementation appears to fully satisfy the requirements!\n")
                    else:
                        # If forced implementation is active, ensure we continue with improvement tasks
                        if force_implementation:
                            await safe_callback(stream_callback, "Performing required implementation iteration to ensure functionality is implemented...\n")
                        else:
                            await safe_callback(stream_callback, f"Found {issue_count} remaining issues.\n")
                
                # Check if implementation is considered complete or if we're no longer making progress
                current_issue_count = len(implementation_analysis.get("missing_features", [])) + len(implementation_analysis.get("incomplete_components", []))
                
                await safe_callback(stream_callback, f"Found {current_issue_count} remaining issues (previous: {previous_issue_count})\n")
                
                if build_success and implementation_complete:
                    await safe_callback(stream_callback, "\ud83c\udf89 Implementation refinement complete! All required features implemented.\n")
                    break
                elif current_issue_count >= previous_issue_count and implementation_iterations > 2:
                    # If we're not making progress after at least 3 iterations, exit the loop
                    await safe_callback(stream_callback, f"\u26a0\ufe0f No further progress being made after {implementation_iterations} iterations.\n")
                    await safe_callback(stream_callback, "Continuing with best implementation so far.\n")
                    break
                else:
                    previous_issue_count = current_issue_count
                    await safe_callback(stream_callback, "Continuing to next refinement iteration...\n")
            
            # Record implementation refinement results
            # Make sure we always have defined values for tracking variables
            if 'current_issue_count' not in locals():
                current_issue_count = 0
            
            if 'build_success' not in locals():
                build_success = False
                
            artifacts["implementation_refinement"] = {
                "iterations": implementation_iterations,
                "complete": implementation_complete,
                "issues_fixed": len(implementation_issues),
                "final_build_success": build_success,
                "remaining_issues": current_issue_count,
                "progress_made": previous_issue_count > current_issue_count
            }
            
            # Update validation summary for later stages
            validation_summary = "Implementation Refinement:\n"
            validation_summary += f"✅ Performed {implementation_iterations} refinement iterations\n"
            if implementation_complete:
                validation_summary += "✅ All required features implemented successfully\n"
            else:
                validation_summary += f"⚠️ {current_issue_count if 'current_issue_count' in locals() else 0} implementation issues remain\n"
            
            if build_success:
                validation_summary += "✅ Project builds successfully\n"
            else:
                validation_summary += "❌ Project build has errors\n"
                
            await safe_callback(stream_callback, "\n" + validation_summary + "\n")
            # ===== END: Iterative Implementation Refinement Loop =====
            
            metrics["coding_time"] = time.time() - coding_start
            metrics["execution_time"] = time.time() - execution_start
            
            implementation_status = 'completed' if failed_subtasks == 0 else 'completed_with_errors' 
            await safe_callback(progress_callback, {
                'stage': 'implementation',
                'status': implementation_status,
                'message': f"Implementation completed: {completed_subtasks} successful, {failed_subtasks} failed",
                'completed': completed_subtasks,
                'total': total_subtasks
            })
            
            implementation_summary = f"Implementation phase completed:\n"
            implementation_summary += f"✅ Successfully completed {completed_subtasks}/{total_subtasks} steps\n"
            if failed_subtasks > 0:
                implementation_summary += f"❌ Failed {failed_subtasks}/{total_subtasks} steps\n"
            if metrics["errors_recovered"] > 0:
                implementation_summary += f"🔄 Automatically recovered from {metrics['errors_recovered']} errors\n"
            
            await safe_callback(stream_callback, f"\n{implementation_summary}\n")
            
            # 5. Testing Phase - Validate the implementation
            logger.info(f"[AutoAgent] Starting testing phase for project: {project_name}")
            await safe_callback(stream_callback, "Beginning automated testing and validation...\n")
            
            # Update agent workflow status for testing phase
            workflow_stages['implementation'] = {'status': 'completed', 'progress': 100}
            workflow_stages['testing'] = {'status': 'in_progress', 'progress': 20}
            await emit_agent_status(
                project_name=project_name,
                active=True,
                stage="testing",
                progress=85,
                message="Testing and validating project implementation",
                stages=workflow_stages
            )
            
            testing_start = time.time()
            
            try:
                # Initialize the project executor for testing
                project_executor = ProjectExecutor(project_name, self.project_manager.projects_dir)
                await project_executor.initialize()
                
                # Run unit tests if they exist
                unit_test_results = await project_executor.test_project()
                artifacts["test_results"]["unit_tests"] = unit_test_results
                
                # Start the project server in the background
                run_options = {"port": 4201}  # Use a different port to avoid conflict with the AutonomousAI itself
                run_result = await project_executor.run_project(run_options)
                
                if run_result.get("success", False):
                    # Run Playwright UI tests
                    await safe_callback(stream_callback, "Running automated UI tests with Playwright...\n")
                    ui_test_options = {
                        "port": run_result.get("port", 4201),
                        "test_url": run_result.get("url", f"http://localhost:4201")
                    }
                    
                    # Run UI tests
                    ui_test_results = await project_executor.run_ui_tests_with_playwright(ui_test_options)
                    artifacts["test_results"]["ui_tests"] = ui_test_results
                    metrics["ui_test_count"] = len(ui_test_results.get("screenshots", []))
                    
                    # Capture screenshots of the running application
                    screenshot_results = await project_executor.capture_screenshots(ui_test_options.get("test_url"))
                    artifacts["test_results"]["screenshots"] = screenshot_results
                    
                    # Run accessibility tests
                    accessibility_results = await project_executor.run_accessibility_tests(ui_test_options.get("test_url"))
                    artifacts["test_results"]["accessibility"] = accessibility_results
                    metrics["accessibility_violations"] = len(accessibility_results.get("violations", []))
                    
                    test_summary = f"UI testing with Playwright completed:\n"
                    
                    if ui_test_results.get("success", False):
                        test_summary += f"✅ UI tests passed successfully\n"
                    else:
                        test_summary += f"⚠️ Some UI tests failed or reported warnings\n"
                    
                    if screenshot_results.get("success", False):
                        test_summary += f"📸 Captured {len(screenshot_results.get('screenshots', []))} responsive design screenshots\n"
                    
                    if accessibility_results.get("success", False):
                        violation_count = len(accessibility_results.get("violations", []))
                        if violation_count == 0:
                            test_summary += f"✅ No accessibility violations detected!\n"
                else:
                            test_summary += f"⚠️ Found {violation_count} accessibility issues to address\n"
                
                await safe_callback(stream_callback, f"\n{test_summary}\n")
            except Exception as e:
                logger.error(f"[AutoAgent] Testing phase error: {e}")
                await safe_callback(stream_callback, f"⚠️ Testing encountered an error: {str(e)}\n")
                artifacts["test_results"] = {'error': str(e)}
            
            metrics["testing_time"] = time.time() - testing_start
            
            # 6. Final validation and quality assessment
            logger.info(f"[AutoAgent] Starting final validation for project: {project_name}")
            await safe_callback(stream_callback, "Performing final validation and quality assessment...\n")
            
            # Update agent workflow status for validation phase
            workflow_stages['testing'] = {'status': 'completed', 'progress': 100}
            workflow_stages['validation'] = {'status': 'in_progress', 'progress': 50}
            await emit_agent_status(
                project_name=project_name,
                active=True,
                stage="validation",
                progress=95,
                message="Performing final validation and quality checks",
                stages=workflow_stages
            )
            
            # Validate project structure
            validation = {
                "success": completed_subtasks > 0 and (failed_subtasks / total_subtasks) < 0.3,
                "project_structure_valid": False,
                "can_run": False,
                "issues": [],
                "recommendations": []
            }
            
            # Check project structure based on project type
            project_type = self.determine_project_type(prompt)
            
            if project_type == "web_app":
                # Check for essential web app files
                essential_files = [
                    'package.json',
                    'src/index.html',
                    'src/main.ts',
                    'src/app/app.module.ts'
                ]
                
                missing_files = []
                for file in essential_files:
                    if not os.path.exists(os.path.join(project_dir, file)):
                        missing_files.append(file)
                
                validation["project_structure_valid"] = len(missing_files) == 0
                if missing_files:
                    validation["issues"].append(f"Missing essential files: {', '.join(missing_files)}")
                    validation["recommendations"].append("Run 'ng new' to create a proper Angular project structure")
                
                # Check if project can run
                try:
                    # Update agent status with current subtask details
                    sub_progress = (completed_subtasks / total_subtasks) * 100
                    subtask_progress = min(45 + int(sub_progress * 0.4), 85)
                    await emit_agent_status(
                        project_name=project_name,
                        active=True,
                        stage="implementation",
                        progress=subtask_progress,
                        message=f"Implementing: {subtask.get('title', 'Task')}",
                        stages=workflow_stages
                    )
                    
                    # Try to build the project
                    build_result = await shell_executor.execute_command('npm run build', timeout=120)
                    validation["can_run"] = "ERROR" not in build_result.upper()
                    if not validation["can_run"]:
                        validation["issues"].append("Project build failed")
                        validation["recommendations"].append("Check for syntax errors in the code")
                except Exception as e:
                    validation["can_run"] = False
                    validation["issues"].append(f"Build failed: {str(e)}")
            
            else:
                # Generic validation for other project types
                validation["project_structure_valid"] = len(artifacts["code_files"]) > 0
                validation["can_run"] = validation["project_structure_valid"]
            
            artifacts["final_validation"] = validation
            
            # Generate final summary and metrics
            metrics["total_execution_time"] = time.time() - start_time
            
            # Generate final report
            success_rate = completed_subtasks / total_subtasks if total_subtasks > 0 else 0
            final_status = 'success' if success_rate > 0.7 else 'partial_success' if success_rate > 0.3 else 'failure'
            
            # Send completion callback
            await safe_callback(completion_callback, {
                'status': final_status,
                'project_name': project_name,
                'project_dir': project_dir,
                'metrics': metrics,
                'validation': validation
            })
            
            # Final summary
            final_summary = f"\n==== Project Creation Complete ====\n"
            final_summary += f"Project: {project_name}\n"
            final_summary += f"Location: {project_dir}\n"
            final_summary += f"Status: {final_status.upper()}\n"
            final_summary += f"Execution time: {metrics['total_execution_time']:.2f} seconds\n\n"
            
            final_summary += f"Implementation: {completed_subtasks}/{total_subtasks} steps completed\n"
            final_summary += f"Error recovery: {metrics['errors_recovered']} issues automatically resolved\n"
            
            if validation["issues"]:
                final_summary += "\nIssues Found:\n"
                for issue in validation["issues"]:
                    final_summary += f"- {issue}\n"
            
            if validation["recommendations"]:
                final_summary += "\nRecommendations:\n"
                for rec in validation["recommendations"]:
                    final_summary += f"- {rec}\n"
            
            if validation["can_run"]:
                final_summary += "\n✅ Project is ready to run!\n"
                if project_type == "web_app":
                    final_summary += "To start the application, run: npm start\n"
            else:
                final_summary += "\n⚠️ Project may require fixes before running\n"
            
            await safe_callback(stream_callback, final_summary)
            
            # Update agent workflow status for completion
            workflow_stages['validation'] = {'status': 'completed', 'progress': 100}
            await emit_agent_status(
                project_name=project_name,
                active=False,
                stage="completed",
                progress=100,
                message=f"Project {project_name} created successfully",
                stages=workflow_stages
            )
            
            logger.info(f"[AutoAgent] Fully automated execution completed for project: {project_name}")
            
            return {
                "success": validation["success"],
                "project_name": project_name,
                "project_dir": project_dir,
                "metrics": metrics,
                "artifacts": artifacts,
                "validation": validation,
                "final_summary": final_summary
            }
            
        except Exception as e:
            logger.error(f"[AutoAgent] Critical error in fully automated execution: {e}")
            error_summary = f"\n==== ERROR ====\n"
            error_summary += f"A critical error occurred during automated project creation:\n{str(e)}\n"
            error_summary += "The process could not be completed successfully.\n"
            
            await safe_callback(stream_callback, error_summary)
            
            # Update agent workflow status for error case
            for stage in workflow_stages:
                if workflow_stages[stage]['status'] == 'in_progress':
                    workflow_stages[stage]['status'] = 'failed'
            
            await emit_agent_status(
                project_name=project_name,
                active=False,
                stage="error",
                progress=100,
                message=f"Project execution failed: {str(e)}",
                stages=workflow_stages
            )
            
            return {
                "success": False,
                "project_name": project_name,
                "error": str(e),
                "metrics": metrics,
                "artifacts": artifacts
            }

    async def _analyze_implementation_against_requirements(self, project_dir: str, requirements: str, project_type: str, build_issues: List[str] = None, model_id: str = None, implementation_iterations: int = 0) -> Dict[str, Any]:
        """
        Analyze the current implementation against the requirements to identify missing features.
        
        Args:
            project_dir: Path to the project directory
            requirements: Original requirements text
            project_type: Type of project (angular, react, etc.)
            build_issues: List of build issues encountered, if any
            
        Returns:
            Analysis results with missing features, issues, etc.
        """
        logger.info(f"[Agent] Analyzing implementation against requirements for {project_type} project")
        
        # Initialize result structure
        analysis = {
            "missing_features": [],
            "incomplete_components": [],
            "requirements_met": False,
            "placeholder_code_detected": False,  # New field for tracking placeholder code
            "placeholder_code_detected": False  # New field for tracking placeholder code
        }
        
        try:
            # Step 1: Collect all implementation files for analysis
            implementation_files = {}
            if project_type.lower() in ["angular", "angular-app", "web_app"]:
                # For Angular, focus on component and service files
                src_app_dir = os.path.join(project_dir, "src", "app")
                if os.path.exists(src_app_dir):
                    for root, _, files in os.walk(src_app_dir):
                        for file in files:
                            if file.endswith(".component.ts") or file.endswith(".service.ts") or file.endswith(".component.html"):
                                file_path = os.path.join(root, file)
                                with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                                    implementation_files[file_path] = f.read()
            elif project_type.lower() == "react":
                # For React, focus on component files
                src_dir = os.path.join(project_dir, "src")
                if os.path.exists(src_dir):
                    for root, _, files in os.walk(src_dir):
                        for file in files:
                            if file.endswith(".js") or file.endswith(".jsx") or file.endswith(".tsx"):
                                file_path = os.path.join(root, file)
                                with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                                    implementation_files[file_path] = f.read()
            else:
                # For other project types, collect all code files
                for root, _, files in os.walk(project_dir):
                    for file in files:
                        if file.endswith((".js", ".py", ".ts", ".jsx", ".tsx", ".html", ".css", ".scss")):
                            file_path = os.path.join(root, file)
                            try:
                                with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                                    implementation_files[file_path] = f.read()
                            except Exception as file_error:
                                logger.warning(f"[Agent] Error reading file {file_path}: {file_error}")
                                continue
        except Exception as e:
            logger.error(f"[Agent] Error collecting implementation files: {e}")
            implementation_files = {}
        
        # ENHANCED: Define patterns that indicate placeholder/boilerplate code
        placeholder_patterns = [
            # TypeScript/JavaScript specific patterns
            r'//\s*TODO',
            r'//\s*FIXME',
            r'//\s*Implement',
            r'//\s*Add.*functionality',
            r'//\s*Add.*implementation',
            r'console\.log\([\'"`].*placeholder.*[\'"`]\)',
            r'console\.log\([\'"`].*TODO.*[\'"`]\)',
            r'return\s*null',
            r'return\s*\[\]',
            r'return\s*\{\}',
            r'\/\*\s*Placeholder.*\*\/',
            r'\/\*\s*TODO.*\*\/',
            # HTML specific patterns
            r'<!--\s*TODO',
            r'<!--\s*FIXME',
            r'<!--\s*Add content',
            r'<div>.*placeholder.*</div>',
            r'<p>.*placeholder.*</p>',
            r'<h\d>.*placeholder.*</h\d>',
            # Angular specific patterns
            r'ngOnInit\(\)\s*\{\s*\}',
            r'constructor\(\)\s*\{\s*\}',
            # React specific patterns
            r'function\s+\w+\(\)\s*\{\s*return\s*\(\s*<div>[^<>]*</div>\s*\);\s*\}',
            r'const\s+\w+\s*=\s*\(\)\s*=>\s*\{\s*return\s*\(\s*<div>[^<>]*</div>\s*\);\s*\};',
            # Empty lifecycle methods
            r'componentDidMount\(\)\s*\{\s*\}',
            r'useEffect\(\(\)\s*=>\s*\{\s*\},\s*\[\]\)',
        ]
        
        # ENHANCED: Check for overly simple implementations (boilerplate code)
        boilerplate_indicators = {
            # Component is too simple (just a few lines)
            'too_few_lines': 10,  # Component with fewer than 10 lines of actual code
            'no_logic_branches': r'if|switch|for|while|map', # No conditional or loop logic
            'no_state_management': r'useState|setState|this\.state|\[\s*\w+\s*,\s*set\w+\s*\]|new BehaviorSubject', # No state
            'default_exports_only': r'export\s+default', # Only exports, no actual code
            'minimal_html': 3, # HTML with fewer than 3 elements
            'no_api_calls': r'fetch|axios|http\.|HttpClient|\.get\(|\.post\(|\.put\(|\.delete\(', # No API calls when likely needed
        }

        # Step 2: Check each component for placeholder code and minimal implementation
        placeholder_components = []
        
        for path, content in implementation_files.items():
            component_name = os.path.basename(path).replace(".component.ts", "")
            if ".component." not in path and ".service." not in path:
                # Skip non-component, non-service files
                continue
            
            placeholder_indicators = []
            
            # Check for placeholder patterns
            for pattern in placeholder_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    placeholder_indicators.append(f"Contains placeholder pattern: {pattern}")
            
            # Check for boilerplate indicators
            actual_code_lines = len([line for line in content.split('\n') if line.strip() and not line.strip().startswith('//')])
            if actual_code_lines < boilerplate_indicators['too_few_lines'] and '.html' not in path:
                placeholder_indicators.append(f"Too few lines of code ({actual_code_lines})")
            
            # Check for absence of logic branches
            if '.ts' in path or '.js' in path:
                if not re.search(boilerplate_indicators['no_logic_branches'], content):
                    placeholder_indicators.append("No conditional or loop logic")
                
                # For services, check for API calls if required
                if '.service.' in path and not re.search(boilerplate_indicators['no_api_calls'], content):
                    # Only flag this issue if the requirements suggest API calls
                    if any(term in requirements.lower() for term in ['api', 'server', 'data', 'fetch', 'http', 'backend']):
                        placeholder_indicators.append("Service lacks API calls")
            
            # For HTML files, check for minimal content
            if '.html' in path:
                html_elements = re.findall(r'<(\w+)[^>]*>', content)
                if len(html_elements) < boilerplate_indicators['minimal_html']:
                    placeholder_indicators.append(f"HTML has minimal elements ({len(html_elements)})")
            
            # If indicators found, add to placeholder_components
            if placeholder_indicators:
                placeholder_components.append({
                    "name": component_name,
                    "path": path,
                    "indicators": placeholder_indicators
                })
                analysis["placeholder_code_detected"] = True
        
        # Add placeholder components to incomplete_components
        for comp in placeholder_components:
            # Check if this component is already in incomplete_components
            existing = False
            for existing_comp in analysis["incomplete_components"]:
                if existing_comp.get("name") == comp["name"]:
                    # Update existing entry
                    existing_comp["placeholder_indicators"] = comp["indicators"]
                    existing = True
                    break
            
            if not existing:
                # Add as new incomplete component
                analysis["incomplete_components"].append({
                    "name": comp["name"],
                    "path": comp["path"],
                    "missing": [f"Placeholder code: {', '.join(comp['indicators'])}"],
                    "placeholder_indicators": comp["indicators"]
                })
        
        # Step 3: Extract key features from requirements
        # This is a simplified approach - in a real implementation, 
        # we would use more sophisticated NLP/LLM techniques
        key_features = self._extract_key_features_from_requirements(requirements)
        
        # Step 4: Check each component against its expected functionality
        # For Angular apps, focus on checking components and services
        if project_type.lower() in ["angular", "angular-app", "web_app"]:
            # Get all component and service files
            component_files = {path: content for path, content in implementation_files.items() 
                              if path.endswith(".component.ts")}
            service_files = {path: content for path, content in implementation_files.items() 
                            if path.endswith(".service.ts")}
            
            # Evaluate component implementation completeness
            for path, content in component_files.items():
                component_name = os.path.basename(path).replace(".component.ts", "")
                html_path = path.replace(".ts", ".html")
                html_content = implementation_files.get(html_path, "")
                
                # Simple checks for placeholder content vs actual implementation
                is_placeholder = False
                missing_functionality = []
                
                if "// Add methods for user interactions" in content:
                    is_placeholder = True
                    missing_functionality.append("User interaction methods")
                
                if "// Implement actual logic based on requirements" in content:
                    is_placeholder = True
                    missing_functionality.append("Business logic implementation")
                
                # Look for HTML template issues
                if html_content and "<!-- Add more UI elements based on requirements -->" in html_content:
                    is_placeholder = True
                    missing_functionality.append("Complete UI implementation")
                
                if is_placeholder or missing_functionality:
                    analysis["incomplete_components"].append({
                        "name": component_name,
                        "path": path,
                        "missing": missing_functionality
                    })
            
            # Check for service implementation completeness
            for path, content in service_files.items():
                service_name = os.path.basename(path).replace(".service.ts", "")
                
                # Check for placeholder service implementation
                is_placeholder = False
                missing_functionality = []
                
                if "// Service for:" in content:
                    is_placeholder = True
                    missing_functionality.append("Service-specific implementation")
                
                if is_placeholder or missing_functionality:
                    analysis["incomplete_components"].append({
                        "name": service_name,
                        "path": path,
                        "missing": missing_functionality
                    })
        
        # Step 5: Compare key features against implementation
        for feature in key_features:
            feature_implemented = False
            
            # Simple check - does the code contain references to this feature?
            # Note: This is a very basic approach. A real solution would use more advanced analysis.
            feature_terms = feature.lower().split()
            for path, content in implementation_files.items():
                content_lower = content.lower()
                if all(term in content_lower for term in feature_terms):
                    feature_implemented = True
                    break
            
            if not feature_implemented:
                analysis["missing_features"].append(feature)
        
        # Step 6: Use AI model to perform deep analysis of implementation against requirements
        # This is where we leverage the model to truly understand both code and requirements
        if model_id:
            try:
                logger.info(f"[Agent] Using AI model {model_id} for deep implementation analysis")
                
                # Get all components and services for better analysis
                component_files = {path: content for path, content in implementation_files.items() 
                                if ".component." in path or ".service." in path}
                
                # Include all files - no artificial limit on file count or content
                # For DeepSeek models, we want to leverage their large context window
                relevant_files = component_files
                
                # Prepare the analysis prompt
                analysis_prompt = f"""Analyze the following code implementation against the requirements:\n\n
                Requirements:\n{requirements}\n\n""" 
                
                # Add all code snippets for analysis - no truncation
                # DeepSeek's larger models can handle higher token counts
                analysis_prompt += "Implementation Code Files:\n"
                for path, content in relevant_files.items():
                    analysis_prompt += f"\n--- {path} ---\n{content}\n"
                
                # Include any build issues
                if build_issues:
                    analysis_prompt += "\n\nBuild Issues:\n"
                    for issue in build_issues:
                        analysis_prompt += f"- {issue}\n"
                
                # Add specific instructions for the AI to analyze the implementation
                analysis_prompt += """\n\nAnalyze in detail:\n
                1. What specific features from the requirements are missing or incomplete in the implementation?
                2. Which components need modification and what functionality should be added?
                3. Are there any API calls, HTTP services, or backend integrations required but not implemented?
                4. What specific code changes are needed to fully implement the requirements?
                5. Are there any architectural or design issues in the implementation?
                
                Format your analysis as structured JSON with these fields:\n
                - missing_features: List of specific features missing from implementation
                - incomplete_components: List of components needing modification with details
                - required_api_calls: List of API calls or services needed to fully implement requirements
                - suggested_code_changes: List of specific code changes needed (file path and description)
                - design_issues: Any architectural or UI/UX issues
                
                Provide specific details for each item, not general observations.
                """
                
                # First try with original model, then fallback to larger models if needed
                # Create LLM instance with original model
                logger.info(f"[Agent] Attempting deep code analysis with model {model_id}")
                llm = LLM.create(model_id)
                
                try:
                    # Try with original model first
                    deep_analysis_result = await llm.generate(
                        analysis_prompt, 
                        project_name="implementation-analysis"
                    )
                except Exception as e:
                    # Check if this is a token limit error
                    if "maximum context length" in str(e).lower() or "requested too many tokens" in str(e).lower():
                        # Fall back to larger model
                        large_context_model = self._get_large_context_model(model_id)
                        logger.info(f"[Agent] Falling back to large context model {large_context_model} for deep code analysis")
                        
                        # Create new LLM instance with large context model
                        llm = LLM.create(large_context_model)
                        
                        # Retry with larger model and explicit context window
                        deep_analysis_result = await llm.generate(
                            analysis_prompt, 
                            project_name="implementation-analysis-fallback"
                        )
                    else:
                        # If it's not a token limit error, re-raise
                        raise
                
                # Extract JSON from the response - the model might wrap JSON in markdown or other text
                json_pattern = re.compile(r'\{[\s\S]*\}')
                match = json_pattern.search(deep_analysis_result)
                if match:
                    try:
                        json_str = match.group(0)
                        deep_analysis_data = json.loads(json_str)
                        
                        # Merge AI analysis with our rule-based analysis
                        if 'missing_features' in deep_analysis_data and isinstance(deep_analysis_data['missing_features'], list):
                            # Use AI-detected missing features, which will be more comprehensive
                            analysis['missing_features'] = deep_analysis_data['missing_features']
                        
                        if 'incomplete_components' in deep_analysis_data:
                            # Combine with our existing incomplete_components, ensuring we don't lose info
                            existing_components = {comp['name']: comp for comp in analysis['incomplete_components']}
                            for comp in deep_analysis_data.get('incomplete_components', []):
                                if isinstance(comp, dict) and 'name' in comp:
                                    comp_name = comp['name']
                                    if comp_name in existing_components:
                                        # Update existing entry
                                        for key, value in comp.items():
                                            if key != 'name':
                                                existing_components[comp_name][key] = value
                                    else:
                                        # Add new entry
                                        analysis['incomplete_components'].append(comp)
                        
                        # Add new fields from AI analysis
                        for key in ['required_api_calls', 'suggested_code_changes', 'design_issues']:
                            if key in deep_analysis_data:
                                analysis[key] = deep_analysis_data[key]
                        
                        # Additional AI-detected insights
                        if 'additional_insights' in deep_analysis_data:
                            analysis['ai_insights'] = deep_analysis_data['additional_insights']
                        
                        logger.info(f"[Agent] AI-powered deep analysis completed successfully")
                    except json.JSONDecodeError as e:
                        # If JSON parsing fails, extract key insights using regex
                        logger.warning(f"[Agent] Failed to parse AI analysis JSON: {e}")
                        # Try to extract key insights using regex
            # Step 2: Check each component for placeholder code and minimal implementation
            placeholder_components = []
            
            for path, content in implementation_files.items():
                component_name = os.path.basename(path).replace(".component.ts", "")
                if ".component." not in path and ".service." not in path:
                    # Skip non-component, non-service files
                    continue
                
                placeholder_indicators = []
                
                # Check for placeholder patterns
                for pattern in placeholder_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        placeholder_indicators.append(f"Contains placeholder pattern: {pattern}")
                
                # Check for boilerplate indicators
                actual_code_lines = len([line for line in content.split('\n') if line.strip() and not line.strip().startswith('//')])
                if actual_code_lines < boilerplate_indicators['too_few_lines'] and '.html' not in path:
                    placeholder_indicators.append(f"Too few lines of code ({actual_code_lines})")
                
                # Check for absence of logic branches
                if '.ts' in path or '.js' in path:
                    if not re.search(boilerplate_indicators['no_logic_branches'], content):
                        placeholder_indicators.append("No conditional or loop logic")
                    
                    # For services, check for API calls if required
                    if '.service.' in path and not re.search(boilerplate_indicators['no_api_calls'], content):
                        # Only flag this issue if the requirements suggest API calls
                        if any(term in requirements.lower() for term in ['api', 'server', 'data', 'fetch', 'http', 'backend']):
                            placeholder_indicators.append("Service lacks API calls")
            
                # For HTML files, check for minimal content
                if '.html' in path:
                    html_elements = re.findall(r'<(\w+)[^>]*>', content)
                    if len(html_elements) < boilerplate_indicators['minimal_html']:
                        placeholder_indicators.append(f"HTML has minimal elements ({len(html_elements)})")
                
                # If indicators found, add to placeholder_components
                if placeholder_indicators:
                    placeholder_components.append({
                        "name": component_name,
                        "path": path,
                        "indicators": placeholder_indicators
                    })
                    analysis["placeholder_code_detected"] = True
            
            # Add placeholder components to incomplete_components
            for comp in placeholder_components:
                # Check if this component is already in incomplete_components
                existing = False
                for existing_comp in analysis["incomplete_components"]:
                    if existing_comp.get("name") == comp["name"]:
                        # Update existing entry
                        existing_comp["placeholder_indicators"] = comp["indicators"]
                        existing = True
                        break
                
                if not existing:
                    # Add as new incomplete component
                    analysis["incomplete_components"].append({
                        "name": comp["name"],
                        "path": comp["path"],
                        "missing": [f"Placeholder code: {', '.join(comp['indicators'])}"],
                        "placeholder_indicators": comp["indicators"]
                    })
            
            # Step 3: Extract key features from requirements
            # This is a simplified approach - in a real implementation, 
            # we would use more sophisticated NLP/LLM techniques
            key_features = self._extract_key_features_from_requirements(requirements)
            
            # Step 4: Check each component against its expected functionality
            # For Angular apps, focus on checking components and services
            if project_type.lower() in ["angular", "angular-app", "web_app"]:
                # Get all component and service files
                component_files = {path: content for path, content in implementation_files.items() 
                                  if path.endswith(".component.ts")}
                service_files = {path: content for path, content in implementation_files.items() 
                                if path.endswith(".service.ts")}
                
                # Evaluate component implementation completeness
                for path, content in component_files.items():
                    component_name = os.path.basename(path).replace(".component.ts", "")
                    html_path = path.replace(".ts", ".html")
                    html_content = implementation_files.get(html_path, "")
                    
                    # Simple checks for placeholder content vs actual implementation
                    is_placeholder = False
                    missing_functionality = []
                    
                    if "// Add methods for user interactions" in content:
                        is_placeholder = True
                        missing_functionality.append("User interaction methods")
                    
                    if "// Implement actual logic based on requirements" in content:
                        is_placeholder = True
                        missing_functionality.append("Business logic implementation")
                    
                    # Look for HTML template issues
                    if html_content and "<!-- Add more UI elements based on requirements -->" in html_content:
                        is_placeholder = True
                        missing_functionality.append("Complete UI implementation")
                    
                    if is_placeholder or missing_functionality:
                        analysis["incomplete_components"].append({
                            "name": component_name,
                            "path": path,
                            "missing": missing_functionality
                        })
                
                # Check for service implementation completeness
                for path, content in service_files.items():
                    service_name = os.path.basename(path).replace(".service.ts", "")
                    
                    # Check for placeholder service implementation
                    is_placeholder = False
                    missing_functionality = []
                    
                    if "// Service for:" in content:
                        is_placeholder = True
                        missing_functionality.append("Service-specific implementation")
                    
                    if is_placeholder or missing_functionality:
                        analysis["incomplete_components"].append({
                            "name": service_name,
                            "path": path,
                            "missing": missing_functionality
                        })
            
            # Step 5: Compare key features against implementation
            for feature in key_features:
                feature_implemented = False
                
                # Simple check - does the code contain references to this feature?
                # Note: This is a very basic approach. A real solution would use more advanced analysis.
                feature_terms = feature.lower().split()
                for path, content in implementation_files.items():
                    content_lower = content.lower()
                    if all(term in content_lower for term in feature_terms):
                        feature_implemented = True
                        break
                
                if not feature_implemented:
                    analysis["missing_features"].append(feature)
            
            # Step 6: Use AI model to perform deep analysis of implementation against requirements
            # This is where we leverage the model to truly understand both code and requirements
            if model_id:
                try:
                    logger.info(f"[Agent] Using AI model {model_id} for deep implementation analysis")
                    
                    # Get all components and services for better analysis
                    component_files = {path: content for path, content in implementation_files.items() 
                                    if ".component." in path or ".service." in path}
                    
                    # Include all files - no artificial limit on file count or content
                    # For DeepSeek models, we want to leverage their large context window
                    relevant_files = component_files
                    
                    # Prepare the analysis prompt
                    analysis_prompt = f"""Analyze the following code implementation against the requirements:\n\n
                    Requirements:\n{requirements}\n\n""" 
                    
                    # Add all code snippets for analysis - no truncation
                    # DeepSeek's larger models can handle higher token counts
                    analysis_prompt += "Implementation Code Files:\n"
                    for path, content in relevant_files.items():
                        analysis_prompt += f"\n--- {path} ---\n{content}\n"
                    
                    # Include any build issues
                    if build_issues:
                        analysis_prompt += "\n\nBuild Issues:\n"
                        for issue in build_issues:
                            analysis_prompt += f"- {issue}\n"
                    
                    # Add specific instructions for the AI to analyze the implementation
                    analysis_prompt += """\n\nAnalyze in detail:\n
                    1. What specific features from the requirements are missing or incomplete in the implementation?
                    2. Which components need modification and what functionality should be added?
                    3. Are there any API calls, HTTP services, or backend integrations required but not implemented?
                    4. What specific code changes are needed to fully implement the requirements?
                    5. Are there any architectural or design issues in the implementation?
                    
                    Format your analysis as structured JSON with these fields:\n
                    - missing_features: List of specific features missing from implementation
                    - incomplete_components: List of components needing modification with details
                    - required_api_calls: List of API calls or services needed to fully implement requirements
                    - suggested_code_changes: List of specific code changes needed (file path and description)
                    - design_issues: Any architectural or UI/UX issues
                    
                    Provide specific details for each item, not general observations.
                    """
                    
                    # First try with original model, then fallback to larger models if needed
                    # Create LLM instance with original model
                    logger.info(f"[Agent] Attempting deep code analysis with model {model_id}")
                    llm = LLM.create(model_id)
                    
                    try:
                        # Try with original model first
                        deep_analysis_result = await llm.generate(
                            analysis_prompt, 
                            project_name="implementation-analysis"
                        )
                    except Exception as e:
                        # Check if this is a token limit error
                        if "maximum context length" in str(e).lower() or "requested too many tokens" in str(e).lower():
                            # Fall back to larger model
                            large_context_model = self._get_large_context_model(model_id)
                            logger.info(f"[Agent] Falling back to large context model {large_context_model} for deep code analysis")
                            
                            # Create new LLM instance with large context model
                            llm = LLM.create(large_context_model)
                            
                            # Retry with larger model and explicit context window
                            deep_analysis_result = await llm.generate(
                                analysis_prompt, 
                                project_name="implementation-analysis-fallback"
                            )
                        else:
                            # If it's not a token limit error, re-raise
                            raise
                    
                    # Extract JSON from the response - the model might wrap JSON in markdown or other text
                    json_pattern = re.compile(r'\{[\s\S]*\}')
                    match = json_pattern.search(deep_analysis_result)
                    if match:
                        try:
                            json_str = match.group(0)
                            deep_analysis_data = json.loads(json_str)
                            
                            # Merge AI analysis with our rule-based analysis
                            if 'missing_features' in deep_analysis_data and isinstance(deep_analysis_data['missing_features'], list):
                                # Use AI-detected missing features, which will be more comprehensive
                                analysis['missing_features'] = deep_analysis_data['missing_features']
                            
                            if 'incomplete_components' in deep_analysis_data:
                                # Combine with our existing incomplete_components, ensuring we don't lose info
                                existing_components = {comp['name']: comp for comp in analysis['incomplete_components']}
                                for comp in deep_analysis_data.get('incomplete_components', []):
                                    if isinstance(comp, dict) and 'name' in comp:
                                        comp_name = comp['name']
                                        if comp_name in existing_components:
                                            # Update existing entry
                                            for key, value in comp.items():
                                                if key != 'name':
                                                    existing_components[comp_name][key] = value
                                        else:
                                            # Add new entry
                                            analysis['incomplete_components'].append(comp)
                            
                            # Add new fields from AI analysis
                            for key in ['required_api_calls', 'suggested_code_changes', 'design_issues']:
                                if key in deep_analysis_data:
                                    analysis[key] = deep_analysis_data[key]
                            
                            # Additional AI-detected insights
                            if 'additional_insights' in deep_analysis_data:
                                analysis['ai_insights'] = deep_analysis_data['additional_insights']
                            
                            logger.info(f"[Agent] AI-powered deep analysis completed successfully")
                        except json.JSONDecodeError as e:
                            # If JSON parsing fails, extract key insights using regex
                            logger.warning(f"[Agent] Failed to parse AI analysis JSON: {e}")
                            # Try to extract key insights using regex
                            missing_features = re.findall(r'missing features?[:\-]\s*([^\n]+)', deep_analysis_result, re.IGNORECASE)
                            for feature in missing_features:
                                if feature and feature.strip() not in analysis['missing_features']:
                                    analysis['missing_features'].append(feature.strip())
                            
                            # Add raw AI response for debugging
                            analysis['ai_raw_analysis'] = deep_analysis_result[:1000]  # Truncate to avoid excessive size
                    else:
                        logger.warning(f"[Agent] Could not extract JSON from AI analysis")
                        # Add raw AI response as fallback
                        analysis['ai_raw_analysis'] = deep_analysis_result[:1000]  # Truncate to avoid excessive size
                        
                except Exception as e:
                    logger.error(f"[Agent] Error in AI-powered implementation analysis: {e}")
                    # Add error to analysis but continue with rule-based results
                    analysis["ai_analysis_error"] = str(e)
            
            # Step 7: Determine if requirements are met based on all analyses
            analysis["requirements_met"] = len(analysis["missing_features"]) == 0 and len(analysis["incomplete_components"]) == 0
            # Always consider an initial build (first iteration) as incomplete to force at least one detailed analysis
            if implementation_iterations <= 1 or \
               len(analysis.get("missing_features", [])) > 0 or \
               len(analysis.get("incomplete_components", [])) > 0:
                analysis["requirements_met"] = False
            
            logger.info(f"[Agent] Implementation analysis complete. Missing features: {len(analysis['missing_features'])}, ")
            logger.info(f"Incomplete components: {len(analysis['incomplete_components'])}")
            
            # Add domain-specific validations based on project type and requirements
            # This checks for specific implementations that should be present based on the project domain
            analysis["domain_specific_issues"] = []
            
            # Music player domain-specific validation
            if "music" in requirements.lower() or "player" in requirements.lower() or "audio" in requirements.lower():
                logger.info("[Agent] Performing domain-specific validation for music player application")
                
                # Check for required components
                music_player_components = ["music-player", "player", "musicplayer", "audio-player", "audioplayer"]
                playlist_components = ["playlist", "track-list", "tracklist", "song-list", "songlist"]
                audio_services = ["audio", "audio-service", "media", "media-service", "player-service"]
                
                # Look for music player components in implementation files
                found_player = False
                found_playlist = False
                found_audio_service = False
                
                for path in implementation_files.keys():
                    basename = os.path.basename(path).lower()
                    
                    if any(comp in basename for comp in music_player_components):
                        found_player = True
                    if any(comp in basename for comp in playlist_components):
                        found_playlist = True
                    if any(comp in basename for comp in audio_services) and ".service." in path.lower():
                        found_audio_service = True
                
                # Add missing core components as issues
                if not found_player:
                    analysis["domain_specific_issues"].append("Missing music player component")
                    analysis["missing_features"].append("Music player interface component")
                
                if not found_playlist:
                    analysis["domain_specific_issues"].append("Missing playlist/track-list component")
                    analysis["missing_features"].append("Playlist or track listing component")
                
                if not found_audio_service:
                    analysis["domain_specific_issues"].append("Missing audio service for playback control")
                    analysis["missing_features"].append("Audio service for media playback")
                
                # For each component, check if it has the required functionality
                for path, content in implementation_files.items():
                    file_lower = path.lower()
                    content_lower = content.lower()
                    
                    # Check player component implementation
                    if any(comp in file_lower for comp in music_player_components):
                        # Player should have play/pause functionality
                        if "play" not in content_lower or "pause" not in content_lower:
                            analysis["domain_specific_issues"].append("Music player component is missing play/pause functionality")
                            comp_name = os.path.basename(path).replace(".component.ts", "")
                            analysis["incomplete_components"].append({
                                "name": comp_name,
                                "path": path,
                                "missing": ["Play/pause functionality required for music player"]
                            })
                    
                    # Check audio service implementation
                    if any(comp in file_lower for comp in audio_services) and ".service." in file_lower:
                        required_methods = ["play", "pause", "stop", "volume"]
                        missing_methods = [method for method in required_methods if method not in content_lower]
                        
                        if missing_methods:
                            analysis["domain_specific_issues"].append(f"Audio service is missing required methods: {', '.join(missing_methods)}")
                            service_name = os.path.basename(path).replace(".service.ts", "")
                            analysis["incomplete_components"].append({
                                "name": service_name,
                                "path": path,
                                "missing": [f"Required audio service methods: {', '.join(missing_methods)}"]
                            })
            
            # E-commerce domain-specific validation
            elif "ecommerce" in requirements.lower() or "shop" in requirements.lower() or "store" in requirements.lower() or "product" in requirements.lower():
                logger.info("[Agent] Performing domain-specific validation for e-commerce application")
                
                # Check for required components
                product_components = ["product", "item", "catalog"]
                cart_components = ["cart", "basket", "shopping-cart"]
                checkout_components = ["checkout", "payment", "order"]
                
                # Look for e-commerce components in implementation files
                found_product = False
                found_cart = False
                found_checkout = False
                
                for path in implementation_files.keys():
                    basename = os.path.basename(path).lower()
                    
                    if any(comp in basename for comp in product_components):
                        found_product = True
                    if any(comp in basename for comp in cart_components):
                        found_cart = True
                    if any(comp in basename for comp in checkout_components):
                        found_checkout = True
                
                # Add missing core components as issues
                if not found_product:
                    analysis["domain_specific_issues"].append("Missing product/catalog component")
                    analysis["missing_features"].append("Product listing component")
                
                if not found_cart:
                    analysis["domain_specific_issues"].append("Missing shopping cart component")
                    analysis["missing_features"].append("Shopping cart functionality")
                
                if not found_checkout:
                    analysis["domain_specific_issues"].append("Missing checkout/payment component")
                    analysis["missing_features"].append("Checkout or payment processing")
            
            # Add more domain-specific validations as needed
            
        except Exception as e:
            logger.error(f"[Agent] Error analyzing implementation: {e}")
            analysis["error"] = str(e)
            
        return analysis
