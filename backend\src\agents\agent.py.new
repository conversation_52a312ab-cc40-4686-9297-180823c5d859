"""
Replacement for the Agent.fully_automated_execute method to fix:
1. Error handling limit - increase max retries from 2 to 100
2. Session continuity - save and load project context between phases
"""

async def fully_automated_execute(self, prompt: str, project_name: str, callbacks=None) -> Dict[str, Any]:
    """
    Execute a project build request in fully automated mode.
    
    Args:
        prompt: The user's prompt
        project_name: The name of the project
        callbacks: Optional dictionary of callbacks for different events
        
    Returns:
        Result dictionary
    """
    start_time = time.time()
    logger.info(f"[Agent] Starting fully automated execution for prompt: {prompt[:100]}...")
    
    # Initialize results
    results = {
        "success": False,
        "project_name": project_name,
        "error": None,
        "execution_time": 0,
        "has_build_issues": False,
        "build_issues": []
    }
    
    # Ensure project directory exists
    project_dir = os.path.join(self.project_manager.get_projects_dir(), project_name)
    
    # Check if this is a continuation of existing development
    is_continuation = os.path.exists(project_dir) and len(os.listdir(project_dir)) > 0
    previous_context = None
    
    if is_continuation:
        logger.info(f"[Agent] This appears to be a continuation of an existing project. Loading context...")
        # Get the existing project context to maintain continuity
        previous_context = await self._load_project_context(project_name)
        if previous_context:
            # Update the prompt with project context
            prompt = self._enhance_prompt_with_context(prompt, previous_context)
            logger.info(f"[Agent] Enhanced prompt with existing project context")
    
    # Ensure callbacks exist
    if not callbacks:
        callbacks = {}
        
    # Helper for safe callbacks
    async def safe_callback(callback, payload):
        if callback:
            try:
                if inspect.iscoroutinefunction(callback):
                    await callback(payload)
                else:
                    callback(payload)
            except Exception as cb_err:
                logger.error(f"[Agent] Error in callback: {cb_err}")
                
    # Send initial status with continuation info
    callback_payload = {
        "status": "starting",
        "message": "Starting automation planning...",
        "progress": 0,
        "is_continuation": is_continuation
    }
    
    if is_continuation and previous_context:
        callback_payload["context"] = {
            "development_phase": previous_context.get("development_phase", "unknown"),
            "last_update": previous_context.get("last_updated", 0),
            "component_count": len(previous_context.get("components", []))
        }
    
    await safe_callback(callbacks.get('status'), callback_payload)
    
    try:
        # ... YOUR EXISTING IMPLEMENTATION HERE ...
        
        # When execution is complete, save the context
        await self._save_context_after_execution(project_name, prompt, results)
        
        # Calculate total execution time
        results["execution_time"] = time.time() - start_time
        
        # Send completion status
        await safe_callback(callbacks.get('status'), {
            "status": "completed",
            "message": "Automation completed successfully" if results["success"] else "Automation completed with issues",
            "progress": 100,
            "results": results
        })
        
        logger.info(f"[Agent] Fully automated execution completed for {project_name} in {results['execution_time']:.2f} seconds")
        
        return results
    
    except Exception as e:
        logger.error(f"[Agent] Error in fully automated execution: {e}")
        logger.error(traceback.format_exc())
        
        results["success"] = False
        results["error"] = str(e)
        results["execution_time"] = time.time() - start_time
        
        # Still save context even on error
        await self._save_context_after_execution(project_name, prompt, results)
        
        # Send error status
        await safe_callback(callbacks.get('status'), {
            "status": "error",
            "message": f"Error: {str(e)}",
            "progress": 0,
            "error": str(e)
        })
        
        return results 