"""
Main Agent class that coordinates the workflow between specialized agents.
"""
import os
import json
import asyncio
import time
from typing import Dict, List, Any, Optional, Callable
import logging
from backend.src.agents.shell_executor import ShellExecutor
from backend.src.agents.automation_executor import AutomationExecutor
from backend.src.agents.project_executor import ProjectExecutor
from backend.src.feedback.feedback_store import FeedbackStore
from backend.src.socket_instance import emit_agent_status, emit_agent_message, emit_plan, emit_terminal_command, emit_code_generation_message, emit_cursor_message, emit_agent_file_update

from backend.src.llm.llm import LLM
from backend.src.agents.planner import Planner
from backend.src.agents.researcher import Researcher
from backend.src.agents.coder import Coder
from backend.src.agents.documenter import Documenter
from backend.src.project import ProjectManager
from backend.src.state import AgentState
from backend.src.llm.searxng_client import searxng_search, searxng_search_structured
import inspect
import re
from datetime import datetime

logger = logging.getLogger(__name__)

class Agent:
    """
    Main Agent class that coordinates the workflow between specialized agents.
    """
    def __init__(self, openai_model_id: str = None, local_llm_model_id: str = None):
        """
        Initialize the Agent with model IDs.
        """
        # Try to load default model configuration from config.json
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config.json")
        default_primary = "deepseek/deepseek-coder"
        default_backup = "openai/gpt-4o-mini"
        
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    if "models" in config:
                        default_primary = config["models"].get("default_primary", default_primary)
                        default_backup = config["models"].get("default_backup", default_backup)
                        logger.info(f"[Agent] Loaded model configuration from config.json: primary={default_primary}, backup={default_backup}")
            except Exception as e:
                logger.error(f"[Agent] Error loading model configuration from config.json: {e}")
        
        # Use provided models or defaults
        self.primary_model_id = openai_model_id or default_primary
        self.backup_model_id = default_backup
        self.local_llm_model_id = local_llm_model_id
        
        logger.info(f"[Agent] Initialized with primary model: {self.primary_model_id}, backup: {self.backup_model_id}")
        
        # Initialize specialized agents with the primary model
        self.planner = Planner(model_id=self.primary_model_id)
        self.researcher = Researcher(model_id=self.primary_model_id)
        self.coder = Coder(model_id=self.primary_model_id)
        self.documenter = Documenter(model_id=self.primary_model_id)
        
        self.project_manager = ProjectManager()
        self.agent_state = AgentState()
        self.context_keywords = []
        self.execution_stats = {
            "subtasks_completed": 0,
            "subtasks_failed": 0,
            "total_execution_time": 0,
            "model_usage": {}
        }
        # Maximum number of retries for failed subtasks
        self.max_retries = 2 