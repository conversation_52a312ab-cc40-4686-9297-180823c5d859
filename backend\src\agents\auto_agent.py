"""
AutoAgent - A fully autonomous end-to-end project creation system

This module provides a high-level interface to the fully automated project creation
capabilities of the Agent system.
"""
import os
import time
import asyncio
import json
import logging
import re
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime

from src.agents.agent import Agent
from src.agents.project_executor import ProjectExecutor
from src.agents.shell_executor import ShellExecutor
from src.agents.coder import Coder
from src.project import ProjectManager
from src.state import AgentState
from src.llm.llm import LLM
from src.llm.searxng_client import searxng_search_structured

logger = logging.getLogger(__name__)

class AutoAgent:
    """
    AutoAgent provides a simplified interface to fully autonomous project creation.
    
    This class coordinates the fully automated workflow for end-to-end project creation
    with minimal human intervention, handling research, planning, coding, testing,
    and validation phases.
    """
    
    def __init__(self, model_id: str = None, local_llm_model_id: str = None):
        """
        Initialize the AutoAgent with model configurations.
        
        Args:
            model_id: ID of the primary model to use (defaults to config settings)
            local_llm_model_id: Optional ID of a local LLM to use as fallback
        """
        # Try to load default model configuration from config.json
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config.json")
        default_primary = "deepseek/deepseek-coder"
        
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    if "models" in config:
                        default_primary = config["models"].get("default_primary", default_primary)
                        logger.info(f"[AutoAgent] Using primary model from config: {default_primary}")
            except Exception as e:
                logger.error(f"[AutoAgent] Error loading model from config.json: {e}")
        
        # Use the model_id parameter if provided, otherwise use the default
        primary_model = model_id or default_primary
        
        self.agent = Agent(primary_model, local_llm_model_id)
        self.project_manager = ProjectManager()
        self.agent_state = AgentState()
        
        # Status tracking
        self.current_status = "idle"
        self.current_project = None
        self.execution_start_time = None
        self.execution_progress = {
            "setup": {"status": "pending", "message": ""},
            "research": {"status": "pending", "message": ""},
            "planning": {"status": "pending", "message": ""},
            "implementation": {"status": "pending", "message": "", "completed": 0, "total": 0},
            "testing": {"status": "pending", "message": ""},
            "validation": {"status": "pending", "message": ""}
        }
        
        # Active executions
        self.active_executions = {}
        
        # Initialize the project executor - will be set when creating a project
        self.project_executor = None
        
    async def create_project(self, 
                         project_name: str, 
                         prompt: str, 
                         callbacks: Dict[str, Callable] = None) -> Dict[str, Any]:
        """
        Create a complete project autonomously with no human intervention.
        
        This method initiates the fully automated project creation workflow, 
        handling all phases from research to validation with built-in error recovery.
        
        Args:
            project_name: Name of the project to create
            prompt: Detailed description of the project
            callbacks: Optional callbacks for progress updates:
                - 'progress': Function called with detailed progress updates
                - 'stream': Function called with streaming text updates
                - 'completion': Function called when project creation completes
                
        Returns:
            Dictionary with execution results and project details
        """
        self.current_project = project_name
        self.current_status = "running"
        self.execution_start_time = time.time()
        self.agent_state.set_agent_active(project_name, True)
        
        # Initialize the project executor
        self.project_executor = ProjectExecutor(project_name, self.project_manager.projects_dir)
        
        # Reset progress tracking
        self.execution_progress = {
            "setup": {"status": "pending", "message": ""},
            "research": {"status": "pending", "message": ""},
            "planning": {"status": "pending", "message": ""},
            "implementation": {"status": "pending", "message": "", "completed": 0, "total": 0},
            "testing": {"status": "pending", "message": ""},
            "validation": {"status": "pending", "message": ""}
        }
        
        # Register this execution
        execution_id = f"{project_name}_{int(self.execution_start_time)}"
        self.active_executions[execution_id] = {
            "project_name": project_name,
            "prompt": prompt,
            "start_time": self.execution_start_time,
            "status": "running",
            "progress": self.execution_progress.copy()
        }
        
        logger.info(f"[AutoAgent] Starting autonomous project creation for {project_name}")
        
        # Create wrapped callback for progress updates
        async def progress_callback(data):
            stage = data.get('stage')
            if stage in self.execution_progress:
                self.execution_progress[stage] = {
                    "status": data.get('status', 'pending'),
                    "message": data.get('message', ''),
                    "completed": data.get('completed', 0),
                    "total": data.get('total', 0)
                }
                
            # Update active execution record
            if execution_id in self.active_executions:
                self.active_executions[execution_id]["progress"] = self.execution_progress.copy()
            
            # Forward to user-provided callback
            if callbacks and 'progress' in callbacks:
                try:
                    callback_fn = callbacks['progress']
                    if asyncio.iscoroutinefunction(callback_fn):
                        await callback_fn(data)
                    else:
                        callback_fn(data)
                except Exception as e:
                    logger.error(f"[AutoAgent] Error in progress callback: {e}")
        
        # Create wrapped callback for streaming output
        async def stream_callback(data):
            if callbacks and 'stream' in callbacks:
                try:
                    callback_fn = callbacks['stream']
                    if asyncio.iscoroutinefunction(callback_fn):
                        await callback_fn(data)
                    else:
                        callback_fn(data)
                except Exception as e:
                    logger.error(f"[AutoAgent] Error in stream callback: {e}")
        
        # Create wrapped callback for completion
        async def completion_callback(data):
            completion_time = time.time()
            execution_time = completion_time - self.execution_start_time
            
            # Update status
            self.current_status = "completed"
            self.agent_state.set_agent_active(project_name, False)
            self.agent_state.set_agent_completed(project_name, True)
            
            # Update execution record
            if execution_id in self.active_executions:
                self.active_executions[execution_id].update({
                    "status": data.get('status', 'completed'),
                    "end_time": completion_time,
                    "execution_time": execution_time,
                    "metrics": data.get('metrics', {}),
                    "validation": data.get('validation', {})
                })
            
            # Forward to user-provided callback
            if callbacks and 'completion' in callbacks:
                try:
                    callback_fn = callbacks['completion']
                    if asyncio.iscoroutinefunction(callback_fn):
                        await callback_fn(data)
                    else:
                        callback_fn(data)
                except Exception as e:
                    logger.error(f"[AutoAgent] Error in completion callback: {e}")
        
        # Prepare all callbacks
        agent_callbacks = {
            'progress': progress_callback,
            'stream': stream_callback if callbacks and 'stream' in callbacks else None,
            'completion': completion_callback
        }
        
        try:
            # Initialize the project to ensure directory structure is set up
            await self.project_executor.initialize()
            
            # First, ask the LLM for a structured plan of all files needed
            await stream_callback("Generating project plan...\n")
            await progress_callback({
                'stage': 'planning',
                'status': 'running',
                'message': 'Generating project structure plan...'
            })
            
            # Determine project type to ensure proper setup
            project_type_prompt = f"""
            What is the most appropriate technology stack for this project?
            
            Project description: {prompt}
            
            Choose one of: angular, react, vue, node, python
            
            Respond with ONLY the technology name, nothing else.
            """
            
            # Determine project type first
            llm = LLM.create("deepseek/deepseek-coder")
            project_type_result = await llm.generate(project_type_prompt, project_name)
            
            # Extract project type from response
            project_type = project_type_result.strip().lower()
            if not project_type or len(project_type.split()) > 1:
                project_type = "angular"  # Default to Angular if response is unclear
            
            await stream_callback(f"Selected technology stack: {project_type}\n")
            
            # Create appropriate project structure for the detected type
            if project_type == "angular":
                await stream_callback("Creating Angular project structure...\n")
                result = await self.project_executor.create_angular_project({"routing": True})
                
                # Verify routing configuration
                if result.get("success", False):
                    await stream_callback("Verifying Angular routing configuration...\n")
                    await self.project_executor.verify_and_fix_angular_routing()
            
            elif project_type == "react":
                await stream_callback("Creating React project structure...\n")
                result = await self.project_executor.create_react_project({"typescript": True})
            
            elif project_type == "vue":
                await stream_callback("Creating Vue project structure...\n")
                result = await self.project_executor.create_vue_project({"typescript": True})
            
            elif project_type == "python":
                await stream_callback("Creating Python project structure...\n")
                result = await self.project_executor.create_python_project({})
            
            else:
                # Default to a simple project with just initialize
                await stream_callback(f"Creating basic {project_type} project structure...\n")
                result = await self.project_executor.initialize()
            
            # Improved component planning approach
            # Break down into smaller, targeted prompts to avoid token limit issues
            
            # Step 1: Get the list of components/services needed
            component_list_prompt = f"""
            Based on the project requirements: {prompt}
            
            Provide a JSON list of components, services, and models needed.
            Do not include implementations, just names and purposes.
            
            Format response as:
            ```json
            {{
              "components": [
                {{"name": "component-name", "purpose": "what it does"}},
              ],
              "services": [
                {{"name": "service-name", "purpose": "what it does"}},
              ],
              "models": [
                {{"name": "model-name", "fields": ["field1", "field2"]}}
              ]
            }}
            ```
            """
            
            await stream_callback("Planning components and services...\n")
            component_plan_result = await llm.generate(component_list_prompt, project_name)
            
            # Extract JSON from the response
            json_match = re.search(r'```json\s*(.*?)\s*```', component_plan_result, re.DOTALL)
            if json_match:
                component_plan_json = json_match.group(1)
            else:
                # Try to extract any JSON object
                json_match = re.search(r'\{\s*"components".*\}', component_plan_result, re.DOTALL)
                if json_match:
                    component_plan_json = json_match.group(0)
                else:
                    component_plan_json = '{}'
            
            # Parse the component plan
            try:
                component_plan = json.loads(component_plan_json)
                
                # Create files in smaller batches to avoid token limit issues
                components = component_plan.get("components", [])
                services = component_plan.get("services", [])
                models = component_plan.get("models", [])
                
                await stream_callback(f"Planning to create {len(components)} components, {len(services)} services, and {len(models)} models\n")
                
                # Update progress
                await progress_callback({
                    'stage': 'implementation',
                    'status': 'running',
                    'message': 'Creating project files...',
                    'completed': 0,
                    'total': len(components) + len(services) + len(models)
                })
                
                # Create components one by one using the project_executor
                files_created = 0
                total_files = len(components) + len(services) + len(models)
                
                # Process components first
                for component in components:
                    component_name = component.get("name", "").strip()
                    if component_name:
                        await stream_callback(f"Generating component: {component_name}...\n")
                        
                        # Determine component type based on project_type
                        component_type = f"{project_type}-component"
                        
                        # Generate the component
                        result = await self.project_executor.generate_components(
                            [{
                                "name": component_name,
                                "type": component_type,
                                "options": {}
                            }],
                            requirements=component.get("purpose", "")
                        )
                        
                        if result.get("success", False):
                            await stream_callback(f"Created component: {component_name}\n")
                            files_created += 1
                        else:
                            await stream_callback(f"Failed to create component {component_name}: {result.get('error', 'unknown error')}\n")
                        
                        # Update progress
                        await progress_callback({
                            'stage': 'implementation',
                            'status': 'running',
                            'message': f'Creating components ({files_created}/{total_files})',
                            'completed': files_created,
                            'total': total_files
                        })
                
                # Process services
                for service in services:
                    service_name = service.get("name", "").strip()
                    if service_name:
                        await stream_callback(f"Generating service: {service_name}...\n")
                        
                        # Determine service type based on project_type
                        service_type = f"{project_type}-service"
                        
                        # Generate the service
                        result = await self.project_executor.generate_components(
                            [{
                                "name": service_name,
                                "type": service_type,
                                "options": {}
                            }],
                            requirements=service.get("purpose", "")
                        )
                        
                        if result.get("success", False):
                            await stream_callback(f"Created service: {service_name}\n")
                            files_created += 1
                        else:
                            await stream_callback(f"Failed to create service {service_name}: {result.get('error', 'unknown error')}\n")
                        
                        # Update progress
                        await progress_callback({
                            'stage': 'implementation',
                            'status': 'running',
                            'message': f'Creating services ({files_created}/{total_files})',
                            'completed': files_created,
                            'total': total_files
                        })
                
                # Process models
                for model in models:
                    model_name = model.get("name", "").strip()
                    if model_name:
                        await stream_callback(f"Generating model: {model_name}...\n")
                        
                        # Get model fields
                        fields = model.get("fields", [])
                        
                        # Determine file path based on project type
                        if project_type == "angular":
                            model_path = f"src/app/models/{model_name}.model.ts"
                        elif project_type == "react" or project_type == "vue":
                            model_path = f"src/models/{model_name}.ts"
                        else:
                            model_path = f"src/models/{model_name}.js"
                        
                        # Generate model content based on fields
                        if fields:
                            if project_type in ["angular", "react", "vue"]:
                                # TypeScript model
                                model_content = f"""export interface {model_name.charAt(0).toUpperCase() + model_name.slice(1)} {{\n"""
                                for field in fields:
                                    model_content += f"  {field}: any;\n"
                                model_content += "}\n"
                            else:
                                # JavaScript model
                                model_content = f"""class {model_name.charAt(0).toUpperCase() + model_name.slice(1)} {{\n"""
                                model_content += "  constructor() {\n"
                                for field in fields:
                                    model_content += f"    this.{field} = null;\n"
                                model_content += "  }\n}\n\n"
                                model_content += f"module.exports = {model_name.charAt(0).toUpperCase() + model_name.slice(1)};\n"
                        else:
                            # Create a basic model if no fields specified
                            if project_type in ["angular", "react", "vue"]:
                                model_content = f"""export interface {model_name.charAt(0).toUpperCase() + model_name.slice(1)} {{\n  id?: number;\n  name?: string;\n}}\n"""
                            else:
                                model_content = f"""class {model_name.charAt(0).toUpperCase() + model_name.slice(1)} {{\n  constructor() {{\n    this.id = null;\n    this.name = null;\n  }}\n}}\n\nmodule.exports = {model_name.charAt(0).toUpperCase() + model_name.slice(1)};\n"""
                        
                        # Create the model file
                        result = await self.project_executor.create_file(model_path, model_content)
                        
                        if result.get("success", False):
                            await stream_callback(f"Created model: {model_name}\n")
                            files_created += 1
                        else:
                            await stream_callback(f"Failed to create model {model_name}: {result.get('error', 'unknown error')}\n")
                        
                        # Update progress
                        await progress_callback({
                            'stage': 'implementation',
                            'status': 'running',
                            'message': f'Creating models ({files_created}/{total_files})',
                            'completed': files_created,
                            'total': total_files
                        })
                
                # Update progress
                await progress_callback({
                    'stage': 'implementation',
                    'status': 'completed',
                    'message': f'Created {files_created} files',
                    'completed': total_files,
                    'total': total_files
                })
                
                # Install dependencies based on project type
                await progress_callback({
                    'stage': 'setup',
                    'status': 'running',
                    'message': 'Installing dependencies...'
                })
                
                # Default dependencies based on project type
                default_dependencies = []
                if project_type == "angular":
                    default_dependencies = ["@angular/material", "@angular/cdk"]
                elif project_type == "react":
                    default_dependencies = ["react-router-dom", "axios"]
                elif project_type == "vue":
                    default_dependencies = ["vue-router", "axios"]
                elif project_type == "node":
                    default_dependencies = ["express", "cors", "body-parser"]
                
                if default_dependencies:
                    await stream_callback(f"Installing dependencies: {', '.join(default_dependencies)}\n")
                    install_result = await self.project_executor.install_dependencies(default_dependencies)
                    
                    if install_result.get("success", False):
                        await stream_callback("Dependencies installed successfully\n")
                    else:
                        await stream_callback(f"Warning: Some dependencies failed to install: {install_result.get('error', 'unknown error')}\n")
                
                # Update progress
                await progress_callback({
                    'stage': 'setup',
                    'status': 'completed',
                    'message': 'Project setup completed'
                })
                
            except json.JSONDecodeError as e:
                logger.warning(f"[AutoAgent] Failed to parse component plan as JSON: {e}. Falling back to normal flow.")
                await stream_callback(f"Warning: Could not parse component plan. Falling back to normal execution flow.\n")
            
            # Now execute the fully automated workflow for implementation and validation
            await stream_callback("Implementing component functionality...\n")
            
            # Execute the fully automated workflow
            result = await self.agent.fully_automated_execute(
                prompt=prompt,
                project_name=project_name,
                callbacks=agent_callbacks
            )
            
            # Process outcome
            if result.get('success', False):
                logger.info(f"[AutoAgent] Successfully created project: {project_name}")
            else:
                logger.warning(f"[AutoAgent] Project creation completed with issues: {project_name}")
                
            return result
            
        except Exception as e:
            logger.error(f"[AutoAgent] Error during project creation: {str(e)}")
            
            # Update status
            self.current_status = "error"
            self.agent_state.set_agent_active(project_name, False)
            
            # Update execution record
            if execution_id in self.active_executions:
                self.active_executions[execution_id].update({
                    "status": "error",
                    "end_time": time.time(),
                    "execution_time": time.time() - self.execution_start_time,
                    "error": str(e)
                })
                
            return {
                "success": False,
                "project_name": project_name,
                "error": str(e),
                "execution_id": execution_id
            }

    def get_execution_status(self, execution_id: str = None) -> Dict[str, Any]:
        """
        Get status of a running or completed execution.
        
        Args:
            execution_id: ID of the execution to check, or None for current execution
            
        Returns:
            Dictionary with execution status and progress
        """
        if execution_id and execution_id in self.active_executions:
            return self.active_executions[execution_id]
        
        # If no execution ID specified, return current project status
        if self.current_project:
            # Find the most recent execution for the current project
            executions = [e for e in self.active_executions.values() 
                         if e.get('project_name') == self.current_project]
            if executions:
                # Sort by start time, descending
                executions.sort(key=lambda x: x.get('start_time', 0), reverse=True)
                return executions[0]
                
        # No active execution found
        return {
            "status": self.current_status,
            "project_name": self.current_project,
            "message": "No active execution found"
        }
    
    def get_project_info(self, project_name: str) -> Dict[str, Any]:
        """
        Get information about a project.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Dictionary with project information
        """
        # Get project directory
        project_dir = os.path.join(self.project_manager.projects_dir, project_name)
        
        if not os.path.exists(project_dir):
            return {
                "exists": False,
                "name": project_name,
                "message": f"Project {project_name} does not exist"
            }
        
        # Check for metadata file
        metadata_path = os.path.join(project_dir, "metadata.json")
        metadata = {}
        if os.path.exists(metadata_path):
            try:
                import json
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)
            except Exception as e:
                logger.error(f"[AutoAgent] Error reading project metadata: {e}")
        
        # Check for active executions
        executions = [e for e in self.active_executions.values() 
                     if e.get('project_name') == project_name]
        
        return {
            "exists": True,
            "name": project_name,
            "path": project_dir,
            "metadata": metadata,
            "has_active_execution": bool(executions),
            "executions": executions
        }
    
    async def create_project_structure(self, project_name: str, project_type: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create the project structure based on the project type.
        
        This method directly creates project structures using appropriate tools like Angular CLI,
        React, Vue, etc., rather than just planning them.
        
        Args:
            project_name: Name of the project
            project_type: Type of project (angular, react, vue, python, etc.)
            options: Optional configuration options for project creation
            
        Returns:
            Dictionary with creation results
        """
        if not self.project_executor:
            self.project_executor = ProjectExecutor(project_name, self.project_manager.projects_dir)
        
        # Initialize the project directory
        await self.project_executor.initialize()
        
        # Create appropriate project structure based on type
        if project_type.lower() == "angular":
            return await self.project_executor.create_angular_project(options)
        elif project_type.lower() == "react":
            return await self.project_executor.create_react_project(options)
        elif project_type.lower() == "vue":
            return await self.project_executor.create_vue_project(options)
        elif project_type.lower() in ["python", "flask", "django", "fastapi"]:
            # Set appropriate option flags based on project type
            if not options:
                options = {}
            if project_type.lower() == "flask":
                options["flask"] = True
            elif project_type.lower() == "django":
                options["django"] = True
            elif project_type.lower() == "fastapi":
                options["fastapi"] = True
            
            return await self.project_executor.create_python_project(options)
        else:
            # Default to basic structure for unknown types
            return {
                "success": True,
                "message": f"Created basic project structure for {project_type}",
                "project_dir": self.project_executor.project_dir
            }
    
    async def generate_components(self, project_name: str, component_specs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate multiple components for a project based on specifications.
        
        Args:
            project_name: Name of the project
            component_specs: List of component specifications with type, name, and options
            
        Returns:
            Dictionary with generation results
        """
        if not self.project_executor:
            self.project_executor = ProjectExecutor(project_name, self.project_manager.projects_dir)
        
        results = []
        success_count = 0
        failure_count = 0
        
        for spec in component_specs:
            component_type = spec.get("type", "").lower()
            component_name = spec.get("name", "")
            options = spec.get("options", {})
            
            if not component_name:
                results.append({
                    "success": False,
                    "message": "Missing component name",
                    "spec": spec
                })
                failure_count += 1
                continue
            
            result = None
            
            if component_type == "angular-component":
                result = await self.project_executor.generate_angular_component(component_name, options)
            elif component_type == "angular-service":
                result = await self.project_executor.generate_angular_service(component_name, options)
            else:
                result = {
                    "success": False,
                    "message": f"Unsupported component type: {component_type}",
                    "component_name": component_name
                }
            
            if result.get("success", False):
                success_count += 1
            else:
                failure_count += 1
                
            results.append(result)
        
        return {
            "success": success_count > 0,
            "message": f"Generated {success_count} components successfully, {failure_count} failures",
            "results": results
        }
    
    async def install_project_dependencies(self, project_name: str, dependencies: List[str], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Install dependencies for a project.
        
        Args:
            project_name: Name of the project
            dependencies: List of dependencies to install
            options: Additional options for installation
            
        Returns:
            Dictionary with installation results
        """
        if not self.project_executor:
            self.project_executor = ProjectExecutor(project_name, self.project_manager.projects_dir)
        
        return await self.project_executor.install_dependencies(dependencies, options)
    
    async def run_project(self, project_name: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Run the project.
        
        Args:
            project_name: Name of the project
            options: Additional options for running the project
            
        Returns:
            Dictionary with execution results
        """
        if not self.project_executor:
            self.project_executor = ProjectExecutor(project_name, self.project_manager.projects_dir)
        
        return await self.project_executor.run_project(options)
    
    async def research_topic(self, topic: str, max_results: int = 5) -> Dict[str, Any]:
        """
        Perform research on a specific topic using SearxNG.
        
        Args:
            topic: The topic to research
            max_results: Maximum number of results to return
            
        Returns:
            Dictionary with research results
        """
        try:
            results = await searxng_search_structured(topic, max_results=max_results)
            return {
                "success": True,
                "topic": topic,
                "results": results
            }
        except Exception as e:
            logger.error(f"[AutoAgent] Error researching topic '{topic}': {e}")
            return {
                "success": False,
                "topic": topic,
                "error": str(e)
            }

    async def execute(self, 
                   prompt: str, 
                   project_name: Optional[str] = None,
                   callbacks: Dict[str, Callable] = None) -> Dict[str, Any]:
        """
        Execute the autonomous agent with the given prompt.
        
        Args:
            prompt: The instruction or task for the agent
            project_name: Optional project name (if None, will be extracted from prompt)
            callbacks: Optional callbacks to receive updates
            
        Returns:
            Dictionary with execution results
        """
        self.execution_start_time = time.time()
        
        # Extract project name from prompt if not provided
        if not project_name:
            # Simple heuristic to extract project name
            project_name_match = re.search(r'project[:\s]+([a-zA-Z0-9_-]+)', prompt, re.IGNORECASE)
            if project_name_match:
                project_name = project_name_match.group(1)
                logger.info(f"Extracted project name from prompt: {project_name}")
            else:
                # Generate a default project name based on prompt
                words = re.findall(r'\b[a-zA-Z][a-zA-Z0-9]*\b', prompt.lower())
                if len(words) >= 2:
                    project_name = f"{words[0]}-{words[1]}"
                else:
                    project_name = f"project-{int(time.time())}"
                logger.info(f"Generated default project name: {project_name}")
        
        # Clean up project name - ensure it's valid
        project_name = re.sub(r'[^a-zA-Z0-9_-]', '-', project_name).lower()
        logger.info(f"Using project name: {project_name}")
        
        self.current_project = project_name
        self.current_status = "running"
        
        # Tell the agent state that we're active
        self.agent_state.set_agent_active(project_name, True)
        
        # Check if the project already exists
        existing_project = None
        if self.project_manager:
            existing_project = self.project_manager.get_project(project_name)
        
        # Initialize the project executor
        self.project_executor = ProjectExecutor(project_name, self.project_manager.projects_dir)
        
        # Create wrapped callback for streaming output
        async def stream_callback(data):
            if callbacks and 'stream' in callbacks:
                try:
                    callback_fn = callbacks['stream']
                    if asyncio.iscoroutinefunction(callback_fn):
                        await callback_fn(data)
                    else:
                        callback_fn(data)
                except Exception as e:
                    logger.error(f"[AutoAgent] Error in stream callback: {e}")
        
        try:
            # First check if this is an existing project
            if existing_project:
                # Load project context and check what we've already done
                await stream_callback(f"Loading existing project: {project_name}\n")
                
                # Let's see if we have an existing context
                context = await self.agent._load_project_context(project_name)
                
                if context:
                    # Get some important info from context
                    framework = context.get("framework", "unknown")
                    existing_components = context.get("components", [])
                    
                    if existing_components:
                        component_names = [c.get("name") for c in existing_components if "name" in c]
                        await stream_callback(f"Found existing project with {len(existing_components)} component(s): {', '.join(component_names[:5])}{' and more' if len(component_names) > 5 else ''}\n")
                    
                    if framework != "unknown":
                        await stream_callback(f"Project uses {framework} framework\n")
                    
                    # Check for memory of previous task
                    previous_tasks = context.get("tasks", [])
                    if previous_tasks:
                        latest_task = previous_tasks[-1]
                        await stream_callback(f"Most recent task: {latest_task.get('prompt', 'Unknown')}\n")
                    
                    # Add the current task to the context
                    if "tasks" not in context:
                        context["tasks"] = []
                    
                    context["tasks"].append({
                        "prompt": prompt,
                        "timestamp": datetime.now().isoformat(),
                        "continued_from_previous": True
                    })
                    
                    # Save updated context
                    await self.agent._save_project_context(project_name, context)
                else:
                    # No existing context, but project exists - create new context
                    await stream_callback(f"Project exists but no context found. Building new context.\n")
                
                # Initialize the project (will detect project framework)
                await self.project_executor.initialize()
            else:
                # This is a new project
                await stream_callback(f"Creating new project: {project_name}\n")
                
                # Initialize the project
                await self.project_executor.initialize()
        
            # Execute the agent's processing pipeline on the input
            result = await self.agent.execute(
                prompt=prompt,
                project_name=project_name,
                callbacks=callbacks
            )
            
            return result
        
        except Exception as e:
            logger.error(f"[AutoAgent] Error in execute: {str(e)}")
            
            # Set status to error
            self.current_status = "error"
            self.agent_state.set_agent_active(project_name, False)
            
            # Return error information
            return {
                "success": False,
                "error": str(e),
                "project_name": project_name
            }

    async def execute_test_project(self, project_name: str, stream_callback=None) -> Dict[str, Any]:
        """
        Run comprehensive tests on a project, including UI tests with Playwright.
        
        Args:
            project_name: The name of the project to test
            stream_callback: Callback for streaming output
            
        Returns:
            Testing results
        """
        # Initialize the project executor
        project_executor = ProjectExecutor(project_name, os.path.join(os.getcwd(), "projects"))
        await project_executor.initialize()
        
        # Stream initial message to the user
        if stream_callback:
            await stream_callback(f"Starting comprehensive testing for project: {project_name}...")
        
        # First, run unit tests
        if stream_callback:
            await stream_callback("Running unit tests...")
        
        unit_test_results = await project_executor.test_project()
        
        # Start the project server
        if stream_callback:
            await stream_callback("Starting project server for UI testing...")
        
        run_options = {"port": 4201}  # Use a different port to avoid conflict with the AutonomousAI itself
        run_result = await project_executor.run_project(run_options)
        
        test_results = {
            "project_name": project_name,
            "unit_tests": unit_test_results,
            "ui_tests": None,
            "accessibility_tests": None,
            "screenshots": None,
            "success": unit_test_results.get("success", False)
        }
        
        if run_result.get("success", False):
            # The project is running, run UI tests with Playwright
            if stream_callback:
                await stream_callback("Running UI tests with Playwright...")
            
            ui_test_options = {
                "port": run_result.get("port", 4201),
                "test_url": run_result.get("url", f"http://localhost:{run_result.get('port', 4201)}")
            }
            
            # Run UI tests
            ui_test_results = await project_executor.run_ui_tests_with_playwright(ui_test_options)
            test_results["ui_tests"] = ui_test_results
            
            # Capture screenshots
            if stream_callback:
                await stream_callback("Capturing screenshots of the application...")
            
            screenshot_results = await project_executor.capture_screenshots(ui_test_options.get("test_url"))
            test_results["screenshots"] = screenshot_results
            
            # Run accessibility tests
            if stream_callback:
                await stream_callback("Running accessibility tests...")
            
            accessibility_results = await project_executor.run_accessibility_tests(ui_test_options.get("test_url"))
            test_results["accessibility_tests"] = accessibility_results
            
            # Generate comprehensive test report
            if stream_callback:
                await stream_callback("Generating comprehensive test report...")
            
            test_summary = "Test Results Summary:\n\n"
            
            # Unit tests summary
            if unit_test_results.get("success", False):
                test_summary += "✅ Unit tests: PASSED\n"
            else:
                test_summary += "❌ Unit tests: FAILED\n"
            
            # UI tests summary
            if ui_test_results.get("success", False):
                test_summary += "✅ UI tests: PASSED\n"
            else:
                test_summary += "❌ UI tests: FAILED\n"
            
            # Accessibility tests summary
            if accessibility_results:
                violation_count = len(accessibility_results.get("violations", []))
                if violation_count == 0:
                    test_summary += "✅ Accessibility: No issues found\n"
                else:
                    test_summary += f"⚠️ Accessibility: {violation_count} issues found\n"
            
            # Screenshots summary
            if screenshot_results and screenshot_results.get("success", False):
                screenshot_count = len(screenshot_results.get("screenshots", []))
                test_summary += f"📸 Captured {screenshot_count} screenshots\n"
            
            if stream_callback:
                await stream_callback(test_summary)
        
        else:
            if stream_callback:
                await stream_callback("❌ Failed to start the project server for UI testing")
        
        return test_results

    async def analyze_code_quality(self, project_name: str, stream_callback=None) -> Dict[str, Any]:
        """
        Perform comprehensive code quality analysis with visualizations using Playwright.
        
        Args:
            project_name: The name of the project to analyze
            stream_callback: Callback for streaming output
            
        Returns:
            Analysis results
        """
        # Initialize the coder
        coder = Coder("openai/gpt-4o")
        
        # Stream initial message to the user
        if stream_callback:
            await stream_callback(f"Starting comprehensive code analysis for project: {project_name}...")
        
        # Perform code analysis
        analysis_results = await coder.generate_code_analysis_report(project_name)
        
        # Generate a user-friendly summary
        if stream_callback:
            summary = "Code Analysis Summary:\n\n"
            
            # Project type
            summary += f"Project Type: {analysis_results.get('project_type', 'Unknown').capitalize()}\n\n"
            
            # Metrics
            metrics = analysis_results.get("metrics", {})
            if metrics:
                summary += "Metrics:\n"
                if "totalFiles" in metrics:
                    summary += f"- Total Files: {metrics['totalFiles']}\n"
                if "totalSize" in metrics:
                    size_kb = metrics['totalSize'] / 1024 if isinstance(metrics['totalSize'], (int, float)) else 0
                    summary += f"- Total Size: {size_kb:.2f} KB\n"
                
                # Code issues
                if "eslint_issues" in metrics:
                    summary += f"- ESLint Issues: {metrics['eslint_issues']}\n"
                if "pylint_issues" in metrics:
                    summary += f"- Pylint Issues: {metrics['pylint_issues']}\n"
                summary += "\n"
            
            # Top issues
            issues = analysis_results.get("issues", [])
            if issues:
                summary += "Top Issues:\n"
                for issue in issues[:5]:  # Show top 5 issues
                    summary += f"- {issue.get('rule', 'Unknown')}: {issue.get('count', 0)} occurrences\n"
                summary += "\n"
            
            # Recommendations
            recommendations = analysis_results.get("recommendations", [])
            if recommendations:
                summary += "Recommendations:\n"
                for rec in recommendations[:5]:  # Show top 5 recommendations
                    summary += f"- {rec.get('title', 'Unknown')}\n"
                summary += "\n"
            
            # Visualizations
            visualizations = analysis_results.get("visualizations", [])
            if visualizations:
                summary += "Generated Visualizations:\n"
                for viz in visualizations:
                    summary += f"- {viz.get('name', 'Unknown')}: {viz.get('html_path', '')}\n"
                summary += "\n"
            
            # Reports
            reports = analysis_results.get("reports", [])
            if reports:
                summary += "Generated Reports:\n"
                for report in reports:
                    summary += f"- {report.get('name', 'Unknown')}: {report.get('path', '')}\n"
                summary += "\n"
            
            await stream_callback(summary)
        
        return analysis_results 