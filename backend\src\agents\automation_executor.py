# backend/src/agents/automation_executor.py
import os
import logging
import asyncio # Needed for running async progress callback
import platform # For potential platform-specific commands
from typing import List, Dict, Any, Optional, Callable, Awaitable
import re
import json
import time
from datetime import datetime

# Assuming ShellExecutor is in the same directory or correctly pathed
from backend.src.agents.shell_executor import ShellExecutor
# Assuming ProjectManager is correctly importable
from backend.src.project import ProjectManager
from backend.src.socket_instance import emit_terminal_command, emit_code_generation_message, emit_cursor_message

logger = logging.getLogger(__name__)

# Define type hint for the async progress callback
ProgressCallback = Callable[[int, int, Dict[str, Any], Dict[str, Any]], Awaitable[None]]

class AutomationExecutor:
    """
    Executes a structured list of automation steps, handling commands,
    file operations, folder creation, and IDE/terminal opening.
    Reports progress via an asynchronous callback.
    """
    def __init__(self, project_name: str, projects_base_dir: str, output_callback: Optional[Callable] = None):
        """
        Initialize the AutomationExecutor.

        Args:
            project_name: The name of the specific project this instance will work on.
            projects_base_dir: The root directory where all projects are stored.
            output_callback: Callback function for output
        """
        if not os.path.isdir(projects_base_dir):
             logger.warning(f"[AutomationExecutor] Base projects directory does not exist: {projects_base_dir}. It may be created later.")
             # Consider creating it here if appropriate: os.makedirs(projects_base_dir, exist_ok=True)

        self.project_name = project_name
        self.projects_base_dir = projects_base_dir
        self.project_dir = os.path.join(projects_base_dir, project_name)
        self.project_manager = ProjectManager() # Assumes ProjectManager knows the base dir or gets it implicitly
        self.shell_executor = ShellExecutor()
        self.output_callback = output_callback
        
        # Initialize execution statistics
        self.execution_stats = {
            "started_at": None,
            "completed_at": None,
            "total_duration": 0,
            "steps_executed": 0,
            "steps_succeeded": 0,
            "steps_failed": 0,
            "step_durations": {}
        }
        
        # Maximum number of concurrent steps (for parallel execution)
        self.max_concurrent_steps = 3
        
        logger.info(f"[AutomationExecutor] Initialized for project '{project_name}'. Base: '{projects_base_dir}', Project: '{self.project_dir}'")

    async def _run_progress_callback(self, callback: Optional[ProgressCallback], *args):
        """Helper to safely run the async progress callback."""
        if callback:
            try:
                await callback(*args)
            except Exception as cb_err:
                logger.error(f"[AutomationExecutor] Error running progress callback: {cb_err}", exc_info=True)

    async def _emit_progress(self, step: Dict[str, Any], status: str, result: Dict[str, Any] = None) -> None:
        """
        Emit progress via socket if available.
        """
        try:
            step_type = step.get('type', 'unknown')
            desc = step.get('description', f"{step_type} step")
            path = step.get('file_path', step.get('path', ''))
            
            if step_type == 'command':
                await emit_terminal_command(self.project_name, f"[{status}] Running command: {step.get('command', 'unknown')}")
            elif step_type == 'file':
                action = "Created" if status == "complete" else "Creating"
                await emit_code_generation_message(self.project_name, path, action.lower())
            elif step_type == 'folder':
                await emit_cursor_message(self.project_name, f"[{status}] {desc}")
            else:
                await emit_cursor_message(self.project_name, f"[{status}] {desc}")
                
        except Exception as e:
            logger.error(f"Error emitting progress: {e}")

    async def run_plan_async(self, plan: List[Dict[str, Any]], progress_callback: Optional[ProgressCallback] = None) -> Dict[str, Any]:
        """
        Execute a structured automation plan asynchronously with parallel execution capability.

        Args:
            plan: List of structured steps
            progress_callback: Async function to report progress after each step

        Returns:
            Dictionary with execution results
        """
        errors = []
        steps_executed = []
        total_steps = len(plan)
        
        # Initialize execution statistics
        self.execution_stats["started_at"] = datetime.now().isoformat()
        
        logger.info(f"[AutomationExecutor] === Starting async automation plan execution for '{self.project_name}' with {total_steps} steps ===")
        
        # Ensure base directories exist
        if not await self._ensure_directories():
            return {
                'success': False, 
                'errors': [{'step': 'Initialization', 'error': 'Failed to create required directories'}],
                'steps_executed': []
            }
        
        # Build dependency graph for the steps
        step_graph = self._build_dependency_graph(plan)
        
        # Track completed steps and their results
        completed_steps = {}
        failed_steps = {}
        
        # Queue of ready steps to execute (those with no dependencies or whose dependencies are met)
        ready_queue = asyncio.Queue()
        
        # Initialize the queue with steps that have no dependencies
        for idx, step in enumerate(plan):
            if not step_graph.get(idx, []):  # No dependencies
                await ready_queue.put(idx)
        
        # Process steps until all are done
        async def process_step():
            while not ready_queue.empty() or len(completed_steps) + len(failed_steps) < total_steps:
                try:
                    # Wait for a step to be ready
                    step_idx = await asyncio.wait_for(ready_queue.get(), timeout=1.0)
                    step = plan[step_idx]
                    
                    # Execute the step
                    start_time = time.time()
                    result = await self._execute_step(step, step_idx, total_steps, progress_callback)
                    duration = time.time() - start_time
                    
                    # Store step duration
                    self.execution_stats["step_durations"][step_idx] = duration
                    
                    # Record the result
                    if result.get('success', False):
                        completed_steps[step_idx] = result
                        self.execution_stats["steps_succeeded"] += 1
                    else:
                        failed_steps[step_idx] = result
                        self.execution_stats["steps_failed"] += 1
                        errors.append({'step': step.get('description', f'Step {step_idx}'), 'error': result.get('stderr', 'Unknown error')})
                    
                    # Update execution stats
                    self.execution_stats["steps_executed"] += 1
                    
                    # Check if this step's completion enables other steps
                    for dependent_idx, dependencies in step_graph.items():
                        if step_idx in dependencies and dependent_idx not in completed_steps and dependent_idx not in failed_steps:
                            # Check if all dependencies for this step are now satisfied
                            all_deps_met = True
                            for dep_idx in dependencies:
                                if dep_idx not in completed_steps:
                                    all_deps_met = False
                                    break
                                    
                            if all_deps_met:
                                await ready_queue.put(dependent_idx)
                    
                    # Emit combined progress
                    total_done = len(completed_steps) + len(failed_steps)
                    if progress_callback:
                        await self._run_progress_callback(
                            progress_callback, 
                            total_done, 
                            total_steps,
                            {'type': 'progress_update'},
                            {'completed': len(completed_steps), 'failed': len(failed_steps)}
                        )
                        
                except asyncio.TimeoutError:
                    # No steps ready yet, just continue the loop
                    pass
                except Exception as e:
                    logger.error(f"[AutomationExecutor] Error processing step: {e}")
                    
                # Break if all steps are accounted for
                if len(completed_steps) + len(failed_steps) >= total_steps:
                    break
        
        # Create workers for parallel execution
        workers = [process_step() for _ in range(min(self.max_concurrent_steps, total_steps))]
        
        # Wait for all workers to complete
        await asyncio.gather(*workers)
        
        # Update final execution statistics
        self.execution_stats["completed_at"] = datetime.now().isoformat()
        start_time = datetime.fromisoformat(self.execution_stats["started_at"])
        end_time = datetime.fromisoformat(self.execution_stats["completed_at"])
        self.execution_stats["total_duration"] = (end_time - start_time).total_seconds()
        
        # Determine overall success
        success = len(completed_steps) == total_steps
        
        logger.info(f"[AutomationExecutor] === Completed async execution with {len(completed_steps)}/{total_steps} steps successful ===")
        
        return {
            'success': success,
            'errors': errors,
            'steps_executed': sorted(list(completed_steps.keys())),
            'steps_failed': sorted(list(failed_steps.keys())),
            'execution_stats': self.execution_stats
        }
    
    def _build_dependency_graph(self, plan: List[Dict[str, Any]]) -> Dict[int, List[int]]:
        """
        Build a dependency graph for steps based on their requirements.
        
        Args:
            plan: List of structured steps
            
        Returns:
            Dictionary mapping step indices to lists of dependency indices
        """
        dependencies = {}
        
        # Initialize with empty dependencies
        for i in range(len(plan)):
            dependencies[i] = []
        
        # Simple rule: Command steps may depend on file/folder creation steps
        # This is a naive approach; better would be to analyze file paths and commands
        for i, step in enumerate(plan):
            if step.get('type') == 'command':
                command = step.get('command', '').lower()
                
                # For each previous step, check if it's a file/folder that this command might need
                for j in range(i):
                    prev_step = plan[j]
                    
                    # If previous step creates a file
                    if prev_step.get('type') == 'file':
                        file_path = prev_step.get('file_path', prev_step.get('path', ''))
                        if file_path and file_path.lower() in command:
                            dependencies[i].append(j)
                    
                    # If previous step creates a folder
                    elif prev_step.get('type') == 'folder':
                        folder_path = prev_step.get('path', '')
                        if folder_path and folder_path.lower() in command:
                            dependencies[i].append(j)
                    
                    # If previous step installs dependencies
                    elif prev_step.get('type') == 'command':
                        prev_command = prev_step.get('command', '').lower()
                        if ('npm install' in prev_command or 'yarn' in prev_command or 'pip install' in prev_command) and ('build' in command or 'run' in command or 'start' in command):
                            dependencies[i].append(j)
        
        return dependencies
    
    async def _ensure_directories(self) -> bool:
        """
        Ensure that the base and project directories exist.
        
        Returns:
            True if directories exist or were created successfully, False otherwise
        """
        try:
            # Create base projects directory if needed
            if not os.path.isdir(self.projects_base_dir):
                logger.info(f"[AutomationExecutor] Creating base projects directory: {self.projects_base_dir}")
                os.makedirs(self.projects_base_dir, exist_ok=True)
            
            # Create project directory if needed
            if not os.path.isdir(self.project_dir):
                logger.info(f"[AutomationExecutor] Creating project directory: {self.project_dir}")
                os.makedirs(self.project_dir, exist_ok=True)
                
            return True
        except Exception as e:
            logger.error(f"[AutomationExecutor] Error creating directories: {e}")
            return False
    
    async def _execute_step(self, step: Dict[str, Any], step_idx: int, total_steps: int, progress_callback: Optional[ProgressCallback] = None) -> Dict[str, Any]:
        """
        Execute a single automation step with progress reporting.
        
        Args:
            step: The step to execute
            step_idx: Index of the step in the plan
            total_steps: Total number of steps in the plan
            progress_callback: Callback for progress reporting
            
        Returns:
            Dictionary with execution results
        """
        step_type = step.get('type')
        step_num = step_idx + 1
        logger.info(f"[AutomationExecutor] --- Step {step_num}/{total_steps}: Type '{step_type}' ---")
        
        # Initialize result
        result = {
            'success': False,
            'step_type': step_type,
            'step_idx': step_idx
        }
        
        # Report step started
        if progress_callback:
            await self._run_progress_callback(
                progress_callback,
                step_num,
                total_steps,
                step,
                {'status': 'started'}
            )
        
        # Emit step start
        await self._emit_progress(step, "starting")
        
        try:
            # Determine working directory
            effective_cwd = self._determine_effective_cwd(step)
            
            # Execute step based on type
            if step_type == 'command':
                result = await self._execute_command_step(step)
            elif step_type == 'folder':
                result = await self._execute_folder_step(step)
            elif step_type == 'file':
                result = await self._execute_file_step(step)
            elif step_type == 'open_vscode':
                result = await self._execute_vscode_step(effective_cwd)
            elif step_type == 'open_terminal':
                result = await self._execute_terminal_step(effective_cwd)
            else:
                logger.warning(f"[AutomationExecutor] Unknown step type: {step_type}")
                result = {
                    'success': False,
                    'stderr': f"Unknown step type: {step_type}"
                }
            
            # Add step information to result
            result['step_type'] = step_type
            result['step_idx'] = step_idx
            
            # Report step completion
            if progress_callback:
                await self._run_progress_callback(
                    progress_callback,
                    step_num,
                    total_steps,
                    step,
                    result
                )
            
            # Emit step completion
            status = "complete" if result.get('success', False) else "failed"
            await self._emit_progress(step, status, result)
            
            return result
            
        except Exception as e:
            logger.error(f"[AutomationExecutor] Error executing step {step_num}: {e}")
            error_result = {
                'success': False,
                'stderr': f"Exception: {str(e)}",
                'step_type': step_type,
                'step_idx': step_idx
            }
            
            # Report error
            if progress_callback:
                await self._run_progress_callback(
                    progress_callback,
                    step_num,
                    total_steps,
                    step,
                    error_result
                )
            
            # Emit error
            await self._emit_progress(step, "failed", error_result)
            
            return error_result
    
    def _determine_effective_cwd(self, step: Dict[str, Any]) -> str:
        """
        Determine the effective working directory for a step.
        
        Args:
            step: The step to determine the CWD for
            
        Returns:
            The effective working directory
        """
        # Default to project directory
        effective_cwd = self.project_dir
        
        # Check for specified CWD
        step_cwd_input = step.get('cwd')
        if step_cwd_input:
            if step_cwd_input == '{project_dir}':
                effective_cwd = self.project_dir
            elif step_cwd_input == '{base_dir}':
                effective_cwd = self.projects_base_dir
            elif os.path.isabs(step_cwd_input):
                # Absolute path specified
                effective_cwd = step_cwd_input
            else:
                # Relative path specified - interpret as relative to project dir
                effective_cwd = os.path.join(self.project_dir, step_cwd_input)
        
        # Ensure the directory exists
        if not os.path.isdir(effective_cwd):
            logger.warning(f"[AutomationExecutor] Effective CWD '{effective_cwd}' does not exist. Falling back to project dir.")
            effective_cwd = self.project_dir
            
            # If project dir doesn't exist either, use base dir
            if not os.path.isdir(effective_cwd):
                logger.warning(f"[AutomationExecutor] Project dir doesn't exist. Falling back to base dir.")
                effective_cwd = self.projects_base_dir
        
        return effective_cwd
    
    async def _execute_command_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a command step.
        
        Args:
            step: The command step to execute
            
        Returns:
            Execution result
        """
        command = step.get('command', '')
        
        if not command:
            return {'success': False, 'stderr': "Command step is missing 'command' field"}
        
        try:
            logger.info(f"[AutomationExecutor] Executing command: {command}")
            
            # Check if we're trying to run ng new inside an Angular workspace
            is_angular_workspace = False
            
            # If we're running an Angular CLI command, check if we're in a workspace
            if command.startswith('ng new'):
                try:
                    # Check for angular.json which indicates an Angular workspace
                    angular_json_path = os.path.join(self.project_manager.get_project_dir(self.project_name), 'angular.json')
                    is_angular_workspace = os.path.exists(angular_json_path)
                    
                    if is_angular_workspace:
                        logger.warning(f"[AutomationExecutor] Attempting to run 'ng new' in an existing Angular workspace. Skipping.")
                        return {
                            'success': False, 
                            'stderr': "Error: This command is not available when running the Angular CLI inside a workspace.",
                            'message': "Cannot run 'ng new' in an existing Angular workspace. The project is already initialized."
                        }
                except Exception as e:
                    logger.error(f"[AutomationExecutor] Error checking for Angular workspace: {e}")
            
            result = await self.shell_executor.run_command(
                command,
                cwd=self.project_manager.get_project_dir(self.project_name),
                timeout=600  # 10 minutes timeout for long-running commands
            )
            
            # Process the result
            if result.get('success', False):
                logger.info(f"[AutomationExecutor] Command executed successfully: {command}")
                return {
                    'success': True, 
                    'message': f"Command '{command}' executed successfully",
                    'stdout': result.get('stdout', '')
                }
            else:
                stderr = result.get('stderr', 'Unknown error')
                logger.error(f"[AutomationExecutor] Command execution failed: {command}, Error: {stderr}")
                
                # Special handling for Angular workspace errors
                if "This command is not available when running the Angular CLI inside a workspace" in stderr:
                    return {
                        'success': False,
                        'stderr': stderr,
                        'message': "Cannot run this Angular CLI command in an existing workspace. The project is already initialized."
                    }
                
                return {'success': False, 'stderr': stderr}
                
        except Exception as e:
            logger.error(f"[AutomationExecutor] Error executing command {command}: {e}")
            return {'success': False, 'stderr': f"Error executing command: {str(e)}"}
    
    async def _execute_folder_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a folder creation step.
        
        Args:
            step: The folder step to execute
            
        Returns:
            Execution result
        """
        path = step.get('path')
        if not path:
            return {'success': False, 'stderr': "Folder step is missing 'path' field"}
        
        # Ensure path is treated as relative to the project directory
        folder_path = os.path.join(self.project_dir, path.strip('/\\'))
        
        try:
            logger.info(f"[AutomationExecutor] Creating folder: {folder_path}")
            os.makedirs(folder_path, exist_ok=True)
            return {'success': True, 'message': f"Folder '{path}' created/ensured"}
        except Exception as e:
            logger.error(f"[AutomationExecutor] Error creating folder {folder_path}: {e}")
            return {'success': False, 'stderr': f"Error creating folder: {str(e)}"}
    
    async def _execute_file_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a file creation step.
        
        Args:
            step: The file step to execute
            
        Returns:
            Execution result
        """
        path = step.get('file_path', step.get('path'))
        content = step.get('content', '')
        
        if not path:
            return {'success': False, 'stderr': "File step is missing 'path' or 'file_path' field"}
        
        try:
            logger.info(f"[AutomationExecutor] Creating/updating file: {path}")
            result = self.project_manager.add_file_to_project(
                self.project_name,
                path.strip('/\\'),
                content
            )
            
            if isinstance(result, dict) and not result.get('error'):
                return {'success': True, 'message': f"File '{path}' saved"}
            else:
                error_msg = result.get('error', 'Unknown error') if isinstance(result, dict) else str(result)
                return {'success': False, 'stderr': f"Error saving file: {error_msg}"}
        except Exception as e:
            logger.error(f"[AutomationExecutor] Error saving file {path}: {e}")
            return {'success': False, 'stderr': f"Error saving file: {str(e)}"}
    
    async def _execute_vscode_step(self, cwd: str) -> Dict[str, Any]:
        """
        Execute a VS Code opening step.
        
        Args:
            cwd: Working directory to open VS Code in
            
        Returns:
            Execution result
        """
        try:
            logger.info(f"[AutomationExecutor] Opening VS Code in: {cwd}")
            result = ShellExecutor.run_command('code .', cwd=cwd)
            
            if result.get('success', False):
                return {'success': True, 'message': f"VS Code open command issued for '{cwd}'"}
            else:
                return {
                    'success': False, 
                    'stderr': result.get('stderr', "Is 'code' command in system PATH?"),
                    'message': f"Failed to issue VS Code open command in '{cwd}'"
                }
        except Exception as e:
            logger.error(f"[AutomationExecutor] Error opening VS Code: {e}")
            return {'success': False, 'stderr': f"Error opening VS Code: {str(e)}"}
    
    async def _execute_terminal_step(self, cwd: str) -> Dict[str, Any]:
        """
        Execute a terminal opening step.
        
        Args:
            cwd: Working directory to open the terminal in
            
        Returns:
            Execution result
        """
        try:
            logger.info(f"[AutomationExecutor] Opening terminal in: {cwd}")
            
            system = platform.system()
            if system == "Windows":
                cmd_to_run = 'start cmd'
            elif system == "Darwin":  # macOS
                cmd_to_run = f'open -a Terminal "{cwd}"'
            else:  # Linux
                cmd_to_run = f'gnome-terminal --working-directory="{cwd}"'
            
            result = ShellExecutor.run_command(cmd_to_run, cwd=self.projects_base_dir)
            
            if result.get('success', False):
                return {'success': True, 'message': f"Terminal open command issued for '{cwd}' (Platform: {system})"}
            else:
                return {
                    'success': False,
                    'stderr': result.get('stderr', 'Failed to execute terminal command'),
                    'message': f"Failed to issue terminal open command for '{cwd}'"
                }
        except Exception as e:
            logger.error(f"[AutomationExecutor] Error opening terminal: {e}")
            return {'success': False, 'stderr': f"Error opening terminal: {str(e)}"}

    def run_plan(self, plan: List[Dict[str, Any]], progress_callback: Optional[ProgressCallback] = None) -> Dict[str, Any]:
        """
        Synchronous wrapper for run_plan_async. Executes a structured automation plan.
        This is kept for backward compatibility. New code should use run_plan_async.

        Args:
            plan: List of structured steps
            progress_callback: Async function to report progress after each step

        Returns:
            Dictionary with execution results
        """
        # Setup event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Run the async plan
            result = loop.run_until_complete(self.run_plan_async(plan, progress_callback))
            return result
        finally:
            # Clean up
            loop.close()

    async def setup_project_environment(self, progress_callback: Optional[ProgressCallback] = None) -> Dict[str, Any]:
        """
        Set up a new project environment for Angular development.
        
        This includes:
        1. Creating the project directory if it doesn't exist
        2. Initializing the Git repository
        3. Installing package.json
        4. Adding common Angular dependencies
        
        Args:
            progress_callback: Optional callback for progress updates
        
        Returns:
            Dictionary with setup results
        """
        try:
            # --- Ensure Project Directory Exists ---
            if not os.path.isdir(self.project_dir):
                logger.info(f"[AutomationExecutor] Creating project directory: {self.project_dir}")
                os.makedirs(self.project_dir, exist_ok=True)
                
            # Set up structured plan for the environment
            steps = [
                {
                    "type": "command",
                    "description": "Initialize Git repository",
                    "command": "git init",
                    "cwd": self.project_dir
                },
                {
                    "type": "command",
                    "description": "Initialize package.json",
                    "command": "npm init -y",
                    "cwd": self.project_dir
                }
            ]
            
            # Execute the plan
            return await self.run_plan_async(steps, progress_callback)
            
        except Exception as e:
            logger.error(f"[AutomationExecutor] Error setting up project environment: {e}")
            return {
                "success": False,
                "error": str(e)
            }