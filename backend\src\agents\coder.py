"""
Code generation agent that creates source code based on plans and research.
"""
import os
import json
import logging
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from jinja2 import Environment, FileSystemLoader
from backend.src.llm.llm import LLM
from backend.src.project import ProjectManager
from backend.src.socket_instance import emit_message, emit_agent_message, emit_agent_file_update, emit_code_generation_message

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Coder:
    """
    Code generation agent that creates source code based on plans and research.
    """
    def __init__(self, model_id: str):
        """
        Initialize the Coder with a model ID.
        
        Args:
            model_id: The ID of the LLM model to use
        """
        self.model_id = model_id
        self.llm = LLM.create(model_id)
        self.env = Environment(loader=FileSystemLoader("templates"))
        self.project_manager = ProjectManager()
    
    async def execute(self, plan: Dict[str, Any], research_results: Dict[str, Any], project_name: str) -> List[Dict[str, Any]]:
        """
        Execute the code generation process based on a plan and research results.
        
        Args:
            plan: The plan from the planner
            research_results: Results from the researcher
            project_name: The name of the project
            
        Returns:
            List of generated code files
        """
        template = self.env.get_template("coder_prompt.jinja2")
        formatted_prompt = template.render(
            plan=json.dumps(plan, indent=2),
            research_results=json.dumps(research_results, indent=2),
            project_name=project_name
        )
        
        await emit_message("coding_started", {"project_name": project_name})
        await emit_agent_message(project_name, "I'm generating code based on the plan...")
        
        response = await self.llm.generate(formatted_prompt, project_name)
        
        code_files = self.parse_response(response)
        
        await emit_message("coding_completed", {"project_name": project_name})
        
        if code_files:
            file_list = "\n".join([f"- {file['path']}" for file in code_files])
            await emit_agent_message(
                project_name,
                f"I've generated the following code files:\n\n{file_list}"
            )
        
        return code_files
    
    def parse_response(self, response: str) -> List[Dict[str, Any]]:
        """
        Parse the LLM response into a list of code files.
        
        Args:
            response: The raw LLM response
            
        Returns:
            A list of dictionaries containing file paths and content
        """
        files = []
        current_file = None
        current_content = []
        
        lines = response.split("\n")
        for i, line in enumerate(lines):
            if line.startswith("```") and i + 1 < len(lines) and not lines[i + 1].startswith("```"):
                file_path = line.replace("```", "").strip()
                
                if current_file is not None and current_content:
                    files.append({
                        "path": current_file,
                        "content": "\n".join(current_content)
                    })
                
                current_file = file_path
                current_content = []
            elif line.startswith("```") and current_file is not None:
                files.append({
                    "path": current_file,
                    "content": "\n".join(current_content)
                })
                current_file = None
                current_content = []
            elif current_file is not None:
                current_content.append(line)
        
        if current_file is not None and current_content:
            files.append({
                "path": current_file,
                "content": "\n".join(current_content)
            })
        
        return files
    
    def save_code_to_project(self, code_files: List[Dict[str, Any]], project_name: str) -> None:
        logger.info(f"[Coder] Saving code files to project: {project_name}")
        
        # Get the project directory
        project_dir = os.path.join(self.project_manager.projects_dir, project_name)
        
        for file in code_files:
            # Sanitize and normalize the path
            safe_path = self._sanitize_path(file['path'])
            
            # Verify that the path is inside the project directory
            file_full_path = os.path.join(project_dir, safe_path)
            file_full_path_norm = os.path.normpath(file_full_path)
            project_dir_norm = os.path.normpath(project_dir)
            
            # Security check to ensure the file is within the project directory
            if not file_full_path_norm.startswith(project_dir_norm):
                logger.warning(f"[Coder] Path '{safe_path}' attempts to write outside project directory. Restricting.")
                # Fall back to just the basename
                safe_path = os.path.basename(safe_path)
            
            logger.debug(f"[Coder] Saving file: {file['path']} (sanitized: {safe_path})")
            result = self.project_manager.add_file_to_project(
                project_name,
                safe_path,
                file["content"]
            )
            if not isinstance(result, dict) or result.get("error"):
                logger.error(f"[Coder] Failed to add file {safe_path} to project {project_name}: {result.get('error') if isinstance(result, dict) else result}")
                continue
            emit_agent_file_update(
                project_name,
                safe_path,
                file["content"]
            )
            logger.info(f"[Coder] Added file {safe_path} to project {project_name}")

    def _sanitize_path(self, file_path: str) -> str:
        """
        Sanitize and normalize a file path to ensure it's valid and secure.
        
        Args:
            file_path: The path to sanitize
            
        Returns:
            A sanitized path that is safe to use
        """
        if not file_path:
            return "unnamed_file.txt"
            
        # Normalize the path to use forward slashes
        normalized = file_path.replace("\\", "/")
        
        # Remove any leading path separators and drive letters
        if normalized.startswith("/") or re.match(r"^[a-zA-Z]:", normalized):
            normalized = re.sub(r"^[a-zA-Z]:[/\\]*|^/+", "", normalized)
            
        # Ensure the path doesn't try to go up directories
        parts = normalized.split("/")
        clean_parts = []
        
        for part in parts:
            if part in [".", "", ".."]:
                continue
            # Replace invalid characters
            clean_part = re.sub(r'[<>:"|?*]', '_', part).strip()
            if clean_part:
                clean_parts.append(clean_part)
                
        if not clean_parts:
            return "unnamed_file.txt"
            
        return "/".join(clean_parts)

    async def generate_file(self, file_path: str, prompt: str, project_name: str) -> Dict[str, Any]:
        """
        Generate the content for a file based on a prompt.
        
        Args:
            file_path: Path to the file
            prompt: Content generation prompt
            project_name: Name of the project
            
        Returns:
            Dictionary with generation results
        """
        # Sanitize the file path to ensure it's within the project
        file_path = self._sanitize_path(file_path)
        
        # Log the file generation with detailed information
        logger.info(f"[Coder] 📝 Generating file: {file_path} for project {project_name}")
        
        # Emit a message about file generation to the UI
        try:
            await emit_code_generation_message(project_name, file_path, "generating")
        except Exception as e:
            logger.error(f"[Coder] Error emitting code generation message: {e}")
        
        # Enhance the prompt with file-specific guidance
        file_ext = os.path.splitext(file_path)[1]
        filename = os.path.basename(file_path)
        enhanced_prompt = self._enhance_prompt_for_file_type(prompt, file_path, file_ext, filename)
        
        logger.info(f"[Coder] Using enhanced prompt for {file_path}: {enhanced_prompt[:100]}...")
        
        try:
            # Generate the file content
            content = await self.llm.generate(enhanced_prompt, project_name)
            
            # Clean up the content to extract code if needed
            cleaned_content = self._clean_code_output(content, file_ext)
            
            logger.info(f"[Coder] ✅ Successfully generated file: {file_path} ({len(cleaned_content)} bytes)")
            
            # Emit a success message to the UI
            try:
                await emit_code_generation_message(project_name, file_path, "generated")
            except Exception as e:
                logger.error(f"[Coder] Error emitting code generation complete message: {e}")
            
            return {
                "success": True,
                "file_path": file_path,
                "content": cleaned_content,
                "raw_content": content
            }
        
        except Exception as e:
            error_msg = f"Error generating content for {file_path}: {str(e)}"
            logger.error(f"[Coder] ❌ {error_msg}")
            
            # Emit an error message to the UI
            try:
                await emit_code_generation_message(project_name, file_path, "failed")
            except Exception as emit_error:
                logger.error(f"[Coder] Error emitting code generation error message: {emit_error}")
            
            return {
                "success": False,
                "file_path": file_path,
                "error": error_msg
            }
    
    async def apply_patch(self, file_path: str, patch: str, project_name: str) -> Dict[str, Any]:
        """
        Apply a patch to an existing file.
        
        Args:
            file_path: Path to the file to patch
            patch: Patch content
            project_name: Name of the project
            
        Returns:
            Dictionary with patch results
        """
        # Log the patch application with detailed information
        logger.info(f"[Coder] 🔧 Applying patch to file: {file_path} for project {project_name}")
        
        # Emit a message about patch application to the UI
        try:
            await emit_code_generation_message(project_name, file_path, "patching")
        except Exception as e:
            logger.error(f"[Coder] Error emitting code patch message: {e}")
        
        # TODO: Implement patch application logic
        return {
            "success": False,
            "file_path": file_path,
            "error": "Patch application not implemented yet"
        }
    
    def _enhance_prompt_for_file_type(self, base_prompt: str, file_path: str, file_ext: str, filename: str) -> str:
        """
        Enhance a code generation prompt with file-specific guidance.
        
        Args:
            base_prompt: Base content generation prompt
            file_path: Full file path
            file_ext: File extension
            filename: File name
            
        Returns:
            Enhanced prompt
        """
        # Add file-specific guidance
        if file_ext == '.ts' or file_path.endswith('.ts'):
            if 'component.ts' in file_path:
                return f"{base_prompt}\n\nThis is an Angular component TypeScript file. Please include:\n- Component decorator with proper selector, templateUrl and styleUrls\n- Necessary imports (Angular core, etc.)\n- Class with proper lifecycle hooks (ngOnInit, etc.)\n- Complete implementation of all methods\n- Proper type definitions"
            elif 'service.ts' in file_path:
                return f"{base_prompt}\n\nThis is an Angular service TypeScript file. Please include:\n- Injectable decorator\n- Necessary imports (Angular core, etc.)\n- Complete implementation of all methods\n- Proper error handling\n- Observable return types where appropriate"
            else:
                return f"{base_prompt}\n\nThis is a TypeScript file. Please include:\n- All necessary imports\n- Complete implementation\n- Proper type definitions\n- Export statements for all public entities"
        
        elif file_ext == '.html' or file_path.endswith('.html'):
            return f"{base_prompt}\n\nThis is an HTML file. Please include:\n- Proper HTML structure\n- Responsive design considerations\n- Angular directives and bindings if this is a component template\n- Semantic HTML elements"
        
        elif file_ext == '.scss' or file_ext == '.css' or file_path.endswith('.scss') or file_path.endswith('.css'):
            return f"{base_prompt}\n\nThis is a style file. Please include:\n- Clean, organized CSS/SCSS\n- Responsive design using media queries\n- Variables and mixins if using SCSS\n- Modern CSS techniques (flexbox, grid, etc.)"
        
        # Default case
        return base_prompt
    
    def _clean_code_output(self, content: str, file_ext: str) -> str:
        """
        Clean AI-generated code output to extract just the code.
        
        Args:
            content: Raw AI-generated content
            file_ext: File extension
            
        Returns:
            Cleaned code content
        """
        # Check if the content is wrapped in a code block
        code_block_match = re.search(r'```(?:\w+)?\s*([\s\S]+?)\s*```', content)
        if code_block_match:
            return code_block_match.group(1).strip()
        
        # If not in a code block, return the content as is
        return content.strip()

    async def generate_code_analysis_report(self, project_name: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive code analysis report with visualizations using Playwright.
        
        Args:
            project_name: The name of the project to analyze
            options: Additional options for the analysis
            
        Returns:
            Dictionary with analysis results and paths to generated reports
        """
        options = options or {}
        
        # Get the project directory
        project_dir = os.path.join(self.project_manager.projects_dir, project_name)
        
        # Initialize the analysis results
        analysis_results = {
            "success": False,
            "project_name": project_name,
            "analysis_date": datetime.now().isoformat(),
            "reports": [],
            "metrics": {},
            "issues": [],
            "visualizations": [],
            "recommendations": []
        }
        
        # Create a reports directory
        reports_dir = os.path.join(project_dir, "reports")
        os.makedirs(reports_dir, exist_ok=True)
        
        # 1. Code Quality Analysis
        try:
            await emit_code_generation_message(project_name, "reports/code-analysis.json", "analyzing")
            
            # Determine the type of project
            project_type = self._detect_project_type(project_dir)
            analysis_results["project_type"] = project_type
            
            # Run appropriate linters based on project type
            if project_type == "javascript" or project_type == "typescript":
                # Use ESLint for JavaScript/TypeScript projects
                from backend.src.agents.shell_executor import ShellExecutor
                shell = ShellExecutor(project_dir=project_dir)
                
                # Check if ESLint is installed
                eslint_check = await shell.run_command("npx eslint --version", timeout=10)
                if not eslint_check["success"]:
                    # Install ESLint
                    await shell.run_command("npm install --save-dev eslint", timeout=180)
                    
                    # Create basic ESLint config if it doesn't exist
                    if not os.path.exists(os.path.join(project_dir, ".eslintrc.json")):
                        with open(os.path.join(project_dir, ".eslintrc.json"), "w") as f:
                            f.write("""
{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": [
    "eslint:recommended"
  ],
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "rules": {
    "indent": ["error", 2],
    "linebreak-style": ["error", "unix"],
    "quotes": ["error", "single"],
    "semi": ["error", "always"]
  }
}
""")
                
                # Run ESLint with JSON reporter
                eslint_cmd = "npx eslint --format json --output-file reports/eslint-results.json ."
                await shell.run_command(eslint_cmd, timeout=60)
                
                # Load and process ESLint results
                eslint_results_path = os.path.join(reports_dir, "eslint-results.json")
                if os.path.exists(eslint_results_path):
                    with open(eslint_results_path, "r") as f:
                        try:
                            eslint_results = json.load(f)
                            issue_count = sum(len(file_result["messages"]) for file_result in eslint_results)
                            
                            analysis_results["metrics"]["eslint_issues"] = issue_count
                            analysis_results["reports"].append({
                                "name": "ESLint Report",
                                "path": "reports/eslint-results.json",
                                "issue_count": issue_count
                            })
                            
                            # Extract the top issues for recommendations
                            issue_types = {}
                            for file_result in eslint_results:
                                for message in file_result["messages"]:
                                    rule_id = message.get("ruleId", "unknown")
                                    if rule_id in issue_types:
                                        issue_types[rule_id] += 1
                                    else:
                                        issue_types[rule_id] = 1
                            
                            # Add top issues to the analysis results
                            top_issues = sorted(issue_types.items(), key=lambda x: x[1], reverse=True)[:5]
                            for rule_id, count in top_issues:
                                analysis_results["issues"].append({
                                    "rule": rule_id,
                                    "count": count,
                                    "type": "eslint"
                                })
                                
                            # Generate recommendations based on top issues
                            for rule_id, count in top_issues:
                                analysis_results["recommendations"].append({
                                    "title": f"Fix {count} instances of '{rule_id}' ESLint rule",
                                    "description": f"Consider addressing these issues to improve code quality"
                                })
                                
                        except Exception as e:
                            logger.error(f"[Coder] Error processing ESLint results: {e}")
            
            elif project_type == "python":
                # Use pylint or flake8 for Python projects
                from backend.src.agents.shell_executor import ShellExecutor
                shell = ShellExecutor(project_dir=project_dir)
                
                # Check if pylint is installed
                pylint_check = await shell.run_command("pylint --version", timeout=10)
                if not pylint_check["success"]:
                    # Install pylint
                    await shell.run_command("pip install pylint", timeout=60)
                
                # Run pylint with JSON reporter
                pylint_cmd = "pylint --output-format=json --recursive=y . > reports/pylint-results.json"
                await shell.run_command(pylint_cmd, timeout=60)
                
                # Load and process pylint results
                pylint_results_path = os.path.join(reports_dir, "pylint-results.json")
                if os.path.exists(pylint_results_path):
                    with open(pylint_results_path, "r") as f:
                        try:
                            pylint_results = json.load(f)
                            issue_count = len(pylint_results)
                            
                            analysis_results["metrics"]["pylint_issues"] = issue_count
                            analysis_results["reports"].append({
                                "name": "Pylint Report",
                                "path": "reports/pylint-results.json",
                                "issue_count": issue_count
                            })
                            
                            # Extract the top issues for recommendations
                            issue_types = {}
                            for message in pylint_results:
                                symbol = message.get("symbol", "unknown")
                                if symbol in issue_types:
                                    issue_types[symbol] += 1
                                else:
                                    issue_types[symbol] = 1
                            
                            # Add top issues to the analysis results
                            top_issues = sorted(issue_types.items(), key=lambda x: x[1], reverse=True)[:5]
                            for symbol, count in top_issues:
                                analysis_results["issues"].append({
                                    "rule": symbol,
                                    "count": count,
                                    "type": "pylint"
                                })
                                
                            # Generate recommendations based on top issues
                            for symbol, count in top_issues:
                                analysis_results["recommendations"].append({
                                    "title": f"Fix {count} instances of '{symbol}' Pylint warning",
                                    "description": f"Consider addressing these issues to improve code quality"
                                })
                                
                        except Exception as e:
                            logger.error(f"[Coder] Error processing Pylint results: {e}")
            
            # 2. Generate Visualization Report using Playwright
            try:
                # Create a visualization script
                visualization_script = os.path.join(reports_dir, "generate-visualizations.js")
                with open(visualization_script, "w") as f:
                    f.write(f"""
const fs = require('fs');
const path = require('path');
const {{ chromium }} = require('playwright');

// Helper function to get project structure
function getProjectStructure(dir, relativePath = '') {{
  const results = [];
  const list = fs.readdirSync(dir);
  
  for (const file of list) {{
    const fullPath = path.join(dir, file);
    const relativeFsPath = path.join(relativePath, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {{
      // Skip node_modules and other common excluded directories
      if (file !== 'node_modules' && file !== '.git' && file !== 'dist' && file !== 'reports') {{
        results.push({{
          name: file,
          path: relativeFsPath,
          type: 'directory',
          children: getProjectStructure(fullPath, relativeFsPath)
        }});
      }}
    }} else {{
      results.push({{
        name: file, 
        path: relativeFsPath,
        type: 'file',
        size: stat.size
      }});
    }}
  }}
  
  return results;
}}

// Get code metrics
function getCodeMetrics(projectStructure) {{
  const extensions = {{
    js: 0,
    ts: 0,
    html: 0,
    css: 0,
    scss: 0,
    json: 0,
    md: 0,
    py: 0,
    other: 0
  }};
  
  let totalFiles = 0;
  let totalSize = 0;
  
  function processItem(item) {{
    if (item.type === 'file') {{
      totalFiles++;
      totalSize += item.size;
      
      const ext = path.extname(item.name).substring(1).toLowerCase();
      if (extensions[ext] !== undefined) {{
        extensions[ext]++;
      }} else {{
        extensions.other++;
      }}
    }} else if (item.children) {{
      item.children.forEach(processItem);
    }}
  }}
  
  projectStructure.forEach(processItem);
  
  return {{
    totalFiles,
    totalSize,
    fileTypes: extensions
  }};
}}

(async () => {{
  try {{
    // Get project structure
    const projectStructure = getProjectStructure('{project_dir}');
    fs.writeFileSync(path.join('{reports_dir}', 'project-structure.json'), JSON.stringify(projectStructure, null, 2));
    
    // Get code metrics
    const metrics = getCodeMetrics(projectStructure);
    fs.writeFileSync(path.join('{reports_dir}', 'code-metrics.json'), JSON.stringify(metrics, null, 2));
    
    // Generate visualizations with Playwright
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Create HTML for the visualizations
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Project Analysis: {project_name}</title>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
          body {{ font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }}
          .chart-container {{ height: 400px; margin-bottom: 30px; }}
          h1, h2 {{ color: #333; }}
          .metrics {{ display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 30px; }}
          .metric {{ background: #f5f5f5; padding: 15px; border-radius: 8px; flex: 1; min-width: 200px; }}
          .metric h3 {{ margin-top: 0; color: #666; }}
          .metric p {{ font-size: 24px; font-weight: bold; margin: 0; }}
        </style>
      </head>
      <body>
        <h1>Project Analysis: {project_name}</h1>
        <div class="metrics">
          <div class="metric">
            <h3>Total Files</h3>
            <p id="totalFiles">Loading...</p>
          </div>
          <div class="metric">
            <h3>Total Size</h3>
            <p id="totalSize">Loading...</p>
          </div>
          <div class="metric">
            <h3>Issues Found</h3>
            <p id="issuesFound">Loading...</p>
          </div>
        </div>
        
        <h2>File Type Distribution</h2>
        <div class="chart-container">
          <canvas id="fileTypesChart"></canvas>
        </div>
        
        <h2>Project Structure</h2>
        <div class="chart-container">
          <canvas id="structureChart"></canvas>
        </div>
        
        <script>
          // Load the data
          fetch('code-metrics.json')
            .then(response => response.json())
            .then(metrics => {{
              document.getElementById('totalFiles').textContent = metrics.totalFiles;
              document.getElementById('totalSize').textContent = (metrics.totalSize / 1024).toFixed(2) + ' KB';
              
              // File types chart
              const fileTypesCtx = document.getElementById('fileTypesChart').getContext('2d');
              const fileTypes = Object.keys(metrics.fileTypes).filter(key => metrics.fileTypes[key] > 0);
              const fileTypeCounts = fileTypes.map(key => metrics.fileTypes[key]);
              
              new Chart(fileTypesCtx, {{
                type: 'pie',
                data: {{
                  labels: fileTypes,
                  datasets: [{{
                    data: fileTypeCounts,
                    backgroundColor: [
                      '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                      '#FF9F40', '#C9CBCF', '#7FD8BE', '#A6D9F7', '#FFCFDF'
                    ]
                  }}]
                }},
                options: {{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {{
                    legend: {{ position: 'right' }}
                  }}
                }}
              }});
            }});
            
          // Try to load issues data
          fetch('eslint-results.json')
            .then(response => response.json())
            .catch(() => fetch('pylint-results.json')
              .then(response => response.json())
              .catch(() => null)
            )
            .then(issuesData => {{
              let issueCount = 0;
              
              if (Array.isArray(issuesData)) {{
                // PyLint format
                issueCount = issuesData.length;
              }} else if (issuesData) {{
                // ESLint format - sum up all messages
                issueCount = issuesData.reduce((sum, file) => sum + file.messages.length, 0);
              }}
              
              document.getElementById('issuesFound').textContent = issueCount;
            }})
            .catch(() => {{
              document.getElementById('issuesFound').textContent = 'N/A';
            }});
            
          // Project structure visualization
          fetch('project-structure.json')
            .then(response => response.json())
            .then(structure => {{
              // Simple representation of structure
              const structureCtx = document.getElementById('structureChart').getContext('2d');
              
              // Extract directory statistics
              const directories = [];
              const fileCounts = [];
              
              function processDir(dir, path = '') {{
                if (dir.type === 'directory') {{
                  const dirPath = path ? path + '/' + dir.name : dir.name;
                  const fileCount = countFiles(dir);
                  
                  if (fileCount > 0) {{
                    directories.push(dirPath);
                    fileCounts.push(fileCount);
                  }}
                  
                  if (dir.children) {{
                    dir.children.forEach(child => {{
                      if (child.type === 'directory') {{
                        processDir(child, dirPath);
                      }}
                    }});
                  }}
                }}
              }}
              
              function countFiles(dir) {{
                let count = 0;
                if (dir.type === 'file') {{
                  return 1;
                }} else if (dir.children) {{
                  dir.children.forEach(child => {{
                    count += countFiles(child);
                  }});
                }}
                return count;
              }}
              
              structure.forEach(item => processDir(item));
              
              // Create the chart
              new Chart(structureCtx, {{
                type: 'bar',
                data: {{
                  labels: directories,
                  datasets: [{{
                    label: 'Number of Files',
                    data: fileCounts,
                    backgroundColor: '#36A2EB',
                    borderColor: '#2693e6',
                    borderWidth: 1
                  }}]
                }},
                options: {{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {{
                    y: {{
                      beginAtZero: true
                    }}
                  }}
                }}
              }});
            }});
        </script>
      </body>
      </html>
    `;
    
    fs.writeFileSync(path.join('{reports_dir}', 'visualization.html'), htmlContent);
    
    // Generate a screenshot of the visualization
    await page.goto('file://' + path.join('{reports_dir}', 'visualization.html'));
    await page.waitForFunction(() => 
      document.getElementById('totalFiles').textContent !== 'Loading...' &&
      document.getElementById('fileTypesChart').getContext('2d') !== null
    );
    
    // Wait for charts to render
    await page.waitForTimeout(2000);
    
    // Take a screenshot
    await page.screenshot({{ path: path.join('{reports_dir}', 'visualization.png'), fullPage: true }});
    
    console.log('Visualization complete. Reports saved to the reports directory.');
    await browser.close();
    
  }} catch (error) {{
    console.error('Error generating visualizations:', error);
    process.exit(1);
  }}
}})();
""")
                
                # Install Playwright if needed
                from backend.src.agents.shell_executor import ShellExecutor
                shell = ShellExecutor(project_dir=project_dir)
                
                # Check if Playwright is installed
                playwright_check = await shell.run_command("npx playwright --version", timeout=10)
                if not playwright_check["success"]:
                    # Install Playwright
                    await shell.run_command("npm install -D playwright", timeout=180)
                
                # Run the visualization script
                await emit_code_generation_message(project_name, "reports/visualization.html", "generating")
                await shell.run_command("node reports/generate-visualizations.js", timeout=60)
                
                # Check if the visualization was generated
                visualization_html = os.path.join(reports_dir, "visualization.html")
                visualization_png = os.path.join(reports_dir, "visualization.png")
                
                if os.path.exists(visualization_html) and os.path.exists(visualization_png):
                    analysis_results["visualizations"].append({
                        "name": "Project Visualization",
                        "html_path": "reports/visualization.html",
                        "image_path": "reports/visualization.png"
                    })
                    
                # Load code metrics if available
                code_metrics_path = os.path.join(reports_dir, "code-metrics.json")
                if os.path.exists(code_metrics_path):
                    with open(code_metrics_path, "r") as f:
                        try:
                            code_metrics = json.load(f)
                            analysis_results["metrics"].update(code_metrics)
                        except Exception as e:
                            logger.error(f"[Coder] Error loading code metrics: {e}")
                
            except Exception as e:
                logger.error(f"[Coder] Error generating visualizations: {e}")
                analysis_results["error_visualization"] = str(e)
            
            # Save the final analysis results
            analysis_results["success"] = True
            analysis_results_path = os.path.join(reports_dir, "code-analysis.json")
            with open(analysis_results_path, "w") as f:
                json.dump(analysis_results, f, indent=2)
            
            await emit_code_generation_message(project_name, "reports/code-analysis.json", "generated")
            
        except Exception as e:
            logger.error(f"[Coder] Error during code analysis: {e}")
            analysis_results["error"] = str(e)
            analysis_results_path = os.path.join(reports_dir, "code-analysis.json")
            with open(analysis_results_path, "w") as f:
                json.dump(analysis_results, f, indent=2)
            
            await emit_code_generation_message(project_name, "reports/code-analysis.json", "failed")
        
        return analysis_results
        
    def _detect_project_type(self, project_dir: str) -> str:
        """
        Detect the type of project based on the files and structure.
        
        Args:
            project_dir: Path to the project directory
            
        Returns:
            The detected project type as a string
        """
        # Check for package.json (Node.js project)
        if os.path.exists(os.path.join(project_dir, "package.json")):
            # Check for TypeScript configuration
            if os.path.exists(os.path.join(project_dir, "tsconfig.json")):
                return "typescript"
            else:
                return "javascript"
        
        # Check for Python files
        python_files = [f for f in os.listdir(project_dir) if f.endswith(".py")]
        if python_files or os.path.exists(os.path.join(project_dir, "requirements.txt")):
            return "python"
        
        # Check for Java files
        java_files = [f for f in os.listdir(project_dir) if f.endswith(".java")]
        if java_files or os.path.exists(os.path.join(project_dir, "pom.xml")):
            return "java"
        
        # Default to generic
        return "generic"
