"""
Documentation agent that creates comprehensive documentation for projects.
"""
import os
import json
import logging
import markdown
import re
from typing import Dict, Any, List, Optional
from xhtml2pdf import pisa
from datetime import datetime

from jinja2 import Environment, FileSystemLoader
from backend.src.llm.llm import LLM
from backend.src.project import ProjectManager
from backend.src.socket_instance import emit_message, emit_agent_message, emit_agent_file_update

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Documenter:
    """
    Documentation agent that creates comprehensive documentation for projects.
    """
    def __init__(self, model_id: str):
        """
        Initialize the Documenter with a model ID.
        
        Args:
            model_id: The ID of the LLM model to use
        """
        self.model_id = model_id
        self.llm = LLM.create(model_id)
        self.env = Environment(loader=FileSystemLoader("templates"))
        self.project_manager = ProjectManager()
    
    async def execute(self, plan: Dict[str, Any], code_files: List[Dict[str, Any]], project_name: str) -> List[Dict[str, Any]]:
        """
        Execute the documentation generation process based on a plan and code files.
        
        Args:
            plan: The plan from the planner
            code_files: Generated code files
            project_name: The name of the project
            
        Returns:
            List of generated documentation files
        """
        template = self.env.get_template("documenter_prompt.jinja2")
        
        code_files_content = []
        for file in code_files:
            code_files_content.append(f"File: {file['path']}\n\n```\n{file['content']}\n```")
        
        formatted_prompt = template.render(
            plan=json.dumps(plan, indent=2),
            code_files="\n\n".join(code_files_content),
            project_name=project_name
        )
        
        await emit_message("documentation_started", {"project_name": project_name})
        await emit_agent_message(project_name, "I'm generating documentation based on the code...")
        
        response = await self.llm.generate(formatted_prompt, project_name)
        
        doc_files = self.parse_response(response)
        
        await emit_message("documentation_completed", {"project_name": project_name})
        
        if doc_files:
            file_list = "\n".join([f"- {file['path']}" for file in doc_files])
            await emit_agent_message(
                project_name,
                f"I've generated the following documentation files:\n\n{file_list}"
            )
        
        return doc_files
    
    def parse_response(self, response: str) -> List[Dict[str, Any]]:
        """
        Parse the LLM response into a list of documentation files.
        
        Args:
            response: The raw LLM response
            
        Returns:
            A list of dictionaries containing file paths and content
        """
        files = []
        current_file = None
        current_content = []
        
        lines = response.split("\n")
        for i, line in enumerate(lines):
            if line.startswith("```") and i + 1 < len(lines) and not lines[i + 1].startswith("```"):
                file_path = line.replace("```", "").strip()
                
                if current_file is not None and current_content:
                    files.append({
                        "path": current_file,
                        "content": "\n".join(current_content)
                    })
                
                current_file = file_path
                current_content = []
            elif line.startswith("```") and current_file is not None:
                files.append({
                    "path": current_file,
                    "content": "\n".join(current_content)
                })
                current_file = None
                current_content = []
            elif current_file is not None:
                current_content.append(line)
        
        if current_file is not None and current_content:
            files.append({
                "path": current_file,
                "content": "\n".join(current_content)
            })
        
        return files
    
    def _sanitize_path(self, path: str) -> str:
        parts = path.replace("\\", "/").split("/")
        sanitized_parts = [re.sub(r'[<>:"|?*]', '_', part).strip().strip('.') for part in parts]
        return "/".join(sanitized_parts)
    
    def save_documentation_to_project(self, doc_files: List[Dict[str, Any]], project_name: str) -> None:
        logger.info(f"[Documenter] Saving documentation files to project: {project_name}")
        for file in doc_files:
            safe_path = self._sanitize_path(file['path'])
            logger.debug(f"[Documenter] Saving file: {file['path']} (sanitized: {safe_path})")
            result = self.project_manager.add_file_to_project(
                project_name,
                safe_path,
                file["content"]
            )
            if not isinstance(result, dict) or result.get("error"):
                logger.error(f"[Documenter] Failed to add documentation file {safe_path} to project {project_name}: {result.get('error') if isinstance(result, dict) else result}")
                continue
            emit_agent_file_update(
                project_name,
                safe_path,
                file["content"]
            )
            logger.info(f"[Documenter] Added documentation file {safe_path} to project {project_name}")
            if safe_path.endswith(".md"):
                pdf_path = safe_path.replace(".md", ".pdf")
                logger.debug(f"[Documenter] Generating PDF for: {pdf_path}")
                self.generate_pdf_from_markdown(file["content"], project_name, pdf_path)
    
    def generate_pdf_from_markdown(self, markdown_content: str, project_name: str, pdf_path: str) -> None:
        logger.info(f"[Documenter] Generating PDF from markdown for project: {project_name}, pdf_path: {pdf_path}")
        try:
            html = markdown.markdown(markdown_content)
            styled_html = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    h1 {{ color: #333366; }}
                    h2 {{ color: #333366; border-bottom: 1px solid #cccccc; }}
                    pre {{ background-color: #f5f5f5; padding: 10px; border-radius: 5px; }}
                    code {{ font-family: Consolas, monospace; }}
                </style>
            </head>
            <body>
                {html}
            </body>
            </html>
            """
            project_dir = os.path.join(self.project_manager.projects_dir, project_name)
            pdf_path_full = os.path.join(project_dir, pdf_path)
            logger.debug(f"[Documenter] pdf_path_full: {pdf_path_full}")
            os.makedirs(os.path.dirname(pdf_path_full), exist_ok=True)
            with open(pdf_path_full, "wb") as pdf_file:
                pisa.CreatePDF(styled_html, dest=pdf_file)
            with open(pdf_path_full, "rb") as pdf_file:
                pdf_content = pdf_file.read()
            result = self.project_manager.add_file_to_project(
                project_name,
                pdf_path,
                pdf_content
            )
            if not isinstance(result, dict) or result.get("error"):
                logger.error(f"[Documenter] Failed to add PDF {pdf_path} to project {project_name}: {result.get('error') if isinstance(result, dict) else result}")
                return
            logger.info(f"[Documenter] Generated PDF file {pdf_path} for project {project_name}")
        except Exception as e:
            logger.error(f"[Documenter] Error generating PDF from markdown: {e}")
