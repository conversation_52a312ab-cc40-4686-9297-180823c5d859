"""
Intelligent Agent - A streamlined, focused AI agent for autonomous software development.

This agent replaces the complex auto_agent.py with a cleaner approach that focuses on:
1. Proper planning and execution
2. Intelligent use of available tools (DeepSeek API, SearxNG, etc.)
3. Robust error handling and recovery
4. Clean project structure management
"""
import os
import logging
import asyncio
import json
import time
from typing import Dict, Any, Optional, List

from backend.src.agents.smart_project_executor import SmartProjectExecutor
from backend.src.llm.llm import LLM
from backend.src.socket_instance import emit_agent_message, emit_agent_typing, emit_terminal_command

logger = logging.getLogger(__name__)

class IntelligentAgent:
    """
    An intelligent autonomous agent that can create complex software projects.
    """
    
    def __init__(self, project_name: str, projects_base_dir: str, model_id: str = "deepseek/deepseek-chat"):
        """Initialize the intelligent agent."""
        self.project_name = project_name
        self.projects_base_dir = projects_base_dir
        self.model_id = model_id
        
        # Initialize components
        self.project_executor = SmartProjectExecutor(project_name, projects_base_dir)
        self.llm = LLM.create(model_id)
        
        # Agent state
        self.current_phase = "planning"
        self.project_plan = None
        self.execution_context = {}
        
        logger.info(f"IntelligentAgent initialized for project: {project_name}")
    
    async def create_project(self, user_request: str, stream_callback=None) -> Dict[str, Any]:
        """
        Main method to create a project based on user request.
        
        This method orchestrates the entire project creation process:
        1. Analyze user request
        2. Create project plan
        3. Execute the plan step by step
        4. Handle errors and recovery
        """
        try:
            await emit_agent_typing(self.project_name, True)
            
            if stream_callback:
                await stream_callback("🤖 Starting intelligent project analysis...\n")
            
            # Phase 1: Analyze the user request
            analysis = await self._analyze_user_request(user_request, stream_callback)
            if not analysis["success"]:
                return analysis
            
            # Phase 2: Create project structure
            structure_result = await self._create_project_structure(analysis["project_type"], analysis["requirements"], stream_callback)
            if not structure_result["success"]:
                return structure_result
            
            # Phase 3: Generate project plan
            plan_result = await self._generate_project_plan(analysis, stream_callback)
            if not plan_result["success"]:
                return plan_result
            
            # Phase 4: Execute the plan
            execution_result = await self._execute_project_plan(plan_result["plan"], stream_callback)
            
            await emit_agent_typing(self.project_name, False)
            
            return execution_result
            
        except Exception as e:
            logger.error(f"Error in create_project: {e}")
            await emit_agent_typing(self.project_name, False)
            return {
                "success": False,
                "error": str(e),
                "phase": self.current_phase
            }
    
    async def _analyze_user_request(self, user_request: str, stream_callback=None) -> Dict[str, Any]:
        """Analyze the user request to determine project type and requirements."""
        self.current_phase = "analysis"
        
        if stream_callback:
            await stream_callback("📋 Analyzing your request...\n")
        
        analysis_prompt = f"""
        Analyze this software development request and provide a structured response:
        
        User Request: "{user_request}"
        
        Please analyze and respond with a JSON object containing:
        {{
            "project_type": "angular|react|vue|python|node|other",
            "complexity": "simple|medium|complex",
            "main_features": ["feature1", "feature2", ...],
            "requirements": {{
                "ui_components": ["component1", "component2", ...],
                "functionality": ["function1", "function2", ...],
                "styling": "basic|modern|custom",
                "data_management": "none|local|api|database"
            }},
            "estimated_files": 5-20,
            "description": "Brief description of what will be built"
        }}
        
        Focus on creating a complete, functional application that matches the user's request.
        """
        
        try:
            response = await self.llm.generate(analysis_prompt, self.project_name)
            
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response[json_start:json_end]
            analysis = json.loads(json_str)
            
            if stream_callback:
                await stream_callback(f"✅ Project Type: {analysis['project_type']}\n")
                await stream_callback(f"✅ Complexity: {analysis['complexity']}\n")
                await stream_callback(f"✅ Main Features: {', '.join(analysis['main_features'])}\n")
            
            return {
                "success": True,
                "project_type": analysis["project_type"],
                "complexity": analysis["complexity"],
                "requirements": analysis["requirements"],
                "main_features": analysis["main_features"],
                "estimated_files": analysis.get("estimated_files", 10),
                "description": analysis.get("description", "")
            }
            
        except Exception as e:
            logger.error(f"Error in analysis: {e}")
            return {
                "success": False,
                "error": f"Failed to analyze request: {str(e)}"
            }
    
    async def _create_project_structure(self, project_type: str, requirements: Dict[str, Any], stream_callback=None) -> Dict[str, Any]:
        """Create the basic project structure using appropriate tools."""
        self.current_phase = "structure_creation"
        
        if stream_callback:
            await stream_callback(f"🏗️ Creating {project_type} project structure...\n")
        
        try:
            if project_type == "angular":
                result = await self.project_executor.create_angular_project({
                    "routing": True,
                    "style": "scss"
                })
            elif project_type == "react":
                # For React, we'll create a basic structure manually since create-react-app is complex
                result = await self._create_basic_react_structure()
            elif project_type == "vue":
                result = await self._create_basic_vue_structure()
            else:
                result = await self._create_basic_project_structure(project_type)
            
            if result["success"] and stream_callback:
                await stream_callback(f"✅ {project_type.title()} project structure created successfully!\n")
            
            return result
            
        except Exception as e:
            logger.error(f"Error creating project structure: {e}")
            return {
                "success": False,
                "error": f"Failed to create project structure: {str(e)}"
            }
    
    async def _generate_project_plan(self, analysis: Dict[str, Any], stream_callback=None) -> Dict[str, Any]:
        """Generate a detailed execution plan for the project."""
        self.current_phase = "planning"
        
        if stream_callback:
            await stream_callback("📝 Generating detailed project plan...\n")
        
        planning_prompt = f"""
        Create a detailed implementation plan for this project:
        
        Project Type: {analysis['project_type']}
        Requirements: {json.dumps(analysis['requirements'], indent=2)}
        Main Features: {analysis['main_features']}
        
        Generate a JSON plan with this structure:
        {{
            "phases": [
                {{
                    "name": "Phase 1: Core Components",
                    "files": [
                        {{
                            "path": "src/app/components/example.component.ts",
                            "type": "component",
                            "description": "Main game component with board logic"
                        }}
                    ]
                }}
            ]
        }}
        
        For each file, provide:
        - Exact file path relative to project root
        - File type (component, service, model, style, etc.)
        - Clear description of what the file should contain
        
        Focus on creating a complete, working application with proper structure.
        """
        
        try:
            response = await self.llm.generate(planning_prompt, self.project_name)
            
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in planning response")
            
            json_str = response[json_start:json_end]
            plan = json.loads(json_str)
            
            self.project_plan = plan
            
            if stream_callback:
                total_files = sum(len(phase["files"]) for phase in plan["phases"])
                await stream_callback(f"✅ Plan generated: {len(plan['phases'])} phases, {total_files} files\n")
            
            return {
                "success": True,
                "plan": plan
            }
            
        except Exception as e:
            logger.error(f"Error generating plan: {e}")
            return {
                "success": False,
                "error": f"Failed to generate plan: {str(e)}"
            }
    
    async def _execute_project_plan(self, plan: Dict[str, Any], stream_callback=None) -> Dict[str, Any]:
        """Execute the project plan step by step."""
        self.current_phase = "execution"
        
        if stream_callback:
            await stream_callback("⚡ Starting project execution...\n")
        
        try:
            total_files = 0
            created_files = 0
            
            for phase in plan["phases"]:
                if stream_callback:
                    await stream_callback(f"\n🔄 {phase['name']}\n")
                
                for file_spec in phase["files"]:
                    total_files += 1
                    
                    # Generate file content
                    content_result = await self._generate_file_content(file_spec)
                    
                    if content_result["success"]:
                        # Create the file
                        create_result = await self.project_executor.create_file(
                            file_spec["path"], 
                            content_result["content"]
                        )
                        
                        if create_result["success"]:
                            created_files += 1
                            if stream_callback:
                                await stream_callback(f"✅ Created: {file_spec['path']}\n")
                        else:
                            if stream_callback:
                                await stream_callback(f"❌ Failed to create: {file_spec['path']}\n")
                    else:
                        if stream_callback:
                            await stream_callback(f"❌ Failed to generate content for: {file_spec['path']}\n")
            
            # Try to build the project
            if stream_callback:
                await stream_callback("\n🔨 Building project...\n")
            
            build_result = await self.project_executor.build_project()
            
            success_rate = (created_files / total_files) * 100 if total_files > 0 else 0
            
            return {
                "success": True,
                "files_created": created_files,
                "total_files": total_files,
                "success_rate": success_rate,
                "build_success": build_result.get("success", False),
                "project_dir": self.project_executor.project_dir
            }
            
        except Exception as e:
            logger.error(f"Error executing plan: {e}")
            return {
                "success": False,
                "error": f"Failed to execute plan: {str(e)}"
            }
    
    async def _generate_file_content(self, file_spec: Dict[str, Any]) -> Dict[str, Any]:
        """Generate content for a specific file based on its specification."""
        try:
            framework = self.project_executor.detect_framework()
            
            content_prompt = f"""
            Generate complete, production-ready code for this file:
            
            File: {file_spec['path']}
            Type: {file_spec['type']}
            Description: {file_spec['description']}
            Framework: {framework}
            
            Requirements:
            - Write complete, functional code
            - Include all necessary imports
            - Follow best practices for {framework}
            - Add proper error handling
            - Include comments for complex logic
            - Make it production-ready
            
            Return ONLY the file content, no explanations or markdown formatting.
            """
            
            content = await self.llm.generate(content_prompt, self.project_name)
            
            # Clean up the content (remove markdown formatting if present)
            if content.startswith("```"):
                lines = content.split('\n')
                if len(lines) > 2:
                    content = '\n'.join(lines[1:-1])
            
            return {
                "success": True,
                "content": content
            }
            
        except Exception as e:
            logger.error(f"Error generating file content: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _create_basic_react_structure(self) -> Dict[str, Any]:
        """Create a basic React project structure manually."""
        # This is a simplified React setup - in production you might use create-react-app
        basic_files = {
            "package.json": """{
  "name": "%s",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build"
  }
}""" % self.project_name,
            "src/index.js": """import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);""",
            "src/App.js": """import React from 'react';

function App() {
  return (
    <div>
      <h1>Welcome to %s</h1>
    </div>
  );
}

export default App;""" % self.project_name,
            "public/index.html": """<!DOCTYPE html>
<html>
<head>
  <title>%s</title>
</head>
<body>
  <div id="root"></div>
</body>
</html>""" % self.project_name
        }
        
        try:
            for file_path, content in basic_files.items():
                await self.project_executor.create_file(file_path, content)
            
            return {"success": True, "message": "Basic React structure created"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _create_basic_vue_structure(self) -> Dict[str, Any]:
        """Create a basic Vue project structure manually."""
        return {"success": False, "error": "Vue project creation not implemented yet"}
    
    async def _create_basic_project_structure(self, project_type: str) -> Dict[str, Any]:
        """Create a basic project structure for unknown project types."""
        return {"success": False, "error": f"Project type {project_type} not supported yet"}
