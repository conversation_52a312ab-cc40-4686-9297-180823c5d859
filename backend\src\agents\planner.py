# backend/src/agents/planner.py
import json
import re
import logging
import os
from typing import Dict, Any, List, Tuple, Optional

from jinja2 import Environment, FileSystemLoader
from llm.llm import LLM  # Assuming LLM is correctly importable
# Make sure socket_instance functions are imported correctly
from socket_instance import emit_message, emit_agent_message, emit_plan

# Configure logging (ensure this is configured globally in your app)
# logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Planner:
    """
    Planning agent that breaks down high-level instructions into actionable steps
    and extracts a structured JSON representation for automation.
    """
    def __init__(self, model_id: str = "deepseek/deepseek-coder"):
        """
        Initialize the Planner with a specific LLM model.
        
        Args:
            model_id: Model ID for the LLM
        """
        self.model_id = model_id
        self.llm = LLM.create(model_id)
        
        # Setup Jinja environment for loading prompt templates
        templates_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "templates")
        self.env = Environment(loader=FileSystemLoader(templates_dir))
        
        # Common project types and their required components
        self.project_templates = {
            "web_app": ["frontend", "backend", "database", "api"],
            "mobile_app": ["ui", "navigation", "state_management", "api_client"],
            "game": ["rendering", "game_logic", "input_handling", "assets"],
            "library": ["core_functions", "api", "documentation", "tests"],
            "data_analysis": ["data_loading", "preprocessing", "analysis", "visualization"],
            "api": ["routes", "controllers", "models", "authentication"]
        }
        
        logger.info(f"[Planner] Initialized with model {model_id}")

    async def execute(self, user_prompt: str, project_name: str) -> str:
        """
        Generate a structured plan based on user prompt.
        
        Args:
            user_prompt: Original user prompt
            project_name: Name of the project
            
        Returns:
            Generated plan as string
        """
        # Log the planning start with the plan symbol
        logger.info(f"[Planner] 📋 Generating plan for project: {project_name}")
        
        try:
            # Try to emit a plan message to the UI
            await emit_plan(project_name, f"Generating project plan for '{project_name}'...")
        except Exception as e:
            logger.error(f"[Planner] Error emitting plan message: {e}")
        
        # Determine the likely project type
        project_type = self._determine_project_type(user_prompt)
        logger.info(f"[Planner] 📋 Determined project type as: {project_type}")
        
        # Load the prompt template
        template = self.env.get_template("planner_prompt.jinja2")
        
        # Enhance the prompt with project type-specific guidelines
        additional_guidelines = self._get_guidelines_for_project_type(project_type)
        
        # Render the prompt with the user request, project name, and enhanced guidelines
        prompt = template.render(
            user_request=user_prompt,
            project_name=project_name,
            project_type=project_type,
            additional_guidelines=additional_guidelines
        )
        
        logger.info(f"[Planner] 📋 Generated prompt for LLM: {prompt[:100]}...")
        
        try:
            # Send the prompt to the LLM
            response = await self.llm.generate(prompt, project_name)
            
            logger.info(f"[Planner] 📋 Received response from LLM: {response[:100]}...")
            
            # Verify the plan structure
            is_valid, validation_error = self._validate_plan_structure(response, project_type)
            
            # If the plan is invalid, regenerate with more explicit instructions
            if not is_valid:
                logger.warning(f"[Planner] ⚠️ Plan validation failed: {validation_error}")
                
                # Update the template with validation feedback
                refined_prompt = template.render(
                    user_request=user_prompt,
                    project_name=project_name,
                    project_type=project_type,
                    additional_guidelines=additional_guidelines,
                    validation_feedback=validation_error
                )
                
                # Try again with the refined prompt
                logger.info(f"[Planner] 📋 Regenerating plan with validation feedback")
                response = await self.llm.generate(refined_prompt, project_name)
                
                # Verify the structure again
                is_valid, validation_error = self._validate_plan_structure(response, project_type)
                if not is_valid:
                    logger.warning(f"[Planner] ⚠️ Plan still invalid after regeneration: {validation_error}")
            
            # Try to emit the plan to the UI
            try:
                await emit_plan(project_name, f"Generated plan for '{project_name}'")
            except Exception as e:
                logger.error(f"[Planner] Error emitting plan complete message: {e}")
            
            return response
            
        except Exception as e:
            error_msg = f"Error generating plan: {str(e)}"
            logger.error(f"[Planner] ❌ {error_msg}")
            
            # Try to emit the error to the UI
            try:
                await emit_plan(project_name, f"Error generating plan: {str(e)}")
            except Exception as emit_e:
                logger.error(f"[Planner] Error emitting plan error message: {emit_e}")
            
            raise RuntimeError(error_msg)
            
    def _determine_project_type(self, user_prompt: str) -> str:
        """
        Analyze the user prompt to determine the type of project.
        
        Args:
            user_prompt: The user's original prompt
            
        Returns:
            The project type as a string
        """
        prompt_lower = user_prompt.lower()
        
        # Check for web application keywords
        if any(kw in prompt_lower for kw in ["web app", "website", "webapp", "frontend", "backend", "full stack", "fullstack"]):
            return "web_app"
        
        # Check for mobile app keywords
        if any(kw in prompt_lower for kw in ["mobile app", "ios app", "android app", "flutter", "react native"]):
            return "mobile_app"
        
        # Check for game keywords
        if any(kw in prompt_lower for kw in ["game", "tetris", "chess", "puzzle", "arcade", "shooter"]):
            return "game"
        
        # Check for library keywords
        if any(kw in prompt_lower for kw in ["library", "module", "package", "sdk", "framework"]):
            return "library"
        
        # Check for data analysis keywords
        if any(kw in prompt_lower for kw in ["data", "analytics", "analysis", "visualization", "dashboard"]):
            return "data_analysis"
        
        # Check for API keywords
        if any(kw in prompt_lower for kw in ["api", "rest", "graphql", "microservice", "endpoint"]):
            return "api"
        
        # Default to web app if no specific type is detected
        return "web_app"
    
    def _get_guidelines_for_project_type(self, project_type: str) -> str:
        """
        Get additional planning guidelines based on project type.
        
        Args:
            project_type: The determined project type
            
        Returns:
            String with additional guidelines
        """
        guidelines = "Your plan should include the following components:\n"
        
        if project_type in self.project_templates:
            for component in self.project_templates[project_type]:
                guidelines += f"- {component.replace('_', ' ').title()}\n"
        
        if project_type == "web_app":
            guidelines += """
Additional considerations for web applications:
1. Ensure proper separation of frontend and backend code
2. Define clear API endpoints for communication
3. Consider authentication and authorization requirements
4. Plan for responsive design and cross-browser compatibility
5. Consider state management approach
"""
        elif project_type == "game":
            guidelines += """
Additional considerations for games:
1. Define the game mechanics and rules clearly
2. Plan for game state management and user interactions
3. Consider rendering approach and performance optimizations
4. Design for different screen sizes if applicable
5. Plan for asset management (graphics, sounds, etc.)
"""
        elif project_type == "api":
            guidelines += """
Additional considerations for APIs:
1. Define clear, RESTful endpoint structure
2. Plan for proper error handling and status codes
3. Consider authentication and rate limiting
4. Include documentation generation
5. Plan for testing and validation
"""
        
        return guidelines
            
    def _validate_plan_structure(self, plan: str, project_type: str) -> Tuple[bool, str]:
        """
        Validate that the plan has the expected structure and components.
        
        Args:
            plan: The generated plan
            project_type: The type of project
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check for minimum components based on project type
        if project_type in self.project_templates:
            required_components = self.project_templates[project_type]
            
            # Naive check for keywords in the plan
            missing_components = []
            for component in required_components:
                if not re.search(r'\b' + re.escape(component.replace('_', ' ')) + r'\b', plan, re.IGNORECASE):
                    missing_components.append(component.replace('_', ' ').title())
            
            if missing_components:
                return False, f"Plan is missing these required components: {', '.join(missing_components)}"
        
        # Check for steps section
        if not re.search(r'(?:Steps|Plan|Implementation Steps):', plan, re.IGNORECASE):
            return False, "Plan is missing a clear 'Steps' or 'Implementation Steps' section"
        
        # Check for numbered steps
        step_matches = re.findall(r'(?:^|\n)(\d+)[.:\)]', plan)
        if not step_matches:
            return False, "Plan does not contain clear numbered steps"
        
        # Extract the number of steps
        step_numbers = [int(s) for s in step_matches]
        
        # Check for at least 3 steps for any non-trivial project
        if len(step_numbers) < 3:
            return False, f"Plan has only {len(step_numbers)} steps, which seems insufficient"
        
        # Check for step number consistency (no gaps)
        expected_numbers = list(range(1, len(step_numbers) + 1))
        if sorted(step_numbers) != expected_numbers:
            return False, "Plan has inconsistent step numbering or missing steps"
        
        # Check for JSON block (if one exists, it should be well-formed)
        json_match = re.search(r'```(?:json)?\s*([\s\S]+?)\s*```', plan)
        if json_match:
            try:
                json.loads(json_match.group(1).strip())
            except json.JSONDecodeError:
                return False, "Plan contains a JSON block that is not valid JSON"
        
        return True, ""

    def parse_response(self, response: str) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Parse the LLM response into a descriptive plan and a list of structured steps.

        Args:
            response: The raw LLM response

        Returns:
            A tuple containing:
            - Dictionary with descriptive plan details (project_name, description, etc.)
            - List of structured automation steps (from JSON block) or an empty list if parsing fails.
        """
        logger.info("[Planner] 📋 Parsing LLM response to extract plan")

        # First, try to find a JSON block in the response
        json_match = re.search(r'```(?:json)?\s*([\s\S]+?)\s*```', response)
        if json_match:
            try:
                plan_json = json_match.group(1).strip()
                plan = json.loads(plan_json)
                logger.info(f"[Planner] 📋 Successfully extracted JSON plan")
                
                # If the plan is a list and contains steps, return it
                if isinstance(plan, list) and len(plan) > 0 and isinstance(plan[0], dict):
                    # Convert to expected format
                    structured_plan = {
                        "steps": plan,
                        "full_response": response
                    }
                    return structured_plan, plan
                
                # If the plan is a dict with a 'steps' key, return it
                if isinstance(plan, dict) and 'steps' in plan and isinstance(plan['steps'], list):
                    plan["full_response"] = response
                    return plan, plan.get('steps', [])
                
                # Otherwise, manually extract steps
                logger.warning(f"[Planner] ⚠️ JSON found but structure is unexpected. Attempting to extract steps manually.")
                structured_plan = self._extract_structured_data(response)
                structured_plan["full_response"] = response
                
                # Use the JSON data as additional context
                structured_plan["json_data"] = plan
                
                return structured_plan, []
                
            except json.JSONDecodeError as e:
                logger.warning(f"[Planner] ⚠️ Found JSON-like block but could not parse it: {e}")
        
        # If no JSON found or it couldn't be parsed, try to extract structured data manually
        result = self._extract_structured_data(response)
        
        # Add the full reasoning
        result['full_response'] = response
        
        logger.info(f"[Planner] 📋 Extracted structured plan with {len(result.get('steps', []))} steps")
        return result, result.get('actionable_steps', [])
    
    def _extract_structured_data(self, text: str) -> Dict[str, Any]:
        """
        Extract structured data from text when JSON parsing fails.
        
        Args:
            text: Text to extract data from
            
        Returns:
            Extracted data as a dictionary
        """
        result = {
            'steps': [],
            'steps_desc': {},
            'description': '',
            'actionable_steps': []
        }
        
        # Try to extract a description
        desc_match = re.search(r'(?:Description|Overview|Summary):\s*(.*?)(?:\n\n|\n#|\Z)', text, re.DOTALL | re.IGNORECASE)
        if desc_match:
            result['description'] = desc_match.group(1).strip()
        
        # Try to extract steps
        steps_section = re.search(r'(?:Steps|Plan|Implementation Steps|Implementation Plan):(.*?)(?:\n\n\w+:|$)', text, re.DOTALL | re.IGNORECASE)
        if steps_section:
            steps_text = steps_section.group(1)
            # Match numbered steps
            step_matches = re.finditer(r'(?:^|\n)(\d+)[.:\)]\s*(.*?)(?=(?:\n\d+[.:\)])|$)', steps_text, re.DOTALL)
            
            for match in step_matches:
                step_num = int(match.group(1))
                step_content = match.group(2).strip()
                
                # Try to parse any sub-bullet points
                sub_points = {}
                sub_matches = re.finditer(r'\n\s*[-*]\s*(.*?)(?=\n\s*[-*]|\Z)', step_content, re.DOTALL)
                for sub_match in sub_matches:
                    sub_content = sub_match.group(1).strip()
                    sub_points[len(sub_points) + 1] = sub_content
                
                result['steps'].append({
                    'step_number': step_num,
                    'description': step_content,
                    'sub_points': sub_points
                })
                
                result['steps_desc'][step_num] = {
                    'description': step_content,
                    'sub_points': sub_points
                }
                
                # Try to convert step to an actionable step
                actionable_step = self._convert_step_to_actionable(step_num, step_content, sub_points)
                if actionable_step:
                    result['actionable_steps'].append(actionable_step)
        
        # Try to extract code blocks that might contain actionable steps
        code_blocks = re.findall(r'```(?:json)?\s*([\s\S]+?)\s*```', text)
        for block in code_blocks:
            try:
                parsed = json.loads(block)
                if isinstance(parsed, list) and len(parsed) > 0 and 'type' in parsed[0]:
                    result['actionable_steps'] = parsed
                    logger.info(f"[Planner] 📋 Found actionable steps in code block")
                    break
            except json.JSONDecodeError:
                continue
        
        return result
        
    def _convert_step_to_actionable(self, step_num: int, description: str, sub_points: dict) -> Optional[Dict[str, Any]]:
        """
        Attempt to convert a textual step to an actionable step dictionary.
        
        Args:
            step_num: The step number
            description: Step description
            sub_points: Sub-points for the step
            
        Returns:
            An actionable step dictionary or None if conversion failed
        """
        # Look for file creation patterns
        file_match = re.search(r'create (?:a |the )?(?:file|component|module|class|service) (?:named |called )?[\'"`]?([^\s\'"`]+)[\'"`]?', description, re.IGNORECASE)
        if file_match:
            file_path = file_match.group(1)
            return {
                'type': 'file',
                'file_path': file_path,
                'description': f"Create file {file_path}",
                'step_number': step_num
            }
        
        # Look for folder creation patterns
        folder_match = re.search(r'create (?:a |the )?(?:folder|directory) (?:named |called )?[\'"`]?([^\s\'"`]+)[\'"`]?', description, re.IGNORECASE)
        if folder_match:
            folder_path = folder_match.group(1)
            return {
                'type': 'folder',
                'path': folder_path,
                'description': f"Create folder {folder_path}",
                'step_number': step_num
            }
        
        # Look for command execution patterns
        command_match = re.search(r'(?:run|execute) (?:the )?(?:command|cmd)? ?[\'"`]?([^\n\'"`]+)[\'"`]?', description, re.IGNORECASE)
        if command_match:
            command = command_match.group(1)
            return {
                'type': 'command',
                'command': command,
                'description': f"Run command: {command}",
                'step_number': step_num
            }
        
        # Fall back to a generic task
        return {
            'type': 'task',
            'description': description,
            'sub_points': sub_points,
            'step_number': step_num
        }

    def _validate_and_enhance_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validates and enhances a step dictionary to ensure it has all required fields.
        
        Args:
            step: The step dictionary to validate
            
        Returns:
            Enhanced step dictionary
        """
        # Clone the step to avoid modifying the original
        validated_step = step.copy()
        
        # Validate and enhance based on step type
        step_type = validated_step.get('type', '')
        
        if step_type == 'file':
            # Ensure file_path exists and is properly formatted
            if 'file_path' not in validated_step:
                validated_step['file_path'] = 'unknown.txt'
                logger.warning(f"[Planner] File step missing file_path. Set to default: {validated_step['file_path']}")
            
            # Ensure content exists
            if 'content' not in validated_step or not validated_step['content']:
                # If no content, add a placeholder comment based on file extension
                file_path = validated_step['file_path']
                ext = os.path.splitext(file_path)[1].lower()
                
                if ext in ['.ts', '.js']:
                    validated_step['content'] = f"// TODO: Implement {file_path}\n// This file needs to be generated"
                elif ext in ['.html']:
                    validated_step['content'] = f"<!-- TODO: Implement {file_path} -->\n<p>This file needs to be generated</p>"
                elif ext in ['.css', '.scss']:
                    validated_step['content'] = f"/* TODO: Implement {file_path} */\n/* This file needs to be generated */\n"
                else:
                    validated_step['content'] = f"TODO: Implement {file_path}\nThis file needs to be generated"
                
                logger.warning(f"[Planner] File step missing content for {file_path}. Added placeholder.")
            
            # Ensure description exists
            if 'description' not in validated_step:
                validated_step['description'] = f"Create file {validated_step['file_path']}"
        
        elif step_type == 'command':
            # Ensure command exists
            if 'command' not in validated_step or not validated_step['command']:
                validated_step['command'] = 'echo "Missing command"'
                logger.warning(f"[Planner] Command step missing command. Set to default: {validated_step['command']}")
            
            # Ensure description exists
            if 'description' not in validated_step:
                validated_step['description'] = f"Run command: {validated_step['command']}"
                
        # Add more validations for other step types as needed
        
        return validated_step
    
    def _extract_code_blocks(self, response: str) -> List[Dict[str, Any]]:
        """
        Extract code blocks from the response that might represent files.
        
        Args:
            response: The raw LLM response
            
        Returns:
            List of dictionaries with path and content for each identified file
        """
        files = []
        
        # Try to find blocks like: ```[file_path] [content] ```
        matches = re.finditer(r'```(?!json)(?:(\S+))?\s*([\s\S]*?)```', response, re.MULTILINE)
        for match in matches:
            file_path = match.group(1) or "unknown.txt"  # Default if no path specified
            content = match.group(2).strip()
            
            # Ignore empty or very short contents
            if len(content) < 5:
                continue
                
            # If file_path looks like a language specifier (ts, js, html, etc.), make a guess at the path
            if file_path in ['ts', 'js', 'html', 'css', 'scss']:
                # Use a default path based on the language
                if file_path == 'ts':
                    file_path = 'src/app/app.component.ts'
                elif file_path == 'js':
                    file_path = 'src/app/app.component.js'
                elif file_path == 'html':
                    file_path = 'src/app/app.component.html'
                elif file_path in ['css', 'scss']:
                    file_path = f'src/app/app.component.{file_path}'
            
            files.append({
                "path": file_path,
                "content": content
            })
        
        return files

    # Optional: Keep the old conversion method as a fallback if JSON parsing fails
    def convert_plan_to_steps_fallback(self, plan_details: dict) -> list:
        """
        Fallback method to convert descriptive plan to structured steps if JSON parsing fails.
        
        Args:
            plan_details: The parsed descriptive plan
            
        Returns:
            List of structured steps
        """
        logger.warning("[Planner] Falling back to keyword-based step conversion.")
        steps = []
        project_name = plan_details.get('project_name', 'default_project')
        
        # Add Angular project initialization
        steps.append({
            "type": "command",
            "command": f"ng new {project_name} --routing --style=scss --skip-git",
            "description": f"Create new Angular project: {project_name}"
        })
        
        # Add dependency installation
        steps.append({
            "type": "command",
            "command": "npm install",
            "description": "Install dependencies"
        })
        
        # Extract components or other generation commands from steps
        step_map = plan_details.get('steps_desc', {})
        for num in sorted(step_map.keys()):
            desc = step_map[num]['description'].lower()
            
            # Look for component generation
            if 'component' in desc or 'ui' in desc:
                component_match = re.search(r'(?:component|ui).*?(\w+)', desc)
                if component_match:
                    comp_name = component_match.group(1)
                    steps.append({
                        "type": "command",
                        "command": f"ng generate component {comp_name}",
                        "description": f"Generate component: {comp_name}"
                    })
            
            # Look for service generation
            elif 'service' in desc:
                service_match = re.search(r'service.*?(\w+)', desc)
                if service_match:
                    service_name = service_match.group(1)
                    steps.append({
                        "type": "command",
                        "command": f"ng generate service {service_name}",
                        "description": f"Generate service: {service_name}"
                    })
        
        # Add minimal working files
        steps.append({
            "type": "file",
            "file_path": "src/app/app.component.html",
            "content": f"<div class=\"container\">\n  <h1>Welcome to {project_name}</h1>\n  <p>This is a starter Angular application.</p>\n</div>",
            "description": "Update app.component.html with basic content"
        })
        
        # Add VS Code and terminal opening
        steps.append({
            "type": "open_vscode",
            "description": "Open VS Code editor"
        })
        
        steps.append({
            "type": "open_terminal",
            "description": "Open terminal"
        })
        
        return steps