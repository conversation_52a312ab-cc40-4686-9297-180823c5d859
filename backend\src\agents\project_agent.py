import datetime
import os
import logging

from .project_executor import ProjectExecutor

# Configure logger
logger = logging.getLogger(__name__)

class ProjectAgent:
    def __init__(self, project_name, project_dir, llm_integration, model):
        self.project_name = project_name
        self.project_dir = project_dir
        self.llm_integration = llm_integration
        self.model = model
        self.progress = None
        self.plan = None

    async def execute_plan(self):
        """Execute the plan"""
        logger.info(f"Executing plan for {self.project_name}")
        
        # Reset progress
        if self.progress.get("status") not in ["completed", "failed"]:
            self.progress = {
                "status": "in_progress",
                "current_step": 0,
                "total_steps": len(self.plan.get("steps", [])),
                "started_at": datetime.datetime.now().isoformat(),
                "updated_at": datetime.datetime.now().isoformat(),
                "message": "Starting execution"
            }
        
        # Get the project executor
        project_executor = ProjectExecutor(
            project_name=self.project_name,
            project_dir=self.project_dir,
            llm_integration=self.llm_integration,
            model=self.model
        )
        
        # Execute each step in the plan
        steps = self.plan.get("steps", [])
        processed_files = []
        
        try:
            for i, step in enumerate(steps):
                if self.progress.get("current_step") > i:
                    # Skip steps that have already been executed
                    continue
                
                # Update progress
                self.progress["current_step"] = i
                self.progress["message"] = f"Executing step {i+1}/{len(steps)}: {step.get('description', '')}"
                self.progress["updated_at"] = datetime.datetime.now().isoformat()
                
                # Execute step
                logger.info(f"Executing step {i+1}/{len(steps)}: {step.get('description', '')}")
                
                # Process any files that need to be created or modified
                files = step.get("files", [])
                for file in files:
                    file_path = file.get("path")
                    content = file.get("content")
                    
                    if not file_path or not content:
                        continue
                    
                    # Add file to project
                    result = await project_executor.add_file(file_path, content)
                    if not result.get("success"):
                        logger.error(f"Failed to add file {file_path}: {result.get('message')}")
                    
                    processed_files.append(file_path)
            
            # All steps completed successfully
            self.progress["status"] = "completed"
            self.progress["message"] = "Project execution completed successfully"
            self.progress["updated_at"] = datetime.datetime.now().isoformat()
            self.progress["completed_at"] = datetime.datetime.now().isoformat()
            
            # Finalize the project after all files are created
            if project_executor.framework_detection.get("is_node", False):
                logger.info(f"Finalizing project {self.project_name} by installing dependencies and building...")
                finalize_result = await project_executor.finalize_project()
                if not finalize_result.get("success"):
                    logger.warning(f"Project finalization had issues: {finalize_result.get('message')}")
                else:
                    logger.info(f"Project {self.project_name} finalized successfully")
            
            return {
                "success": True,
                "message": "Plan executed successfully",
                "processed_files": processed_files
            }
        except Exception as e:
            logger.error(f"Failed to execute plan: {str(e)}", exc_info=True)
            
            self.progress["status"] = "failed"
            self.progress["message"] = f"Failed to execute plan: {str(e)}"
            self.progress["updated_at"] = datetime.datetime.now().isoformat()
            
            return {
                "success": False,
                "message": f"Failed to execute plan: {str(e)}",
                "error": str(e)
            } 