"""
ProjectExecutor - Specialized executor for project initialization and management.

This module provides capabilities for automated project creation, setup,
and management using various frameworks and tools.
"""
import os
import logging
import asyncio
import platform
import re
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Awaitable, Tuple
import shutil
import fnmatch
import tempfile
from datetime import datetime

from backend.src.agents.shell_executor import ShellExecutor
from backend.src.project import ProjectManager
from backend.src.socket_instance import emit_terminal_command, emit_code_generation_message, emit_cursor_message, emit_agent_file_update
from backend.src.llm.llm import LLM

logger = logging.getLogger(__name__)

class ProjectExecutor:
    """
    Executes project initialization and setup commands.
    Specializes in framework-specific operations (Angular, React, Vue, etc.).
    """
    def __init__(self, project_name: str, projects_base_dir: str):
        """
        Initialize the ProjectExecutor.

        Args:
            project_name: The name of the project
            projects_base_dir: The base directory for all projects
        """
        self.project_name = project_name
        self.projects_base_dir = projects_base_dir
        self.project_dir = os.path.join(projects_base_dir, project_name)
        self.shell_executor = ShellExecutor()
        self.project_manager = ProjectManager(projects_base_dir)
        self.initialized = False
        
        # Common ignore patterns (directories and files to ignore)
        self.ignore_patterns = [
            'node_modules', '.git', 'dist', 'build', '.cache', 
            '__pycache__', '.pytest_cache', '.angular', 'coverage',
            '.next', '.nuxt', '.output', '.vscode', '.idea',
            '*.log', '*.lock', '*.min.js', '*.min.css'
        ]
        
        # Track and store framework detection results
        self.framework_detection = {
            "is_angular": False,
            "is_react": False,
            "is_vue": False,
            "is_node": False,
            "is_python": False,
            "framework": "unknown"
        }
        
        # For tracking build errors
        self.build_errors = []
        
        # Flag to track whether component implementation has been completed
        self.component_implementation_done = False
        
        # Default model ID for LLM-based generation
        self.model_id = None
        
        # Context management - store project state, files, and metadata
        self.context = {
            "project_name": project_name,
            "created_at": time.time(),
            "last_updated": time.time(),
            "framework": "unknown",
            "files_created": [],
            "commands_executed": [],
            "build_attempts": 0,
            "successful_builds": 0,
            "component_implementations": {},
            "session_history": [],
            "session_id": self._generate_session_id()
        }
        
        # Load existing context if available
        self._load_context()
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID based on timestamp."""
        return f"session_{int(time.time())}_{os.getpid()}"
    
    def _load_context(self) -> None:
        """Load project context from disk if available."""
        try:
            context_file = os.path.join(self.project_dir, "project_executor_context.json")
            if os.path.exists(context_file):
                with open(context_file, "r", encoding="utf-8") as f:
                    stored_context = json.load(f)
                
                # Update our context with stored values while preserving current session info
                current_session = self.context["session_id"]
                session_history = self.context["session_history"]
                
                # Merge the stored context with our current context
                self.context.update(stored_context)
                
                # Ensure we maintain current session information
                self.context["session_id"] = current_session
                
                # Add the previous session to history if it's not already there
                if stored_context.get("session_id") and stored_context["session_id"] not in session_history:
                    self.context["session_history"].append(stored_context["session_id"])
                
                # Update last updated timestamp
                self.context["last_updated"] = time.time()
                
                # Load framework detection results
                if "framework_detection" in stored_context:
                    self.framework_detection = stored_context["framework_detection"]
                
                # Restore build errors if present
                if "build_errors" in stored_context:
                    self.build_errors = stored_context["build_errors"]
                
                logger.info(f"Loaded project context from {context_file}")
            else:
                logger.info(f"No previous context found for project {self.project_name}")
        except Exception as e:
            logger.error(f"Error loading project context: {e}")
    
    async def _save_context(self) -> None:
        """Save project context to disk."""
        try:
            # Create project directory if it doesn't exist
            os.makedirs(self.project_dir, exist_ok=True)
            
            # Update context before saving
            self.context["last_updated"] = time.time()
            self.context["framework"] = self.framework_detection.get("framework", "unknown")
            self.context["framework_detection"] = self.framework_detection
            self.context["build_errors"] = self.build_errors
            
            # Save to file
            context_file = os.path.join(self.project_dir, "project_executor_context.json")
            with open(context_file, "w", encoding="utf-8") as f:
                json.dump(self.context, f, indent=2)
            
            logger.info(f"Saved project context to {context_file}")
        except Exception as e:
            logger.error(f"Error saving project context: {e}")
    
    def update_context(self, key: str, value: Any) -> None:
        """
        Update a specific key in the context.
        
        Args:
            key: The context key to update
            value: The value to set
        """
        self.context[key] = value
        self.context["last_updated"] = time.time()
        
        # Automatically save context after updates to ensure we don't lose information
        asyncio.create_task(self._save_context())
    
    def get_context(self, key: str = None) -> Any:
        """
        Get a value from the context or the entire context if no key is specified.
        
        Args:
            key: The context key to retrieve (optional)
            
        Returns:
            The value associated with the key or the entire context dict
        """
        if key is None:
            return self.context
        return self.context.get(key)
    
    def track_file_creation(self, file_path: str, content_length: int) -> None:
        """
        Track file creation in the context.
        
        Args:
            file_path: Path to the created file (relative to project directory)
            content_length: Length of the file content
        """
        self.context.setdefault("files_created", []).append({
            "path": file_path,
            "created_at": time.time(),
            "size": content_length,
            "session_id": self.context["session_id"]
        })
        asyncio.create_task(self._save_context())
    
    def track_command_execution(self, command: str, success: bool, output: str = None) -> None:
        """
        Track command execution in the context.
        
        Args:
            command: The command that was executed
            success: Whether the command execution was successful
            output: Command output (truncated if too large)
        """
        # Truncate large outputs to avoid context bloat
        if output and len(output) > 1000:
            output = output[:500] + "..." + output[-500:]
            
        self.context.setdefault("commands_executed", []).append({
            "command": command,
            "executed_at": time.time(),
            "success": success,
            "output": output,
            "session_id": self.context["session_id"]
        })
        asyncio.create_task(self._save_context())
    
    def track_build_attempt(self, success: bool, errors: List[Dict[str, Any]] = None) -> None:
        """
        Track build attempts in the context.
        
        Args:
            success: Whether the build was successful
            errors: List of build errors if any
        """
        self.context["build_attempts"] = self.context.get("build_attempts", 0) + 1
        if success:
            self.context["successful_builds"] = self.context.get("successful_builds", 0) + 1
        
        if errors:
            self.context.setdefault("build_history", []).append({
                "timestamp": time.time(),
                "success": success,
                "errors": errors,
                "session_id": self.context["session_id"]
            })
            
        asyncio.create_task(self._save_context())
    
    def track_component_implementation(self, component_name: str, files: List[str], success: bool) -> None:
        """
        Track component implementation in the context.
        
        Args:
            component_name: Name of the component
            files: List of files associated with the component
            success: Whether the implementation was successful
        """
        self.context.setdefault("component_implementations", {})[component_name] = {
            "implemented_at": time.time(),
            "files": files,
            "success": success,
            "session_id": self.context["session_id"]
        }
        asyncio.create_task(self._save_context())
    
    async def initialize(self) -> Dict[str, Any]:
        """
        Initialize the project directory structure if it doesn't exist.
        Fix any structural issues if found.
        
        Returns:
            Result dictionary with status information
        """
        logger.info(f"Initializing project: {self.project_name}")
        
        # Create main project directory if it doesn't exist
        os.makedirs(self.project_dir, exist_ok=True)
        
        # Import the utility here to avoid circular imports
        try:
            from ..project_utils import fix_nested_project_structure
            
            # Check and fix project structure issues
            fixes = fix_nested_project_structure(self.project_dir)
            if fixes.get("total_fixes", 0) > 0:
                logger.info(f"Fixed {fixes['total_fixes']} project structure issues")
                
                if fixes.get("nested_folders_fixed", 0) > 0:
                    await emit_terminal_command(self.project_name, f"Fixed {fixes['nested_folders_fixed']} nested folder issues")
                    
                if fixes.get("duplicate_memory_files_fixed", 0) > 0:
                    await emit_terminal_command(self.project_name, f"Fixed {fixes['duplicate_memory_files_fixed']} duplicate memory file issues")
                    
                if fixes.get("src_structure_fixed", 0) > 0:
                    await emit_terminal_command(self.project_name, f"Fixed {fixes['src_structure_fixed']} src directory structure issues")
        except ImportError:
            logger.warning("Could not import fix_nested_project_structure utility")
        except Exception as e:
            logger.error(f"Error checking project structure: {e}")
        
        # Detect framework in this project
        self.framework_detection = self._detect_framework()
        
        # Create initial project structure
        created_dirs = []
        
        # Common directories for all project types
        common_dirs = ["src", "docs", "tests"]
        for dir_name in common_dirs:
            dir_path = os.path.join(self.project_dir, dir_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                created_dirs.append(dir_path)
        
        # Check for a README.md file and create if it doesn't exist
        readme_path = os.path.join(self.project_dir, "README.md")
        if not os.path.exists(readme_path):
            with open(readme_path, "w") as f:
                f.write(f"# {self.project_name}\n\nThis project was created with Autonomous AI.\n")
        
        self.initialized = True
        
        return {
            "success": True,
            "message": f"Initialized project directory structure for {self.project_name}",
            "created_dirs": created_dirs,
            "framework": self.framework_detection.get("framework", "unknown")
        }
    
    async def _create_basic_structure(self) -> None:
        """
        Create a basic project structure with common directories.
        """
        # Default directories for all projects
        base_directories = [
            "src",
            "docs",
            "tests",
            "assets"
        ]
        
        # Create base directories
        for directory in base_directories:
            dir_path = os.path.join(self.project_dir, directory)
            os.makedirs(dir_path, exist_ok=True)
        
        # Detect framework type from existing files
        if os.path.exists(os.path.join(self.project_dir, "package.json")):
            try:
                with open(os.path.join(self.project_dir, "package.json"), "r") as f:
                    package_data = json.load(f)
                    
                # Get dependencies to detect framework
                dependencies = {
                    **package_data.get("dependencies", {}),
                    **package_data.get("devDependencies", {})
                }
                
                # Angular project structure
                if "@angular/core" in dependencies:
                    self.framework_detection["is_angular"] = True
                    self.framework_detection["framework"] = "angular"
                    
                    # Create Angular-specific directories
                    angular_dirs = [
                        "src/app",
                        "src/app/components",
                        "src/app/services",
                        "src/app/models",
                        "src/app/guards",
                        "src/app/interfaces",
                        "src/assets",
                        "src/environments"
                    ]
                    for directory in angular_dirs:
                        dir_path = os.path.join(self.project_dir, directory)
                        os.makedirs(dir_path, exist_ok=True)
                    
                # React project structure
                elif "react" in dependencies or "react-dom" in dependencies:
                    self.framework_detection["is_react"] = True
                    self.framework_detection["framework"] = "react"
                    
                    # Create React-specific directories
                    react_dirs = [
                        "src/components",
                        "src/containers",
                        "src/services",
                        "src/utils",
                        "src/hooks",
                        "src/context",
                        "src/assets",
                        "public"
                    ]
                    for directory in react_dirs:
                        dir_path = os.path.join(self.project_dir, directory)
                        os.makedirs(dir_path, exist_ok=True)
                    
                # Vue project structure
                elif "vue" in dependencies:
                    self.framework_detection["is_vue"] = True
                    self.framework_detection["framework"] = "vue"
                    
                    # Create Vue-specific directories
                    vue_dirs = [
                        "src/components",
                        "src/views",
                        "src/services",
                        "src/store",
                        "src/router",
                        "src/assets",
                        "public"
                    ]
                    for directory in vue_dirs:
                        dir_path = os.path.join(self.project_dir, directory)
                        os.makedirs(dir_path, exist_ok=True)
                
                # Node/Express project
                elif "express" in dependencies:
                    self.framework_detection["is_node"] = True
                    self.framework_detection["is_express"] = True
                    self.framework_detection["framework"] = "express"
                    
                    # Create Express-specific directories
                    express_dirs = [
                        "src/routes",
                        "src/controllers",
                        "src/models",
                        "src/middleware",
                        "src/utils",
                        "src/config",
                        "public",
                        "views"
                    ]
                    for directory in express_dirs:
                        dir_path = os.path.join(self.project_dir, directory)
                        os.makedirs(dir_path, exist_ok=True)
            except Exception as e:
                logger.warning(f"Error reading package.json to detect framework: {e}")
        
        # Python projects
        elif os.path.exists(os.path.join(self.project_dir, "requirements.txt")):
            self.framework_detection["is_python"] = True
            
            # Check for specific Python frameworks
            try:
                with open(os.path.join(self.project_dir, "requirements.txt"), "r") as f:
                    requirements = f.read().lower()
                    
                # Django structure
                if "django" in requirements:
                    self.framework_detection["is_django"] = True
                    self.framework_detection["framework"] = "django"
                    
                    # Create Django project dirs
                    django_dirs = [
                        "app",
                        "app/templates",
                        "app/static",
                        "app/static/css",
                        "app/static/js",
                        "app/static/images",
                        "app/models",
                        "app/views",
                        "app/urls",
                        "app/migrations"
                    ]
                    for directory in django_dirs:
                        dir_path = os.path.join(self.project_dir, directory)
                        os.makedirs(dir_path, exist_ok=True)
                
                # Flask structure
                elif "flask" in requirements:
                    self.framework_detection["is_flask"] = True
                    self.framework_detection["framework"] = "flask"
                    
                    # Create Flask project dirs
                    flask_dirs = [
                        "app",
                        "app/templates",
                        "app/static",
                        "app/static/css",
                        "app/static/js",
                        "app/static/images",
                        "app/models",
                        "app/views",
                        "app/utils"
                    ]
                    for directory in flask_dirs:
                        dir_path = os.path.join(self.project_dir, directory)
                        os.makedirs(dir_path, exist_ok=True)
                
                # FastAPI structure
                elif "fastapi" in requirements:
                    self.framework_detection["is_fastapi"] = True
                    self.framework_detection["framework"] = "fastapi"
                    
                    # Create FastAPI project dirs
                    fastapi_dirs = [
                        "app",
                        "app/routers",
                        "app/models",
                        "app/schemas",
                        "app/crud",
                        "app/utils",
                        "app/dependencies"
                    ]
                    for directory in fastapi_dirs:
                        dir_path = os.path.join(self.project_dir, directory)
                        os.makedirs(dir_path, exist_ok=True)
            except Exception as e:
                logger.warning(f"Error reading requirements.txt to detect framework: {e}")
        
        # Create a basic README.md if it doesn't exist
        readme_path = os.path.join(self.project_dir, "README.md")
        if not os.path.exists(readme_path):
            with open(readme_path, "w") as f:
                f.write(f"# {self.project_name}\n\nThis project was automatically generated.\n")
    
    def _ensure_path_inside_project(self, path: str) -> str:
        """
        Ensure that a file path is contained within the project directory.
        If the path tries to escape the project directory with '../', it will be sanitized.
        
        Args:
            path: The file path to check
            
        Returns:
            A sanitized version of the path that is guaranteed to be inside the project
        """
        # Clean the path first (resolve '..' and '.')
        target_path = os.path.abspath(os.path.join(self.project_dir, path))
        
        # Check if the path is within the project directory
        if not target_path.startswith(self.project_dir):
            logger.warning(f"Attempted to access path outside project directory: {path}")
            
            # Fallback to a safe path by keeping only the filename and placing it in the project root
            if os.path.basename(path):
                sanitized_path = os.path.join(self.project_dir, os.path.basename(path))
                logger.info(f"Sanitized path to: {sanitized_path}")
                return os.path.relpath(sanitized_path, self.project_dir)
            else:
                # No valid filename, return project root
                return ""
                
        # Return the relative path from the project directory
        return os.path.relpath(target_path, self.project_dir)
    
    async def _detect_frameworks(self) -> Dict[str, bool]:
        """
        Detect which frameworks and technologies are present in the project.
        
        Returns:
            Dictionary with framework detection results
        """
        logger.info(f"Detecting frameworks in project: {self.project_name}")
        
        # Check for package.json (Node.js projects)
        package_json_path = os.path.join(self.project_dir, "package.json")
        if os.path.exists(package_json_path):
            try:
                with open(package_json_path, "r") as f:
                    package_data = json.load(f)
                
                # Node.js detected
                self.framework_detection["is_node"] = True
                
                # Check dependencies for frameworks
                dependencies = {
                    **package_data.get("dependencies", {}),
                    **package_data.get("devDependencies", {})
                }
                
                if "@angular/core" in dependencies:
                    self.framework_detection["is_angular"] = True
                    self.framework_detection["framework"] = "angular"
                elif "react" in dependencies:
                    self.framework_detection["is_react"] = True
                    self.framework_detection["framework"] = "react"
                elif "vue" in dependencies:
                    self.framework_detection["is_vue"] = True
                    self.framework_detection["framework"] = "vue"
            except Exception as e:
                logger.error(f"Error reading package.json: {str(e)}")
        
        # Check for requirements.txt or setup.py (Python projects)
        if os.path.exists(os.path.join(self.project_dir, "requirements.txt")) or \
           os.path.exists(os.path.join(self.project_dir, "setup.py")):
            self.framework_detection["is_python"] = True
            self.framework_detection["framework"] = "python"
        
        logger.info(f"Framework detection results: {self.framework_detection}")
        return self.framework_detection
    
    async def create_angular_project(self, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create a new Angular project using the Angular CLI.
        
        Args:
            options: Additional options for the Angular CLI
            
        Returns:
            Dictionary with the results of the operation
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        style_preprocessor = options.get("style", "scss")
        routing = options.get("routing", True)
        strict = options.get("strict", True)
        skip_install = True  # Always skip install during initial creation
        
        # Check if Angular CLI is installed
        cli_check = await self.shell_executor.run_command("ng --version", timeout=10)
        
        if not cli_check["success"]:
            logger.warning("Angular CLI not found, attempting to install it globally")
            install_result = await self.shell_executor.run_command("npm install -g @angular/cli", timeout=120)
            if not install_result["success"]:
                return {
                    "success": False,
                    "message": "Failed to install Angular CLI",
                    "error": install_result["stderr"]
                }
        
        # To avoid the nested directory problem (Demo/Demo):
        # 1. Clean the project directory first
        if os.path.exists(self.project_dir):
            await emit_terminal_command(self.project_name, "Preparing project directory...")
            try:
                for item in os.listdir(self.project_dir):
                    item_path = os.path.join(self.project_dir, item)
                    if os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                    else:
                        os.remove(item_path)
            except Exception as e:
                logger.error(f"Error cleaning project directory: {e}")
        else:
            os.makedirs(self.project_dir, exist_ok=True)
        
        # 2. Create project directly in the parent directory to avoid nesting issues
        parent_dir = os.path.dirname(self.project_dir)

        logger.info(f"Creating Angular project in parent directory: {parent_dir}")

        # Create the command - let Angular CLI create the project folder
        cmd = f"ng new {self.project_name} --style={style_preprocessor} --skip-git=true"
        if routing:
            cmd += " --routing=true"
        if strict:
            cmd += " --strict=true"

        # Add all additional options
        for key, value in options.items():
            if key not in ["style", "routing", "strict", "skip-git"]:
                if isinstance(value, bool):
                    cmd += f" --{key}={'true' if value else 'false'}"
                else:
                    cmd += f" --{key}={value}"

        # Run the command in parent directory - Angular CLI will create the project folder
        await emit_terminal_command(self.project_name, f"Creating Angular project with command: {cmd}")
        result = await self.shell_executor.run_command(cmd, cwd=parent_dir, timeout=600)
        
        if not result["success"]:
            await emit_terminal_command(self.project_name, f"Failed to create Angular project: {result['stderr']}")
            return {
                "success": False,
                "message": "Failed to create Angular project",
                "error": result["stderr"]
            }

        # Project should be created directly in parent_dir/project_name (which is our target project_dir)
        if not os.path.exists(self.project_dir):
            await emit_terminal_command(self.project_name, f"Project files not found in expected location: {self.project_dir}")
            return {
                "success": False,
                "message": "Project files not found after creation",
                "error": "Angular CLI did not create the expected directory structure"
            }

        await emit_terminal_command(self.project_name, "Angular project structure created successfully!")
        
        # Update framework detection
        self.framework_detection["is_angular"] = True
        self.framework_detection["is_node"] = True
        self.framework_detection["framework"] = "angular"
        
        # IMPORTANT: Verify and fix Angular routing configuration
        await emit_terminal_command(self.project_name, "Verifying and fixing Angular routing configuration...")
        routing_result = await self.verify_and_fix_angular_routing()
        
        if routing_result["success"]:
            fixes = routing_result.get("fixes_applied", [])
            if fixes:
                await emit_terminal_command(self.project_name, f"Applied {len(fixes)} routing fixes: {', '.join(fixes)}")
        else:
            await emit_terminal_command(self.project_name, f"Warning: Could not verify routing configuration: {routing_result.get('error', 'unknown error')}")
        
        await emit_terminal_command(self.project_name, "Angular project created successfully!")
        
        # Note: We don't run npm install here - it will be run separately after all files are created
        return {
            "success": True,
            "message": "Angular project created successfully. Dependencies will be installed after all files are created.",
            "project_dir": self.project_dir,
            "stdout": result["stdout"],
            "framework": "angular",
            "needs_install": True,  # Flag to indicate installation is needed
            "routing_fixes": routing_result.get("fixes_applied", [])
        }
    
    async def finalize_project(self, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Finalize the project by installing dependencies and building the project.
        Should be called after all files are created.
        
        Args:
            options: Additional options for finalization
            
        Returns:
            Dictionary with the results of the operation
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        skip_build = options.get("skip_build", False)
        skip_validation = options.get("skip_validation", False)
        
        # First, ensure the project directory exists and we are in the right place
        if not os.path.exists(self.project_dir):
            return {
                "success": False,
                "message": "Project directory does not exist",
                "error": "Project directory not found"
            }
        
        # Run pre-build validation to ensure project structure is correct
        if not skip_validation:
            await emit_terminal_command(self.project_name, "Running pre-build project structure validation...")
            validation_result = await self.pre_build_validation()
            
            if validation_result.get("fixes_applied", []):
                fixes_applied = validation_result.get("fixes_applied", [])
                await emit_terminal_command(self.project_name, f"Applied {len(fixes_applied)} structure fixes before building")
                logger.info(f"Pre-build validation applied fixes: {fixes_applied}")
            else:
                await emit_terminal_command(self.project_name, "Pre-build validation completed with no issues found")
        
        # Check for package.json to confirm it's a Node.js project
        package_json_path = os.path.join(self.project_dir, "package.json")
        if not os.path.exists(package_json_path):
            return {
                "success": False,
                "message": "Not a Node.js project - package.json not found",
                "error": "package.json not found"
            }
        
        # Run npm install
        await emit_terminal_command(self.project_name, "Installing dependencies with npm install...")
        install_result = await self.shell_executor.run_command("npm install", cwd=self.project_dir, timeout=600)
        
        # Track command execution in context
        self.track_command_execution("npm install", install_result["success"], 
                                     install_result.get("stdout", "")[:200])
        
        if not install_result["success"]:
            await emit_terminal_command(self.project_name, f"Failed to install dependencies: {install_result['stderr']}")
            return {
                "success": False,
                "message": "Failed to install dependencies",
                "error": install_result["stderr"]
            }
        
        await emit_terminal_command(self.project_name, "Dependencies installed successfully!")
        
        # Run build if not skipped
        if not skip_build:
            # For Angular projects, always validate the routing configuration before building
            if self.framework_detection.get("is_angular", False):
                await emit_terminal_command(self.project_name, "Validating Angular routing configuration...")
                routing_result = await self.verify_and_fix_angular_routing()
                if routing_result.get("fixes_applied", []):
                    await emit_terminal_command(self.project_name, f"Fixed Angular routing configuration")
            
            build_cmd = "npm run build"
            if self.framework_detection.get("is_angular", False):
                build_cmd = "ng build"
            
            await emit_terminal_command(self.project_name, f"Building project with command: {build_cmd}")
            build_result = await self.shell_executor.run_command(build_cmd, cwd=self.project_dir, timeout=300)
            
            # Track command execution in context
            self.track_command_execution(build_cmd, build_result["success"], 
                                         build_result.get("stdout", "")[:200])
            
            if build_result["success"]:
                await emit_terminal_command(self.project_name, "Project built successfully!")
                # Track successful build
                self.track_build_attempt(True)
                
                return {
                    "success": True,
                    "message": "Project finalized successfully: dependencies installed and build completed",
                    "install_stdout": install_result["stdout"],
                    "build_stdout": build_result["stdout"]
                }
            else:
                await emit_terminal_command(self.project_name, f"Build failed: {build_result['stderr']}")
                # Parse build errors and attempt to fix them
                build_errors = self._parse_build_errors(build_result["stderr"], build_result["stdout"])
                
                # Track build failure with errors
                self.track_build_attempt(False, build_errors)
                
                if build_errors:
                    await emit_terminal_command(self.project_name, f"Found {len(build_errors)} errors to fix")
                    
                    # Attempt to fix the errors
                    fixed = await self._fix_build_errors(build_errors)
                    
                    if fixed:
                        # Run pre-build validation again after fixing errors
                        if not skip_validation:
                            await emit_terminal_command(self.project_name, "Running pre-build validation after fixing errors...")
                            await self.pre_build_validation()
                        
                        # Try building again
                        await emit_terminal_command(self.project_name, "Attempting to build again after fixes...")
                        retry_build_result = await self.shell_executor.run_command(build_cmd, cwd=self.project_dir, timeout=300)
                        
                        # Track retry build attempt
                        self.track_command_execution(build_cmd, retry_build_result["success"], 
                                                    "Retry build after fixes")
                        
                        if retry_build_result["success"]:
                            await emit_terminal_command(self.project_name, "Build succeeded after fixes!")
                            # Track successful build
                            self.track_build_attempt(True)
                            
                            return {
                                "success": True,
                                "message": "Project finalized successfully after fixing build errors",
                                "install_stdout": install_result["stdout"],
                                "build_stdout": retry_build_result["stdout"],
                                "fixed_errors": build_errors
                            }
                        else:
                            # Build still failed, try one more attempt with aggressive fixes
                            await emit_terminal_command(self.project_name, "Build still failing, trying more aggressive fixes...")
                            
                            # For Angular projects, run aggressive routing fixes
                            if self.framework_detection.get("is_angular", False):
                                await self._aggressive_router_outlet_fix()
                                # And validate folder structure again
                                await self.validate_angular_folder_structure()
                            
                            # One final build attempt
                            await emit_terminal_command(self.project_name, "Final build attempt after aggressive fixes...")
                            final_build_result = await self.shell_executor.run_command(build_cmd, cwd=self.project_dir, timeout=300)
                            
                            if final_build_result["success"]:
                                await emit_terminal_command(self.project_name, "Final build succeeded!")
                                return {
                                    "success": True,
                                    "message": "Project finalized successfully after aggressive fixes",
                                    "install_stdout": install_result["stdout"],
                                    "build_stdout": final_build_result["stdout"]
                                }
                
                return {
                    "success": False,
                    "message": "Dependencies installed but build failed",
                    "install_stdout": install_result["stdout"],
                    "build_error": build_result["stderr"],
                    "detected_errors": build_errors
                }
        
        return {
            "success": True,
            "message": "Project finalized successfully: dependencies installed",
            "install_stdout": install_result["stdout"]
        }
    
    def _parse_build_errors(self, stderr: str, stdout: str) -> List[Dict[str, Any]]:
        """
        Parse build errors from the build output.
        
        Args:
            stderr: Standard error output from the build process
            stdout: Standard output from the build process
            
        Returns:
            List of error dictionaries with file, line, column, and message
        """
        errors = []
        
        # Combine stderr and stdout for processing
        full_output = f"{stdout}\n{stderr}"
        
        # Angular error patterns
        angular_error_pattern = r'(Error:|ERROR:).*? ([\/\\][^\s:]+):(\d+):(\d+) - (error|Error) TS\d+: (.*?)(?:\n|$)'
        angular_matches = re.finditer(angular_error_pattern, full_output, re.MULTILINE)
        
        for match in angular_matches:
            file_path = match.group(2)
            line_num = int(match.group(3))
            col_num = int(match.group(4))
            error_msg = match.group(6)
            
            # Make the file path relative to the project directory
            rel_path = os.path.relpath(file_path, self.project_dir) if os.path.isabs(file_path) else file_path
            
            errors.append({
                "file": rel_path,
                "line": line_num,
                "column": col_num,
                "message": error_msg,
                "type": "typescript"
            })
        
        # React/JavaScript error patterns
        js_error_pattern = r'(?:ERROR|Error) in ([^:]+):(\d+):(\d+)[\s\n]+(.*?)(?:\n|$)'
        js_matches = re.finditer(js_error_pattern, full_output, re.MULTILINE)
        
        for match in js_matches:
            file_path = match.group(1)
            line_num = int(match.group(2))
            col_num = int(match.group(3))
            error_msg = match.group(4).strip()
            
            errors.append({
                "file": file_path,
                "line": line_num,
                "column": col_num,
                "message": error_msg,
                "type": "javascript"
            })
        
        # ESLint error patterns
        eslint_error_pattern = r'(.*?):(\d+):(\d+) - (error|warning) (.*?) \('
        eslint_matches = re.finditer(eslint_error_pattern, full_output, re.MULTILINE)
        
        for match in eslint_matches:
            file_path = match.group(1)
            line_num = int(match.group(2))
            col_num = int(match.group(3))
            error_type = match.group(4)
            error_msg = match.group(5)
            
            if error_type == "error":
                errors.append({
                    "file": file_path,
                    "line": line_num,
                    "column": col_num,
                    "message": error_msg,
                    "type": "eslint"
                })
        
        # Log found errors
        if errors:
            logger.info(f"Found {len(errors)} build errors to fix")
            for error in errors:
                logger.info(f"Error in {error['file']}:{error['line']} - {error['message']}")
        
        return errors
    
    async def _fix_build_errors(self, errors: List[Dict[str, Any]]) -> bool:
        """
        Attempt to fix build errors by analyzing the code and applying fixes.
        
        Args:
            errors: List of error dictionaries with file, line, column, and message
            
        Returns:
            True if any errors were fixed, False otherwise
        """
        any_fixed = False
        
        # First, let's categorize errors by type to prioritize fixes
        angular_errors = []
        router_errors = []
        import_errors = []
        type_errors = []
        other_errors = []
        
        for error in errors:
            # Check for Angular-specific NG errors
            if "NG" in error.get("message", ""):
                if "router-outlet" in error.get("message", "").lower() or "NG8001" in error.get("message", ""):
                    router_errors.append(error)
                else:
                    angular_errors.append(error)
            # Check for import errors
            elif "Cannot find module" in error.get("message", "") or "cannot be found" in error.get("message", ""):
                import_errors.append(error)
            # Check for type errors
            elif "Type" in error.get("message", "") and "is not assignable to type" in error.get("message", ""):
                type_errors.append(error)
            # Other errors
            else:
                other_errors.append(error)
        
        # Handle router-outlet errors first as they're most critical
        if router_errors:
            logger.info(f"Found {len(router_errors)} router-outlet errors to fix")
            router_fixed = await self._fix_router_outlet_errors(router_errors)
            if router_fixed:
                any_fixed = True
                await emit_terminal_command(self.project_name, f"Fixed router-outlet configuration issues")
        
        # Handle other Angular errors
        if angular_errors:
            logger.info(f"Found {len(angular_errors)} Angular-specific errors to fix")
            ng_fixed = await self._fix_angular_specific_errors(angular_errors)
            if ng_fixed:
                any_fixed = True
                await emit_terminal_command(self.project_name, f"Fixed Angular-specific errors")
        
        # Handle import errors
        if import_errors:
            logger.info(f"Found {len(import_errors)} import errors to fix")
            import_fixed = await self._fix_multiple_import_errors(import_errors)
            if import_fixed:
                any_fixed = True
                await emit_terminal_command(self.project_name, f"Fixed import errors")
        
        # Handle type errors
        if type_errors:
            logger.info(f"Found {len(type_errors)} type errors to fix")
            type_fixed = await self._fix_multiple_type_errors(type_errors)
            if type_fixed:
                any_fixed = True
                await emit_terminal_command(self.project_name, f"Fixed type errors")
        
        # Handle remaining errors
        for error in other_errors:
            file_path = os.path.join(self.project_dir, error["file"])
            
            # Skip if file doesn't exist
            if not os.path.exists(file_path):
                logger.warning(f"Error file not found: {file_path}")
                continue
            
            # Read the file content
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()
                    
                # Get the problematic code (a few lines around the error)
                lines = file_content.split('\n')
                start_line = max(0, error["line"] - 3)
                end_line = min(len(lines), error["line"] + 3)
                
                context_lines = lines[start_line:end_line]
                context = '\n'.join(context_lines)
                
                # Generate a fix using an external API or built-in logic
                fixed_code = await self._generate_code_fix(
                    file_path=file_path,
                    error=error,
                    code_context=context,
                    full_content=file_content
                )
                
                if fixed_code and fixed_code != file_content:
                    # Apply the fix
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_code)
                    
                    await emit_terminal_command(self.project_name, f"Fixed error in {error['file']}")
                    any_fixed = True
                    
            except Exception as e:
                logger.error(f"Error fixing file {file_path}: {e}")
        
        return any_fixed
    
    async def _fix_router_outlet_errors(self, errors: List[Dict[str, Any]]) -> bool:
        """
        Fix router-outlet related errors which are common in Angular projects.
        
        Args:
            errors: List of router-outlet related errors
            
        Returns:
            True if any errors were fixed, False otherwise
        """
        # Check if we've already verified and fixed routing
        if hasattr(self, '_routing_verified') and self._routing_verified:
            # We already verified routing, but still have errors - try more aggressive fixes
            return await self._aggressive_router_outlet_fix()
        
        # First, run our standard routing verification and fix
        routing_result = await self.verify_and_fix_angular_routing()
        
        # Mark that we've verified routing
        self._routing_verified = True
        
        if routing_result.get("success", False) and routing_result.get("fixes_applied", []):
            logger.info(f"Fixed routing with standard verification: {routing_result.get('fixes_applied')}")
            return True
        
        # If standard fix didn't work, try more targeted fixes for each error
        any_fixed = False
        
        for error in errors:
            error_message = error.get("message", "").lower()
            
            # Most common NG8001 error: 'router-outlet' is not a known element
            if "router-outlet" in error_message and ("is not a known element" in error_message or "is not a known component" in error_message):
                fixed = await self._fix_missing_router_outlet(error)
                if fixed:
                    any_fixed = True
            
            # NG1010: Router missing dependency for standalone component
            elif "ng1010" in error_message and "router" in error_message:
                fixed = await self._fix_router_standalone_dependency(error)
                if fixed:
                    any_fixed = True
            
            # NG9002: No matching provider found for RouterOutletContract
            elif "ng9002" in error_message and "routeroutletcontract" in error_message:
                fixed = await self._fix_router_outlet_contract(error)
                if fixed:
                    any_fixed = True
                    
            # Generic routing errors - try aggressive fix if targeted fixes didn't work
            elif "router" in error_message or "routing" in error_message:
                if not any_fixed:
                    fixed = await self._aggressive_router_outlet_fix()
                    if fixed:
                        any_fixed = True
        
        if not any_fixed:
            # If none of our targeted fixes worked, try the aggressive fix as a last resort
            any_fixed = await self._aggressive_router_outlet_fix()
        
        return any_fixed
    
    async def _fix_missing_router_outlet(self, error: Dict[str, Any]) -> bool:
        """
        Fix missing router-outlet errors by ensuring RouterModule or RouterOutlet is properly imported.
        
        Args:
            error: Error dictionary with file, line, column, and message
            
        Returns:
            True if fixed, False otherwise
        """
        file_path = os.path.join(self.project_dir, error["file"])
        
        # Skip if file doesn't exist
        if not os.path.exists(file_path):
            logger.warning(f"Cannot fix router-outlet error: file {file_path} doesn't exist")
            return False
        
        # Check if this is in a component file
        if not file_path.endswith('.ts') and not file_path.endswith('.html'):
            logger.warning(f"Cannot fix router-outlet error: file {file_path} is not a component file")
            return False
        
        # If it's a component .ts file, fix the imports
        if file_path.endswith('.ts'):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # More robust detection of standalone components
                is_standalone = bool(re.search(r'standalone\s*:\s*true', content, re.IGNORECASE))
                
                # For Angular 16+, also check for bootstrapApplication pattern
                is_bootstrap_application = 'bootstrapApplication' in content
                
                logger.info(f"Component in {file_path} is {'standalone' if is_standalone else 'module-based'}")
                
                if is_standalone or is_bootstrap_application:
                    # For standalone components, we need to import RouterOutlet 
                    if "RouterOutlet" not in content and "provideRouter" not in content:
                        # Add RouterOutlet to imports
                        new_content = content
                        
                        # First add the import at the top
                        if "import {" in new_content:
                            # Find a good spot to add the import
                            import_lines = re.findall(r'import\s+{[^}]+}\s+from\s+[\'"][^\'""]+[\'"];', new_content)
                            if import_lines:
                                last_import = import_lines[-1]
                                router_import = "\nimport { RouterOutlet } from '@angular/router';"
                                new_content = new_content.replace(last_import, last_import + router_import)
                            else:
                                # No import pattern found, add at the top
                                router_import = "import { RouterOutlet } from '@angular/router';\n"
                                new_content = router_import + new_content
                        else:
                            # No imports found, add at the top
                            router_import = "import { RouterOutlet } from '@angular/router';\n\n"
                            new_content = router_import + new_content
                        
                        # Then add RouterOutlet to imports array
                        imports_match = re.search(r'imports\s*:\s*\[(.*?)\]', new_content, re.DOTALL)
                        if imports_match:
                            imports_content = imports_match.group(1).strip()
                            if imports_content:
                                # Imports array not empty
                                last_char = imports_content[-1]
                                if last_char == ',':
                                    new_imports = imports_content + " RouterOutlet"
                                else:
                                    new_imports = imports_content + ", RouterOutlet"
                            else:
                                # Imports array empty
                                new_imports = "RouterOutlet"
                            
                            new_content = new_content.replace(imports_match.group(0), f"imports: [{new_imports}]")
                        else:
                            # Couldn't find imports array, try to add it to decorators
                            decorator_match = re.search(r'@Component\s*\(\s*{(.*?)}\s*\)', new_content, re.DOTALL)
                            if decorator_match:
                                decorator_content = decorator_match.group(1)
                                if 'imports:' not in decorator_content:
                                    # No imports found in decorator, add it
                                    if decorator_content.strip().endswith(','):
                                        decorator_with_imports = decorator_content + "\n  imports: [RouterOutlet]"
                                    else:
                                        decorator_with_imports = decorator_content + ",\n  imports: [RouterOutlet]"
                                    
                                    new_content = new_content.replace(decorator_match.group(1), decorator_with_imports)
                        
                        # Write the updated content
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(new_content)
                        
                        logger.info(f"Added RouterOutlet import and included it in imports array in {file_path}")
                        return True
                    else:
                        logger.info(f"RouterOutlet already properly imported in {file_path}")
                else:
                    # For module-based components, ensure RouterModule is imported in the module
                    # Find the module file
                    module_file = await self._find_module_for_component(file_path)
                    if module_file:
                        fixed = await self._add_router_module_to_module(module_file)
                        return fixed
                    else:
                        logger.warning(f"Could not find module file for component {file_path}")
                        # Try to add RouterModule directly to component as fallback
                        if "RouterModule" not in content:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            # Add RouterModule import
                            if "import {" in content:
                                import_lines = re.findall(r'import\s+{[^}]+}\s+from\s+[\'"][^\'""]+[\'"];', content)
                                if import_lines:
                                    last_import = import_lines[-1]
                                    router_import = "\nimport { RouterModule } from '@angular/router';"
                                    content = content.replace(last_import, last_import + router_import)
                                
                                # If there's an imports array in the component's NgModule, add RouterModule
                                ngmodule_match = re.search(r'@NgModule\s*\(\s*{(.*?)}\s*\)', content, re.DOTALL)
                                if ngmodule_match:
                                    ngmodule_content = ngmodule_match.group(1)
                                    imports_match = re.search(r'imports\s*:\s*\[(.*?)\]', ngmodule_content, re.DOTALL)
                                    if imports_match:
                                        imports_content = imports_match.group(1).strip()
                                        if imports_content:
                                            # Check if RouterModule is already there
                                            if "RouterModule" not in imports_content:
                                                last_char = imports_content[-1]
                                                if last_char == ',':
                                                    new_imports = imports_content + " RouterModule"
                                                else:
                                                    new_imports = imports_content + ", RouterModule"
                                                content = content.replace(imports_match.group(0), f"imports: [{new_imports}]")
                                
                                with open(file_path, 'w', encoding='utf-8') as f:
                                    f.write(content)
                                
                                logger.info(f"Added RouterModule import directly to component {file_path}")
                                return True
            
            except Exception as e:
                logger.error(f"Error fixing router-outlet in TS file {file_path}: {e}")
                import traceback
                logger.error(traceback.format_exc())
                return False
        
        # If it's an HTML file, check if the router-outlet tag is used correctly
        elif file_path.endswith('.html'):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check if router-outlet is self-closing or not
                if "<router-outlet>" in content and "</router-outlet>" not in content:
                    # It should be self-closing
                    new_content = content.replace("<router-outlet>", "<router-outlet></router-outlet>")
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    logger.info(f"Fixed router-outlet tag in {file_path}")
                    return True
                
                # If router-outlet is missing, add it to app.component.html
                if "app.component.html" in file_path and "<router-outlet" not in content:
                    # Add router-outlet to the file
                    if content.strip():
                        new_content = content + "\n\n<router-outlet></router-outlet>\n"
                    else:
                        new_content = "<router-outlet></router-outlet>\n"
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    logger.info(f"Added router-outlet tag to {file_path}")
                    return True
            
            except Exception as e:
                logger.error(f"Error fixing router-outlet in HTML file {file_path}: {e}")
                import traceback
                logger.error(traceback.format_exc())
                return False
        
        return False
    
    async def _fix_router_outlet_contract(self, error: Dict[str, Any]) -> bool:
        """
        Fix RouterOutletContract provider errors which can occur in newer Angular versions.
        
        Args:
            error: Error dictionary with file, line, column, and message
            
        Returns:
            True if fixed, False otherwise
        """
        file_path = os.path.join(self.project_dir, error["file"])
        
        # Skip if file doesn't exist
        if not os.path.exists(file_path):
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if we're dealing with main.ts bootstrap application pattern
            if 'bootstrapApplication' in content:
                # Need to add routerOutletProvider
                if 'RouterOutletContract' not in content and 'provideRouter' not in content:
                    # Add provideRouter import
                    new_content = content
                    
                    # Add import at the top
                    if "import {" in new_content:
                        import_lines = re.findall(r'import\s+{[^}]+}\s+from\s+[\'"][^\'""]+[\'"];', new_content)
                        if import_lines:
                            last_import = import_lines[-1]
                            router_import = "\nimport { provideRouter } from '@angular/router';"
                            new_content = new_content.replace(last_import, last_import + router_import)
                    else:
                        # No imports found, add at the top
                        router_import = "import { provideRouter } from '@angular/router';\n\n"
                        new_content = router_import + new_content
                    
                    # Add provideRouter to the bootstrap providers
                    providers_match = re.search(r'providers\s*:\s*\[(.*?)\]', new_content, re.DOTALL)
                    if providers_match:
                        providers_content = providers_match.group(1).strip()
                        if providers_content:
                            # Providers array not empty
                            last_char = providers_content[-1]
                            if last_char == ',':
                                new_providers = providers_content + " provideRouter([])"
                            else:
                                new_providers = providers_content + ", provideRouter([])"
                        else:
                            # Providers array empty
                            new_providers = "provideRouter([])"
                        
                        new_content = new_content.replace(providers_match.group(0), f"providers: [{new_providers}]")
                    else:
                        # No providers array found, add it to bootstrapApplication
                        bootstrap_pattern = r'bootstrapApplication\s*\(\s*(.*?),\s*{(.*?)}\s*\)'
                        bootstrap_match = re.search(bootstrap_pattern, new_content, re.DOTALL)
                        
                        if bootstrap_match:
                            app_component = bootstrap_match.group(1)
                            bootstrap_options = bootstrap_match.group(2)
                            
                            if 'providers' not in bootstrap_options:
                                # No providers property, add it
                                if bootstrap_options.strip():
                                    if bootstrap_options.strip().endswith(','):
                                        bootstrap_with_providers = bootstrap_options + "\n  providers: [provideRouter([])]"
                                    else:
                                        bootstrap_with_providers = bootstrap_options + ",\n  providers: [provideRouter([])]"
                                else:
                                    bootstrap_with_providers = "  providers: [provideRouter([])]"
                                
                                bootstrap_replacement = f"bootstrapApplication({app_component}, {{{bootstrap_with_providers}}})"
                                new_content = re.sub(bootstrap_pattern, bootstrap_replacement, new_content, flags=re.DOTALL)
                        else:
                            # No bootstrap options found, add basic options
                            bootstrap_basic_pattern = r'bootstrapApplication\s*\(\s*(.*?)\s*\)'
                            bootstrap_basic_match = re.search(bootstrap_basic_pattern, new_content, re.DOTALL)
                            
                            if bootstrap_basic_match:
                                app_component = bootstrap_basic_match.group(1)
                                bootstrap_replacement = f"bootstrapApplication({app_component}, {{\n  providers: [provideRouter([])]\n}})"
                                new_content = re.sub(bootstrap_basic_pattern, bootstrap_replacement, new_content, flags=re.DOTALL)
                    
                    # Write the updated content
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    logger.info(f"Added provideRouter to bootstrap providers in {file_path}")
                    return True
            
            # Check for app.module.ts and ensure it has RouterModule
            if 'app.module.ts' in file_path:
                if 'RouterModule' not in content:
                    # Add RouterModule import
                    if "import {" in content:
                        import_lines = re.findall(r'import\s+{[^}]+}\s+from\s+[\'"][^\'""]+[\'"];', content)
                        if import_lines:
                            last_import = import_lines[-1]
                            router_import = "\nimport { RouterModule } from '@angular/router';"
                            content = content.replace(last_import, last_import + router_import)
                    else:
                        # No imports found, add at the top
                        router_import = "import { RouterModule } from '@angular/router';\n\n"
                        content = router_import + content
                    
                    # Add to imports array
                    imports_match = re.search(r'imports\s*:\s*\[(.*?)\]', content, re.DOTALL)
                    if imports_match:
                        imports_content = imports_match.group(1).strip()
                        if imports_content:
                            # Imports array not empty
                            last_char = imports_content[-1]
                            if last_char == ',':
                                new_imports = imports_content + " RouterModule.forRoot([])"
                            else:
                                new_imports = imports_content + ", RouterModule.forRoot([])"
                        else:
                            # Imports array empty
                            new_imports = "RouterModule.forRoot([])"
                        
                        content = content.replace(imports_match.group(0), f"imports: [{new_imports}]")
                    
                    # Write the updated content
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    logger.info(f"Added RouterModule to imports in {file_path}")
                    return True
            
            return False
                
        except Exception as e:
            logger.error(f"Error fixing RouterOutletContract in {file_path}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    async def _find_module_for_component(self, component_file: str) -> Optional[str]:
        """
        Find the module file that declares a component.
        
        Args:
            component_file: Path to the component file
            
        Returns:
            Path to the module file or None if not found
        """
        # Extract component name from file path
        component_name = os.path.basename(component_file)
        component_name = component_name.replace(".component.ts", "")
        
        # Look for potential module files
        dir_path = os.path.dirname(component_file)
        
        # Check if there's a module in the same directory
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            module_files = [f for f in os.listdir(dir_path) if f.endswith('.module.ts')]
            if module_files:
                return os.path.join(dir_path, module_files[0])
        
        # If not, check parent directory
        parent_dir = os.path.dirname(dir_path)
        if os.path.exists(parent_dir) and os.path.isdir(parent_dir):
            module_files = [f for f in os.listdir(parent_dir) if f.endswith('.module.ts')]
            if module_files:
                return os.path.join(parent_dir, module_files[0])
        
        # Check two levels up (for feature modules)
        grandparent_dir = os.path.dirname(parent_dir)
        if os.path.exists(grandparent_dir) and os.path.isdir(grandparent_dir):
            module_files = [f for f in os.listdir(grandparent_dir) if f.endswith('.module.ts')]
            if module_files:
                return os.path.join(grandparent_dir, module_files[0])
        
        # If we still haven't found it, look for app.module.ts
        # First check if we're in a nested src structure
        nested_project_dir = os.path.join(self.project_dir, self.project_name)
        nested_src_dir = os.path.join(nested_project_dir, "src")
        
        if os.path.exists(nested_src_dir) and os.path.isdir(nested_src_dir):
            app_module_path = os.path.join(nested_src_dir, 'app', 'app.module.ts')
            if os.path.exists(app_module_path):
                return app_module_path
        
        # Otherwise check the root src directory
        app_module_path = os.path.join(self.project_dir, 'src', 'app', 'app.module.ts')
        if os.path.exists(app_module_path):
            return app_module_path
            
        # Last resort: search for any module.ts file in the project
        logger.info(f"Searching for any module.ts file for component {component_name}")
        for root, _, files in os.walk(self.project_dir):
            for file in files:
                if file.endswith('.module.ts'):
                    return os.path.join(root, file)
        
        logger.warning(f"Could not find any module file for component {component_name}")
        return None
    
    async def _add_router_module_to_module(self, module_file: str) -> bool:
        """
        Add RouterModule to an Angular module file.
        
        Args:
            module_file: Path to the module file
            
        Returns:
            True if the module was updated, False otherwise
        """
        logger.info(f"Adding RouterModule to module file: {module_file}")
        try:
            with open(module_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if RouterModule is already imported
            if "RouterModule" in content:
                # Check if it's in the imports array
                imports_section = content.split("imports")[1].split("]")[0] if "imports" in content else ""
                if "RouterModule" not in imports_section:
                    # RouterModule is imported but not in the imports array, add it
                    imports_match = re.search(r'imports\s*:\s*\[(.*?)\]', content, re.DOTALL)
                    if imports_match:
                        imports_content = imports_match.group(1).strip()
                        if imports_content:
                            # Imports array not empty, add a comma if needed
                            last_char = imports_content[-1]
                            if last_char == ',':
                                new_imports = imports_content + " RouterModule.forRoot([])"
                            else:
                                new_imports = imports_content + ", RouterModule.forRoot([])"
                        else:
                            # Imports array empty
                            new_imports = "RouterModule.forRoot([])"
                        
                        content = content.replace(imports_match.group(0), f"imports: [{new_imports}]")
                        
                        with open(module_file, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        logger.info(f"Added RouterModule.forRoot to imports array in {module_file}")
                        return True
                
                logger.info(f"RouterModule already properly imported in {module_file}")
                return False  # Already properly imported
            
            # RouterModule not imported, add it
            # Add import at the top
            if "import {" in content:
                # Find a good spot to add the import
                import_lines = re.findall(r'import\s+{[^}]+}\s+from\s+[\'"][^\'""]+[\'"];', content)
                if import_lines:
                    last_import = import_lines[-1]
                    router_import = "\nimport { RouterModule } from '@angular/router';"
                    content = content.replace(last_import, last_import + router_import)
                else:
                    # No imports found, add at the top
                    router_import = "import { RouterModule } from '@angular/router';\n\n"
                    content = router_import + content
            else:
                # No imports found, add at the top
                router_import = "import { RouterModule } from '@angular/router';\n\n"
                content = router_import + content
            
            # Add RouterModule to imports array
            imports_match = re.search(r'imports\s*:\s*\[(.*?)\]', content, re.DOTALL)
            if imports_match:
                imports_content = imports_match.group(1).strip()
                if imports_content:
                    # Imports array not empty
                    last_char = imports_content[-1]
                    if last_char == ',':
                        new_imports = imports_content + " RouterModule.forRoot([])"
                    else:
                        new_imports = imports_content + ", RouterModule.forRoot([])"
                else:
                    # Imports array empty
                    new_imports = "RouterModule.forRoot([])"
                
                content = content.replace(imports_match.group(0), f"imports: [{new_imports}]")
            else:
                logger.warning(f"Could not find imports array in module file {module_file}")
                # Try to find the NgModule decorator and add imports
                ngmodule_match = re.search(r'@NgModule\s*\(\s*{(.*?)}\s*\)', content, re.DOTALL)
                if ngmodule_match:
                    ngmodule_content = ngmodule_match.group(1)
                    if 'imports:' not in ngmodule_content:
                        # No imports found in decorator, add it
                        if ngmodule_content.strip().endswith(','):
                            ngmodule_with_imports = ngmodule_content + "\n  imports: [RouterModule.forRoot([])]"
                        else:
                            ngmodule_with_imports = ngmodule_content + ",\n  imports: [RouterModule.forRoot([])]"
                        
                        content = content.replace(ngmodule_match.group(1), ngmodule_with_imports)
            
            # Write the updated content
            with open(module_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"Added RouterModule import and imports array in {module_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding RouterModule to module file {module_file}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    async def _fix_router_standalone_dependency(self, error: Dict[str, Any]) -> bool:
        """
        Fix router dependency issues in standalone components.
        
        Args:
            error: Error dictionary with file, line, column, and message
            
        Returns:
            True if fixed, False otherwise
        """
        file_path = os.path.join(self.project_dir, error["file"])
        
        # Skip if file doesn't exist
        if not os.path.exists(file_path):
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if this is a standalone component
            is_standalone = "standalone: true" in content.lower()
            
            if not is_standalone:
                return False
            
            # Add RouterOutlet to imports
            if "RouterOutlet" not in content:
                # Add import at the top
                if "import {" in content:
                    # Find a good spot to add the import
                    import_lines = re.findall(r'import\s+{[^}]+}\s+from\s+[\'"][^\'""]+[\'"];', content)
                    if import_lines:
                        last_import = import_lines[-1]
                        router_import = "\nimport { RouterOutlet } from '@angular/router';"
                        content = content.replace(last_import, last_import + router_import)
                else:
                    # No imports found, add at the top
                    router_import = "import { RouterOutlet } from '@angular/router';\n\n"
                    content = router_import + content
                
                # Add RouterOutlet to imports array
                if "imports: [" in content:
                    # Find the imports array and add RouterOutlet
                    imports_pattern = r'imports:\s*\[(.*?)\]'
                    imports_match = re.search(imports_pattern, content, re.DOTALL)
                    if imports_match:
                        imports_content = imports_match.group(1)
                        if imports_content.strip():
                            # Imports array not empty, add a comma if needed
                            if not imports_content.strip().endswith(','):
                                new_imports = imports_content + ", RouterOutlet"
                            else:
                                new_imports = imports_content + " RouterOutlet"
                        else:
                            # Imports array empty
                            new_imports = "RouterOutlet"
                        
                        content = content.replace(imports_match.group(0), f"imports: [{new_imports}]")
                
                # Write the updated content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                return True
                
        except Exception as e:
            logger.error(f"Error fixing router standalone dependency in {file_path}: {e}")
            return False
        
        return False
    
    async def _aggressive_router_outlet_fix(self) -> bool:
        """
        Apply aggressive fixes for router-outlet errors when standard fixes fail.
        
        Returns:
            True if any fixes were applied, False otherwise
        """
        any_fixed = False
        
        # 1. First, make sure app.component.html has router-outlet
        app_component_html = os.path.join(self.project_dir, 'src', 'app', 'app.component.html')
        if os.path.exists(app_component_html):
            try:
                with open(app_component_html, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "<router-outlet" not in content:
                    # Add router-outlet to the file
                    new_content = content.strip() + "\n\n<router-outlet></router-outlet>\n"
                    
                    with open(app_component_html, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    any_fixed = True
                    logger.info("Added router-outlet to app.component.html")
            except Exception as e:
                logger.error(f"Error updating app.component.html: {e}")
        
        # 2. Add a routes file if it doesn't exist
        app_routing_module = os.path.join(self.project_dir, 'src', 'app', 'app-routing.module.ts')
        if not os.path.exists(app_routing_module):
            try:
                # Create a basic routing module
                routing_content = """import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  // Define routes here
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  { path: '**', redirectTo: '/home' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
"""
                os.makedirs(os.path.dirname(app_routing_module), exist_ok=True)
                with open(app_routing_module, 'w', encoding='utf-8') as f:
                    f.write(routing_content)
                
                any_fixed = True
                logger.info("Created app-routing.module.ts")
            except Exception as e:
                logger.error(f"Error creating app-routing.module.ts: {e}")
        
        # 3. Make sure AppRoutingModule is imported in AppModule
        app_module = os.path.join(self.project_dir, 'src', 'app', 'app.module.ts')
        if os.path.exists(app_module):
            try:
                with open(app_module, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "AppRoutingModule" not in content:
                    # Add import
                    if "import {" in content:
                        # Find a good spot to add the import
                        import_lines = re.findall(r'import\s+{[^}]+}\s+from\s+[\'"][^\'""]+[\'"];', content)
                        if import_lines:
                            last_import = import_lines[-1]
                            routing_import = "\nimport { AppRoutingModule } from './app-routing.module';"
                            content = content.replace(last_import, last_import + routing_import)
                    else:
                        # No imports found, add at the top
                        routing_import = "import { AppRoutingModule } from './app-routing.module';\n\n"
                        content = routing_import + content
                    
                    # Add to imports array
                    if "imports" in content:
                        imports_pattern = r'imports:\s*\[(.*?)\]'
                        imports_match = re.search(imports_pattern, content, re.DOTALL)
                        if imports_match:
                            imports_content = imports_match.group(1)
                            if imports_content.strip():
                                if not imports_content.strip().endswith(','):
                                    new_imports = imports_content + ", AppRoutingModule"
                                else:
                                    new_imports = imports_content + " AppRoutingModule"
                            else:
                                new_imports = "AppRoutingModule"
                            
                            content = content.replace(imports_match.group(0), f"imports: [{new_imports}]")
                    
                    # Write the changes
                    with open(app_module, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    any_fixed = True
                    logger.info("Added AppRoutingModule to app.module.ts")
            except Exception as e:
                logger.error(f"Error updating app.module.ts: {e}")
        
        return any_fixed
    
    async def create_react_project(self, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create a new React project using Create React App or Vite.
        
        Args:
            options: Additional options for project creation
            
        Returns:
            Dictionary with the results of the operation
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        use_typescript = options.get("typescript", True)
        use_vite = options.get("vite", True)  # Default to Vite for modern projects
        
        # Move up to parent directory to run create commands
        parent_dir = os.path.dirname(self.project_dir)
        
        if use_vite:
            template = "react-ts" if use_typescript else "react"
            cmd = f"npm create vite@latest {self.project_name} -- --template {template}"
        else:
            template = "--template typescript" if use_typescript else ""
            cmd = f"npx create-react-app {self.project_name} {template}"
        
        # Run the command
        await emit_terminal_command(self.project_name, f"Creating React project with command: {cmd}")
        result = await self.shell_executor.run_command(cmd, cwd=parent_dir, timeout=300)
        
        if result["success"]:
            await emit_terminal_command(self.project_name, "React project created successfully!")
            
            # Update framework detection
            self.framework_detection["is_react"] = True
            self.framework_detection["is_node"] = True
            self.framework_detection["framework"] = "react"
            
            return {
                "success": True,
                "message": "React project created successfully. Dependencies will be installed during finalization.",
                "project_dir": self.project_dir,
                "stdout": result["stdout"],
                "framework": "react",
                "needs_install": True
            }
        else:
            await emit_terminal_command(self.project_name, f"Failed to create React project: {result['stderr']}")
            return {
                "success": False,
                "message": "Failed to create React project",
                "error": result["stderr"]
            }
    
    async def create_vue_project(self, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create a new Vue.js project using Vue CLI or create-vue.
        
        Args:
            options: Additional options for project creation
            
        Returns:
            Dictionary with the results of the operation
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        use_typescript = options.get("typescript", True)
        use_create_vue = options.get("create_vue", True)  # Default to create-vue (Vue 3)
        
        # Move up to parent directory to run create commands
        parent_dir = os.path.dirname(self.project_dir)
        
        if use_create_vue:
            cmd = f"npm create vue@latest {self.project_name}"
            # Vue CLI will interactively ask for options, so we need to handle that separately
            # For now, accept defaults for simplicity
            interactive = True
        else:
            # For Vue CLI, we can specify options directly
            typescript_flag = "--typescript" if use_typescript else ""
            cmd = f"npx @vue/cli create {self.project_name} {typescript_flag} --default"
            interactive = False
        
        # Run the command
        await emit_terminal_command(self.project_name, f"Creating Vue project with command: {cmd}")
        
        if interactive:
            # For interactive commands, we need special handling
            # This is a simplified version - real implementation would require proper interactive process handling
            result = await self.shell_executor.run_command(cmd, cwd=parent_dir, timeout=300)
        else:
            result = await self.shell_executor.run_command(cmd, cwd=parent_dir, timeout=300)
        
        if result["success"]:
            await emit_terminal_command(self.project_name, "Vue project created successfully!")
            
            # Install dependencies
            install_cmd = "npm install"
            await emit_terminal_command(self.project_name, "Installing dependencies...")
            await self.shell_executor.run_command(install_cmd, cwd=self.project_dir, timeout=300)
            
            # Update framework detection
            self.framework_detection["is_vue"] = True
            self.framework_detection["is_node"] = True
            self.framework_detection["framework"] = "vue"
            
            return {
                "success": True,
                "message": "Vue project created successfully",
                "project_dir": self.project_dir,
                "stdout": result["stdout"],
                "framework": "vue"
            }
        else:
            await emit_terminal_command(self.project_name, f"Failed to create Vue project: {result['stderr']}")
            return {
                "success": False,
                "message": "Failed to create Vue project",
                "error": result["stderr"]
            }
    
    async def create_python_project(self, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create a new Python project with virtual environment and basic structure.
        
        Args:
            options: Additional options for project creation
            
        Returns:
            Dictionary with the results of the operation
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        create_venv = options.get("create_venv", True)
        add_flask = options.get("flask", False)
        add_django = options.get("django", False)
        add_fastapi = options.get("fastapi", False)
        
        # Create Python project structure
        structure = [
            self.project_name,
            f"{self.project_name}/tests",
            f"{self.project_name}/docs"
        ]
        
        for directory in structure:
            os.makedirs(os.path.join(self.project_dir, directory), exist_ok=True)
        
        # Create __init__.py files
        with open(os.path.join(self.project_dir, self.project_name, "__init__.py"), "w") as f:
            f.write(f"""\"\"\"
{self.project_name} package.
\"\"\"
__version__ = "0.1.0"
""")
        
        with open(os.path.join(self.project_dir, self.project_name, "tests", "__init__.py"), "w") as f:
            f.write("")
        
        # Create setup.py
        with open(os.path.join(self.project_dir, "setup.py"), "w") as f:
            setup_py = f"""
from setuptools import setup, find_packages

setup(
    name="{self.project_name}",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
"""
            if add_flask:
                setup_py += '        "flask",\n'
            if add_django:
                setup_py += '        "django",\n'
            if add_fastapi:
                setup_py += '        "fastapi",\n'
                setup_py += '        "uvicorn",\n'
            
            setup_py += """    ],
    author="",
    author_email="",
    description="A Python project",
    keywords="",
    url="",
)
"""
            f.write(setup_py)
        
        # Create requirements.txt
        with open(os.path.join(self.project_dir, "requirements.txt"), "w") as f:
            reqs = []
            if add_flask:
                reqs.append("flask>=2.0.0")
            if add_django:
                reqs.append("django>=4.0.0")
            if add_fastapi:
                reqs.append("fastapi>=0.95.0")
                reqs.append("uvicorn>=0.21.0")
            
            f.write("\n".join(reqs))
        
        # Create a virtual environment if requested
        if create_venv:
            venv_cmd = "python -m venv venv"
            await emit_terminal_command(self.project_name, "Creating virtual environment...")
            venv_result = await self.shell_executor.run_command(venv_cmd, cwd=self.project_dir, timeout=60)
            
            if not venv_result["success"]:
                logger.warning(f"Failed to create virtual environment: {venv_result['stderr']}")
        
        # Update framework detection
        self.framework_detection["is_python"] = True
        self.framework_detection["framework"] = "python"
        if add_flask:
            self.framework_detection["framework"] = "flask"
        elif add_django:
            self.framework_detection["framework"] = "django"
        elif add_fastapi:
            self.framework_detection["framework"] = "fastapi"
        
        return {
            "success": True,
            "message": "Python project created successfully",
            "project_dir": self.project_dir,
            "framework": self.framework_detection["framework"]
        }
    
    async def generate_angular_component(self, component_name: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate an Angular component using Angular CLI or direct file creation if CLI is not available.
        
        Args:
            component_name: The name of the component to generate
            options: Optional component generation options
            
        Returns:
            Dictionary with generation results
        """
        if not self.initialized:
            await self.initialize()
            
        # Build the command with options
        options = options or {}
        cmd_options = []
        
        for key, value in options.items():
            if isinstance(value, bool):
                if value:
                    cmd_options.append(f"--{key}")
            else:
                cmd_options.append(f"--{key}={value}")
                
        cmd = f"ng generate component {component_name}"
        if cmd_options:
            cmd += " " + " ".join(cmd_options)
        
        # Run the command
        await emit_terminal_command(self.project_name, f"Generating Angular component: {component_name}")
        result = await self.shell_executor.run_command(cmd, cwd=self.project_dir, timeout=60)
        
        if result["success"]:
            await emit_terminal_command(self.project_name, f"Component {component_name} generated successfully!")
            return {
                "success": True,
                "message": f"Component {component_name} generated successfully",
                "stdout": result["stdout"]
            }
        else:
            # If the command failed, try direct file creation as fallback
            self.logger.info(f"CLI component generation failed, attempting direct file creation: {result['stderr']}")
            await emit_terminal_command(self.project_name, f"Using fallback method to create component files...")
            
            # Use a better structured prompt for the LLM
            component_prompt = f"""
Create the following Angular component files for component named '{component_name}':

1. {component_name}.component.ts
2. {component_name}.component.html
3. {component_name}.component.scss

For the TypeScript file, include proper imports, decorator, and implementation.
For the HTML template, create proper markup that includes component functionality.
For the SCSS file, include styles that make the component visually appealing.

Respond with the content of each file clearly labeled:

FILE: src/app/{component_name}/{component_name}.component.ts
```typescript
// TypeScript content here
```

FILE: src/app/{component_name}/{component_name}.component.html
```html
<!-- HTML content here -->
```

FILE: src/app/{component_name}/{component_name}.component.scss
```scss
/* SCSS content here */
```
"""
            try:
                # Generate component files using the LLM
                model_id = self.model_id or "deepseek/deepseek-coder"
                llm = LLM.create(model_id)
                response = await llm.generate(component_prompt, project_name=self.project_name)
                
                # Parse the response to extract file contents
                ts_content = None
                html_content = None
                scss_content = None
                
                # Improved file content extraction
                ts_match = re.search(r'FILE:.*?component\.ts\s*```(?:typescript)?\s*(.*?)\s*```', response, re.DOTALL)
                html_match = re.search(r'FILE:.*?component\.html\s*```(?:html)?\s*(.*?)\s*```', response, re.DOTALL)
                scss_match = re.search(r'FILE:.*?component\.scss\s*```(?:scss)?\s*(.*?)\s*```', response, re.DOTALL)
                
                if ts_match:
                    ts_content = ts_match.group(1).strip()
                if html_match:
                    html_content = html_match.group(1).strip()
                if scss_match:
                    scss_content = scss_match.group(1).strip()
                
                # Ensure the component directory exists
                component_dir = os.path.join(self.project_dir, 'src', 'app', component_name)
                os.makedirs(component_dir, exist_ok=True)
                
                # Create component files
                files_created = []
                success = False
                
                if ts_content:
                    ts_path = os.path.join(component_dir, f"{component_name}.component.ts")
                    with open(ts_path, 'w', encoding='utf-8') as f:
                        f.write(ts_content)
                    files_created.append(ts_path)
                    success = True
                
                if html_content:
                    html_path = os.path.join(component_dir, f"{component_name}.component.html")
                    with open(html_path, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    files_created.append(html_path)
                
                if scss_content:
                    scss_path = os.path.join(component_dir, f"{component_name}.component.scss")
                    with open(scss_path, 'w', encoding='utf-8') as f:
                        f.write(scss_content)
                    files_created.append(scss_path)
                
                # Generate default files if any are missing
                if not ts_content:
                    ts_path = os.path.join(component_dir, f"{component_name}.component.ts")
                    self._create_default_component_ts(component_name, ts_path)
                    files_created.append(ts_path)
                
                if not html_content:
                    html_path = os.path.join(component_dir, f"{component_name}.component.html")
                    self._create_default_component_html(component_name, html_path)
                    files_created.append(html_path)
                
                if not scss_content:
                    scss_path = os.path.join(component_dir, f"{component_name}.component.scss")
                    self._create_default_component_scss(component_name, scss_path)
                    files_created.append(scss_path)
                
                await emit_terminal_command(self.project_name, f"Component files created: {', '.join(files_created)}")
                
                # Also update the module file if it exists to declare the component
                self._update_app_module(component_name)
                
                return {
                    "success": True,
                    "message": f"Component {component_name} files created successfully with direct approach",
                    "files": files_created
                }
            except Exception as e:
                await emit_terminal_command(self.project_name, f"Error creating component files: {str(e)}")
                return {
                    "success": False,
                    "message": f"Failed to generate component {component_name}",
                    "error": str(e)
                }
        
    def _create_default_component_ts(self, component_name: str, file_path: str) -> None:
        """Create a default TypeScript component file."""
        pascal_name = ''.join(word.capitalize() for word in component_name.replace('-', ' ').split())
        if not pascal_name.endswith('Component'):
            pascal_name += 'Component'
            
        content = f"""import {{ Component, OnInit }} from '@angular/core';

@Component({{
  selector: 'app-{component_name}',
  templateUrl: './{component_name}.component.html',
  styleUrls: ['./{component_name}.component.scss']
}})
export class {pascal_name} implements OnInit {{
  
  constructor() {{ }}

  ngOnInit(): void {{
  }}

}}
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _create_default_component_html(self, component_name: str, file_path: str) -> None:
        """Create a default HTML template file."""
        display_name = ' '.join(word.capitalize() for word in component_name.replace('-', ' ').split())
        content = f"""<div class="{component_name}-container">
  <h2>{display_name}</h2>
  <p>This is the {display_name} component</p>
</div>
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _create_default_component_scss(self, component_name: str, file_path: str) -> None:
        """Create a default SCSS file."""
        content = f""".{component_name}-container {{
  padding: 20px;
  margin: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
  
  h2 {{
    margin-bottom: 10px;
    color: #333;
  }}
  
  p {{
    color: #666;
  }}
}}
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _update_app_module(self, component_name: str) -> None:
        """Update app.module.ts to include the new component."""
        try:
            # Find app.module.ts
            app_module_path = os.path.join(self.project_dir, 'src', 'app', 'app.module.ts')
            if not os.path.exists(app_module_path):
                return
            
            # Read existing content
            with open(app_module_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Create pascal case component name
            pascal_name = ''.join(word.capitalize() for word in component_name.replace('-', ' ').split())
            if not pascal_name.endswith('Component'):
                pascal_name += 'Component'
            
            # Check if component is already declared
            if pascal_name in content:
                return
            
            # Add import statement if not already present
            import_statement = f"import {{ {pascal_name} }} from './{component_name}/{component_name}.component';"
            if import_statement not in content:
                # Find the last import statement
                last_import = re.search(r'^import.*?;$', content, re.MULTILINE | re.DOTALL)
                if last_import:
                    pos = last_import.end()
                    content = content[:pos] + '\n' + import_statement + content[pos:]
                else:
                    content = import_statement + '\n' + content
            
            # Add to declarations array
            declarations_match = re.search(r'declarations\s*:\s*\[(.*?)\]', content, re.DOTALL)
            if declarations_match:
                declarations = declarations_match.group(1)
                if pascal_name not in declarations:
                    if declarations.strip():
                        # There are existing declarations
                        new_declarations = declarations.rstrip() + ',\n    ' + pascal_name 
                    else:
                        # Empty declarations array
                        new_declarations = '\n    ' + pascal_name + '\n  '
                    
                    content = content.replace(declarations, new_declarations)
            
            # Write updated content
            with open(app_module_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
        except Exception as e:
            self.logger.error(f"Error updating app.module.ts: {e}")
            # Continue without failing - this is a non-critical operation
    
    async def generate_angular_service(self, service_name: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate an Angular service using Angular CLI or direct file creation if CLI is not available.
        
        Args:
            service_name: The name of the service to generate
            options: Optional service generation options
            
        Returns:
            Dictionary with generation results
        """
        if not self.initialized:
            await self.initialize()
            
        options = options or {}
        cmd_options = []
        
        for key, value in options.items():
            if isinstance(value, bool):
                if value:
                    cmd_options.append(f"--{key}")
            else:
                cmd_options.append(f"--{key}={value}")
                
        cmd = f"ng generate service {service_name}"
        if cmd_options:
            cmd += " " + " ".join(cmd_options)
        
        # Run the command
        await emit_terminal_command(self.project_name, f"Generating Angular service: {service_name}")
        result = await self.shell_executor.run_command(cmd, cwd=self.project_dir, timeout=60)
        
        if result["success"]:
            await emit_terminal_command(self.project_name, f"Service {service_name} generated successfully!")
            return {
                "success": True,
                "message": f"Service {service_name} generated successfully",
                "stdout": result["stdout"]
            }
        else:
            # If the command failed, try direct file creation as fallback
            self.logger.info(f"CLI service generation failed, attempting direct file creation: {result['stderr']}")
            await emit_terminal_command(self.project_name, f"Using fallback method to create service files...")
            
            # Use a better structured prompt for the LLM
            service_prompt = f"""
Create an Angular service file for a service named '{service_name}'.

Include:
- Proper imports (Injectable, HttpClient if needed)
- Service class with appropriate methods
- Error handling
- RxJS usage where appropriate
- Proper typing

Respond with the content clearly labeled:

FILE: src/app/services/{service_name}.service.ts
```typescript
// TypeScript content here
```
"""
            try:
                # Generate service file using the LLM
                model_id = self.model_id or "deepseek/deepseek-coder"
                llm = LLM.create(model_id)
                response = await llm.generate(service_prompt, project_name=self.project_name)
                
                # Parse the response to extract file content
                service_match = re.search(r'FILE:.*?service\.ts\s*```(?:typescript)?\s*(.*?)\s*```', response, re.DOTALL)
                service_content = None
                
                if service_match:
                    service_content = service_match.group(1).strip()
                
                # Ensure the service directory exists
                service_dir = os.path.join(self.project_dir, 'src', 'app', 'services')
                os.makedirs(service_dir, exist_ok=True)
                
                # Create service file
                service_path = os.path.join(service_dir, f"{service_name}.service.ts")
                
                if service_content:
                    with open(service_path, 'w', encoding='utf-8') as f:
                        f.write(service_content)
                else:
                    # Create default service file if LLM generation failed
                    self._create_default_service_ts(service_name, service_path)
                
                await emit_terminal_command(self.project_name, f"Service file created: {service_path}")
                
                return {
                    "success": True,
                    "message": f"Service {service_name} file created successfully with direct approach",
                    "files": [service_path]
                }
            except Exception as e:
                await emit_terminal_command(self.project_name, f"Error creating service file: {str(e)}")
                return {
                    "success": False,
                    "message": f"Failed to generate service {service_name}",
                    "error": str(e)
                }
    
    def _create_default_service_ts(self, service_name: str, file_path: str) -> None:
        """Create a default Angular service file."""
        pascal_name = ''.join(word.capitalize() for word in service_name.replace('-', ' ').split())
        if not pascal_name.endswith('Service'):
            pascal_name += 'Service'
            
        content = f"""import {{ Injectable }} from '@angular/core';
import {{ HttpClient }} from '@angular/common/http';
import {{ Observable, throwError }} from 'rxjs';
import {{ catchError }} from 'rxjs/operators';

@Injectable({{
  providedIn: 'root'
}})
export class {pascal_name} {{
  
  constructor(private http: HttpClient) {{ }}
  
  getData(): Observable<any[]> {{
    return this.http.get<any[]>('/api/data')
      .pipe(
        catchError(this.handleError)
      );
  }}
  
  private handleError(error: any) {{
    console.error('An error occurred', error);
    return throwError(() => error);
  }}
}}
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    async def generate_components(self, component_specs: List[Dict[str, Any]], requirements: str = None) -> Dict[str, Any]:
        """
        Generate multiple components for a project based on specifications.
        
        Args:
            component_specs: List of component specifications with type, name, and options
            requirements: Optional requirements text for implementing functionality
            
        Returns:
            Dictionary with generation results
        """
        if not self.initialized:
            await self.initialize()
            
        results = []
        successful_components = []  # Keep track of successfully generated components
        
        # For each component, generate it and update the results
        for spec in component_specs:
            component_type = spec.get("type", "").lower()
            component_name = spec.get("name", "")
            
            if not component_name:
                continue
                
            try:
                # Generate the component
                if component_type == "angular-component":
                    result = await self.generate_angular_component(component_name, spec.get("options", {}))
                elif component_type == "angular-service":
                    result = await self.generate_angular_service(component_name, spec.get("options", {}))
                else:
                    result = {
                        "success": False,
                        "message": f"Unsupported component type: {component_type}",
                        "error": f"Unsupported component type: {component_type}"
                    }
                
                results.append(result)
                
                # If the component was generated successfully, add it to the list of successful components
                if result["success"]:
                    successful_components.append({
                        "type": component_type,
                        "name": component_name,
                        "options": spec.get("options", {})
                    })
            except Exception as e:
                logger.error(f"[ProjectExecutor] Error generating component {component_name}: {str(e)}")
                results.append({
                    "success": False,
                    "component": component_name,
                    "message": f"Error generating component: {str(e)}"
                })
        
        # Implement functionality for all generated components if requirements are provided
        if requirements and successful_components:
            implementation_result = await self.implement_component_functionality(successful_components, requirements)
            
            # Add implementation results to the response
            return {
                "success": any(r["success"] for r in results),
                "message": f"Generated {len(successful_components)} components successfully, {len(results) - len(successful_components)} failures, and implemented functionality based on requirements",
                "results": results,
                "implementation": implementation_result
            }
        
        return {
            "success": any(r["success"] for r in results),
            "message": f"Generated {len(successful_components)} components successfully, {len(results) - len(successful_components)} failures",
            "results": results
        }
    
    async def implement_component_functionality(self, component_specs, requirements: str) -> Dict[str, Any]:
        """
        Implement functionality in a component based on requirements.
        
        Args:
            component_specs: Either a string component name or a list of component specifications
            requirements: String describing the requirements/functionality for the components
            
        Returns:
            Dictionary with implementation results
        """
        component_name = ""
        try:
            # Force implementation flag to ensure components get implemented
            # even if the build was successful
            force_implementation = True
            
            # Handle both string component name and list of component specs
            if isinstance(component_specs, str):
                component_name = component_specs
                component_type = "component"  # Default type
                
                # Try to determine the appropriate component type based on name
                if component_name.endswith('-service'):
                    component_type = "service"
                
                # Use the new phased implementation approach for components
                if component_type == "component":
                    logger.info(f"Using phased implementation approach for component {component_name}")
                    # This method should be implemented but is currently missing
                    # Return a placeholder response for now
                    return {
                        "success": False,
                        "message": "Phased implementation not yet available",
                        "component": component_name
                    }
                
                # For services, get the component files
                component_files = await self._get_component_files(component_name)
                if not component_files:
                    return {
                        "success": False,
                        "error": f"Could not find component files for {component_name}"
                    }
                
                # Get the main component file (typically the .ts file for Angular)
                component_file = next(iter(component_files.values()))
                
                # Check if this is an Angular component
                is_angular = self.framework_detection.get("is_angular", False)
                
                # Generate implementation for the component
                implementation = await self._generate_component_implementation(component_name, requirements)
                new_content = implementation.get("content", "")
            else:
                return {
                    "success": False,
                    "error": "Component specs must be a string or a list"
                }
                
            # If we have an empty content, use original
            if not new_content or len(new_content.strip()) < 10:
                return {
                    "success": False,
                    "error": "Generated implementation was empty or too short"
                }
                
            # Update the component file
            # We need a smarter implementation that preserves the structure but updates the functionality
            new_content = self._prepare_content_for_file(new_content, component_file)
            with open(component_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
                
            logger.info(f"[ProjectExecutor] Successfully implemented functionality in {component_file}")
            
            # If we have an Angular component.ts file, also look for the HTML template
            if is_angular and 'component.ts' in component_file:
                html_file = component_file.replace('.ts', '.html')
                if os.path.exists(html_file):
                    # Also update the HTML template if available
                    with open(html_file, 'r', encoding='utf-8') as f:
                        current_html = f.read()
                    
                    if not self._has_meaningful_implementation(current_html, html_file):
                        # Generate HTML implementation
                        html_prompt = f"Implement the HTML template for the {component_name} Angular component based on these requirements:\n\n{requirements}\n\nCurrent HTML template:\n```\n{current_html}\n```\n\nReference TypeScript implementation:\n```\n{new_content}\n```\n\nProvide a complete and functional HTML template with Angular bindings and directives."
                        
                        html_implementation = await self._generate_component_implementation(component_name, html_prompt)
                        
                        html_content = html_implementation.get("html") or html_implementation.get("content", "")
                        
                        if html_content and len(html_content.strip()) > 10:
                            html_content = self._prepare_content_for_file(html_content, html_file)
                            with open(html_file, 'w', encoding='utf-8') as f:
                                f.write(html_content)
                                
                            logger.info(f"[ProjectExecutor] Successfully implemented HTML template in {html_file}")
            
            return {
                "success": True,
                "message": f"Successfully implemented functionality in {component_name}",
                "component": component_name,
                "file": component_file
            }
            
        except Exception as e:
            logger.error(f"[ProjectExecutor] Error implementing component {component_name}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _generate_component_implementation(self, component_name: str, requirements: str) -> Dict[str, Any]:
        """
        Generate implementation code for a component based on requirements.
        
        Args:
            component_name: Name of the component
            requirements: Requirements for the component functionality
            
        Returns:
            Dictionary with generated implementation, including content and other fields
        """
        try:
            # Extract the component name for display purposes (removing path parts)
            display_name = component_name.split("/")[-1] if "/" in component_name else component_name
            
            # Use LLM to generate implementation based on requirements
            from backend.src.llm.llm import LLM
            
            # Get model ID for code generation - use a model specializing in code
            model_id = getattr(self, 'model_id', None) or "deepseek/deepseek-coder"
            
            # Determine if this is a component or service
            component_type = "component"
            if component_name.endswith('-service'):
                component_type = "service"
            
            # Create a prompt for the specific component type
            prompt = f"""Generate Angular {component_type} implementation for '{display_name}' based on:

{requirements}

Return complete, functional code with proper Angular patterns.
Include imports, class definition, and all required methods.
For components, include @Component decorator, lifecycle hooks, and property bindings.
For services, include @Injectable, proper dependency injection, and HTTP handling.

Return only the code without explanations or markdown.
"""
            
            # Use LLM to generate code
            llm = LLM.create(model_id)
            implementation = await llm.generate(prompt, project_name=self.project_name)
            
            # Clean up the response
            code = implementation.strip()
            
            # Clean up any markdown code blocks
            if code.startswith("```") and code.endswith("```"):
                code = "\n".join(code.split("\n")[1:-1])
            elif code.startswith("```"):
                code = "\n".join(code.split("\n")[1:])
            elif code.endswith("```"):
                code = "\n".join(code.split("\n")[:-1])
            
            # Remove language identifier if present
            if code.startswith("typescript") or code.startswith("html"):
                code = "\n".join(code.split("\n")[1:])
            
            # Extract HTML if this was an HTML implementation
            html_content = None
            if requirements and "HTML template" in requirements:
                html_content = code
            
            return {
                "content": code,
                "html": html_content,
                "component_name": component_name,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error generating component implementation: {e}")
            return {
                "content": "",
                "success": False,
                "error": str(e)
            }
    
    def _has_meaningful_implementation(self, content: str, file_path: str) -> bool:
        """
        Check if the content already has meaningful implementation.
        
        Args:
            content: The content to check
            file_path: Path to the file (used to determine the type)
            
        Returns:
            True if the content has meaningful implementation, False otherwise
        """
        if not content or len(content.strip()) < 20:
            return False
            
        # Check for placeholder/boilerplate indicators
        placeholder_patterns = [
            r'//\s*TODO',
            r'//\s*FIXME',
            r'//\s*Implement',
            r'/\*\s*Placeholder',
            r'<!--\s*TODO',
            r'<!--\s*Placeholder',
            r'works!',  # Angular default "component works!" text
            r'component\s+works',
            r'app is running'
        ]
        
        for pattern in placeholder_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return False
        
        # File-specific checks
        if file_path.endswith('.component.ts'):
            # Check for meaningful TypeScript implementation
            # A real component should have more than just the basic boilerplate
            
            # Check if it has only the constructor and ngOnInit
            if re.search(r'export\s+class\s+\w+Component.*?{.*?(?:constructor\(\).*?{.*?})?.*?(?:ngOnInit\(\).*?{.*?})?.*?}', content, re.DOTALL):
                # Check if there are additional properties or methods defined
                other_content = re.sub(r'constructor\(\).*?{.*?}', '', content, flags=re.DOTALL)
                other_content = re.sub(r'ngOnInit\(\).*?{.*?}', '', other_content, flags=re.DOTALL)
                
                # If there's nothing substantial left after removing boilerplate, it's not meaningful
                cleaned = re.sub(r'import.*?;', '', other_content, flags=re.DOTALL)
                cleaned = re.sub(r'@Component\(\{.*?\}\)', '', cleaned, flags=re.DOTALL)
                cleaned = re.sub(r'export\s+class\s+\w+Component.*?{', '', cleaned, flags=re.DOTALL)
                cleaned = re.sub(r'}$', '', cleaned, flags=re.DOTALL)
                
                if len(cleaned.strip()) < 30:
                    return False
        
        elif file_path.endswith('.component.html'):
            # Check for meaningful HTML implementation
            # A real template should have more than just placeholder text
            
            # Remove comments
            content_no_comments = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
            
            # Check if there's only a simple tag with no attributes or nested content
            if re.match(r'^\s*<p>\s*\w+\s+works!\s*</p>\s*$', content_no_comments, re.IGNORECASE):
                return False
                
            # Check for Angular binding or directives
            has_angular_features = any(pattern in content_no_comments for pattern in ['*ngIf', '*ngFor', '[(ngModel)]', '[', ']', '(', ')'])
            
            # If there are no Angular features and the content is very simple, it's likely not meaningful
            if not has_angular_features and len(content_no_comments.strip()) < 50:
                return False
        
        elif file_path.endswith(('.component.scss', '.component.css')):
            # Check for meaningful styles
            # Real styles should have more than just comments and empty rules
            
            # Remove comments
            content_no_comments = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
            content_no_comments = re.sub(r'//.*?$', '', content_no_comments, flags=re.MULTILINE)
            
            # Count the number of actual CSS rules
            rule_count = len(re.findall(r'[^}]*?{[^}]*?}', content_no_comments))
            
            # If there are very few rules and not much content, it's not meaningful
            if rule_count < 2 and len(content_no_comments.strip()) < 30:
                return False
        
        # If we got here, the content has meaningful implementation
        return True
    
    def _prepare_content_for_file(self, content: str, file_path: str) -> str:
        """
        Prepare content for saving to a file by normalizing line endings and ensuring it has the right format.
        
        Args:
            content: The content to prepare
            file_path: The file path where the content will be saved
            
        Returns:
            Prepared content ready for saving
        """
        if not content:
            return ""
        
        # Extract code from markdown blocks if present
        code_block_match = re.search(r'```(?:\w+)?\s*([\s\S]+?)\s*```', content)
        if code_block_match:
            content = code_block_match.group(1)
        
        # Normalize line endings
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # Ensure file ends with newline
        if not content.endswith('\n'):
            content += '\n'
        
        # Special handling for different file types
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.ts':
            # Ensure TypeScript files have proper imports
            if 'component.ts' in file_path.lower() and '@Component' in content and not 'import { Component' in content:
                content = 'import { Component, OnInit } from \'@angular/core\';\n\n' + content
            
            # Ensure services have proper imports
            if 'service.ts' in file_path.lower() and '@Injectable' in content and not 'import { Injectable' in content:
                content = 'import { Injectable } from \'@angular/core\';\n\n' + content
        
        # Remove any trailing backticks that might have been left
        content = content.strip('`')
        
        return content
    
    async def run_project(self, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Run the project using the appropriate command based on the detected framework.
        
        Args:
            options: Additional options for running the project
            
        Returns:
            Dictionary with the results of the operation
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        
        # Update framework detection if needed
        if not self.framework_detection["framework"] or self.framework_detection["framework"] == "unknown":
            await self._detect_frameworks()
        
        # Determine an available port if specified port is already in use
        port = options.get("port", 4201)  # Default to 4201 to avoid conflict with AutonomousAI
        
        # Check if port is in use and find an available one if needed
        available_port = await self._find_available_port(port)
        if available_port != port:
            logger.info(f"[ProjectExecutor] Port {port} is in use, using {available_port} instead")
            port = available_port
        
        # Determine the appropriate command to run the project
        cmd = None
        framework = self.framework_detection["framework"]
        
        if framework == "angular":
            cmd = f"ng serve --port={port}"
            if options.get("open", True):
                cmd += " --open"
        elif framework == "react" or framework == "vue":
            # Check if using Vite or other build system
            if os.path.exists(os.path.join(self.project_dir, "vite.config.js")) or \
               os.path.exists(os.path.join(self.project_dir, "vite.config.ts")):
                # For Vite projects, specify port
                cmd = f"npm run dev -- --port={port}"
            else:
                # For create-react-app and similar, use PORT environment variable
                if os.name == 'nt':  # Windows
                    cmd = f"set PORT={port} && npm start"
                else:  # Unix/Linux/Mac
                    cmd = f"PORT={port} npm start"
        elif framework == "python":
            # Check for specific Python frameworks
            if os.path.exists(os.path.join(self.project_dir, "manage.py")):
                # Django project
                cmd = f"python manage.py runserver {port}"
            elif os.path.exists(os.path.join(self.project_dir, "app.py")) or \
                 os.path.exists(os.path.join(self.project_dir, f"{self.project_name}/app.py")):
                # Likely Flask or FastAPI
                python_cmd = "python"
                if os.path.exists(os.path.join(self.project_dir, "venv")):
                    # Use virtual environment if it exists
                    python_cmd = os.path.join("venv", "bin", "python")
                    if platform.system() == "Windows":
                        python_cmd = os.path.join("venv", "Scripts", "python")
                
                # Find the app file
                app_file = "app.py"
                if os.path.exists(os.path.join(self.project_dir, f"{self.project_name}/app.py")):
                    app_file = f"{self.project_name}/app.py"
                
                cmd = f"{python_cmd} {app_file} --port={port}"
        
        if not cmd:
            return {
                "success": False,
                "message": "Could not determine how to run this project",
                "error": "Unknown project type or framework"
            }
        
        # Run the command
        await emit_terminal_command(self.project_name, f"Running project with command: {cmd}")
        
        # Run the command with background=True to not block
        result = await self.shell_executor.run_command(cmd, cwd=self.project_dir, timeout=None, background=True)
        
        # Track command execution in context
        self.track_command_execution(cmd, True, "Running in background")
        
        # Since most dev servers keep running and don't return, we consider it a success if it starts
        return {
            "success": True,
            "message": f"Project started successfully on port {port}",
            "command": cmd,
            "framework": framework,
            "port": port,
            "url": f"http://localhost:{port}/"
        }
    
    async def _find_available_port(self, start_port: int = 4201, max_attempts: int = 10) -> int:
        """
        Find an available port starting from the given port.
        
        Args:
            start_port: The port to start checking from
            max_attempts: Maximum number of ports to check
            
        Returns:
            An available port
        """
        import socket
        
        for port in range(start_port, start_port + max_attempts):
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                try:
                    s.bind(('localhost', port))
                    return port
                except OSError:
                    # Port is in use, try the next one
                    continue
        
        # If all ports are taken, return a higher port
        return start_port + max_attempts
    
    async def test_project(self, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Run tests for the project.
        
        Args:
            options: Additional options for testing
            
        Returns:
            Dictionary with the test results
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        
        # Update framework detection if needed
        if not self.framework_detection["framework"] or self.framework_detection["framework"] == "unknown":
            await self._detect_frameworks()
        
        # Determine the appropriate test command
        cmd = None
        framework = self.framework_detection["framework"]
        
        if framework == "angular":
            cmd = "ng test"
            if options.get("watch", False) == False:
                cmd += " --watch=false"
        elif framework == "react" or framework == "vue":
            cmd = "npm test"
            if options.get("watch", False) == False:
                cmd += " -- --watchAll=false"
        elif framework == "python":
            # Check for specific Python frameworks
            if os.path.exists(os.path.join(self.project_dir, "manage.py")):
                # Django project
                cmd = "python manage.py test"
            else:
                # Try to find common test runners
                if os.path.exists(os.path.join(self.project_dir, "pytest.ini")) or \
                   os.path.exists(os.path.join(self.project_dir, "conftest.py")):
                    cmd = "pytest"
                else:
                    cmd = "python -m unittest discover"
        
        if not cmd:
            return {
                "success": False,
                "message": "Could not determine how to test this project",
                "error": "Unknown project type or testing framework"
            }
        
        # Run the command
        await emit_terminal_command(self.project_name, f"Testing project with command: {cmd}")
        result = await self.shell_executor.run_command(cmd, cwd=self.project_dir, timeout=120)
        
        return {
            "success": result["success"],
            "message": "Tests completed",
            "command": cmd,
            "framework": framework,
            "stdout": result["stdout"],
            "stderr": result["stderr"],
            "exit_code": result.get("exit_code", 0)
        }
    
    async def install_dependencies(self, dependencies: List[str], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Install project dependencies.
        
        Args:
            dependencies: List of dependencies to install
            options: Optional parameters:
                dev: Install as dev dependencies (default: False)
                global: Install globally (default: False)
                
        Returns:
            Result dictionary with status and output information
        """
        if not dependencies:
            return {
                "success": True,
                "message": "No dependencies to install"
            }
            
        options = options or {}
        dev_dependencies = options.get('dev', False)
        global_install = options.get('global', False)
        
        # Track installed dependencies to avoid redundant installations
        # Initialize from project memory file if it exists
        memory_path = os.path.join(self.project_dir, "project_memory.json")
        installed_packages = []
        
        try:
            if os.path.exists(memory_path):
                with open(memory_path, "r") as f:
                    memory_data = json.load(f)
                    installed_packages = memory_data.get("installed_packages", [])
        except Exception as e:
            logger.warning(f"Error loading project memory for dependency tracking: {e}")
        
        # Filter out already installed packages
        packages_to_install = [dep for dep in dependencies if dep not in installed_packages]
        
        if not packages_to_install:
            logger.info(f"All dependencies already installed: {dependencies}")
            return {
                "success": True,
                "message": "All dependencies already installed"
            }
        
        logger.info(f"Installing dependencies: {packages_to_install}")
        
        # Determine package manager based on project structure
        package_manager = "npm"
        if os.path.exists(os.path.join(self.project_dir, 'yarn.lock')):
            package_manager = "yarn"
        elif os.path.exists(os.path.join(self.project_dir, 'pnpm-lock.yaml')):
            package_manager = "pnpm"
        
        try:
            # Build the install command
            if package_manager == "npm":
                if global_install:
                    cmd = f"npm install -g {' '.join(packages_to_install)}"
                elif dev_dependencies:
                    cmd = f"npm install --save-dev {' '.join(packages_to_install)}"
                else:
                    cmd = f"npm install {' '.join(packages_to_install)}"
            elif package_manager == "yarn":
                if global_install:
                    cmd = f"yarn global add {' '.join(packages_to_install)}"
                elif dev_dependencies:
                    cmd = f"yarn add --dev {' '.join(packages_to_install)}"
                else:
                    cmd = f"yarn add {' '.join(packages_to_install)}"
            elif package_manager == "pnpm":
                if global_install:
                    cmd = f"pnpm add -g {' '.join(packages_to_install)}"
                elif dev_dependencies:
                    cmd = f"pnpm add -D {' '.join(packages_to_install)}"
                else:
                    cmd = f"pnpm add {' '.join(packages_to_install)}"
                
            # Emit command to the terminal
            await emit_terminal_command(self.project_name, f"Installing dependencies: {' '.join(packages_to_install)}...")
            
            # Run the install command
            install_result = await self.shell_executor.run_command(cmd, cwd=self.project_dir, timeout=600)
            
            # Update project memory with installed packages
            try:
                memory_data = {}
                if os.path.exists(memory_path):
                    with open(memory_path, "r") as f:
                        memory_data = json.load(f)
                
                memory_data["installed_packages"] = list(set(installed_packages + packages_to_install))
                
                with open(memory_path, "w") as f:
                    json.dump(memory_data, f, indent=2)
            except Exception as e:
                logger.warning(f"Error updating project memory with installed packages: {e}")
            
            output = install_result.get('output', '')
            error = install_result.get('error', '')
            exit_code = install_result.get('exit_code', 1)
            
            # Check if the installation was successful
            if exit_code == 0:
                return {
                    "success": True,
                    "message": f"Successfully installed dependencies",
                    "output": output
                }
            else:
                # Try fallback installation for npm if the first attempt failed
                if package_manager == "npm":
                    logger.warning(f"First npm install attempt failed, trying fallback with --no-optional")
                    
                    # Try with --no-optional flag
                    retry_cmd = f"npm install {' '.join(packages_to_install)} --no-optional"
                    await emit_terminal_command(self.project_name, f"Retrying with --no-optional flag...")
                    retry_result = await self.shell_executor.run_command(retry_cmd, cwd=self.project_dir, timeout=600)
                    
                    if retry_result.get('exit_code', 1) == 0:
                        return {
                            "success": True,
                            "message": f"Successfully installed dependencies with --no-optional flag",
                            "output": retry_result.get('output', '')
                        }
                    
                    # If that fails, try with --legacy-peer-deps as a last resort
                    logger.warning(f"Second npm install attempt failed, trying fallback with --legacy-peer-deps")
                    retry_cmd = f"npm install {' '.join(packages_to_install)} --legacy-peer-deps --force"
                    await emit_terminal_command(self.project_name, f"Retrying with --legacy-peer-deps and --force flags...")
                    retry_result = await self.shell_executor.run_command(retry_cmd, cwd=self.project_dir, timeout=600)
                    
                    if retry_result.get('exit_code', 1) == 0:
                        return {
                            "success": True,
                            "message": f"Successfully installed dependencies with --legacy-peer-deps and --force flags",
                            "output": retry_result.get('output', '')
                        }
                
                return {
                    "success": False,
                    "message": f"Failed to install dependencies",
                    "error": error,
                    "output": output
                }
        except Exception as e:
            logger.error(f"Error installing dependencies: {str(e)}")
            return {
                "success": False,
                "message": f"Error installing dependencies: {str(e)}",
                "error": str(e)
            }
    
    async def run_ui_tests_with_playwright(self, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Run UI tests with Playwright for automated browser testing.
        
        Args:
            options: Additional options for testing
            
        Returns:
            Dictionary with the UI test results
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        
        # Check if Playwright is installed
        check_cmd = "npx playwright --version"
        check_result = await self.shell_executor.run_command(check_cmd, cwd=self.project_dir, timeout=10)
        
        if not check_result["success"]:
            # Playwright not installed, try to install it
            await emit_terminal_command(self.project_name, "Playwright not found. Installing Playwright...")
            install_cmd = "npm init playwright@latest -y"
            install_result = await self.shell_executor.run_command(install_cmd, cwd=self.project_dir, timeout=180)
            
            if not install_result["success"]:
                return {
                    "success": False,
                    "message": "Failed to install Playwright",
                    "error": install_result["stderr"]
                }
            
            # Install browsers
            browsers_cmd = "npx playwright install --with-deps chromium"
            await self.shell_executor.run_command(browsers_cmd, cwd=self.project_dir, timeout=180)
        
        # Generate a basic Playwright test if none exists
        test_dir = os.path.join(self.project_dir, "tests")
        if not os.path.exists(test_dir):
            os.makedirs(test_dir, exist_ok=True)
        
        playwright_test_file = os.path.join(test_dir, "ui-test.spec.js")
        if not os.path.exists(playwright_test_file):
            await emit_terminal_command(self.project_name, "Creating basic Playwright test...")
            
            # Determine the URL to test based on framework
            port = options.get("port", 4201)
            test_url = options.get("test_url", f"http://localhost:{port}")
            
            # Create a basic test that loads the homepage and captures a screenshot
            js_test_content = f"""
const {{ test, expect }} = require('@playwright/test');

test('basic homepage test', async ({{ page }}) => {{  // pyright-ignore[reportUndefinedVariable]
  // Navigate to the application
  await page.goto('{test_url}');
  
  // Wait for the page to load (adjust selector based on your app)
  await page.waitForSelector('body', {{ timeout: 5000 }});
  
  // Take a screenshot for visual verification
  await page.screenshot({{ path: 'homepage.png' }});
  
  // Basic assertions
  const title = await page.title();
  console.log(`Page title: ${{title}}`);
  
  // Check that the page loaded successfully
  expect(page.url()).toBe('{test_url}/');
}});
"""
            with open(playwright_test_file, "w") as f:
                f.write(js_test_content)
        
        # Create a basic Playwright config if it doesn't exist
        playwright_config_file = os.path.join(self.project_dir, "playwright.config.js")
        if not os.path.exists(playwright_config_file):
            config_content = """
const { defineConfig } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './tests',
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  use: {
    headless: true,
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    video: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results.json' }]
  ],
});
"""
            with open(playwright_config_file, "w") as f:
                f.write(config_content)
        
        # Run the Playwright tests
        await emit_terminal_command(self.project_name, "Running UI tests with Playwright...")
        test_cmd = "npx playwright test"
        
        # Run with specific options if provided
        if options.get("headed", False):
            test_cmd += " --headed"
        if options.get("browser"):
            test_cmd += f" --browser={options['browser']}"
        if options.get("project"):
            test_cmd += f" --project={options['project']}"
        
        result = await self.shell_executor.run_command(test_cmd, cwd=self.project_dir, timeout=180)
        
        # Check for screenshots
        screenshots = []
        for root, _, files in os.walk(self.project_dir):
            for file in files:
                if file.endswith(".png") and ("screenshot" in file.lower() or "homepage" in file.lower()):
                    screenshots.append(os.path.join(root, file))
        
        return {
            "success": result["success"],
            "message": "UI tests completed",
            "command": test_cmd,
            "stdout": result["stdout"],
            "stderr": result["stderr"],
            "exit_code": result.get("exit_code", 0),
            "screenshots": screenshots
        }
    
    async def capture_screenshots(self, url: str = None, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Capture screenshots of the running project using Playwright.
        
        Args:
            url: URL to capture (defaults to localhost with project port)
            options: Additional options for screenshot capture
            
        Returns:
            Dictionary with screenshot results
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        
        # Determine project URL if not provided
        if not url:
            port = options.get("port", 4201)
            url = f"http://localhost:{port}"
        
        # Create a script to capture screenshots
        script_dir = os.path.join(self.project_dir, "scripts")
        os.makedirs(script_dir, exist_ok=True)
        
        script_file = os.path.join(script_dir, "capture-screenshots.js")
        
        # Use a variable name 'js_page' instead of 'page' to avoid the Pylance warning
        js_code = """
const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch();
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  const js_page = await context.newPage();
  
  try {
    console.log('Navigating to URL_PLACEHOLDER');
    await js_page.goto('URL_PLACEHOLDER', { waitUntil: 'networkidle', timeout: 30000 });
    
    // Take a full page screenshot
    console.log('Capturing full page screenshot');
    await js_page.screenshot({ path: 'screenshots/full-page.png', fullPage: true });
    
    // Take a mobile view screenshot
    console.log('Capturing mobile view screenshot');
    await context.setViewportSize({ width: 375, height: 667 });
    await js_page.screenshot({ path: 'screenshots/mobile-view.png' });
    
    // Take a tablet view screenshot
    console.log('Capturing tablet view screenshot');
    await context.setViewportSize({ width: 768, height: 1024 });
    await js_page.screenshot({ path: 'screenshots/tablet-view.png' });
    
    console.log('All screenshots captured successfully');
  } catch (error) {
    console.error('Error capturing screenshots:', error);
    process.exit(1);
  } finally {
    await browser.close();
  }
})();
"""
        # Replace URL placeholder with actual URL
        script_content = js_code.replace('URL_PLACEHOLDER', url)
        with open(script_file, "w") as f:
            f.write(script_content)
        
        # Create screenshots directory
        screenshots_dir = os.path.join(self.project_dir, "screenshots")
        os.makedirs(screenshots_dir, exist_ok=True)
        
        # Check if Playwright is installed
        check_cmd = "npx playwright --version"
        check_result = await self.shell_executor.run_command(check_cmd, cwd=self.project_dir, timeout=10)
        
        if not check_result["success"]:
            # Install Playwright
            await emit_terminal_command(self.project_name, "Installing Playwright for screenshots...")
            install_cmd = "npm install -D playwright"
            await self.shell_executor.run_command(install_cmd, cwd=self.project_dir, timeout=180)
        
        # Run the screenshot script
        await emit_terminal_command(self.project_name, f"Capturing screenshots of {url}...")
        cmd = "node scripts/capture-screenshots.js"
        result = await self.shell_executor.run_command(cmd, cwd=self.project_dir, timeout=60)
        
        # Collect screenshot paths
        screenshots = []
        if os.path.exists(screenshots_dir):
            for file in os.listdir(screenshots_dir):
                if file.endswith(".png"):
                    screenshots.append(os.path.join(screenshots_dir, file))
        
        return {
            "success": result["success"] and len(screenshots) > 0,
            "message": f"Captured {len(screenshots)} screenshots",
            "screenshots": screenshots,
            "stdout": result["stdout"],
            "stderr": result["stderr"]
        }
    
    async def run_accessibility_tests(self, url: str = None, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Run accessibility tests on the project using axe-core and Playwright.
        
        Args:
            url: URL to test (defaults to localhost with project port)
            options: Additional options for accessibility testing
            
        Returns:
            Dictionary with accessibility test results
        """
        if not self.initialized:
            await self.initialize()
        
        options = options or {}
        
        # Determine project URL if not provided
        if not url:
            port = options.get("port", 4201)
            url = f"http://localhost:{port}"
        
        # Create script directory
        script_dir = os.path.join(self.project_dir, "scripts")
        os.makedirs(script_dir, exist_ok=True)
        
        # Create accessibility test script
        script_file = os.path.join(script_dir, "accessibility-test.js")
        script_content = f"""
const {{ chromium }} = require('playwright');
const {{ AxeBuilder }} = require('@axe-core/playwright');

(async () => {{
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {{
    console.log('Navigating to {url}');
    await page.goto('{url}', {{ waitUntil: 'networkidle', timeout: 30000 }});
    
    console.log('Running accessibility tests...');
    const accessibilityScanResults = await new AxeBuilder({{ page }}).analyze();
    
    // Output results to a file
    const fs = require('fs');
    fs.writeFileSync('accessibility-results.json', JSON.stringify(accessibilityScanResults, null, 2));
    
    // Log summary to console
    console.log(`Accessibility violations found: ${{accessibilityScanResults.violations.length}}`);
    for (const violation of accessibilityScanResults.violations) {{
      console.log(`- ${{violation.id}}: ${{violation.help}} (${{violation.nodes.length}} nodes affected)`);
    }}
    
    if (accessibilityScanResults.violations.length === 0) {{
      console.log('No accessibility violations found! 🎉');
    }}
    
  }} catch (error) {{
    console.error('Error running accessibility tests:', error);
    process.exit(1);
  }} finally {{
    await browser.close();
  }}
}})();
"""
        with open(script_file, "w") as f:
            f.write(script_content)
        
        # Install required dependencies
        await emit_terminal_command(self.project_name, "Installing accessibility testing tools...")
        install_cmd = "npm install -D playwright @axe-core/playwright"
        await self.shell_executor.run_command(install_cmd, cwd=self.project_dir, timeout=120)
        
        # Run the accessibility test
        await emit_terminal_command(self.project_name, f"Running accessibility tests on {url}...")
        cmd = "node scripts/accessibility-test.js"
        result = await self.shell_executor.run_command(cmd, cwd=self.project_dir, timeout=60)
        
        # Parse results if available
        results_file = os.path.join(self.project_dir, "accessibility-results.json")
        accessibility_results = {}
        if os.path.exists(results_file):
            try:
                with open(results_file, "r") as f:
                    accessibility_results = json.load(f)
            except Exception as e:
                logger.error(f"Error reading accessibility results: {e}")
        
        return {
            "success": result["success"],
            "message": "Accessibility tests completed",
            "violations": accessibility_results.get("violations", []),
            "passes": accessibility_results.get("passes", []),
            "url": url,
            "stdout": result["stdout"],
            "stderr": result["stderr"]
        } 
    
    async def install_dependencies_with_recovery(self, dependencies: List[str], retry_attempts: int = 3) -> Dict[str, Any]:
        """
        Install dependencies with advanced error recovery mechanisms.
        This enhanced version handles common npm/yarn installation issues.
        
        Args:
            dependencies: List of dependency names to install
            retry_attempts: Number of retry attempts for failed installations
            
        Returns:
            Dictionary with installation results
        """
        if not self.initialized:
            await self.initialize()
            
        if not dependencies:
            return {"success": True, "message": "No dependencies specified"}
            
        # Track results
        results = {
            "success": False,
            "message": "",
            "installed": [],
            "failed": [],
            "error": None
        }
        
        shell_executor = ShellExecutor(project_dir=self.project_dir)
        package_manager = "npm"  # Default, could detect yarn/pnpm if needed
        
        # Try to detect package manager
        if os.path.exists(os.path.join(self.project_dir, "yarn.lock")):
            package_manager = "yarn"
        elif os.path.exists(os.path.join(self.project_dir, "pnpm-lock.yaml")):
            package_manager = "pnpm"
            
        # Prepare installation commands based on package manager
        install_cmd = {
            "npm": f"npm install {' '.join(dependencies)}",
            "yarn": f"yarn add {' '.join(dependencies)}",
            "pnpm": f"pnpm add {' '.join(dependencies)}"
        }
        
        # Default install command for the detected package manager
        cmd = install_cmd[package_manager]
        
        # Attempt normal installation first
        try:
            await emit_terminal_command(self.project_name, f"📦 Installing dependencies: {', '.join(dependencies)}")
            result = await shell_executor.execute_command(cmd, cwd=self.project_dir, timeout=180)
            
            # Check if installation was successful
            if package_manager == "npm" and "ERR!" not in result:
                results["success"] = True
                results["message"] = f"Successfully installed dependencies with {package_manager}"
                results["installed"] = dependencies
                return results
            elif package_manager == "yarn" and "error" not in result.lower():
                results["success"] = True
                results["message"] = f"Successfully installed dependencies with {package_manager}"
                results["installed"] = dependencies
                return results
            elif package_manager == "pnpm" and "error" not in result.lower():
                results["success"] = True
                results["message"] = f"Successfully installed dependencies with {package_manager}"
                results["installed"] = dependencies
                return results
                
            # Installation failed, extract error message
            error_lines = []
            for line in result.split("\n"):
                if package_manager == "npm" and "ERR!" in line:
                    error_lines.append(line)
                elif "error" in line.lower():
                    error_lines.append(line)
                    
            error_msg = "\n".join(error_lines) if error_lines else "Unknown installation error"
            results["error"] = error_msg
            
            # Implement recovery strategies based on error type
            recovery_attempted = False
            
            # Network issues
            if any(net_err in error_msg for net_err in ["ETIMEDOUT", "ENETUNREACH", "ENOTFOUND", "network"]):
                recovery_attempted = True
                await emit_terminal_command(self.project_name, "⚠️ Network issues detected. Trying alternative approach...")
                
                # Configure package manager for better network resilience
                if package_manager == "npm":
                    # Configure npm for retries
                    await shell_executor.execute_command('npm config set fetch-retry-mintimeout 20000', cwd=self.project_dir)
                    await shell_executor.execute_command('npm config set fetch-retry-maxtimeout 120000', cwd=self.project_dir)
                    await shell_executor.execute_command('npm config set fetch-retries 5', cwd=self.project_dir)
                
                # Try with longer timeout
                retry_result = await shell_executor.execute_command(cmd, cwd=self.project_dir, timeout=300)
                
                # If still failing, try one by one installation
                if (package_manager == "npm" and "ERR!" in retry_result) or "error" in retry_result.lower():
                    await emit_terminal_command(self.project_name, "⚠️ Still having issues. Installing packages individually...")
                    
                    # Install each dependency individually
                    installed = []
                    failed = []
                    
                    for dep in dependencies:
                        try:
                            single_cmd = {
                                "npm": f"npm install {dep}",
                                "yarn": f"yarn add {dep}",
                                "pnpm": f"pnpm add {dep}"
                            }[package_manager]
                            
                            single_result = await shell_executor.execute_command(single_cmd, cwd=self.project_dir, timeout=120)
                            success = (package_manager == "npm" and "ERR!" not in single_result) or ("error" not in single_result.lower())
                            
                            if success:
                                installed.append(dep)
                                await emit_terminal_command(self.project_name, f"✅ Installed {dep}")
                            else:
                                failed.append(dep)
                                await emit_terminal_command(self.project_name, f"❌ Failed to install {dep}")
                        except Exception:
                            failed.append(dep)
                    
                    # Update results
                    results["installed"] = installed
                    results["failed"] = failed
                    results["success"] = len(installed) > 0
                    results["message"] = f"Installed {len(installed)}/{len(dependencies)} packages individually"
                else:
                    # Retry with longer timeout worked
                    results["success"] = True
                    results["message"] = "Successfully installed dependencies with adjusted network settings"
                    results["installed"] = dependencies
            
            # Permission issues
            elif any(perm_err in error_msg.lower() for perm_err in ["eacces", "permission", "access denied"]):
                recovery_attempted = True
                await emit_terminal_command(self.project_name, "⚠️ Permission issues detected. Trying alternative approach...")
                
                if package_manager == "npm":
                    # Try with --no-optional flag
                    retry_cmd = f"npm install {' '.join(dependencies)} --no-optional"
                    retry_result = await shell_executor.execute_command(retry_cmd, cwd=self.project_dir, timeout=180)
                    
                    if "ERR!" not in retry_result:
                        results["success"] = True
                        results["message"] = "Successfully installed dependencies with --no-optional flag"
                        results["installed"] = dependencies
                    else:
                        # Try global prefix approach (avoiding sudo)
                        await shell_executor.execute_command("npm config set prefix ~/.npm", cwd=self.project_dir)
                        retry_result = await shell_executor.execute_command(cmd, cwd=self.project_dir, timeout=180)
                        
                        if "ERR!" not in retry_result:
                            results["success"] = True
                            results["message"] = "Successfully installed dependencies with adjusted npm prefix"
                            results["installed"] = dependencies
            
            # Dependency conflicts
            elif any(conf_err in error_msg.lower() for conf_err in ["dependency", "conflict", "peer", "incompatible"]):
                recovery_attempted = True
                await emit_terminal_command(self.project_name, "⚠️ Dependency conflicts detected. Trying alternative approach...")
                
                if package_manager == "npm":
                    # Try with --legacy-peer-deps and --force
                    retry_cmd = f"npm install {' '.join(dependencies)} --legacy-peer-deps --force"
                    retry_result = await shell_executor.execute_command(retry_cmd, cwd=self.project_dir, timeout=180)
                    
                    if "ERR!" not in retry_result:
                        results["success"] = True
                        results["message"] = "Successfully installed dependencies with --legacy-peer-deps and --force flags"
                        results["installed"] = dependencies
                elif package_manager == "yarn":
                    # Try with --ignore-engines
                    retry_cmd = f"yarn add {' '.join(dependencies)} --ignore-engines"
                    retry_result = await shell_executor.execute_command(retry_cmd, cwd=self.project_dir, timeout=180)
                    
                    if "error" not in retry_result.lower():
                        results["success"] = True
                        results["message"] = "Successfully installed dependencies with --ignore-engines flag"
                        results["installed"] = dependencies
            
            # If no specific recovery was attempted or they all failed, try one last generic recovery
            if not recovery_attempted or not results["success"]:
                # Last resort: try installing just base dependencies
                await emit_terminal_command(self.project_name, "⚠️ Trying generic dependency installation as last resort...")
                
                generic_cmd = {
                    "npm": "npm install",
                    "yarn": "yarn",
                    "pnpm": "pnpm install"
                }[package_manager]
                
                generic_result = await shell_executor.execute_command(generic_cmd, cwd=self.project_dir, timeout=240)
                
                # At least base dependencies might have been installed
                results["message"] = "Attempted generic dependency installation"
                
                # Then try to install each dependency individually
                installed = []
                failed = []
                
                for dep in dependencies:
                    try:
                        single_cmd = {
                            "npm": f"npm install {dep}",
                            "yarn": f"yarn add {dep}",
                            "pnpm": f"pnpm add {dep}"
                        }[package_manager]
                        
                        single_result = await shell_executor.execute_command(single_cmd, cwd=self.project_dir, timeout=120)
                        success = (package_manager == "npm" and "ERR!" not in single_result) or ("error" not in single_result.lower())
                        
                        if success:
                            installed.append(dep)
                    except Exception:
                        failed.append(dep)
                
                # Update results
                results["installed"] = installed
                results["failed"] = failed
                results["success"] = len(installed) > 0
                results["message"] = f"Installed {len(installed)}/{len(dependencies)} packages after generic recovery"
                
        except Exception as e:
            logger.error(f"[ProjectExecutor] Error installing dependencies: {e}")
            results["error"] = str(e)
            results["message"] = f"Error installing dependencies: {str(e)}"
            
            # Try to fall back to basic dependency installation
            try:
                await emit_terminal_command(self.project_name, "⚠️ Error during dependency installation. Trying basic installation...")
                
                basic_cmd = {
                    "npm": "npm install",
                    "yarn": "yarn",
                    "pnpm": "pnpm install"
                }[package_manager]
                
                await shell_executor.execute_command(basic_cmd, cwd=self.project_dir, timeout=240)
                results["message"] += ". Attempted basic package installation as fallback."
            except Exception as fallback_err:
                logger.error(f"[ProjectExecutor] Error during fallback installation: {fallback_err}")
        
        # Log the result
        if results["success"]:
            logger.info(f"[ProjectExecutor] Successfully installed {len(results['installed'])}/{len(dependencies)} dependencies: {', '.join(results['installed'])}")
            if results["failed"]:
                logger.warning(f"[ProjectExecutor] Failed to install {len(results['failed'])} dependencies: {', '.join(results['failed'])}")
        else:
            logger.error(f"[ProjectExecutor] Failed to install dependencies: {results['error']}")
        
        return results
    
    async def _check_component_exists(self, component_name: str) -> bool:
        """
        Check if a component already exists in the project.
        
        Args:
            component_name: Name of the component to check
            
        Returns:
            True if the component exists, False otherwise
        """
        implementation_files = await self._get_component_files(component_name)
        return len(implementation_files) > 0
        
    async def _get_component_files(self, component_name: str) -> Dict[str, str]:
        """
        Get all files related to a component.
        
        Args:
            component_name: Name of the component
            
        Returns:
            Dictionary of file paths (relative to project directory) and their content
        """
        files = {}
        
        # Component patterns based on framework conventions
        patterns = [
            # Angular patterns
            f"**/components/{component_name}/{component_name}.component.ts",
            f"**/components/{component_name}/{component_name}.component.html",
            f"**/components/{component_name}/{component_name}.component.scss",
            f"**/components/{component_name}/{component_name}.component.css",
            f"**/components/{component_name}.component.ts",
            f"**/components/{component_name}.component.html",
            f"**/components/{component_name}.component.scss",
            f"**/components/{component_name}.component.css",
            # General patterns (also for nested components)
            f"**/{component_name}/{component_name}.component.ts",
            f"**/{component_name}/{component_name}.component.html",
            f"**/{component_name}/{component_name}.component.scss",
            f"**/{component_name}/{component_name}.component.css",
            f"**/{component_name}.component.ts",
            f"**/{component_name}.component.html",
            f"**/{component_name}.component.scss",
            f"**/{component_name}.component.css",
            # Service patterns
            f"**/services/{component_name}.service.ts",
            f"**/{component_name}.service.ts",
            # React/general patterns
            f"**/{component_name}.jsx",
            f"**/{component_name}.tsx",
            f"**/{component_name}.js",
            f"**/{component_name}.ts",
            f"**/{component_name}.css",
            f"**/{component_name}.scss"
        ]
        
        for pattern in patterns:
            for file_path in Path(self.project_dir).glob(pattern):
                rel_path = str(file_path).replace(self.project_dir, "").lstrip("/\\")
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                    files[rel_path] = content
                except Exception as e:
                    self.logger.warning(f"Error reading file {file_path}: {e}")
        
        return files
    
    async def _get_component_details(self, component_name: str) -> Dict[str, Any]:
        """
        Get detailed information about a component.
        
        Args:
            component_name: Name of the component
            
        Returns:
            Dictionary with component details
        """
        files = await self._get_component_files(component_name)
        
        # Determine component type based on files
        component_type = "unknown"
        if any("component.ts" in file for file in files.keys()):
            component_type = "angular-component" 
        elif any("service.ts" in file for file in files.keys()):
            component_type = "angular-service"
        elif any(file.endswith(".jsx") for file in files.keys()):
            component_type = "react-component"
        elif any(file.endswith(".tsx") for file in files.keys()):
            component_type = "react-component-ts"
        
        return {
            "name": component_name,
            "type": component_type,
            "file_count": len(files),
            "files": list(files.keys())
        }
    
    async def create_file(self, file_path: str, content: str) -> Dict[str, Any]:
        """
        Create a file with the specified content.
        
        Args:
            file_path: Path to the file to create
            content: Content to write to the file
            
        Returns:
            Result dictionary with success status and file path
        """
        try:
            # Normalize path for consistency across platforms
            normalized_path = os.path.normpath(file_path)
            
            # Check for absolute paths and make them relative to the project directory
            if os.path.isabs(normalized_path):
                logger.warning(f"Converting absolute path to relative: {normalized_path}")
                try:
                    # Try to make it relative to project_dir
                    normalized_path = os.path.relpath(normalized_path, self.project_dir)
                except ValueError:
                    # If relpath fails, just take the file name
                    normalized_path = os.path.basename(normalized_path)
            
            # Ensure the file path is within the project
            sanitized_path = self._ensure_path_inside_project(normalized_path)
            
            if not sanitized_path:
                return {
                    "success": False,
                    "error": "Invalid file path",
                    "message": f"Cannot create file with invalid path: {file_path}"
                }
            
            # Get the absolute path
            abs_file_path = os.path.join(self.project_dir, sanitized_path)
            
            # Double check path is inside the project (security)
            if not os.path.abspath(abs_file_path).startswith(os.path.abspath(self.project_dir)):
                logger.error(f"Invalid file path would be outside project directory: {file_path}")
                return {
                    "success": False,
                    "error": "Invalid file path",
                    "message": f"File path would be outside project directory: {file_path}"
                }
            
            # Detect the project framework for better structure enforcement
            framework_type = self._detect_framework().get("framework", "unknown")
            
            # Apply framework-specific path corrections
            if framework_type == "angular":
                # Angular project path correction logic
                sanitized_path, abs_file_path = self._fix_angular_path(sanitized_path)
            elif framework_type == "react":
                # React project path correction logic
                sanitized_path, abs_file_path = self._fix_react_path(sanitized_path)
            
            # Ensure the directory exists
            os.makedirs(os.path.dirname(abs_file_path), exist_ok=True)
            
            # Write the content to the file
            with open(abs_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Track file creation in context
            self.track_file_creation(sanitized_path, len(content))
                
            # Notify via socket
            await emit_agent_file_update(self.project_name, abs_file_path, content)
            
            # Log success
            rel_path = os.path.relpath(abs_file_path, self.project_dir)
            logger.info(f"Created file: {rel_path}")
            
            # Return both the fixed file path and the original request for reference
            return {
                "success": True,
                "file_path": rel_path,
                "original_path": file_path,
                "message": f"Created file: {rel_path}"
            }
            
        except Exception as e:
            logger.error(f"Error creating file {file_path}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create file: {file_path}"
            }
    
    def _fix_react_path(self, path: str) -> tuple:
        """
        Fixes React-specific path issues to ensure files are created in the correct locations.
        
        Args:
            path: The relative path to fix
            
        Returns:
            Tuple of (fixed_relative_path, fixed_absolute_path)
        """
        # Check if src directory exists - if not, we might need to create files in the appropriate structure
        has_src = os.path.exists(os.path.join(self.project_dir, "src"))
        
        # Normalize path separators
        path = path.replace('\\', '/')
        
        # Convert the path to absolute
        abs_path = os.path.join(self.project_dir, path)
        
        # Fix common React path issues
        
        # Case 1: Files not in src/ directory
        if not path.startswith('src/') and has_src:
            # Check if this is a component file
            component_match = re.search(r'(.*?)([\w-]+)\.(jsx|tsx|js|ts)$', path)
            if component_match:
                dir_part = component_match.group(1).strip('/')
                component_name = component_match.group(2)
                file_ext = component_match.group(3)
                
                # For React components, use PascalCase for file names
                pascal_case_name = ''.join(word.capitalize() for word in component_name.split('-'))
                
                # Construct proper React path for components
                if dir_part:
                    # If there's a directory part, maintain it under src/components
                    new_path = f"src/components/{dir_part}/{pascal_case_name}.{file_ext}"
                else:
                    # Otherwise, put it directly in src/components
                    new_path = f"src/components/{pascal_case_name}.{file_ext}"
                
                logger.info(f"Fixing React component path: {path} -> {new_path}")
                return new_path, os.path.join(self.project_dir, new_path)
            
            # Check if this is a CSS/SCSS file
            style_match = re.search(r'(.*?)([\w-]+)\.(css|scss)$', path)
            if style_match:
                dir_part = style_match.group(1).strip('/')
                file_name = style_match.group(2)
                file_ext = style_match.group(3)
                
                # Construct proper path for style files
                if dir_part:
                    new_path = f"src/{dir_part}/{file_name}.{file_ext}"
                else:
                    new_path = f"src/styles/{file_name}.{file_ext}"
                
                logger.info(f"Fixing React style path: {path} -> {new_path}")
                return new_path, os.path.join(self.project_dir, new_path)
            
            # Check if this is an API or service file
            service_match = re.search(r'(.*?)(?:api|service|util)/([\w-]+)\.(js|ts)$', path)
            if service_match:
                dir_part = service_match.group(1).strip('/')
                file_name = service_match.group(2)
                file_ext = service_match.group(3)
                
                # Keep the service/util/api structure but move to src
                if dir_part:
                    new_path = f"src/{dir_part}/{file_name}.{file_ext}"
                else:
                    new_path = f"src/services/{file_name}.{file_ext}"
                
                logger.info(f"Fixing React service path: {path} -> {new_path}")
                return new_path, os.path.join(self.project_dir, new_path)
                
            # Special handling for App.js/App.jsx/App.tsx
            if path.lower() in ["app.js", "app.jsx", "app.tsx", "app.ts"]:
                file_ext = path.split('.')[-1]
                new_path = f"src/App.{file_ext}"
                logger.info(f"Fixing React App path: {path} -> {new_path}")
                return new_path, os.path.join(self.project_dir, new_path)
            
            # Special handling for index.js/index.jsx/index.tsx
            if path.lower() in ["index.js", "index.jsx", "index.tsx", "index.ts"]:
                file_ext = path.split('.')[-1]
                new_path = f"src/index.{file_ext}"
                logger.info(f"Fixing React index path: {path} -> {new_path}")
                return new_path, os.path.join(self.project_dir, new_path)
        
        # Case 2: Component files in wrong directory
        if path.startswith('src/'):
            # Check if component should be in components directory
            component_match = re.search(r'src/(.*?)([\w-]+)\.(jsx|tsx|js|ts)$', path)
            if component_match:
                dir_part = component_match.group(1).strip('/')
                component_name = component_match.group(2)
                file_ext = component_match.group(3)
                
                # Skip App.js, index.js and files already in components or pages directory
                if (component_name.lower() == 'app' or 
                    component_name.lower() == 'index' or
                    dir_part.startswith('components/') or 
                    dir_part.startswith('pages/')):
                    return path, abs_path
                
                # Use PascalCase for component names
                pascal_case_name = ''.join(word.capitalize() for word in component_name.split('-'))
                
                # Components should typically be in src/components or src/pages
                if 'page' in component_name.lower() or 'view' in component_name.lower():
                    new_path = f"src/pages/{pascal_case_name}.{file_ext}"
                else:
                    # For other components, keep any subdirectories after components/
                    if dir_part:
                        dir_parts = dir_part.split('/')
                        if len(dir_parts) > 1:
                            new_path = f"src/components/{'/'.join(dir_parts[1:])}/{pascal_case_name}.{file_ext}"
                        else:
                            new_path = f"src/components/{pascal_case_name}.{file_ext}"
                    else:
                        new_path = f"src/components/{pascal_case_name}.{file_ext}"
                
                # Only change the path if it's an actual improvement
                if new_path != path:
                    logger.info(f"Fixing React component path structure: {path} -> {new_path}")
                    return new_path, os.path.join(self.project_dir, new_path)
        
        # If no path fixes are needed, return the original path
        return path, abs_path
    
    def _fix_angular_path(self, path: str) -> tuple:
        """
        Fixes Angular-specific path issues to ensure files are created in the correct locations.
        
        Args:
            path: The relative path to fix
            
        Returns:
            Tuple of (fixed_relative_path, fixed_absolute_path)
        """
        # Normalize path separators
        path = path.replace('\\', '/')
        
        # First, check if we have a duplicate src folder structure and determine the correct one
        root_src_dir = os.path.join(self.project_dir, "src")
        nested_project_dir = os.path.join(self.project_dir, self.project_name)
        nested_src_dir = os.path.join(nested_project_dir, "src")
        
        # Determine which src directory to use (prioritize root src if both exist and are valid)
        use_root_src = True
        if os.path.exists(root_src_dir) and os.path.exists(nested_src_dir):
            root_is_valid = self._is_valid_angular_src(root_src_dir)
            nested_is_valid = self._is_valid_angular_src(nested_src_dir)
            
            if not root_is_valid and nested_is_valid:
                use_root_src = False
            elif root_is_valid and nested_is_valid:
                # Both are valid, check which has more files
                root_files = self._count_files_recursive(root_src_dir)
                nested_files = self._count_files_recursive(nested_src_dir)
                if nested_files > root_files:
                    use_root_src = False
        elif not os.path.exists(root_src_dir) and os.path.exists(nested_src_dir):
            use_root_src = False
        
        # Base src path to use for all fixes
        base_src = "" if use_root_src else f"{self.project_name}/"
        
        # Convert the path to absolute (but handle this at the end)
        if use_root_src:
            abs_base_path = self.project_dir
        else:
            abs_base_path = os.path.dirname(self.project_dir)  # Go up one level if using nested
        
        # Fix common Angular path issues
        
        # Case 1: Files not in src/ directory
        if not path.startswith('src/'):
            # Check if this is a component file
            component_match = re.search(r'(.*?)([\w-]+)\.component\.(ts|html|scss|css)$', path)
            if component_match:
                dir_part = component_match.group(1).strip('/')
                component_name = component_match.group(2)
                file_ext = component_match.group(3)
                
                # Ensure component goes in its own folder
                if dir_part:
                    new_path = f"{base_src}src/app/{dir_part}/{component_name}/{component_name}.component.{file_ext}"
                else:
                    new_path = f"{base_src}src/app/components/{component_name}/{component_name}.component.{file_ext}"
                
                logger.info(f"Fixing Angular component path: {path} -> {new_path}")
                return new_path, os.path.join(abs_base_path, new_path)
            
            # Check if this is a service file
            service_match = re.search(r'(.*?)([\w-]+)\.service\.ts$', path)
            if service_match:
                dir_part = service_match.group(1).strip('/')
                service_name = service_match.group(2)
                
                # Construct proper Angular path for services
                if dir_part and dir_part != "services":
                    new_path = f"{base_src}src/app/{dir_part}/{service_name}.service.ts"
                else:
                    new_path = f"{base_src}src/app/services/{service_name}.service.ts"
                
                logger.info(f"Fixing Angular service path: {path} -> {new_path}")
                return new_path, os.path.join(abs_base_path, new_path)
            
            # Check if this is a module file
            module_match = re.search(r'(.*?)([\w-]+)\.module\.ts$', path)
            if module_match:
                dir_part = module_match.group(1).strip('/')
                module_name = module_match.group(2)
                
                # Construct proper Angular path for modules
                if dir_part:
                    new_path = f"{base_src}src/app/{dir_part}/{module_name}.module.ts"
                else:
                    new_path = f"{base_src}src/app/{module_name}.module.ts"
                
                logger.info(f"Fixing Angular module path: {path} -> {new_path}")
                return new_path, os.path.join(abs_base_path, new_path)
                
            # Check for app-routing.module.ts special case
            if "app-routing.module.ts" in path or "app.routes.ts" in path:
                new_path = f"{base_src}src/app/app-routing.module.ts" if "app-routing.module.ts" in path else f"{base_src}src/app/app.routes.ts"
                logger.info(f"Fixing Angular routing module path: {path} -> {new_path}")
                return new_path, os.path.join(abs_base_path, new_path)
                
            # Check for other app files
            if path.startswith("app.") or path.startswith("app-"):
                new_path = f"{base_src}src/app/{path}"
                logger.info(f"Fixing Angular app file path: {path} -> {new_path}")
                return new_path, os.path.join(abs_base_path, new_path)
            
            # General case for non-src files that should be in src
            if path.endswith('.ts') or path.endswith('.html') or path.endswith('.scss') or path.endswith('.css'):
                # If it's a source file but not in src, put it in src/app
                new_path = f"{base_src}src/app/{path}"
                logger.info(f"Moving Angular source file to src/app: {path} -> {new_path}")
                return new_path, os.path.join(abs_base_path, new_path)
        
        # Case 2: Path starts with src/ but might need fixing for the correct src directory
        elif path.startswith('src/'):
            if use_root_src:
                # Keep as is
                return path, os.path.join(self.project_dir, path)
            else:
                # Fix to use nested src
                new_path = f"{self.project_name}/{path}"
                return new_path, os.path.join(os.path.dirname(self.project_dir), new_path)
        
        # Case 3: Component files not in their own directory
        if (path.startswith('src/app/') or path.startswith(f"{self.project_name}/src/app/")):
            # Extract the relevant part after src/app/
            app_relative_path = path.split('src/app/')[-1] if 'src/app/' in path else path.split(f"{self.project_name}/src/app/")[-1]
            
            component_match = re.search(r'(.*?)([\w-]+)\.component\.(ts|html|scss|css)$', app_relative_path)
            if component_match:
                dir_part = component_match.group(1).strip('/')
                component_name = component_match.group(2)
                file_ext = component_match.group(3)
                
                # Check if the component is already in its own directory with its name
                if not path.endswith(f"/{component_name}/{component_name}.component.{file_ext}"):
                    # Put it in its own directory
                    if dir_part:
                        new_rel_path = f"src/app/{dir_part}/{component_name}/{component_name}.component.{file_ext}"
                    else:
                        new_rel_path = f"src/app/components/{component_name}/{component_name}.component.{file_ext}"
                    
                    # Apply base src prefix if needed
                    new_path = f"{base_src}{new_rel_path}"
                    logger.info(f"Fixing Angular component directory structure: {path} -> {new_path}")
                    return new_path, os.path.join(abs_base_path, new_path)
        
        # If no fixes needed, return original path but ensure absolute path is correct
        abs_path = os.path.join(self.project_dir, path) if use_root_src else os.path.join(os.path.dirname(self.project_dir), path)
        return path, abs_path
    
    async def verify_and_fix_angular_routing(self) -> Dict[str, Any]:
        """
        Verify and fix Angular routing configuration.
        This ensures that routing is properly set up, including:
        - AppRoutingModule is created if needed
        - router-outlet is present in app.component.html
        - RouterModule is imported in app.module.ts
        - Required dependencies for standalone components
        
        Returns:
            Dict with success status, fixes applied, and routing files created
        """
        if not self.framework_detection.get("is_angular", False):
            return {
                "success": False,
                "message": "Not an Angular project",
                "fixes_applied": []
            }
        
        fixes_applied = []
        routing_files_created = []
        
        try:
            # 1. Check for app-routing.module.ts - create if it doesn't exist
            app_routing_module = os.path.join(self.project_dir, 'src', 'app', 'app-routing.module.ts')
            
            if not os.path.exists(app_routing_module):
                # Create a basic routing module
                routing_content = """import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  // Define routes here
  { path: '', redirectTo: '/', pathMatch: 'full' },
  { path: '**', redirectTo: '/' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
"""
                os.makedirs(os.path.dirname(app_routing_module), exist_ok=True)
                with open(app_routing_module, 'w', encoding='utf-8') as f:
                    f.write(routing_content)
                
                routing_files_created.append("app-routing.module.ts")
                fixes_applied.append("Created app-routing.module.ts")
                logger.info("Created app-routing.module.ts")
            
            # 2. Check app.module.ts to make sure AppRoutingModule is imported
            app_module_path = os.path.join(self.project_dir, 'src', 'app', 'app.module.ts')
            
            # Check if this is a standalone components project (no app.module.ts)
            standalone_mode = not os.path.exists(app_module_path)
            
            if not standalone_mode and os.path.exists(app_module_path):
                with open(app_module_path, 'r', encoding='utf-8') as f:
                    module_content = f.read()
                
                if "AppRoutingModule" not in module_content:
                    # Add import
                    if "import {" in module_content:
                        # Find a good spot to add the import
                        import_lines = re.findall(r'import\s+{[^}]+}\s+from\s+[\'"][^\'""]+[\'"];', module_content)
                        if import_lines:
                            last_import = import_lines[-1]
                            routing_import = "\nimport { AppRoutingModule } from './app-routing.module';"
                            module_content = module_content.replace(last_import, last_import + routing_import)
                    else:
                        # No imports found, add at the top
                        routing_import = "import { AppRoutingModule } from './app-routing.module';\n\n"
                        module_content = routing_import + module_content
                    
                    # Add to imports array
                    if "imports: [" in module_content:
                        imports_pattern = r'imports:\s*\[(.*?)\]'
                        imports_match = re.search(imports_pattern, module_content, re.DOTALL)
                        if imports_match:
                            imports_content = imports_match.group(1)
                            if imports_content.strip():
                                if not imports_content.strip().endswith(','):
                                    new_imports = imports_content + ", AppRoutingModule"
                                else:
                                    new_imports = imports_content + " AppRoutingModule"
                            else:
                                new_imports = "AppRoutingModule"
                            
                            module_content = module_content.replace(imports_match.group(0), f"imports: [{new_imports}]")
                    
                    # Write the changes
                    with open(app_module_path, 'w', encoding='utf-8') as f:
                        f.write(module_content)
                    
                    fixes_applied.append("Added AppRoutingModule to app.module.ts")
                    logger.info("Added AppRoutingModule to app.module.ts")
            
            # 3. Check if RouterModule is in imports
            if not standalone_mode and os.path.exists(app_module_path):
                with open(app_module_path, 'r', encoding='utf-8') as f:
                    module_content = f.read()
                
                if "RouterModule" not in module_content:
                    # Add import for RouterModule
                    if "import {" in module_content:
                        # Find a good spot to add the import
                        import_lines = re.findall(r'import\s+{[^}]+}\s+from\s+[\'"][^\'""]+[\'"];', module_content)
                        if import_lines:
                            last_import = import_lines[-1]
                            router_import = "\nimport { RouterModule } from '@angular/router';"
                            module_content = module_content.replace(last_import, last_import + router_import)
                    else:
                        # No imports found, add at the top
                        router_import = "import { RouterModule } from '@angular/router';\n\n"
                        module_content = router_import + module_content
                    
                    # No need to add to imports array since AppRoutingModule already does that
                    # Just write the import
                    with open(app_module_path, 'w', encoding='utf-8') as f:
                        f.write(module_content)
                    
                    fixes_applied.append("Added RouterModule import to app.module.ts")
                    logger.info("Added RouterModule import to app.module.ts")
            
            # 4. Check app.component.html for router-outlet
            app_component_path = os.path.join(self.project_dir, 'src', 'app', 'app.component.html')
            router_outlet_present = False
            
            if os.path.exists(app_component_path):
                with open(app_component_path, 'r', encoding='utf-8') as f:
                    app_component_content = f.read()
                
                router_outlet_present = "<router-outlet" in app_component_content
                
                if not router_outlet_present:
                    # For existing content, try to intelligently insert the router-outlet
                    if '<main>' in app_component_content:
                        # Insert inside existing main tag
                        new_component_content = app_component_content.replace(
                            '<main>',
                            '<main>\n  <router-outlet></router-outlet>'
                        )
                    elif '<div' in app_component_content:
                        # Find the first closing div tag and insert before it
                        div_parts = app_component_content.split('</div>', 1)
                        if len(div_parts) > 1:
                            new_component_content = div_parts[0] + '\n  <router-outlet></router-outlet>\n</div>' + div_parts[1]
                        else:
                            # Just append to the end
                            new_component_content = app_component_content + '\n<router-outlet></router-outlet>\n'
                    else:
                        # Just append to the end
                        new_component_content = app_component_content + '\n<router-outlet></router-outlet>\n'
                    
                    # Write the updated component
                    with open(app_component_path, 'w', encoding='utf-8') as f:
                        f.write(new_component_content)
                    
                    router_outlet_present = True
                    fixes_applied.append("Added router-outlet to app.component.html")
                    logger.info("Added router-outlet to app.component.html")
            else:
                # Create app.component.html if it doesn't exist
                logger.info("Creating missing app.component.html with router-outlet")
                
                new_component_content = """<div class="app-container">
  <!-- Router outlet is required for routing to work -->
  <router-outlet></router-outlet>
</div>
"""
                
                # Create directory if needed
                os.makedirs(os.path.dirname(app_component_path), exist_ok=True)
                
                # Write the component HTML
                with open(app_component_path, 'w', encoding='utf-8') as f:
                    f.write(new_component_content)
                
                router_outlet_present = True
                fixes_applied.append("Created app.component.html with router-outlet")
                logger.info("Created app.component.html with router-outlet")
            
            # 5. Check app.component.ts for standalone components
            app_component_ts_path = os.path.join(self.project_dir, 'src', 'app', 'app.component.ts')
            if os.path.exists(app_component_ts_path):
                with open(app_component_ts_path, 'r', encoding='utf-8') as f:
                    app_component_ts_content = f.read()
                
                # Check if this is a standalone component
                is_standalone_component = 'standalone: true' in app_component_ts_content
                
                if is_standalone_component and router_outlet_present and 'RouterOutlet' not in app_component_ts_content:
                    # Add RouterOutlet import for standalone components
                    logger.info("Adding RouterOutlet to app.component.ts standalone imports")
                    
                    if 'import { Component' in app_component_ts_content:
                        app_component_ts_content = app_component_ts_content.replace(
                            'import { Component',
                            'import { Component, RouterOutlet'
                        )
                    else:
                        # Add import at the top
                        router_import = "import { RouterOutlet } from '@angular/router';\n"
                        app_component_ts_content = router_import + app_component_ts_content
                    
                    # Add to imports array
                    if 'imports: [' in app_component_ts_content:
                        app_component_ts_content = app_component_ts_content.replace(
                            'imports: [',
                            'imports: [RouterOutlet, '
                        )
                    elif 'imports: []' in app_component_ts_content:
                        app_component_ts_content = app_component_ts_content.replace(
                            'imports: []',
                            'imports: [RouterOutlet]'
                        )
                    
                    # Write the updated component
                    with open(app_component_ts_path, 'w', encoding='utf-8') as f:
                        f.write(app_component_ts_content)
                    
                    fixes_applied.append("Added RouterOutlet to app.component.ts standalone imports")
            
            return {
                "success": True,
                "fixes_applied": fixes_applied,
                "routing_files_created": routing_files_created,
                "message": "Angular routing verification and fixes completed"
            }
            
        except Exception as e:
            logger.error(f"Error while verifying and fixing routing: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": str(e),
                "message": "Error verifying and fixing Angular routing",
                "fixes_applied": fixes_applied if 'fixes_applied' in locals() else [],
                "routing_files_created": routing_files_created if 'routing_files_created' in locals() else []
            }
    
    async def validate_angular_folder_structure(self) -> Dict[str, Any]:
        """
        Validate and fix Angular project folder structure, focusing on duplicate src folders.
        This addresses specifically the issue where src folder appears in both:
        - project_dir/src
        - project_dir/project_name/src
        
        The method will identify the correct src folder location based on Angular conventions,
        and move files from the incorrect location to the correct one.
        
        Returns:
            Dictionary with results of validation and fixes
        """
        if not self.framework_detection.get("is_angular", False):
            logger.info(f"Project {self.project_name} is not an Angular project, skipping folder structure validation")
            return {
                "success": False,
                "message": "Not an Angular project",
                "changes_made": False
            }
        
        logger.info(f"Validating Angular folder structure for {self.project_name}")
        
        # Directory paths to check
        root_src_dir = os.path.join(self.project_dir, "src")
        nested_project_dir = os.path.join(self.project_dir, self.project_name)
        nested_src_dir = os.path.join(nested_project_dir, "src")
        
        changes_made = False
        fixes_applied = []
        
        # Check if we have the duplicate src folder issue
        if os.path.exists(root_src_dir) and os.path.exists(nested_src_dir):
            logger.info(f"Detected duplicate src folders in {self.project_name}")
            
            # Determine which is the correct src folder by checking for Angular-specific files
            root_is_valid = self._is_valid_angular_src(root_src_dir)
            nested_is_valid = self._is_valid_angular_src(nested_src_dir)
            
            logger.info(f"Root src valid: {root_is_valid}, Nested src valid: {nested_is_valid}")
            
            # ALWAYS prefer the root src directory unless there's a clear reason not to
            if root_is_valid or not nested_is_valid:
                # Root src is valid (or nested is invalid) - move any unique files from nested to root
                await self._merge_src_folders(nested_src_dir, root_src_dir)
                fixes_applied.append(f"Merged files from {nested_src_dir} to {root_src_dir}")
                
                # Remove the nested src directory after merging
                try:
                    if os.path.exists(nested_src_dir):
                        if not os.listdir(nested_src_dir):
                            # Directory is empty, remove it
                            shutil.rmtree(nested_src_dir)
                            fixes_applied.append(f"Removed empty directory {nested_src_dir}")
                        else:
                            # Directory still has files, force removal after another merge attempt
                            logger.warning(f"Nested src directory {nested_src_dir} still has files after merge, forcing another merge")
                            await self._merge_src_folders(nested_src_dir, root_src_dir)
                            shutil.rmtree(nested_src_dir)
                            fixes_applied.append(f"Force removed nested src directory {nested_src_dir} after second merge attempt")
                except Exception as e:
                    logger.error(f"Error removing nested src directory: {e}")
                
                # Update angular.json to point to the correct src directory
                await self._update_angular_config_paths(False)  # Use root src
                fixes_applied.append("Updated angular.json configuration to use root src directory")
                
                changes_made = True
            
            elif nested_is_valid and not root_is_valid:
                # Nested src is valid, root one is invalid - move any unique files from root to nested
                await self._merge_src_folders(root_src_dir, nested_src_dir)
                fixes_applied.append(f"Merged files from {root_src_dir} to {nested_src_dir}")
                
                # Remove the root src directory after merging
                try:
                    if os.path.exists(root_src_dir):
                        if not os.listdir(root_src_dir):
                            # Directory is empty, remove it
                            shutil.rmtree(root_src_dir)
                            fixes_applied.append(f"Removed empty directory {root_src_dir}")
                        else:
                            # Directory still has files, force removal after another merge attempt
                            logger.warning(f"Root src directory {root_src_dir} still has files after merge, forcing another merge")
                            await self._merge_src_folders(root_src_dir, nested_src_dir)
                            shutil.rmtree(root_src_dir)
                            fixes_applied.append(f"Force removed root src directory {root_src_dir} after second merge attempt")
                except Exception as e:
                    logger.error(f"Error removing root src directory: {e}")
                
                # Update angular.json to point to the correct src directory
                await self._update_angular_config_paths(True)  # Use nested src
                fixes_applied.append("Updated angular.json configuration to use nested src directory")
                
                changes_made = True
            
            elif root_is_valid and nested_is_valid:
                # Both are valid - this is unusual, STRONGLY prefer root src
                logger.warning(f"Both src directories are valid in {self.project_name}, preferring root src")
                
                root_files_count = self._count_files_recursive(root_src_dir)
                nested_files_count = self._count_files_recursive(nested_src_dir)
                
                logger.info(f"Root src files: {root_files_count}, Nested src files: {nested_files_count}")
                
                # Only use nested if it has SIGNIFICANTLY more files
                if nested_files_count > root_files_count * 2:
                    logger.info(f"Nested src has significantly more files ({nested_files_count} vs {root_files_count}), using nested")
                    # Nested has way more files, use it instead
                    await self._merge_src_folders(root_src_dir, nested_src_dir)
                    fixes_applied.append(f"Merged files from {root_src_dir} to {nested_src_dir} (much more complete)")
                    
                    try:
                        if os.path.exists(root_src_dir):
                            shutil.rmtree(root_src_dir)
                            fixes_applied.append(f"Removed redundant root src directory {root_src_dir}")
                    except Exception as e:
                        logger.error(f"Error removing root src directory: {e}")
                    
                    # Update angular.json to point to the correct src directory
                    await self._update_angular_config_paths(True)  # Use nested src
                    fixes_applied.append("Updated angular.json configuration to use nested src directory")
                else:
                    # Root has comparable or more files, use it (the default approach)
                    await self._merge_src_folders(nested_src_dir, root_src_dir)
                    fixes_applied.append(f"Merged files from {nested_src_dir} to {root_src_dir} (preferred location)")
                    
                    try:
                        if os.path.exists(nested_src_dir):
                            shutil.rmtree(nested_src_dir)
                            fixes_applied.append(f"Removed redundant nested src directory {nested_src_dir}")
                    except Exception as e:
                        logger.error(f"Error removing nested src directory: {e}")
                    
                    # Update angular.json to point to the correct src directory
                    await self._update_angular_config_paths(False)  # Use root src
                    fixes_applied.append("Updated angular.json configuration to use root src directory")
                
                changes_made = True
        
        # Check if nested_project_dir exists but has no src folder
        elif os.path.exists(nested_project_dir) and not os.path.exists(nested_src_dir) and os.path.exists(root_src_dir):
            # If the nested project directory exists but has no src folder,
            # it might be an incomplete or malformed project structure
            if not os.listdir(nested_project_dir):
                # If nested_project_dir is empty, remove it
                shutil.rmtree(nested_project_dir)
                fixes_applied.append(f"Removed empty duplicate project directory {nested_project_dir}")
                changes_made = True
            else:
                # If it has other files, check if they should be moved to the root
                # This is a bit risky, but we'll move Angular-specific files
                for item in os.listdir(nested_project_dir):
                    item_path = os.path.join(nested_project_dir, item)
                    # Check if this is an Angular-specific file or directory that should be at the root
                    if item in ["angular.json", "tsconfig.json", "package.json", "node_modules", "e2e", "dist"]:
                        target_path = os.path.join(self.project_dir, item)
                        if not os.path.exists(target_path):
                            # If the file doesn't exist at the root, move it there
                            try:
                                shutil.move(item_path, target_path)
                                fixes_applied.append(f"Moved {item} from nested project directory to root")
                                changes_made = True
                            except Exception as e:
                                logger.error(f"Error moving {item} to root: {e}")
                
                # After moving files, if the directory is empty, remove it
                if not os.listdir(nested_project_dir):
                    shutil.rmtree(nested_project_dir)
                    fixes_applied.append(f"Removed empty nested project directory after moving files")
                    changes_made = True
        
        # Check for incorrect nested structure with node_modules or other folders
        if os.path.exists(nested_project_dir) and os.path.isdir(nested_project_dir):
            for folder in ['node_modules', 'dist', 'e2e', '.angular']:
                nested_folder = os.path.join(nested_project_dir, folder)
                root_folder = os.path.join(self.project_dir, folder)
                
                if os.path.exists(nested_folder) and os.path.exists(root_folder):
                    # Both exist, likely an error, prioritize root
                    logger.info(f"Found duplicate {folder} folder in both root and nested project directory")
                    try:
                        shutil.rmtree(nested_folder)
                        fixes_applied.append(f"Removed duplicate {folder} folder from nested project directory")
                        changes_made = True
                    except Exception as e:
                        logger.error(f"Error removing duplicate folder {nested_folder}: {e}")
                elif os.path.exists(nested_folder) and not os.path.exists(root_folder):
                    # Only nested exists, move it to root
                    try:
                        shutil.move(nested_folder, root_folder)
                        fixes_applied.append(f"Moved {folder} from nested project directory to root")
                        changes_made = True
                    except Exception as e:
                        logger.error(f"Error moving folder {nested_folder}: {e}")
        
        # Verify package.json location
        root_package_json = os.path.join(self.project_dir, "package.json")
        nested_package_json = os.path.join(nested_project_dir, "package.json")
        
        if os.path.exists(nested_package_json) and os.path.exists(root_package_json):
            # Both exist, compare which one is more complete
            try:
                with open(root_package_json, 'r', encoding='utf-8') as f:
                    root_pkg = json.load(f)
                with open(nested_package_json, 'r', encoding='utf-8') as f:
                    nested_pkg = json.load(f)
                
                root_deps_count = len(root_pkg.get('dependencies', {})) + len(root_pkg.get('devDependencies', {}))
                nested_deps_count = len(nested_pkg.get('dependencies', {})) + len(nested_pkg.get('devDependencies', {}))
                
                if root_deps_count >= nested_deps_count:
                    # Root has more dependencies, keep it
                    try:
                        os.remove(nested_package_json)
                        fixes_applied.append("Removed duplicate package.json from nested directory (keeping more complete version)")
                        changes_made = True
                    except Exception as e:
                        logger.error(f"Error removing nested package.json: {e}")
                else:
                    # Nested has more dependencies, merge it to root
                    try:
                        # Merge dependencies and devDependencies
                        root_deps = root_pkg.get('dependencies', {})
                        nested_deps = nested_pkg.get('dependencies', {})
                        merged_deps = {**root_deps, **nested_deps}
                        root_pkg['dependencies'] = merged_deps
                        
                        root_dev_deps = root_pkg.get('devDependencies', {})
                        nested_dev_deps = nested_pkg.get('devDependencies', {})
                        merged_dev_deps = {**root_dev_deps, **nested_dev_deps}
                        root_pkg['devDependencies'] = merged_dev_deps
                        
                        # Write the updated package.json
                        with open(root_package_json, 'w', encoding='utf-8') as f:
                            json.dump(root_pkg, f, indent=2)
                        
                        # Remove the nested package.json
                        os.remove(nested_package_json)
                        fixes_applied.append("Merged nested package.json with root package.json and removed duplicate")
                        changes_made = True
                    except Exception as e:
                        logger.error(f"Error merging package.json files: {e}")
            except Exception as e:
                logger.error(f"Error comparing package.json files: {e}")
                
        elif os.path.exists(nested_package_json) and not os.path.exists(root_package_json):
            # Only nested exists, move it to root
            try:
                shutil.move(nested_package_json, root_package_json)
                fixes_applied.append("Moved package.json from nested directory to root")
                changes_made = True
            except Exception as e:
                logger.error(f"Error moving nested package.json to root: {e}")
        
        # Check for valid angular.json
        angular_json_path = os.path.join(self.project_dir, "angular.json")
        if not os.path.exists(angular_json_path):
            nested_angular_json = os.path.join(nested_project_dir, "angular.json")
            if os.path.exists(nested_angular_json):
                # Move angular.json to root
                try:
                    shutil.move(nested_angular_json, angular_json_path)
                    fixes_applied.append("Moved angular.json from nested directory to root")
                    
                    # Update paths in angular.json to account for the move
                    await self._update_angular_config_paths(False)
                    changes_made = True
                except Exception as e:
                    logger.error(f"Error moving angular.json to root: {e}")
        
        # Additional cleanup if nested directory is now empty
        if os.path.exists(nested_project_dir) and os.path.isdir(nested_project_dir):
            try:
                if not os.listdir(nested_project_dir):
                    shutil.rmtree(nested_project_dir)
                    fixes_applied.append(f"Removed empty nested project directory {nested_project_dir}")
                    changes_made = True
            except Exception as e:
                logger.error(f"Error checking/removing nested project directory: {e}")
        
        # Final result
        if changes_made:
            logger.info(f"Successfully fixed Angular folder structure: {fixes_applied}")
        else:
            logger.info(f"No changes needed for Angular folder structure in {self.project_name}")
        
        return {
            "success": True,
            "message": "Angular folder structure validation completed",
            "changes_made": changes_made,
            "fixes_applied": fixes_applied
        }

    async def pre_build_validation(self) -> Dict[str, Any]:
        """
        Run pre-build validation to ensure project structure is correct.
        This addresses critical structural issues before attempting to build.
        
        Returns:
            Dictionary with validation results
        """
        logger.info(f"Running pre-build validation for {self.project_name}")
        
        # Detect project framework
        if not self.framework_detection.get("framework", "unknown") != "unknown":
            self.framework_detection = self._detect_framework()
        
        fixes_applied = []
        validations_run = []
        
        # For Angular projects, validate folder structure
        if self.framework_detection.get("is_angular", False):
            validations_run.append("angular_structure")
            angular_structure_result = await self.validate_angular_folder_structure()
            
            if angular_structure_result.get("changes_made", False):
                fixes_applied.extend(angular_structure_result.get("fixes_applied", []))
            
            # Also validate Angular routing
            validations_run.append("angular_routing")
            angular_routing_result = await self.verify_and_fix_angular_routing()
            
            if angular_routing_result.get("success", False):
                fixes_applied.extend(angular_routing_result.get("fixes_applied", []))
        
        # For React projects, validate structure (to be implemented)
        elif self.framework_detection.get("is_react", False):
            validations_run.append("react_structure")
            # TODO: Implement React structure validation
            pass
        
        # For Vue projects, validate structure (to be implemented)
        elif self.framework_detection.get("is_vue", False):
            validations_run.append("vue_structure")
            # TODO: Implement Vue structure validation
            pass
        
        # For all project types, check for common issues
        validations_run.append("common_structure")
        
        # Check for duplicate directories with the same name as the project
        nested_project_dir = os.path.join(self.project_dir, self.project_name)
        if os.path.exists(nested_project_dir) and os.path.isdir(nested_project_dir):
            # Count files in root and nested
            root_files = sum(1 for _ in os.walk(self.project_dir)) - sum(1 for _ in os.walk(nested_project_dir))
            nested_files = sum(1 for _ in os.walk(nested_project_dir))
            
            # If nested has very few files compared to root, it's likely a mistake
            if nested_files < root_files * 0.1:  # Less than 10% of root files
                try:
                    # Move any important files to root first
                    for item in os.listdir(nested_project_dir):
                        item_path = os.path.join(nested_project_dir, item)
                        target_path = os.path.join(self.project_dir, item)
                        if not os.path.exists(target_path) and not item.startswith('.'):
                            try:
                                if os.path.isdir(item_path):
                                    shutil.copytree(item_path, target_path)
                                else:
                                    shutil.copy2(item_path, target_path)
                                logger.info(f"Copied {item} from nested directory to root")
                            except Exception as e:
                                logger.error(f"Error copying {item} to root: {e}")
                    
                    # Then remove the nested directory
                    shutil.rmtree(nested_project_dir)
                    fixes_applied.append(f"Removed redundant nested project directory {nested_project_dir} after copying important files")
                except Exception as e:
                    logger.error(f"Error cleaning up nested project directory: {e}")
        
        return {
            "success": True,
            "validations_run": validations_run,
            "fixes_applied": fixes_applied,
            "message": "Pre-build validation completed successfully"
        }
        
    def _is_valid_angular_src(self, src_dir: str) -> bool:
        """
        Check if a directory is a valid Angular src folder by looking for key files.
        
        Args:
            src_dir: Path to the src directory to check
            
        Returns:
            True if it appears to be a valid Angular src folder, False otherwise
        """
        # Check for key Angular files/folders
        app_dir = os.path.join(src_dir, "app")
        main_ts = os.path.join(src_dir, "main.ts")
        index_html = os.path.join(src_dir, "index.html")
        
        # Basic validation - must have app directory
        if not os.path.exists(app_dir) or not os.path.isdir(app_dir):
            logger.info(f"Directory {src_dir} doesn't have app subdirectory")
            return False
        
        # Should have either main.ts for newer Angular or main.js for older versions
        has_main = os.path.exists(main_ts) or os.path.exists(os.path.join(src_dir, "main.js"))
        
        # Should have index.html
        has_index = os.path.exists(index_html)
        
        if not has_main or not has_index:
            logger.info(f"Directory {src_dir} missing main.ts/js ({has_main}) or index.html ({has_index})")
        
        # Check app directory contents
        if os.path.exists(app_dir) and os.path.isdir(app_dir):
            app_files = os.listdir(app_dir)
            # Look for component files
            has_components = any(
                file.endswith('.component.ts') or file.endswith('.component.js') 
                for file in app_files
            )
            # Look for module files
            has_modules = any(
                file.endswith('.module.ts') or file.endswith('.module.js') 
                for file in app_files
            )
            
            if not (has_components or has_modules):
                logger.info(f"Directory {src_dir}/app lacks component or module files")
            
            # Valid if:
            # 1. Has main.ts/js and index.html, or
            # 2. Has component or module files in the app directory
            return (has_main and has_index) or (has_components or has_modules)
        
        return False
    
    def _count_files_recursive(self, directory: str) -> int:
        """
        Count the number of files in a directory and its subdirectories.
        
        Args:
            directory: Path to the directory to count files in
            
        Returns:
            Number of files found
        """
        count = 0
        for root, _, files in os.walk(directory):
            count += len(files)
        return count
    
    async def _merge_src_folders(self, source_dir: str, target_dir: str) -> None:
        """
        Merge files from source_dir into target_dir, handling duplicates intelligently.
        
        Args:
            source_dir: Source directory to merge from
            target_dir: Target directory to merge into
        """
        logger.info(f"Merging src folders from {source_dir} to {target_dir}")
        
        # Make sure target directory exists
        os.makedirs(target_dir, exist_ok=True)
        
        # Walk through the source directory
        for root, dirs, files in os.walk(source_dir):
            # Get the relative path from source_dir
            rel_path = os.path.relpath(root, source_dir)
            target_path = os.path.join(target_dir, rel_path) if rel_path != '.' else target_dir
            
            # Create directories if they don't exist
            os.makedirs(target_path, exist_ok=True)
            
            # Copy files
            for file in files:
                source_file = os.path.join(root, file)
                target_file = os.path.join(target_path, file)
                
                try:
                    if not os.path.exists(target_file):
                        # File doesn't exist in target, just copy it
                        shutil.copy2(source_file, target_file)
                        logger.info(f"Copied {source_file} to {target_file}")
                    else:
                        # File exists in both places, check which is newer/larger
                        source_stat = os.stat(source_file)
                        target_stat = os.stat(target_file)
                        
                        # Compare modification times - newer file wins
                        if source_stat.st_mtime > target_stat.st_mtime:
                            # Source is newer
                            shutil.copy2(source_file, target_file)
                            logger.info(f"Updated {target_file} (newer version found)")
                        # If timestamps are very close, compare sizes
                        elif abs(source_stat.st_mtime - target_stat.st_mtime) < 60 and source_stat.st_size > target_stat.st_size:
                            # Source is larger, might have more content
                            shutil.copy2(source_file, target_file)
                            logger.info(f"Updated {target_file} (larger version found)")
                except Exception as e:
                    logger.error(f"Error merging file {source_file} to {target_file}: {e}")
    
    async def _update_angular_config_paths(self, use_nested: bool) -> None:
        """
        Update paths in angular.json to use the correct src directory.
        
        Args:
            use_nested: Whether to configure paths for the nested src directory
        """
        angular_json_path = os.path.join(self.project_dir, "angular.json")
        if not os.path.exists(angular_json_path):
            logger.warning(f"Cannot update angular.json paths: file not found at {angular_json_path}")
            return
        
        try:
            with open(angular_json_path, 'r', encoding='utf-8') as f:
                angular_config = json.load(f)
            
            # Check if there are any projects defined
            if 'projects' not in angular_config or not angular_config['projects']:
                logger.warning("No projects found in angular.json")
                return
            
            # Try to get the project using the project name, or fallback to the first project
            project_keys = list(angular_config.get('projects', {}).keys())
            if not project_keys:
                logger.warning("No projects found in angular.json")
                return
                
            # Try to find the project by name or use the first one
            project_name = self.project_name if self.project_name in project_keys else project_keys[0]
            if project_name not in angular_config['projects']:
                logger.warning(f"Project {project_name} not found in angular.json, using first project instead")
                project_name = project_keys[0]
                
            project = angular_config['projects'][project_name]
            
            # Update source root based on use_nested flag
            if 'sourceRoot' in project:
                if use_nested:
                    if not project['sourceRoot'].startswith(f"{self.project_name}/"):
                        project['sourceRoot'] = f"{self.project_name}/src"
                        logger.info(f"Updated sourceRoot to {project['sourceRoot']}")
                else:
                    if project['sourceRoot'].startswith(f"{self.project_name}/"):
                        project['sourceRoot'] = project['sourceRoot'].replace(f"{self.project_name}/", "")
                        logger.info(f"Updated sourceRoot to {project['sourceRoot']}")
            
            # Update architect configurations
            architect = project.get('architect', {})
            
            # Process paths based on use_nested flag
            if use_nested:
                # Update to use nested paths
                for config_name, config in architect.items():
                    if 'options' in config:
                        options = config['options']
                        for option_key in ['main', 'index', 'polyfills', 'tsConfig', 'karmaConfig']:
                            if option_key in options and isinstance(options[option_key], str):
                                if options[option_key].startswith('src/') and not options[option_key].startswith(f"{self.project_name}/src/"):
                                    options[option_key] = options[option_key].replace('src/', f"{self.project_name}/src/")
                                    logger.info(f"Updated {config_name}.options.{option_key} to {options[option_key]}")
                        
                        # Handle arrays of assets, styles, scripts
                        for array_key in ['assets', 'styles', 'scripts']:
                            if array_key in options and isinstance(options[array_key], list):
                                for i, item in enumerate(options[array_key]):
                                    if isinstance(item, str) and item.startswith('src/') and not item.startswith(f"{self.project_name}/src/"):
                                        options[array_key][i] = item.replace('src/', f"{self.project_name}/src/")
                                        logger.info(f"Updated {config_name}.options.{array_key}[{i}] to {options[array_key][i]}")
            else:
                # Update to use root paths
                for config_name, config in architect.items():
                    if 'options' in config:
                        options = config['options']
                        for option_key in ['main', 'index', 'polyfills', 'tsConfig', 'karmaConfig']:
                            if option_key in options and isinstance(options[option_key], str):
                                if options[option_key].startswith(f"{self.project_name}/src/"):
                                    options[option_key] = options[option_key].replace(f"{self.project_name}/src/", 'src/')
                                    logger.info(f"Updated {config_name}.options.{option_key} to {options[option_key]}")
                        
                        # Handle arrays of assets, styles, scripts
                        for array_key in ['assets', 'styles', 'scripts']:
                            if array_key in options and isinstance(options[array_key], list):
                                for i, item in enumerate(options[array_key]):
                                    if isinstance(item, str) and item.startswith(f"{self.project_name}/src/"):
                                        options[array_key][i] = item.replace(f"{self.project_name}/src/", 'src/')
                                        logger.info(f"Updated {config_name}.options.{array_key}[{i}] to {options[array_key][i]}")
            
            # Write updated angular.json
            with open(angular_json_path, 'w', encoding='utf-8') as f:
                json.dump(angular_config, f, indent=2)
            
            logger.info(f"Successfully updated angular.json to use {'nested' if use_nested else 'root'} src paths")
            
        except Exception as e:
            logger.error(f"Error updating angular.json paths: {e}")
            import traceback
            logger.error(traceback.format_exc())
