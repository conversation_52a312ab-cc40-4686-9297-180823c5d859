"""
Research agent that identifies information needs and gathers relevant data.
"""
import json
import logging
import re
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio

from jinja2 import Environment, FileSystemLoader
from backend.src.llm.llm import LLM
from backend.src.socket_instance import emit_message, emit_agent_message
from backend.src.llm.searxng_client import searxng_search

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Researcher:
    """
    Research agent that identifies information needs and gathers relevant data.
    """
    def __init__(self, model_id: str):
        """
        Initialize the Researcher with a model ID.
        
        Args:
            model_id: The ID of the LLM model to use
        """
        self.model_id = model_id
        self.llm = LLM.create(model_id)
        self.env = Environment(loader=FileSystemLoader("templates"))
        
        # Dictionary to cache search results to avoid redundant queries
        self.search_cache = {}
        
        # Track the last time a cache cleanup was performed
        self.last_cache_cleanup = datetime.now()
        
        # Maximum age of cached results in seconds (2 hours)
        self.cache_max_age = 7200
        
        # Maximum number of searches to perform for a single topic
        self.max_searches_per_topic = 3
        
        # Store recent research topics for context
        self.recent_topics = []
        self.max_recent_topics = 5
    
    async def execute(self, plan: Dict[str, Any], keywords: List[str], project_name: str) -> Dict[str, Any]:
        """
        Execute the research process based on a plan and keywords.
        
        Args:
            plan: The plan from the planner
            keywords: Context keywords for research
            project_name: The name of the project
            
        Returns:
            Research results
        """
        # Clean up the cache if needed
        self._cleanup_cache()
        
        logger.info(f"[Researcher] Executing research for project: {project_name}")
        logger.debug(f"[Researcher] Plan: {plan}")
        logger.debug(f"[Researcher] Keywords: {keywords}")
        template = self.env.get_template("researcher_prompt.jinja2")
        formatted_prompt = template.render(
            plan=json.dumps(plan, indent=2),
            keywords=keywords,
            project_name=project_name
        )
        
        await emit_message("research_started", {"project_name": project_name})
        await emit_agent_message(project_name, "I'm analyzing your request to identify any information needs...")
        
        response = await self.llm.generate(formatted_prompt, project_name)
        logger.debug(f"[Researcher] LLM response: {response}")
        
        research_results = self.parse_response(response)
        logger.debug(f"[Researcher] Parsed research results: {research_results}")
        
        # Add search queries to recent topics
        for query in research_results.get("queries", []):
            self._add_to_recent_topics(query)
        
        # Perform web searches if needed
        if research_results.get("requires_web_search", False) and research_results.get("queries"):
            logger.info(f"[Researcher] Performing web searches for {len(research_results['queries'])} queries")
            search_results = await self._execute_parallel_searches(research_results["queries"], project_name)
            
            # Analyze and organize search results
            organized_results = await self._analyze_search_results(
                research_results["queries"], 
                search_results, 
                project_name
            )
            
            research_results["search_results"] = search_results
            research_results["organized_results"] = organized_results
        
        await emit_message("research_completed", {"project_name": project_name})
        
        if research_results.get("queries"):
            query_list = "\n".join([f"- {query}" for query in research_results["queries"]])
            await emit_agent_message(
                project_name,
                f"I've identified the following research topics:\n\n{query_list}"
            )
            
            if research_results.get("organized_results"):
                await emit_agent_message(
                    project_name,
                    f"I've gathered and analyzed information from the web to help with your request."
                )
        
        return research_results
    
    def parse_response(self, response: str) -> Dict[str, Any]:
        """
        Parse the LLM response into structured research results.
        
        Args:
            response: The raw LLM response
            
        Returns:
            A dictionary containing the parsed research results
        """
        result = {
            "queries": [],
            "context": {},
            "requires_web_search": False
        }
        
        current_section = None
        current_query = None
        
        for line in response.split("\n"):
            line = line.strip()
            
            if not line:
                continue
                
            if line.startswith("QUERIES:"):
                current_section = "queries"
            elif line.startswith("CONTEXT:"):
                current_section = "context"
            elif line.startswith("WEB_SEARCH:"):
                result["requires_web_search"] = "yes" in line.lower() or "true" in line.lower()
            elif current_section == "queries" and line.startswith("- "):
                result["queries"].append(line[2:].strip())
            elif current_section == "context" and ":" in line:
                key, value = line.split(":", 1)
                result["context"][key.strip()] = value.strip()
        
        # If we found no queries but the response mentions web search, try to extract queries
        if not result["queries"] and result["requires_web_search"]:
            # Look for patterns that might indicate search queries
            query_patterns = [
                r'(?:search|query)(?:ing)? for[:\s]+"([^"]+)"',
                r'(?:search|query)(?:ing)? for[:\s]+\'([^\']+)\'',
                r'(?:search|query)(?:ing)? for[:\s]+([^\n\.,]+)',
                r'(?:need|should|could|would) (?:to )?(?:search|query|research|find information) (?:about|on|regarding)[:\s]+([^\n\.,]+)'
            ]
            
            for pattern in query_patterns:
                matches = re.findall(pattern, response, re.IGNORECASE)
                for match in matches:
                    if match.strip() not in result["queries"]:
                        result["queries"].append(match.strip())
        
        # If we still found no queries but web search is required, generate a default query
        if not result["queries"] and result["requires_web_search"] and "project_name" in result["context"]:
            result["queries"].append(f"{result['context']['project_name']} tutorial")
            result["queries"].append(f"{result['context']['project_name']} getting started")
        
        return result
    
    async def search_web(self, queries: List[str], project_name: str) -> Dict[str, Any]:
        """
        Perform web searches for the given queries.
        
        Args:
            queries: List of search queries
            project_name: The name of the project
            
        Returns:
            Search results
        """
        # Clean up the cache if needed
        self._cleanup_cache()
        
        await emit_agent_message(
            project_name,
            "I'm searching the web for information..."
        )
        
        # Execute searches in parallel
        search_results = await self._execute_parallel_searches(queries, project_name)
        
        # Analyze and organize search results
        organized_results = await self._analyze_search_results(queries, search_results, project_name)
        
        await emit_agent_message(
            project_name,
            "I've gathered information from the web to help with your request."
        )
        
        return {
            "queries": queries,
            "results": search_results,
            "organized_results": organized_results
        }
    
    async def _execute_parallel_searches(self, queries: List[str], project_name: str) -> Dict[str, str]:
        """
        Execute multiple search queries in parallel.
        
        Args:
            queries: List of search queries
            project_name: Name of the project
            
        Returns:
            Dictionary mapping queries to search results
        """
        results = {}
        search_tasks = []
        queries_to_search = []
        
        # Create a task for each search query
        for query in queries:
            # Check if we have a cached result for this query
            if query in self.search_cache:
                logger.info(f"[Researcher] Using cached result for query: {query}")
                results[query] = self.search_cache[query]
            else:
                # Create a task for this query
                search_tasks.append(self._execute_search(query))
                queries_to_search.append(query)
        
        # Execute all search tasks in parallel
        if search_tasks:
            search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            # Process the results
            for i, query in enumerate(queries_to_search):
                # Handle exceptions
                if isinstance(search_results[i], Exception):
                    logger.error(f"[Researcher] Error searching for '{query}': {search_results[i]}")
                    results[query] = f"Error: {str(search_results[i])}"
                else:
                    results[query] = search_results[i]
                    # Cache the result
                    timestamp = datetime.now()
                    self.search_cache[query] = (search_results[i], timestamp)
        
        return results
    
    async def _execute_search(self, query: str) -> str:
        """
        Execute a single search query.
        
        Args:
            query: Search query
            
        Returns:
            Search results as text
        """
        logger.info(f"[Researcher] Executing search for: {query}")
        try:
            # Use SearxNG to perform the search
            results = await searxng_search(query)
            return results
        except Exception as e:
            logger.error(f"[Researcher] Error during search for '{query}': {e}")
            raise
    
    async def _analyze_search_results(self, queries: List[str], search_results: Dict[str, str], project_name: str) -> Dict[str, Any]:
        """
        Analyze and organize search results.
        
        Args:
            queries: List of search queries
            search_results: Dictionary of search results
            project_name: Name of the project
            
        Returns:
            Organized research results
        """
        # Combine all search results into a single text
        combined_results = "\n\n".join([
            f"Results for '{query}':\n{results}"
            for query, results in search_results.items()
        ])
        
        # Prepare a prompt for the LLM to analyze the results
        analysis_prompt = f"""
        You are a research assistant helping to analyze search results for the following project:
        
        PROJECT: {project_name}
        
        SEARCH QUERIES:
        {", ".join(queries)}
        
        Below are the search results from multiple queries. Please analyze these results and extract the most relevant information.
        Organize the information into the following categories:
        1. Key Facts: Important factual information relevant to the project
        2. Technical Details: Technical specifications, code examples, or implementation details
        3. Best Practices: Recommended approaches or best practices
        4. Common Issues: Known problems, limitations, or gotchas
        5. Resources: Useful links, tutorials, or documentation
        
        For each piece of information, include the source if available.
        
        SEARCH RESULTS:
        {combined_results}
        
        Format your response as JSON with the categories as keys. The value for each key should be an array of relevant points.
        """
        
        try:
            # Ask the LLM to analyze the results
            analysis = await self.llm.generate(analysis_prompt, project_name)
            
            # Try to extract JSON from the response
            json_match = re.search(r'```(?:json)?\s*([\s\S]+?)\s*```', analysis)
            if json_match:
                try:
                    organized_results = json.loads(json_match.group(1).strip())
                    logger.info(f"[Researcher] Successfully parsed JSON analysis")
                    return {
                        "analysis": organized_results,
                        "summary": self._generate_summary(organized_results)
                    }
                except json.JSONDecodeError:
                    logger.warning(f"[Researcher] Failed to parse JSON analysis, falling back to raw format")
            
            # Fallback to returning the raw analysis
            return {
                "analysis": analysis,
                "summary": "See full analysis for details"
            }
            
        except Exception as e:
            logger.error(f"[Researcher] Error analyzing search results: {e}")
            return {
                "error": str(e),
                "summary": "Error analyzing search results"
            }
    
    def _generate_summary(self, organized_results: Dict[str, List[str]]) -> str:
        """
        Generate a summary of the organized results.
        
        Args:
            organized_results: Dictionary of organized results
            
        Returns:
            Summary text
        """
        summary_parts = []
        
        # Add a summary for each category
        for category, items in organized_results.items():
            if items:
                # Clean up the category name for display
                display_category = category.replace('_', ' ').title()
                
                # Add the category header
                summary_parts.append(f"{display_category}: {len(items)} items found")
                
                # Add a brief summary of the items
                if len(items) <= 3:
                    # If there are only a few items, include all of them
                    for item in items:
                        # Truncate long items
                        if isinstance(item, str):
                            summary = item[:100] + "..." if len(item) > 100 else item
                            summary_parts.append(f"- {summary}")
                else:
                    # If there are many items, just include a count
                    summary_parts.append(f"- Found {len(items)} relevant points")
        
        # Combine the summary parts
        if summary_parts:
            return "\n".join(summary_parts)
        else:
            return "No relevant information found"
    
    def _cleanup_cache(self) -> None:
        """
        Clean up old entries from the search cache.
        """
        now = datetime.now()
        
        # Only clean up once per hour
        if (now - self.last_cache_cleanup).total_seconds() < 3600:
            return
        
        self.last_cache_cleanup = now
        
        # Remove entries older than the max age
        keys_to_remove = []
        for query, (result, timestamp) in self.search_cache.items():
            if (now - timestamp).total_seconds() > self.cache_max_age:
                keys_to_remove.append(query)
        
        for key in keys_to_remove:
            del self.search_cache[key]
            
        logger.info(f"[Researcher] Cleaned up {len(keys_to_remove)} old cache entries")
    
    def _extract_keywords(self, text: str) -> List[str]:
        """
        Extract keywords from text.
        
        Args:
            text: The text to extract keywords from
            
        Returns:
            List of keywords
        """
        # Remove common words and symbols
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = text.split()
        
        # Common words to filter out
        common_words = {
            "the", "and", "a", "an", "in", "on", "at", "to", "for", "with", "about",
            "is", "are", "am", "was", "were", "be", "been", "being",
            "have", "has", "had", "do", "does", "did", "will", "would", "shall", "should",
            "can", "could", "may", "might", "must", "of", "from", "by", "research",
            "find", "search", "information", "me", "my", "mine", "you", "your", "yours"
        }
        
        # Filter out common words and short words
        keywords = [word for word in words if word not in common_words and len(word) > 2]
        
        # Remove duplicates while preserving order
        unique_keywords = []
        for keyword in keywords:
            if keyword not in unique_keywords:
                unique_keywords.append(keyword)
        
        return unique_keywords
    
    def _add_to_recent_topics(self, topic: str) -> None:
        """
        Add a topic to the list of recent topics.
        
        Args:
            topic: The topic to add
        """
        # Only add if it's not already in the list
        if topic not in self.recent_topics:
            self.recent_topics.append(topic)
            
            # Trim the list if it's too long
            if len(self.recent_topics) > self.max_recent_topics:
                self.recent_topics.pop(0)
