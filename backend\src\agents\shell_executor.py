"""
ShellExecutor - Handles shell command execution
"""
import os
import subprocess
import asyncio
import platform
import logging
import time
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class ShellExecutor:
    """
    Utility class to run shell commands and capture output, errors, and exit code.
    """
    @staticmethod
    def run_command(command: str, cwd: Optional[str] = None, timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Run a shell command and capture output.
        Args:
            command: The shell command to run
            cwd: Working directory to run the command in
            timeout: Optional timeout in seconds
        Returns:
            Dict with keys: 'stdout', 'stderr', 'exit_code', 'success', 'command', 'cwd'
        """
        logger.info(f"[ShellExecutor] Running command: {command} (cwd={cwd})")
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            logger.info(f"[ShellExecutor] Command finished with exit code {result.returncode}")
            if result.stdout:
                logger.debug(f"[ShellExecutor] STDOUT: {result.stdout}")
            if result.stderr:
                logger.debug(f"[ShellExecutor] STDERR: {result.stderr}")
            return {
                "command": command,
                "cwd": cwd,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "exit_code": result.returncode,
                "success": result.returncode == 0
            }
        except subprocess.TimeoutExpired as e:
            logger.error(f"[ShellExecutor] Command timed out: {command}")
            return {
                "command": command,
                "cwd": cwd,
                "stdout": e.stdout or '',
                "stderr": e.stderr or 'Timeout expired',
                "exit_code": -1,
                "success": False
            }
        except Exception as e:
            logger.error(f"[ShellExecutor] Exception running command: {command}: {e}")
            return {
                "command": command,
                "cwd": cwd,
                "stdout": '',
                "stderr": str(e),
                "exit_code": -1,
                "success": False
            } 