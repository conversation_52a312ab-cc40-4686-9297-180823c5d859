"""
Smart Project Executor - Streamlined and intelligent project creation and management.

This module replaces the complex project_executor.py with a cleaner, more reliable approach
that focuses on getting the basics right without over-engineering.
"""
import os
import logging
import shutil
import asyncio
import json
from typing import Dict, Any, Optional, List
from pathlib import Path

from backend.src.agents.shell_executor import ShellExecutor
from backend.src.socket_instance import emit_terminal_command, emit_agent_file_update

logger = logging.getLogger(__name__)

class SmartProjectExecutor:
    """
    A streamlined project executor that focuses on reliability and simplicity.
    """
    
    def __init__(self, project_name: str, projects_base_dir: str):
        """Initialize the smart project executor."""
        self.project_name = project_name
        self.projects_base_dir = projects_base_dir
        self.project_dir = os.path.join(projects_base_dir, project_name)
        self.shell_executor = ShellExecutor()
        self.framework_type = None
        
        # Ensure project directory exists
        os.makedirs(self.project_dir, exist_ok=True)
        
        logger.info(f"SmartProjectExecutor initialized for project: {project_name}")
        logger.info(f"Project directory: {self.project_dir}")
    
    async def create_angular_project(self, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create a new Angular project using Angular CLI.
        
        This method creates the project directly in the correct location without
        complex path manipulations that cause issues.
        """
        options = options or {}
        
        try:
            # Check if Angular CLI is available
            ng_check = await self.shell_executor.run_command("ng version", timeout=30)
            if not ng_check["success"]:
                return {
                    "success": False,
                    "error": "Angular CLI not found. Please install it with: npm install -g @angular/cli"
                }
            
            # Clean the project directory if it exists and has content
            if os.path.exists(self.project_dir) and os.listdir(self.project_dir):
                logger.info(f"Cleaning existing project directory: {self.project_dir}")
                shutil.rmtree(self.project_dir)
                os.makedirs(self.project_dir, exist_ok=True)
            
            # Build Angular CLI command
            cmd_parts = ["ng", "new", self.project_name]
            
            # Add standard options
            cmd_parts.extend([
                "--routing=true",
                "--style=scss", 
                "--skip-git=true",
                "--skip-install=false",  # Let it install dependencies
                "--package-manager=npm"
            ])
            
            # Add custom options
            for key, value in options.items():
                if key not in ["routing", "style", "skip-git", "skip-install", "package-manager"]:
                    if isinstance(value, bool):
                        cmd_parts.append(f"--{key}={'true' if value else 'false'}")
                    else:
                        cmd_parts.append(f"--{key}={value}")
            
            cmd = " ".join(cmd_parts)
            
            # Run the command in the parent directory so Angular CLI creates the project folder
            parent_dir = os.path.dirname(self.project_dir)
            
            await emit_terminal_command(self.project_name, f"Creating Angular project: {cmd}")
            logger.info(f"Running Angular CLI command: {cmd} in directory: {parent_dir}")
            
            result = await self.shell_executor.run_command(
                cmd, 
                cwd=parent_dir, 
                timeout=600  # 10 minutes for project creation and npm install
            )
            
            if result["success"]:
                # Verify the project was created correctly
                expected_files = ["package.json", "angular.json", "src/app/app.component.ts"]
                missing_files = []
                
                for file_path in expected_files:
                    full_path = os.path.join(self.project_dir, file_path)
                    if not os.path.exists(full_path):
                        missing_files.append(file_path)
                
                if missing_files:
                    logger.warning(f"Angular project created but missing files: {missing_files}")
                    return {
                        "success": False,
                        "error": f"Angular project incomplete. Missing files: {missing_files}"
                    }
                
                self.framework_type = "angular"
                await emit_terminal_command(self.project_name, "Angular project created successfully!")
                
                return {
                    "success": True,
                    "message": "Angular project created successfully",
                    "project_dir": self.project_dir,
                    "framework": "angular"
                }
            else:
                error_msg = result.get("stderr", "Unknown error during Angular project creation")
                logger.error(f"Angular project creation failed: {error_msg}")
                await emit_terminal_command(self.project_name, f"Angular project creation failed: {error_msg}")
                
                return {
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            logger.error(f"Exception during Angular project creation: {e}")
            return {
                "success": False,
                "error": f"Exception during project creation: {str(e)}"
            }
    
    async def create_file(self, file_path: str, content: str) -> Dict[str, Any]:
        """
        Create a file with proper path handling.
        
        This method uses simple, reliable path logic without over-engineering.
        """
        try:
            # Normalize the file path
            file_path = file_path.replace("\\", "/").strip("/")
            
            # Remove any project name prefix to avoid nesting
            if file_path.startswith(f"{self.project_name}/"):
                file_path = file_path[len(f"{self.project_name}/"):]
            
            # Create absolute path
            abs_file_path = os.path.join(self.project_dir, file_path)
            
            # Security check: ensure file is within project directory
            abs_project_dir = os.path.abspath(self.project_dir)
            abs_file_path = os.path.abspath(abs_file_path)
            
            if not abs_file_path.startswith(abs_project_dir):
                logger.warning(f"File path outside project directory: {file_path}")
                # Force it to be in the project directory
                file_path = os.path.basename(file_path)
                abs_file_path = os.path.join(self.project_dir, file_path)
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(abs_file_path), exist_ok=True)
            
            # Write the file
            with open(abs_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Get relative path for logging
            rel_path = os.path.relpath(abs_file_path, self.project_dir)
            
            logger.info(f"Created file: {rel_path}")
            await emit_agent_file_update(self.project_name, abs_file_path, content)
            
            return {
                "success": True,
                "file_path": rel_path,
                "message": f"Created file: {rel_path}"
            }
            
        except Exception as e:
            logger.error(f"Error creating file {file_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to create file: {file_path}"
            }
    
    async def run_command(self, command: str, cwd: Optional[str] = None) -> Dict[str, Any]:
        """Run a command in the project directory or specified directory."""
        if cwd is None:
            cwd = self.project_dir
            
        await emit_terminal_command(self.project_name, f"Running: {command}")
        result = await self.shell_executor.run_command(command, cwd=cwd, timeout=300)
        
        if result["success"]:
            await emit_terminal_command(self.project_name, f"Command completed successfully")
        else:
            await emit_terminal_command(self.project_name, f"Command failed: {result.get('stderr', 'Unknown error')}")
        
        return result
    
    def detect_framework(self) -> str:
        """Detect the framework type of the current project."""
        if self.framework_type:
            return self.framework_type
            
        # Check for Angular
        if os.path.exists(os.path.join(self.project_dir, "angular.json")):
            self.framework_type = "angular"
            return "angular"
        
        # Check for React
        package_json_path = os.path.join(self.project_dir, "package.json")
        if os.path.exists(package_json_path):
            try:
                with open(package_json_path, 'r') as f:
                    package_data = json.load(f)
                    dependencies = package_data.get("dependencies", {})
                    if "react" in dependencies:
                        self.framework_type = "react"
                        return "react"
                    elif "@angular/core" in dependencies:
                        self.framework_type = "angular"
                        return "angular"
            except Exception:
                pass
        
        # Check for Vue
        if os.path.exists(os.path.join(self.project_dir, "vue.config.js")):
            self.framework_type = "vue"
            return "vue"
        
        # Default
        self.framework_type = "unknown"
        return "unknown"
    
    async def build_project(self) -> Dict[str, Any]:
        """Build the project based on detected framework."""
        framework = self.detect_framework()
        
        if framework == "angular":
            return await self.run_command("npm run build")
        elif framework == "react":
            return await self.run_command("npm run build")
        elif framework == "vue":
            return await self.run_command("npm run build")
        else:
            return {
                "success": False,
                "error": f"Unknown framework: {framework}. Cannot determine build command."
            }
    
    async def start_dev_server(self) -> Dict[str, Any]:
        """Start the development server for the project."""
        framework = self.detect_framework()
        
        if framework == "angular":
            return await self.run_command("npm start")
        elif framework == "react":
            return await self.run_command("npm start")
        elif framework == "vue":
            return await self.run_command("npm run serve")
        else:
            return {
                "success": False,
                "error": f"Unknown framework: {framework}. Cannot determine start command."
            }
