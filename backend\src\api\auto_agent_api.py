"""
API endpoints for the AutoAgent system.

This module provides REST API endpoints for interacting with the fully autonomous
AutoAgent system for end-to-end project creation.
"""

import os
import time
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from pydantic import BaseModel

from src.agents.auto_agent import AutoAgent
from src.state import AgentState

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(prefix="/auto-agent", tags=["AutoAgent"])

# Global AutoAgent instance
auto_agent = AutoAgent()

# Model for creating a new project
class ProjectCreateRequest(BaseModel):
    project_name: str
    prompt: str
    model_id: Optional[str] = "openai/gpt-4o"
    local_model_id: Optional[str] = None

# Model for project status response
class ProjectStatusResponse(BaseModel):
    project_name: str
    status: str
    execution_id: Optional[str] = None
    progress: Optional[Dict[str, Any]] = None
    start_time: Optional[float] = None
    completion_time: Optional[float] = None
    execution_time: Optional[float] = None
    success: Optional[bool] = None
    error: Optional[str] = None

# Active project executions
active_executions: Dict[str, Dict[str, Any]] = {}

# WebSocket connections for streaming updates
websocket_connections: Dict[str, List[WebSocket]] = {}

# Function to send updates to all connected WebSockets for a project
async def broadcast_to_project_websockets(project_name: str, data: Dict[str, Any]) -> None:
    """
    Broadcast a message to all WebSocket connections for a project.
    
    Args:
        project_name: Name of the project
        data: Data to send
    """
    if project_name in websocket_connections:
        # Make a copy of the connections list to avoid modification during iteration
        connections = websocket_connections[project_name].copy()
        for websocket in connections:
            try:
                await websocket.send_json(data)
            except Exception as e:
                logger.error(f"Error sending to WebSocket: {e}")
                # Remove broken connections
                if websocket in websocket_connections[project_name]:
                    websocket_connections[project_name].remove(websocket)

# Background task for project creation
async def create_project_background_task(execution_id: str, project_name: str, prompt: str, 
                                        model_id: str, local_model_id: Optional[str] = None) -> None:
    """
    Background task to create a project autonomously.
    
    Args:
        execution_id: Unique ID for this execution
        project_name: Name of the project to create
        prompt: Project description/requirements
        model_id: ID of the main model to use
        local_model_id: Optional ID of a local model to use as fallback
    """
    logger.info(f"Starting background task for project: {project_name}")
    
    # Create a new AutoAgent instance with the specified models
    agent = AutoAgent(openai_model_id=model_id, local_llm_model_id=local_model_id)
    
    # Set up callbacks
    async def progress_callback(data: Dict[str, Any]) -> None:
        # Update the execution record
        if execution_id in active_executions:
            if 'stage' in data:
                active_executions[execution_id]['progress'][data['stage']] = {
                    'status': data.get('status', 'pending'),
                    'message': data.get('message', ''),
                    'completed': data.get('completed', 0),
                    'total': data.get('total', 0)
                }
        
        # Broadcast update to WebSockets
        await broadcast_to_project_websockets(project_name, {
            'type': 'progress',
            'execution_id': execution_id,
            'data': data
        })
    
    async def stream_callback(text: str) -> None:
        # Broadcast text update to WebSockets
        await broadcast_to_project_websockets(project_name, {
            'type': 'stream',
            'execution_id': execution_id,
            'text': text
        })
    
    async def completion_callback(data: Dict[str, Any]) -> None:
        # Update the execution record with completion info
        if execution_id in active_executions:
            active_executions[execution_id].update({
                'status': data.get('status', 'completed'),
                'completion_time': time.time(),
                'execution_time': time.time() - active_executions[execution_id].get('start_time', time.time()),
                'success': data.get('status') == 'success',
                'metrics': data.get('metrics', {}),
                'validation': data.get('validation', {})
            })
        
        # Broadcast completion to WebSockets
        await broadcast_to_project_websockets(project_name, {
            'type': 'completion',
            'execution_id': execution_id,
            'data': data
        })
    
    # Set up the callbacks
    callbacks = {
        'progress': progress_callback,
        'stream': stream_callback,
        'completion': completion_callback
    }
    
    try:
        # Execute the create_project method
        result = await agent.create_project(
            project_name=project_name,
            prompt=prompt,
            callbacks=callbacks
        )
        
        # Update execution record with result
        if execution_id in active_executions:
            active_executions[execution_id].update({
                'status': 'completed',
                'completion_time': time.time(),
                'execution_time': time.time() - active_executions[execution_id].get('start_time', time.time()),
                'success': result.get('success', False),
                'result': result
            })
            
    except Exception as e:
        logger.error(f"Error in background task for project {project_name}: {e}")
        
        # Update execution record with error
        if execution_id in active_executions:
            active_executions[execution_id].update({
                'status': 'error',
                'completion_time': time.time(),
                'execution_time': time.time() - active_executions[execution_id].get('start_time', time.time()),
                'success': False,
                'error': str(e)
            })
        
        # Broadcast error to WebSockets
        await broadcast_to_project_websockets(project_name, {
            'type': 'error',
            'execution_id': execution_id,
            'error': str(e)
        })

@router.post("/projects", response_model=Dict[str, Any])
async def create_project(request: ProjectCreateRequest, background_tasks: BackgroundTasks) -> Dict[str, Any]:
    """
    Start fully autonomous project creation.
    
    This endpoint initiates the creation of a complete project based on the provided description,
    running as a background task with no further human intervention required.
    
    Returns a response with execution_id that can be used to track progress.
    """
    # Generate a unique execution ID
    execution_id = f"{request.project_name}_{int(time.time())}"
    
    # Set up initial execution record
    active_executions[execution_id] = {
        'project_name': request.project_name,
        'prompt': request.prompt,
        'model_id': request.model_id,
        'local_model_id': request.local_model_id,
        'start_time': time.time(),
        'status': 'running',
        'progress': {
            "setup": {"status": "pending", "message": ""},
            "research": {"status": "pending", "message": ""},
            "planning": {"status": "pending", "message": ""},
            "implementation": {"status": "pending", "message": "", "completed": 0, "total": 0},
            "testing": {"status": "pending", "message": ""},
            "validation": {"status": "pending", "message": ""}
        }
    }
    
    # Add the task to the background tasks
    background_tasks.add_task(
        create_project_background_task,
        execution_id=execution_id,
        project_name=request.project_name,
        prompt=request.prompt,
        model_id=request.model_id,
        local_model_id=request.local_model_id
    )
    
    logger.info(f"Started project creation for {request.project_name} with execution ID: {execution_id}")
    
    return {
        "status": "started",
        "execution_id": execution_id,
        "project_name": request.project_name,
        "message": f"Project creation started. Track progress with the execution ID: {execution_id}"
    }

@router.get("/projects/{project_name}/status", response_model=ProjectStatusResponse)
async def get_project_status(project_name: str) -> ProjectStatusResponse:
    """
    Get the status of a project's creation.
    
    Returns the current status of the specified project, including progress information
    if the project is being created.
    """
    # Get project info from AutoAgent
    project_info = auto_agent.get_project_info(project_name)
    
    if not project_info.get('exists', False):
        raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
    
    # Find the most recent execution for this project
    matching_executions = [
        (exec_id, exec_data) for exec_id, exec_data in active_executions.items()
        if exec_data.get('project_name') == project_name
    ]
    
    if not matching_executions:
        # Project exists but no executions found
        return ProjectStatusResponse(
            project_name=project_name,
            status="unknown",
            progress=None
        )
    
    # Sort by start time (most recent first)
    matching_executions.sort(key=lambda x: x[1].get('start_time', 0), reverse=True)
    exec_id, exec_data = matching_executions[0]
    
    return ProjectStatusResponse(
        project_name=project_name,
        status=exec_data.get('status', 'unknown'),
        execution_id=exec_id,
        progress=exec_data.get('progress'),
        start_time=exec_data.get('start_time'),
        completion_time=exec_data.get('completion_time'),
        execution_time=exec_data.get('execution_time'),
        success=exec_data.get('success'),
        error=exec_data.get('error')
    )

@router.get("/executions/{execution_id}", response_model=Dict[str, Any])
async def get_execution_status(execution_id: str) -> Dict[str, Any]:
    """
    Get detailed status of a specific execution by ID.
    
    Returns detailed information about a specific project creation execution,
    including progress, metrics, and results.
    """
    if execution_id not in active_executions:
        raise HTTPException(status_code=404, detail=f"Execution ID {execution_id} not found")
    
    return active_executions[execution_id]

@router.websocket("/ws/{project_name}")
async def websocket_endpoint(websocket: WebSocket, project_name: str):
    """
    WebSocket endpoint for receiving real-time updates about project creation.
    
    Connects a client to receive streaming updates about the creation of a specific project.
    """
    await websocket.accept()
    
    # Add this connection to the project's WebSocket connections
    if project_name not in websocket_connections:
        websocket_connections[project_name] = []
    websocket_connections[project_name].append(websocket)
    
    # Send initial status update
    try:
        # Find the most recent execution for this project
        matching_executions = [
            (exec_id, exec_data) for exec_id, exec_data in active_executions.items()
            if exec_data.get('project_name') == project_name
        ]
        
        if matching_executions:
            # Sort by start time (most recent first)
            matching_executions.sort(key=lambda x: x[1].get('start_time', 0), reverse=True)
            exec_id, exec_data = matching_executions[0]
            
            # Send current status
            await websocket.send_json({
                'type': 'status',
                'execution_id': exec_id,
                'project_name': project_name,
                'status': exec_data.get('status', 'unknown'),
                'progress': exec_data.get('progress')
            })
    except Exception as e:
        logger.error(f"Error sending initial status: {e}")
    
    try:
        # Keep the connection open and handle incoming messages
        while True:
            # Wait for messages from the client (can be used for cancellation or other commands)
            data = await websocket.receive_text()
            
            # Handle incoming messages (optional)
            # Example: if data == "cancel": ...
            
    except WebSocketDisconnect:
        # Remove this connection when it disconnects
        if project_name in websocket_connections and websocket in websocket_connections[project_name]:
            websocket_connections[project_name].remove(websocket)
            logger.info(f"WebSocket disconnected for project: {project_name}")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        # Remove this connection on error
        if project_name in websocket_connections and websocket in websocket_connections[project_name]:
            websocket_connections[project_name].remove(websocket) 