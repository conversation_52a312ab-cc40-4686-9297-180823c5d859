"""
AutoAgent API routes for executing fully automated project creation and management.
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, List, Optional
import logging
import asyncio
import os

from src.agents.auto_agent import AutoAgent
from src.socket_instance import emit_message

router = APIRouter()
logger = logging.getLogger(__name__)

# Singleton AutoAgent instance for reuse
_auto_agent_instance = None

def get_auto_agent():
    """Get or create a singleton AutoAgent instance."""
    global _auto_agent_instance
    if _auto_agent_instance is None:
        # Initialize with default models (can be customized via config later)
        _auto_agent_instance = AutoAgent(openai_model_id="openai/gpt-4o")
    return _auto_agent_instance

@router.post("/create_project")
async def create_project(
    project_name: str,
    prompt: str,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    Create a new project using the AutoAgent.
    
    Args:
        project_name: Name of the project
        prompt: Detailed description of the project
        
    Returns:
        Dictionary with execution ID and initial status
    """
    auto_agent = get_auto_agent()
    
    # Check if project name is valid
    if not project_name or '/' in project_name or '\\' in project_name:
        raise HTTPException(status_code=400, detail="Invalid project name")
    
    # Create async progress callback
    async def progress_callback(data):
        await emit_message("auto_agent_progress", {
            "project_name": project_name,
            "data": data
        })
    
    # Create async stream callback
    async def stream_callback(text):
        await emit_message("auto_agent_stream", {
            "project_name": project_name,
            "text": text
        })
    
    # Create async completion callback
    async def completion_callback(data):
        await emit_message("auto_agent_completed", {
            "project_name": project_name,
            "data": data
        })
    
    # Register the task
    execution_id = f"{project_name}_{int(asyncio.get_event_loop().time())}"
    
    # Run in background to avoid timeout
    background_tasks.add_task(
        auto_agent.create_project,
        project_name=project_name,
        prompt=prompt,
        callbacks={
            "progress": progress_callback,
            "stream": stream_callback,
            "completion": completion_callback
        }
    )
    
    # Return initial information immediately
    return {
        "execution_id": execution_id,
        "project_name": project_name,
        "status": "started",
        "message": f"Project creation started for {project_name}"
    }

@router.post("/create_project_structure")
async def create_project_structure(
    project_name: str,
    project_type: str,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a project structure directly using the specified framework.
    
    Args:
        project_name: Name of the project
        project_type: Type of project (angular, react, vue, python, etc.)
        options: Additional options for project creation
        
    Returns:
        Dictionary with creation results
    """
    auto_agent = get_auto_agent()
    
    # Check if project name is valid
    if not project_name or '/' in project_name or '\\' in project_name:
        raise HTTPException(status_code=400, detail="Invalid project name")
    
    # Check if project type is valid
    valid_project_types = ["angular", "react", "vue", "python", "flask", "django", "fastapi"]
    if project_type.lower() not in valid_project_types:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid project type. Supported types: {', '.join(valid_project_types)}"
        )
    
    # Execute project creation
    try:
        result = await auto_agent.create_project_structure(project_name, project_type, options)
        return result
    except Exception as e:
        logger.error(f"Error creating project structure: {e}")
        raise HTTPException(status_code=500, detail=f"Error creating project structure: {str(e)}")

@router.post("/generate_components")
async def generate_components(
    project_name: str,
    component_specs: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Generate components for a project.
    
    Args:
        project_name: Name of the project
        component_specs: List of component specifications
        
    Returns:
        Dictionary with generation results
    """
    auto_agent = get_auto_agent()
    
    # Check if project name is valid
    if not project_name or '/' in project_name or '\\' in project_name:
        raise HTTPException(status_code=400, detail="Invalid project name")
    
    # Check if component specs are valid
    if not component_specs:
        raise HTTPException(status_code=400, detail="No component specifications provided")
    
    # Execute component generation
    try:
        result = await auto_agent.generate_components(project_name, component_specs)
        return result
    except Exception as e:
        logger.error(f"Error generating components: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating components: {str(e)}")

@router.post("/install_dependencies")
async def install_dependencies(
    project_name: str,
    dependencies: List[str],
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Install dependencies for a project.
    
    Args:
        project_name: Name of the project
        dependencies: List of dependencies to install
        options: Additional options for installation
        
    Returns:
        Dictionary with installation results
    """
    auto_agent = get_auto_agent()
    
    # Check if project name is valid
    if not project_name or '/' in project_name or '\\' in project_name:
        raise HTTPException(status_code=400, detail="Invalid project name")
    
    # Check if dependencies are valid
    if not dependencies:
        raise HTTPException(status_code=400, detail="No dependencies provided")
    
    # Execute dependency installation
    try:
        result = await auto_agent.install_project_dependencies(project_name, dependencies, options)
        return result
    except Exception as e:
        logger.error(f"Error installing dependencies: {e}")
        raise HTTPException(status_code=500, detail=f"Error installing dependencies: {str(e)}")

@router.post("/run_project")
async def run_project(
    project_name: str,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Run a project.
    
    Args:
        project_name: Name of the project
        options: Additional options for running the project
        
    Returns:
        Dictionary with execution results
    """
    auto_agent = get_auto_agent()
    
    # Check if project name is valid
    if not project_name or '/' in project_name or '\\' in project_name:
        raise HTTPException(status_code=400, detail="Invalid project name")
    
    # Execute project
    try:
        result = await auto_agent.run_project(project_name, options)
        return result
    except Exception as e:
        logger.error(f"Error running project: {e}")
        raise HTTPException(status_code=500, detail=f"Error running project: {str(e)}")

@router.post("/research_topic")
async def research_topic(
    topic: str,
    max_results: Optional[int] = 5
) -> Dict[str, Any]:
    """
    Research a topic using SearxNG.
    
    Args:
        topic: The topic to research
        max_results: Maximum number of results to return
        
    Returns:
        Dictionary with research results
    """
    auto_agent = get_auto_agent()
    
    # Check if topic is valid
    if not topic or len(topic) < 3:
        raise HTTPException(status_code=400, detail="Invalid topic (must be at least 3 characters)")
    
    # Execute research
    try:
        result = await auto_agent.research_topic(topic, max_results)
        return result
    except Exception as e:
        logger.error(f"Error researching topic: {e}")
        raise HTTPException(status_code=500, detail=f"Error researching topic: {str(e)}")

@router.get("/execution_status/{execution_id}")
async def get_execution_status(execution_id: str) -> Dict[str, Any]:
    """
    Get status of a running or completed execution.
    
    Args:
        execution_id: ID of the execution to check
        
    Returns:
        Dictionary with execution status and progress
    """
    auto_agent = get_auto_agent()
    
    # Get execution status
    status = auto_agent.get_execution_status(execution_id)
    
    # If execution not found, return 404
    if status.get("status") == "not_found":
        raise HTTPException(status_code=404, detail=f"Execution {execution_id} not found")
    
    return status

@router.get("/project_info/{project_name}")
async def get_project_info(project_name: str) -> Dict[str, Any]:
    """
    Get information about a project.
    
    Args:
        project_name: Name of the project
        
    Returns:
        Dictionary with project information
    """
    auto_agent = get_auto_agent()
    
    # Get project info
    info = auto_agent.get_project_info(project_name)
    
    # If project not found, return 404
    if not info.get("exists", False):
        raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
    
    return info 