from fastapi import APIRouter, BackgroundTasks, HTTPException
import subprocess
import sys
import os
import logging
from typing import Optional

# Add the parent directory to sys.path for imports to work
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/demos", tags=["demos"])

# Track running processes
running_demos = {}

@router.post("/run/{demo_type}")
async def run_demo(demo_type: str, project_name: Optional[str] = None, background_tasks: BackgroundTasks = None):
    """
    Run a demonstration script.
    
    Args:
        demo_type: Type of demo to run (quick, calculator, utilities)
        project_name: Optional custom project name
    """
    # Map demo types to their script paths
    demo_scripts = {
        "quick": "backend/src/quick_demo.py",
        "calculator": "backend/src/demo_auto_calculator.py",
        "utilities": "backend/src/demo_auto_utilities.py"
    }
    
    if demo_type not in demo_scripts:
        raise HTTPException(status_code=400, detail=f"Invalid demo type. Must be one of: {', '.join(demo_scripts.keys())}")
    
    script_path = demo_scripts[demo_type]
    
    # Base command
    cmd = [sys.executable, script_path]
    
    # Add project name if specified
    if project_name:
        cmd.extend(["--project-name", project_name])
    
    logger.info(f"Running demo: {' '.join(cmd)}")
    
    try:
        # Run the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1
        )
        
        # Store process ID
        process_id = str(process.pid)
        running_demos[process_id] = {
            "process": process,
            "demo_type": demo_type,
            "project_name": project_name
        }
        
        # Add background task to clean up the process when done
        if background_tasks:
            background_tasks.add_task(wait_for_process, process_id)
        
        return {"status": "started", "process_id": process_id, "message": f"Started {demo_type} demo"}
        
    except Exception as e:
        logger.error(f"Error running demo: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start demo: {str(e)}")

@router.get("/status/{process_id}")
async def get_demo_status(process_id: str):
    """
    Get the status of a running demo process.
    """
    if process_id not in running_demos:
        raise HTTPException(status_code=404, detail="Demo process not found")
    
    process_info = running_demos[process_id]
    process = process_info["process"]
    
    # Check if process is still running
    is_running = process.poll() is None
    
    # Get output if available
    output = ""
    if process.stdout:
        output_lines = []
        while True:
            line = process.stdout.readline()
            if not line and not is_running:
                break
            if line:
                output_lines.append(line)
            else:
                break
                
        output = "".join(output_lines)
    
    return {
        "is_running": is_running,
        "exit_code": process.returncode if not is_running else None,
        "demo_type": process_info["demo_type"],
        "project_name": process_info["project_name"],
        "output": output
    }

@router.delete("/stop/{process_id}")
async def stop_demo(process_id: str):
    """
    Stop a running demo process.
    """
    if process_id not in running_demos:
        raise HTTPException(status_code=404, detail="Demo process not found")
    
    process_info = running_demos[process_id]
    process = process_info["process"]
    
    # Try to terminate the process
    try:
        process.terminate()
        process.wait(timeout=5)
    except:
        # Force kill if termination fails
        process.kill()
    
    # Clean up
    if process_id in running_demos:
        del running_demos[process_id]
    
    return {"status": "stopped", "message": f"Stopped demo process {process_id}"}

async def wait_for_process(process_id: str):
    """
    Wait for a process to complete and clean up.
    """
    if process_id not in running_demos:
        return
    
    process = running_demos[process_id]["process"]
    process.wait()
    
    # Clean up when done
    if process_id in running_demos:
        del running_demos[process_id] 