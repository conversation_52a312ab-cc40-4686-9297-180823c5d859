"""
Intelligent Agent API - Clean API endpoints for the new intelligent agent system.
"""
import os
import logging
import asyncio
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel

from backend.src.agents.intelligent_agent import IntelligentAgent
from backend.src.project import ProjectManager
from backend.src.socket_instance import emit_agent_message, emit_agent_typing, emit_agent_stream_token

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/intelligent-agent", tags=["intelligent-agent"])

class ProjectCreationRequest(BaseModel):
    project_name: str
    user_request: str
    model_id: str = "deepseek/deepseek-chat"
    streaming: bool = True

class ProjectStatusRequest(BaseModel):
    project_name: str

# Global state for tracking active agents
active_agents: Dict[str, IntelligentAgent] = {}
project_manager = ProjectManager()

@router.post("/create-project")
async def create_project_with_intelligent_agent(request: ProjectCreationRequest):
    """
    Create a new project using the intelligent agent.
    
    This endpoint replaces the complex auto-agent system with a streamlined approach.
    """
    try:
        project_name = request.project_name
        user_request = request.user_request
        model_id = request.model_id
        
        # Check if project already exists
        if project_manager.get_project(project_name):
            raise HTTPException(
                status_code=400, 
                detail=f"Project '{project_name}' already exists"
            )
        
        # Check if agent is already running for this project
        if project_name in active_agents:
            raise HTTPException(
                status_code=409,
                detail=f"Agent is already running for project '{project_name}'"
            )
        
        # Create the project in project manager first
        project_manager.create_project(project_name)
        
        # Initialize the intelligent agent
        agent = IntelligentAgent(
            project_name=project_name,
            projects_base_dir=project_manager.projects_dir,
            model_id=model_id
        )
        
        # Store the agent
        active_agents[project_name] = agent
        
        # Start the project creation process
        if request.streaming:
            # For streaming, start the task and return immediately
            asyncio.create_task(
                _run_agent_with_streaming(agent, user_request, project_name)
            )
            
            return {
                "success": True,
                "message": "Project creation started",
                "project_name": project_name,
                "streaming": True
            }
        else:
            # For non-streaming, wait for completion
            result = await agent.create_project(user_request)
            
            # Clean up
            if project_name in active_agents:
                del active_agents[project_name]
            
            return {
                "success": result.get("success", False),
                "message": "Project creation completed",
                "project_name": project_name,
                "result": result
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in create_project_with_intelligent_agent: {e}")
        
        # Clean up on error
        if request.project_name in active_agents:
            del active_agents[request.project_name]
        
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/project-status/{project_name}")
async def get_project_status(project_name: str):
    """Get the status of a project creation process."""
    try:
        # Check if agent is running
        is_running = project_name in active_agents
        
        # Check if project exists
        project = project_manager.get_project(project_name)
        project_exists = project is not None
        
        # Get project directory info if it exists
        project_info = {}
        if project_exists:
            project_dir = project.get("path", "")
            if os.path.exists(project_dir):
                # Count files in project
                file_count = 0
                for root, dirs, files in os.walk(project_dir):
                    file_count += len(files)
                
                project_info = {
                    "project_dir": project_dir,
                    "file_count": file_count,
                    "has_package_json": os.path.exists(os.path.join(project_dir, "package.json")),
                    "has_angular_json": os.path.exists(os.path.join(project_dir, "angular.json")),
                    "has_src_dir": os.path.exists(os.path.join(project_dir, "src"))
                }
        
        return {
            "project_name": project_name,
            "agent_running": is_running,
            "project_exists": project_exists,
            "project_info": project_info
        }
        
    except Exception as e:
        logger.error(f"Error getting project status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting project status: {str(e)}"
        )

@router.post("/stop-agent")
async def stop_agent(request: ProjectStatusRequest):
    """Stop a running agent for a project."""
    try:
        project_name = request.project_name
        
        if project_name not in active_agents:
            raise HTTPException(
                status_code=404,
                detail=f"No active agent found for project '{project_name}'"
            )
        
        # Remove the agent (this will stop it)
        del active_agents[project_name]
        
        # Emit a message to notify the frontend
        await emit_agent_message(
            project_name,
            "Agent stopped by user request."
        )
        
        return {
            "success": True,
            "message": f"Agent stopped for project '{project_name}'"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping agent: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error stopping agent: {str(e)}"
        )

@router.get("/active-agents")
async def get_active_agents():
    """Get a list of all active agents."""
    try:
        active_projects = list(active_agents.keys())
        
        return {
            "active_agents": active_projects,
            "count": len(active_projects)
        }
        
    except Exception as e:
        logger.error(f"Error getting active agents: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting active agents: {str(e)}"
        )

async def _run_agent_with_streaming(agent: IntelligentAgent, user_request: str, project_name: str):
    """
    Run the agent with streaming output to the frontend.
    """
    try:
        await emit_agent_typing(project_name, True)
        
        # Define streaming callback
        async def stream_callback(chunk: str):
            await emit_agent_stream_token(project_name, chunk)
        
        # Run the agent
        result = await agent.create_project(user_request, stream_callback)
        
        # Send final result
        if result.get("success", False):
            await emit_agent_message(
                project_name,
                f"✅ Project creation completed successfully! "
                f"Created {result.get('files_created', 0)} files."
            )
        else:
            await emit_agent_message(
                project_name,
                f"❌ Project creation failed: {result.get('error', 'Unknown error')}"
            )
        
        await emit_agent_typing(project_name, False)
        
    except Exception as e:
        logger.error(f"Error in streaming agent execution: {e}")
        
        await emit_agent_message(
            project_name,
            f"❌ Agent execution failed: {str(e)}"
        )
        await emit_agent_typing(project_name, False)
        
    finally:
        # Clean up the agent
        if project_name in active_agents:
            del active_agents[project_name]

@router.post("/test-connection")
async def test_agent_connection():
    """Test the intelligent agent system connectivity."""
    try:
        # Test LLM connection
        from backend.src.llm.llm import LLM
        
        test_llm = LLM.create("deepseek/deepseek-chat")
        test_response = await test_llm.generate("Hello, this is a test. Respond with 'Test successful!'", "test")
        
        return {
            "success": True,
            "message": "Intelligent agent system is working",
            "llm_response": test_response[:100] + "..." if len(test_response) > 100 else test_response
        }
        
    except Exception as e:
        logger.error(f"Error testing agent connection: {e}")
        return {
            "success": False,
            "error": str(e)
        }
