from fastapi import APIRouter, Body, BackgroundTasks, Depends
from typing import Dict, Any, Optional
import os
import time
from src.agents.project_executor import ProjectExecutor
from src.agents.coder import Coder
from src.agents.auto_agent import AutoAgent
from src.socket_instance import emit_message

router = APIRouter()

@router.post("/projects/{project_name}/run")
async def run_project(
    project_name: str, 
    options: Optional[Dict[str, Any]] = None
):
    """Run the project."""
    project_executor = ProjectExecutor(project_name, os.path.join(os.getcwd(), "projects"))
    await project_executor.initialize()
    
    result = await project_executor.run_project(options)
    return result

@router.post("/projects/{project_name}/test-with-playwright")
async def test_project_with_playwright(
    project_name: str, 
    options: Optional[Dict[str, Any]] = None
):
    """Run automated UI tests using <PERSON>wright."""
    project_executor = ProjectExecutor(project_name, os.path.join(os.getcwd(), "projects"))
    await project_executor.initialize()
    
    # First, run the project
    run_options = {"port": 4201}  # Use a different port to avoid conflict
    run_result = await project_executor.run_project(run_options)
    
    if not run_result.get("success", False):
        return {
            "success": False,
            "message": "Failed to start the project for UI testing",
            "run_result": run_result
        }
    
    # Run UI tests with Playwright
    ui_test_options = {
        "port": run_result.get("port", 4201),
        "test_url": run_result.get("url", f"http://localhost:4201"),
        **(options or {})
    }
    
    ui_test_results = await project_executor.run_ui_tests_with_playwright(ui_test_options)
    
    # Capture screenshots
    screenshot_results = await project_executor.capture_screenshots(ui_test_options.get("test_url"))
    
    # Run accessibility tests
    accessibility_results = await project_executor.run_accessibility_tests(ui_test_options.get("test_url"))
    
    return {
        "success": ui_test_results.get("success", False),
        "message": "UI tests completed",
        "ui_tests": ui_test_results,
        "screenshots": screenshot_results,
        "accessibility": accessibility_results
    }

@router.post("/projects/{project_name}/analyze-code")
async def analyze_code(
    project_name: str, 
    options: Optional[Dict[str, Any]] = None
):
    """Analyze code quality with visualizations using Playwright."""
    coder = Coder("openai/gpt-4o")
    analysis_results = await coder.generate_code_analysis_report(project_name, options)
    
    return analysis_results

@router.post("/projects/{project_name}/comprehensive-test")
async def comprehensive_test(
    project_name: str, 
    background_tasks: BackgroundTasks,
    options: Optional[Dict[str, Any]] = None
):
    """Run comprehensive testing including unit tests, UI tests with Playwright, and accessibility tests."""
    # Start a WebSocket connection for streaming updates
    socket_id = f"test_{project_name}_{int(time.time())}"
    
    # Define a callback for streaming updates
    async def stream_callback(message: str):
        await emit_message("test_progress", {
            "project_name": project_name,
            "socket_id": socket_id,
            "message": message
        })
    
    # Start the comprehensive testing in the background
    background_tasks.add_task(
        AutoAgent().execute_test_project,
        project_name=project_name,
        stream_callback=stream_callback
    )
    
    return {
        "success": True,
        "message": "Comprehensive testing started",
        "socket_id": socket_id,
        "project_name": project_name
    }

@router.post("/projects/{project_name}/autonomous-task")
async def execute_autonomous_task(
    project_name: str,
    background_tasks: BackgroundTasks,
    prompt: str = Body(..., embed=True)
):
    """Execute a task on a project autonomously using the agent."""
    # Start a WebSocket connection for streaming updates
    socket_id = f"task_{project_name}_{int(time.time())}"
    
    # Define a callback for streaming updates
    async def stream_callback(message: str):
        await emit_message("task_progress", {
            "project_name": project_name,
            "socket_id": socket_id,
            "message": message
        })
    
    # Start the autonomous task execution in the background
    background_tasks.add_task(
        AutoAgent().execute_project_task,
        prompt=prompt,
        project_name=project_name,
        stream_callback=stream_callback
    )
    
    return {
        "success": True,
        "message": "Task execution started",
        "socket_id": socket_id,
        "project_name": project_name,
        "prompt": prompt
    } 