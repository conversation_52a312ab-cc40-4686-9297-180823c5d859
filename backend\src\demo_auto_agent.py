#!/usr/bin/env python
"""
Demo script for the fully autonomous AutoAgent system.

This script demonstrates using the AutoAgent to create projects 
completely autonomously with minimal human intervention.
"""

import os
import sys
import asyncio
import logging
import argparse
from typing import Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agents.auto_agent import AutoAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('auto_agent_demo.log')
    ]
)
logger = logging.getLogger('AutoAgentDemo')

# Sample project descriptions for demo purposes
SAMPLE_PROJECTS = {
    "todo": "Create a simple todo list web application using Angular. The app should allow users to add, edit, delete, and mark tasks as completed. Each task should have a title, description, due date, and priority level. The app should also allow filtering tasks by status and priority.",
    
    "dashboard": "Create a responsive admin dashboard web application with Angular. Include login functionality, user profile management, and data visualization with charts. The dashboard should have a sidebar navigation, header with user info, and content area for different views. Include at least three different data visualization charts.",
    
    "blog": "Build a blog website with Angular that allows users to read blog posts and administrators to create, edit and delete posts. Each post should have a title, content, author, publication date, and tags. The site should have a homepage showing recent posts, a categories page, and a search function.",
    
    "inventory": "Create an inventory management system using Angular. The application should track products with details like name, description, price, quantity, and category. Include features for adding, editing, and removing products, as well as search and filter capabilities. Add a dashboard that shows low stock alerts and inventory statistics."
}

async def progress_callback(data: Dict[str, Any]) -> None:
    """
    Callback for progress updates from AutoAgent.
    
    Args:
        data: Progress update data
    """
    stage = data.get('stage', 'unknown')
    status = data.get('status', 'pending')
    message = data.get('message', '')
    
    # Only log detailed updates for important events
    if status in ['completed', 'error'] or 'stage' in data:
        logger.info(f"Progress: {stage} - {status} - {message}")

async def stream_callback(text: str) -> None:
    """
    Callback for streaming text updates from AutoAgent.
    
    Args:
        text: Text update
    """
    # Print stream updates directly to console with a prefix
    print(f"🤖 {text}", end="", flush=True)

async def completion_callback(data: Dict[str, Any]) -> None:
    """
    Callback for completion notification from AutoAgent.
    
    Args:
        data: Completion data
    """
    status = data.get('status', 'unknown')
    project_name = data.get('project_name', 'unknown')
    project_dir = data.get('project_dir', 'unknown')
    
    logger.info(f"Project creation {status}: {project_name}")
    logger.info(f"Project location: {project_dir}")
    
    # Print completion metrics
    metrics = data.get('metrics', {})
    if metrics:
        logger.info(f"Execution time: {metrics.get('total_execution_time', 0):.2f}s")
        logger.info(f"Steps completed: {metrics.get('subtasks_completed', 0)}")
        logger.info(f"Errors recovered: {metrics.get('errors_recovered', 0)}")

async def main() -> None:
    """
    Main entry point for the demo script.
    """
    parser = argparse.ArgumentParser(description="Demonstrate the fully autonomous AutoAgent system")
    parser.add_argument("--project-type", choices=list(SAMPLE_PROJECTS.keys()) + ["custom"], 
                        default="todo", help="Type of project to create")
    parser.add_argument("--project-name", type=str, help="Custom name for the project")
    parser.add_argument("--description", type=str, help="Custom project description (for custom project type)")
    parser.add_argument("--model", type=str, default="deepseek/deepseek-coder", 
                        help="Model to use (default: deepseek/deepseek-coder)")
    parser.add_argument("--local-model", type=str, 
                        help="Optional local model to use as fallback")
    args = parser.parse_args()
    
    # Determine project name and description
    if args.project_type == "custom":
        if not args.description:
            print("Error: --description is required for custom project type")
            sys.exit(1)
        project_description = args.description
        project_name = args.project_name or "CustomProject"
    else:
        project_description = SAMPLE_PROJECTS[args.project_type]
        project_name = args.project_name or f"{args.project_type.capitalize()}App"
    
    # Initialize the AutoAgent
    auto_agent = AutoAgent(
        model_id=args.model,
        local_llm_model_id=args.local_model
    )
    
    logger.info(f"Starting fully autonomous project creation: {project_name}")
    logger.info(f"Project description: {project_description[:100]}...")
    
    # Set up callbacks
    callbacks = {
        'progress': progress_callback,
        'stream': stream_callback,
        'completion': completion_callback
    }
    
    try:
        # Start the autonomous project creation process
        result = await auto_agent.create_project(
            project_name=project_name,
            prompt=project_description,
            callbacks=callbacks
        )
        
        # Display result summary
        if result.get('success', False):
            logger.info("Project creation completed successfully!")
        else:
            logger.warning("Project creation completed with issues.")
            if 'error' in result:
                logger.error(f"Error: {result['error']}")
        
        # Show project information
        project_info = auto_agent.get_project_info(project_name)
        if project_info.get('exists', False):
            logger.info(f"Project directory: {project_info['project_dir']}")
            logger.info(f"Files created: {project_info['file_count']}")
            
            # Print sample files
            if project_info.get('files'):
                logger.info("\nSample files created:")
                for file in project_info.get('files', [])[:5]:  # Show first 5 files
                    logger.info(f"  - {file}")
                
                if len(project_info.get('files', [])) > 5:
                    logger.info(f"  ... and {len(project_info.get('files', [])) - 5} more files")
        
    except Exception as e:
        logger.error(f"Error during demo execution: {str(e)}")
        sys.exit(1)
    
    logger.info("Demo completed.")

if __name__ == "__main__":
    asyncio.run(main()) 