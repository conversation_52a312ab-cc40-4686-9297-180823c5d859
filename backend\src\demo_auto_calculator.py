#!/usr/bin/env python
"""
Demo script showing the fully automated creation of a calculator application.

This script demonstrates how the enhanced AutoAgent creates a complete, 
functional calculator application with no user intervention required.
"""

import os
import sys
import asyncio
import logging
import argparse
from typing import Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agents.auto_agent import AutoAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('calculator_demo.log')
    ]
)
logger = logging.getLogger('CalculatorDemo')

# Calculator app description
CALCULATOR_DESCRIPTION = """
Create a complete, functional calculator application with Angular. 
The calculator must have:

1. A clean, responsive UI with a display and buttons
2. Support for all basic arithmetic operations (addition, subtraction, multiplication, division)
3. A clear button to reset the calculator
4. A backspace button to delete the last digit
5. Support for decimal numbers
6. Proper error handling (e.g., division by zero)
7. Memory functions (store, recall, clear memory)
8. Percentage calculation
9. A history of calculations feature

Implement this as a complete Angular application with:
- Properly structured components
- Well-organized services for calculator logic
- Comprehensive tests
- Attractive styling with SCSS
- Responsive design that works on mobile and desktop
"""

async def run_demo(project_name: str, model_id: str) -> None:
    """
    Run the calculator app demo.
    
    Args:
        project_name: Name for the calculator project
        model_id: ID of the model to use
    """
    # Initialize the AutoAgent
    auto_agent = AutoAgent(openai_model_id=model_id)
    
    # Create a simple progress tracking
    latest_stage = ""
    implementation_progress = {"completed": 0, "total": 0}
    
    # Define callbacks
    async def progress_callback(data: Dict[str, Any]) -> None:
        nonlocal latest_stage, implementation_progress
        stage = data.get('stage', '')
        status = data.get('status', '')
        
        if stage and stage != latest_stage:
            latest_stage = stage
            print(f"\n📋 {stage.upper()} PHASE {'started' if status == 'in_progress' else status}")
        
        # Track implementation progress specifically
        if stage == 'implementation':
            implementation_progress["completed"] = data.get('completed', 0)
            implementation_progress["total"] = data.get('total', 0)
            if implementation_progress["total"] > 0:
                percent = (implementation_progress["completed"] / implementation_progress["total"]) * 100
                print(f"⏳ Implementation: {implementation_progress['completed']}/{implementation_progress['total']} steps ({percent:.1f}%)")
    
    async def stream_callback(text: str) -> None:
        print(f"🤖 {text}", end="", flush=True)
    
    async def completion_callback(data: Dict[str, Any]) -> None:
        status = data.get('status', 'unknown')
        print(f"\n✅ Project creation {status.upper()}")
        
        metrics = data.get('metrics', {})
        if metrics:
            print(f"⏱️ Total time: {metrics.get('total_execution_time', 0):.1f}s")
            print(f"📊 Tasks completed: {metrics.get('subtasks_completed', 0)}")
            print(f"🔄 Auto-resolved errors: {metrics.get('errors_recovered', 0)}")
    
    # Set up the callbacks
    callbacks = {
        'progress': progress_callback,
        'stream': stream_callback,
        'completion': completion_callback
    }
    
    print(f"\n{'='*60}")
    print(f"🧮 AUTONOMOUS CALCULATOR APP CREATION DEMO")
    print(f"{'='*60}")
    print(f"\nProject: {project_name}")
    print(f"Model: {model_id}")
    print(f"{'='*60}\n")
    
    try:
        # Start autonomous project creation
        start_time = asyncio.get_event_loop().time()
        
        result = await auto_agent.create_project(
            project_name=project_name,
            prompt=CALCULATOR_DESCRIPTION,
            callbacks=callbacks
        )
        
        end_time = asyncio.get_event_loop().time()
        duration = end_time - start_time
        
        # Get project info
        project_info = auto_agent.get_project_info(project_name)
        
        print(f"\n{'='*60}")
        print(f"🏁 CALCULATOR APP CREATION COMPLETED IN {duration:.1f}s")
        print(f"{'='*60}\n")
        
        if project_info.get('exists', False):
            # Show summary of files created
            file_count = project_info.get('file_count', 0)
            files = project_info.get('files', [])
            
            print(f"📂 Files created: {file_count}")
            
            # Filter to show only key files related to the calculator
            calculator_files = [f for f in files if any(key in f for key in 
                ['calculator', 'component', 'service', 'model', 'app.module', 'index.html'])]
            
            if calculator_files:
                print("\n📄 Key calculator files created:")
                for file in calculator_files[:10]:  # Limit to first 10 files
                    print(f"  - {file}")
                
                if len(calculator_files) > 10:
                    print(f"  - ... and {len(calculator_files) - 10} more calculator-related files")
            
            # Show how to run the app
            project_dir = project_info.get('project_dir', '')
            print(f"\n▶️ To run the calculator app:")
            print(f"  1. cd {project_dir}")
            print(f"  2. npm install")
            print(f"  3. npm start")
            print(f"  4. Open browser to http://localhost:4200/")
            
            # If there were any issues, show them
            validation = result.get('validation', {})
            issues = validation.get('issues', [])
            if issues:
                print("\n⚠️ Issues that may need manual fixing:")
                for issue in issues:
                    print(f"  - {issue}")
        else:
            print(f"❌ Failed to create calculator project")
            if 'error' in result:
                print(f"Error: {result['error']}")
        
    except Exception as e:
        print(f"\n❌ Error during demo: {str(e)}")
        return 1
    
    return 0

async def main() -> int:
    """
    Main entry point.
    
    Returns:
        Exit code
    """
    parser = argparse.ArgumentParser(description="Autonomous Calculator App Creation Demo")
    parser.add_argument("--project-name", type=str, default="CalculatorApp", 
                       help="Name for the calculator project")
    parser.add_argument("--model", type=str, default="openai/gpt-4o", 
                       help="Model to use (default: openai/gpt-4o)")
    parser.add_argument("--verbose", action="store_true", 
                       help="Enable verbose logging")
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        return await run_demo(args.project_name, args.model)
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user.")
        return 1
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        return 1

if __name__ == "__main__":
    asyncio.run(main()) 