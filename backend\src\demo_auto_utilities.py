#!/usr/bin/env python
"""
Demo script showing the fully automated creation of a calculator application.

This script demonstrates using the enhanced AutoAgent to create a
calculator app entirely autonomously with no user intervention.
"""

import os
import sys
import asyncio
import logging
import argparse
from typing import Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agents.auto_agent import AutoAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('auto_calculator_demo.log')
    ]
)
logger = logging.getLogger('AutoCalculatorDemo')

# Calculator project description
CALCULATOR_PROJECT_DESCRIPTION = """
Create a calculator application with <PERSON>ular. The calculator should have:

1. Basic arithmetic operations (addition, subtraction, multiplication, division)
2. Percentage calculation functionality
3. Clear/reset button
4. Backspace/delete button
5. Support for decimal numbers
6. Display showing current input and calculation results
7. Memory functions (store, recall, clear memory)
8. Clean, responsive design that works on both desktop and mobile

The calculator should handle edge cases like:
- Division by zero
- Multiple operations in sequence
- Starting a new calculation after completing one
"""

async def progress_callback(data: Dict[str, Any]) -> None:
    """
    Callback for progress updates from AutoAgent.
    
    Args:
        data: Progress update data
    """
    stage = data.get('stage', 'unknown')
    status = data.get('status', 'pending')
    message = data.get('message', '')
    
    logger.info(f"Progress: {stage} - {status} - {message}")
    
    # Print more detailed information for implementation stage
    if stage == 'implementation':
        completed = data.get('completed', 0)
        total = data.get('total', 0)
        if total > 0:
            percent = (completed / total) * 100
            logger.info(f"Implementation progress: {completed}/{total} ({percent:.1f}%)")

async def stream_callback(text: str) -> None:
    """
    Callback for streaming text updates from AutoAgent.
    
    Args:
        text: Text update
    """
    # Print stream updates directly to console with a prefix
    print(f"🔄 {text}", end="", flush=True)

async def completion_callback(data: Dict[str, Any]) -> None:
    """
    Callback for completion notification from AutoAgent.
    
    Args:
        data: Completion data
    """
    status = data.get('status', 'unknown')
    project_name = data.get('project_name', 'unknown')
    project_dir = data.get('project_dir', 'unknown')
    
    logger.info(f"Project creation {status}: {project_name}")
    logger.info(f"Project location: {project_dir}")
    
    # Print completion metrics
    metrics = data.get('metrics', {})
    if metrics:
        logger.info(f"Execution time: {metrics.get('total_execution_time', 0):.2f}s")
        logger.info(f"Steps completed: {metrics.get('subtasks_completed', 0)}")
        logger.info(f"Errors recovered: {metrics.get('errors_recovered', 0)}")
    
    # Print validation results
    validation = data.get('validation', {})
    if validation:
        if validation.get('success', False):
            logger.info("✅ Project validation successful")
        else:
            logger.info("⚠️ Project validation encountered issues")
            
        if validation.get('can_run', False):
            logger.info("✅ Project can be run with: npm start")
        
        issues = validation.get('issues', [])
        if issues:
            logger.info("Issues found:")
            for issue in issues:
                logger.info(f"- {issue}")

async def main() -> None:
    """
    Main entry point for the demo script.
    """
    parser = argparse.ArgumentParser(description="Demonstrate fully autonomous calculator app creation")
    parser.add_argument("--project-name", type=str, default="CalculatorApp", 
                        help="Name for the calculator project")
    parser.add_argument("--model", type=str, default="openai/gpt-4o", 
                        help="Model to use (default: openai/gpt-4o)")
    args = parser.parse_args()
    
    # Initialize the AutoAgent
    auto_agent = AutoAgent(
        openai_model_id=args.model
    )
    
    logger.info(f"Starting fully autonomous calculator app creation: {args.project_name}")
    logger.info("This process will run completely autonomously with no user intervention required")
    
    # Set up callbacks
    callbacks = {
        'progress': progress_callback,
        'stream': stream_callback,
        'completion': completion_callback
    }
    
    try:
        # Start the autonomous project creation process
        print(f"\n{'='*50}")
        print(f"STARTING AUTONOMOUS CALCULATOR APP CREATION")
        print(f"{'='*50}\n")
        
        result = await auto_agent.create_project(
            project_name=args.project_name,
            prompt=CALCULATOR_PROJECT_DESCRIPTION,
            callbacks=callbacks
        )
        
        # Display result summary
        print(f"\n{'='*50}")
        print(f"AUTONOMOUS CALCULATOR APP CREATION COMPLETE")
        print(f"{'='*50}")
        
        if result.get('success', False):
            logger.info("Calculator app creation completed successfully!")
            
            # Project information
            project_info = auto_agent.get_project_info(args.project_name)
            if project_info.get('exists', False):
                print(f"\nFiles created: {project_info['file_count']}")
                
                # Print sample files
                if project_info.get('files'):
                    print("\nKey files created:")
                    for file in project_info.get('files', []):
                        if any(key in file for key in ['calculator', 'component', 'service', 'app.module', 'index.html']):
                            print(f"  - {file}")
                    
                print(f"\nTo run the calculator app:")
                print(f"  1. cd {project_info['project_dir']}")
                print(f"  2. npm install")
                print(f"  3. npm start")
                print(f"  4. Open browser to http://localhost:4200/")
        else:
            logger.warning("Calculator app creation completed with issues.")
            if 'error' in result:
                logger.error(f"Error: {result['error']}")
        
    except Exception as e:
        logger.error(f"Error during demo execution: {str(e)}")
        sys.exit(1)
    
    logger.info("Demo completed.")

if __name__ == "__main__":
    asyncio.run(main()) 