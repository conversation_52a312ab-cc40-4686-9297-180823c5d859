"""
Feedback storage and learning system for the Autonomous AI.
This module handles storing, retrieving, and analyzing user feedback to improve agent performance.
"""
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import sqlite3
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeedbackStore:
    """
    Stores and analyzes user feedback to improve agent performance over time.
    Uses SQLite for persistent storage and provides analysis capabilities.
    """
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the feedback store.
        
        Args:
            db_path: Path to SQLite database file, defaults to 'feedback.db' in data directory
        """
        if db_path is None:
            # Use data directory in project root
            data_dir = os.path.join(os.getcwd(), "data")
            os.makedirs(data_dir, exist_ok=True)
            db_path = os.path.join(data_dir, "feedback.db")
        
        self.db_path = db_path
        self._init_db()
        logger.info(f"[FeedbackStore] Initialized with database: {db_path}")

    def _init_db(self):
        """Create database tables if they don't exist."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create feedback table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS feedback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_name TEXT NOT NULL,
                task_type TEXT NOT NULL,
                task_description TEXT,
                model_id TEXT,
                rating INTEGER NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT
            )
            ''')
            
            # Create learned preferences table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS learned_preferences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                preference_key TEXT UNIQUE,
                preference_value TEXT,
                confidence REAL,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"[FeedbackStore] Error initializing database: {e}")
            raise

    def store_feedback(self, 
                      project_name: str, 
                      task_type: str, 
                      rating: int, 
                      task_description: Optional[str] = None,
                      model_id: Optional[str] = None, 
                      metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Store user feedback for a task.
        
        Args:
            project_name: Name of the project
            task_type: Type of task (e.g., 'file', 'command', 'browser_test')
            rating: User rating (positive: 1, negative: -1, neutral: 0)
            task_description: Optional description of the task
            model_id: Optional ID of the model used
            metadata: Optional additional metadata as dictionary
            
        Returns:
            Success status
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            metadata_json = json.dumps(metadata) if metadata else None
            
            cursor.execute(
                "INSERT INTO feedback (project_name, task_type, task_description, model_id, rating, metadata) "
                "VALUES (?, ?, ?, ?, ?, ?)",
                (project_name, task_type, task_description, model_id, rating, metadata_json)
            )
            
            conn.commit()
            conn.close()
            
            # After storing new feedback, update learned preferences
            self._update_learned_preferences()
            
            logger.info(f"[FeedbackStore] Stored feedback for {project_name}/{task_type}, rating: {rating}")
            return True
        except Exception as e:
            logger.error(f"[FeedbackStore] Error storing feedback: {e}")
            return False

    def _update_learned_preferences(self):
        """Update learned preferences based on all feedback data."""
        try:
            # Calculate model performance and user preferences
            model_performance = self._analyze_model_performance()
            task_preferences = self._analyze_task_preferences()
            style_preferences = self._analyze_style_preferences()
            
            # Store/update the learned preferences
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Update model performance preferences
            for model_id, performance in model_performance.items():
                cursor.execute(
                    "INSERT OR REPLACE INTO learned_preferences (preference_key, preference_value, confidence, last_updated) "
                    "VALUES (?, ?, ?, CURRENT_TIMESTAMP)",
                    (f"model_performance_{model_id}", json.dumps(performance), performance['confidence'])
                )
            
            # Update task type preferences
            for task_type, preference in task_preferences.items():
                cursor.execute(
                    "INSERT OR REPLACE INTO learned_preferences (preference_key, preference_value, confidence, last_updated) "
                    "VALUES (?, ?, ?, CURRENT_TIMESTAMP)",
                    (f"task_preference_{task_type}", json.dumps(preference), preference['confidence'])
                )
            
            # Update style preferences
            for style_key, preference in style_preferences.items():
                cursor.execute(
                    "INSERT OR REPLACE INTO learned_preferences (preference_key, preference_value, confidence, last_updated) "
                    "VALUES (?, ?, ?, CURRENT_TIMESTAMP)",
                    (f"style_preference_{style_key}", json.dumps(preference), preference['confidence'])
                )
            
            conn.commit()
            conn.close()
            logger.info("[FeedbackStore] Updated learned preferences")
        except Exception as e:
            logger.error(f"[FeedbackStore] Error updating learned preferences: {e}")

    def _analyze_model_performance(self) -> Dict[str, Dict[str, Any]]:
        """Analyze model performance based on feedback."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get all feedback with model information
            cursor.execute(
                "SELECT model_id, rating FROM feedback WHERE model_id IS NOT NULL"
            )
            results = cursor.fetchall()
            conn.close()
            
            # Process results by model
            model_performance = {}
            for model_id, rating in results:
                if model_id not in model_performance:
                    model_performance[model_id] = {
                        'positive': 0,
                        'negative': 0,
                        'total': 0,
                        'score': 0.0,
                        'confidence': 0.0
                    }
                
                model_performance[model_id]['total'] += 1
                if rating > 0:
                    model_performance[model_id]['positive'] += 1
                elif rating < 0:
                    model_performance[model_id]['negative'] += 1
            
            # Calculate scores and confidence
            for model_id, data in model_performance.items():
                positive = data['positive']
                total = data['total']
                
                # Calculate score (0-1 scale)
                if total > 0:
                    data['score'] = positive / total
                
                # Calculate confidence (increases with more data points)
                data['confidence'] = min(1.0, total / 30.0)  # Max confidence at 30+ ratings
            
            return model_performance
        except Exception as e:
            logger.error(f"[FeedbackStore] Error analyzing model performance: {e}")
            return {}

    def _analyze_task_preferences(self) -> Dict[str, Dict[str, Any]]:
        """Analyze user preferences for different task types."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT task_type, rating FROM feedback"
            )
            results = cursor.fetchall()
            conn.close()
            
            task_preferences = {}
            for task_type, rating in results:
                if task_type not in task_preferences:
                    task_preferences[task_type] = {
                        'positive': 0,
                        'negative': 0,
                        'total': 0,
                        'preference_score': 0.0,
                        'confidence': 0.0
                    }
                
                task_preferences[task_type]['total'] += 1
                if rating > 0:
                    task_preferences[task_type]['positive'] += 1
                elif rating < 0:
                    task_preferences[task_type]['negative'] += 1
            
            # Calculate preference scores
            for task_type, data in task_preferences.items():
                positive = data['positive']
                total = data['total']
                
                if total > 0:
                    data['preference_score'] = positive / total
                
                # Calculate confidence
                data['confidence'] = min(1.0, total / 20.0)  # Max confidence at 20+ ratings
            
            return task_preferences
        except Exception as e:
            logger.error(f"[FeedbackStore] Error analyzing task preferences: {e}")
            return {}

    def _analyze_style_preferences(self) -> Dict[str, Dict[str, Any]]:
        """Analyze style preferences from metadata."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT metadata, rating FROM feedback WHERE metadata IS NOT NULL"
            )
            results = cursor.fetchall()
            conn.close()
            
            # Extract style preferences from metadata
            style_preferences = {
                'code_verbosity': {'verbose': 0, 'concise': 0, 'total': 0, 'preference': 'balanced', 'confidence': 0.0},
                'documentation': {'detailed': 0, 'minimal': 0, 'total': 0, 'preference': 'balanced', 'confidence': 0.0},
                'testing': {'thorough': 0, 'basic': 0, 'total': 0, 'preference': 'balanced', 'confidence': 0.0}
            }
            
            for metadata_json, rating in results:
                try:
                    metadata = json.loads(metadata_json)
                    
                    # Process code verbosity preference
                    if 'code_style' in metadata and rating != 0:
                        style = metadata['code_style']
                        style_preferences['code_verbosity']['total'] += 1
                        
                        if 'verbose' in style.lower() and rating > 0:
                            style_preferences['code_verbosity']['verbose'] += 1
                        elif 'concise' in style.lower() and rating > 0:
                            style_preferences['code_verbosity']['concise'] += 1
                    
                    # Process documentation preference
                    if 'documentation' in metadata and rating != 0:
                        doc_style = metadata['documentation']
                        style_preferences['documentation']['total'] += 1
                        
                        if 'detailed' in doc_style.lower() and rating > 0:
                            style_preferences['documentation']['detailed'] += 1
                        elif 'minimal' in doc_style.lower() and rating > 0:
                            style_preferences['documentation']['minimal'] += 1
                    
                    # Process testing preference
                    if 'testing' in metadata and rating != 0:
                        test_style = metadata['testing']
                        style_preferences['testing']['total'] += 1
                        
                        if 'thorough' in test_style.lower() and rating > 0:
                            style_preferences['testing']['thorough'] += 1
                        elif 'basic' in test_style.lower() and rating > 0:
                            style_preferences['testing']['basic'] += 1
                except:
                    continue
            
            # Determine preferences and confidence
            for style_key, data in style_preferences.items():
                total = data['total']
                
                if style_key == 'code_verbosity':
                    verbose = data['verbose']
                    concise = data['concise']
                    
                    if total > 0:
                        if verbose > concise * 1.5:
                            data['preference'] = 'verbose'
                        elif concise > verbose * 1.5:
                            data['preference'] = 'concise'
                        else:
                            data['preference'] = 'balanced'
                
                elif style_key == 'documentation':
                    detailed = data['detailed']
                    minimal = data['minimal']
                    
                    if total > 0:
                        if detailed > minimal * 1.5:
                            data['preference'] = 'detailed'
                        elif minimal > detailed * 1.5:
                            data['preference'] = 'minimal'
                        else:
                            data['preference'] = 'balanced'
                
                elif style_key == 'testing':
                    thorough = data['thorough']
                    basic = data['basic']
                    
                    if total > 0:
                        if thorough > basic * 1.5:
                            data['preference'] = 'thorough'
                        elif basic > thorough * 1.5:
                            data['preference'] = 'basic'
                        else:
                            data['preference'] = 'balanced'
                
                # Calculate confidence
                data['confidence'] = min(1.0, total / 15.0)  # Max confidence at 15+ ratings
            
            return style_preferences
        except Exception as e:
            logger.error(f"[FeedbackStore] Error analyzing style preferences: {e}")
            return {}

    def get_learned_preferences(self) -> Dict[str, Any]:
        """
        Get all learned preferences.
        
        Returns:
            Dictionary of learned preferences
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT preference_key, preference_value, confidence FROM learned_preferences"
            )
            results = cursor.fetchall()
            conn.close()
            
            preferences = {}
            for key, value_json, confidence in results:
                try:
                    value = json.loads(value_json)
                    preferences[key] = {
                        'value': value,
                        'confidence': confidence
                    }
                except:
                    preferences[key] = {
                        'value': value_json,
                        'confidence': confidence
                    }
            
            return preferences
        except Exception as e:
            logger.error(f"[FeedbackStore] Error getting learned preferences: {e}")
            return {}

    def get_model_recommendation(self, task_type: str) -> Optional[str]:
        """
        Recommend the best model for a specific task type based on learned preferences.
        
        Args:
            task_type: Type of task to get model recommendation for
            
        Returns:
            Recommended model ID or None if no data available
        """
        preferences = self.get_learned_preferences()
        
        # Extract model performance data
        model_performance = {}
        for key, data in preferences.items():
            if key.startswith('model_performance_'):
                model_id = key.replace('model_performance_', '')
                # Only consider models with reasonable confidence
                if data['confidence'] > 0.3:
                    model_performance[model_id] = data['value']['score']
        
        if not model_performance:
            return None
        
        # Get the best model
        best_model = max(model_performance.items(), key=lambda x: x[1])
        return best_model[0]

    def get_user_preferences(self) -> Dict[str, Any]:
        """
        Get a consolidated view of user preferences.
        
        Returns:
            Dictionary with user preferences
        """
        all_preferences = self.get_learned_preferences()
        
        # Extract style preferences
        style_prefs = {}
        for key, data in all_preferences.items():
            if key.startswith('style_preference_'):
                style_key = key.replace('style_preference_', '')
                if data['confidence'] > 0.3:  # Only consider preferences with reasonable confidence
                    style_prefs[style_key] = data['value']['preference']
        
        # Get preferred models for different tasks
        task_model_prefs = {}
        task_types = set()
        
        # Get all task types
        for key in all_preferences:
            if key.startswith('task_preference_'):
                task_type = key.replace('task_preference_', '')
                task_types.add(task_type)
        
        # For each task type, find the best model
        for task_type in task_types:
            recommended_model = self.get_model_recommendation(task_type)
            if recommended_model:
                task_model_prefs[task_type] = recommended_model
        
        return {
            'style_preferences': style_prefs,
            'task_model_preferences': task_model_prefs
        }

    def get_feedback_summary(self, project_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a summary of feedback data.
        
        Args:
            project_name: Optional project name to filter by
            
        Returns:
            Dictionary with feedback summary statistics
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if project_name:
                cursor.execute(
                    "SELECT COUNT(*) as total, "
                    "SUM(CASE WHEN rating > 0 THEN 1 ELSE 0 END) as positive, "
                    "SUM(CASE WHEN rating < 0 THEN 1 ELSE 0 END) as negative "
                    "FROM feedback WHERE project_name = ?",
                    (project_name,)
                )
            else:
                cursor.execute(
                    "SELECT COUNT(*) as total, "
                    "SUM(CASE WHEN rating > 0 THEN 1 ELSE 0 END) as positive, "
                    "SUM(CASE WHEN rating < 0 THEN 1 ELSE 0 END) as negative "
                    "FROM feedback"
                )
            
            totals = cursor.fetchone()
            
            # Get feedback by task type
            if project_name:
                cursor.execute(
                    "SELECT task_type, COUNT(*) as count, "
                    "SUM(CASE WHEN rating > 0 THEN 1 ELSE 0 END) as positive "
                    "FROM feedback WHERE project_name = ? "
                    "GROUP BY task_type",
                    (project_name,)
                )
            else:
                cursor.execute(
                    "SELECT task_type, COUNT(*) as count, "
                    "SUM(CASE WHEN rating > 0 THEN 1 ELSE 0 END) as positive "
                    "FROM feedback GROUP BY task_type"
                )
            
            by_task_type = cursor.fetchall()
            
            # Get feedback by model
            if project_name:
                cursor.execute(
                    "SELECT model_id, COUNT(*) as count, "
                    "SUM(CASE WHEN rating > 0 THEN 1 ELSE 0 END) as positive "
                    "FROM feedback WHERE project_name = ? AND model_id IS NOT NULL "
                    "GROUP BY model_id",
                    (project_name,)
                )
            else:
                cursor.execute(
                    "SELECT model_id, COUNT(*) as count, "
                    "SUM(CASE WHEN rating > 0 THEN 1 ELSE 0 END) as positive "
                    "FROM feedback WHERE model_id IS NOT NULL "
                    "GROUP BY model_id"
                )
            
            by_model = cursor.fetchall()
            
            conn.close()
            
            # Process results
            total, positive, negative = totals
            
            task_type_data = {}
            for task_type, count, positive_count in by_task_type:
                task_type_data[task_type] = {
                    'count': count,
                    'positive_rate': (positive_count / count) if count > 0 else 0
                }
            
            model_data = {}
            for model_id, count, positive_count in by_model:
                if model_id:  # Skip null values
                    model_data[model_id] = {
                        'count': count,
                        'positive_rate': (positive_count / count) if count > 0 else 0
                    }
            
            return {
                'total_feedback': total or 0,
                'positive_rate': (positive / total) if total and total > 0 else 0,
                'negative_rate': (negative / total) if total and total > 0 else 0,
                'by_task_type': task_type_data,
                'by_model': model_data
            }
        except Exception as e:
            logger.error(f"[FeedbackStore] Error getting feedback summary: {e}")
            return {
                'total_feedback': 0,
                'positive_rate': 0,
                'negative_rate': 0,
                'by_task_type': {},
                'by_model': {}
            }
