"""
DeepSeek API client implementation.

This module provides a robust client for the DeepSeek API with advanced features:
1. Intelligent chunking for handling large contexts
2. Circuit breaker pattern to prevent cascading failures
3. Exponential backoff for retries
4. Robust JSON response parsing
5. Token optimization to maximize context usage
"""
import os
import logging
import asyncio
import time
import json
import re
from typing import Optional, List, Dict, Any, Union, Tuple

try:
    from openai import AsyncOpenAI, OpenAIError  # Use OpenAI's SDK for DeepSeek API
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False
    import aiohttp  # Fallback to aiohttp if OpenAI SDK is not available

from backend.src.llm.llm import LLM
from config_loader import get_api_key, get_service_config

logger = logging.getLogger(__name__)

class CircuitBreaker:
    """Circuit breaker pattern to prevent repeated failures when API is down."""
    
    def __init__(self, failure_threshold: int = 5, reset_timeout: int = 300):
        """Initialize circuit breaker."""
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.failure_count = 0
        self.is_open = False
        self.last_failure_time = 0
        self.rate_limit_count = 0  # Track rate limit errors
    
    def record_failure(self, is_rate_limit=False):
        """Record a failure and potentially open the circuit."""
        self.failure_count += 1
        if is_rate_limit:
            self.rate_limit_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            if not self.is_open:
                logger.warning(f"DeepSeek circuit breaker opened after {self.failure_count} consecutive failures")
            self.is_open = True
    
    def record_success(self):
        """Record a successful operation and reset failure count."""
        self.failure_count = 0
        if self.rate_limit_count > 0:
            self.rate_limit_count -= 1
        if self.is_open:
            logger.info("DeepSeek circuit breaker closed after successful operation")
            self.is_open = False
    
    async def check(self):
        """Check if circuit is open and whether to allow the operation."""
        if not self.is_open:
            return True
        
        # If circuit is open but reset timeout has elapsed, allow a trial request
        if time.time() - self.last_failure_time >= self.reset_timeout:
            logger.info(f"DeepSeek circuit breaker allowing trial request after {self.reset_timeout}s timeout")
            return False  # Circuit still technically open, but allowing trial
        
        # Circuit is open and timeout hasn't elapsed
        raise Exception(f"DeepSeek circuit breaker is open. API unavailable. Try again in {int(self.reset_timeout - (time.time() - self.last_failure_time))}s")


class ContentChunker:
    """Handles intelligent chunking for large contexts and prompts."""
    
    def __init__(self, max_chunk_size: int = 8000, 
                 overlap: int = 500, 
                 preserve_sections: bool = True):
        """
        Initialize the content chunker.
        
        Args:
            max_chunk_size: Maximum number of tokens per chunk
            overlap: Number of tokens to overlap between chunks
            preserve_sections: Whether to try preserving logical sections
        """
        self.max_chunk_size = max_chunk_size
        self.overlap = overlap
        self.preserve_sections = preserve_sections
        
        # Section boundary markers (headers, dividers, etc.)
        self.section_markers = [
            r"^#{1,6}\s+.+", # Markdown headers
            r"^---+$", # Horizontal rules
            r"^===+$",
            r"^```\w*$", # Code blocks start/end
            r"^<\/?\w+>$", # HTML tags
        ]
    
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate the number of tokens in text (rough approximation).
        
        Args:
            text: The text to estimate token count for
            
        Returns:
            Estimated token count
        """
        # Very rough approximation: 4 chars per token
        return len(text) // 4 + 1
    
    def find_section_boundary(self, text: str, target_position: int) -> int:
        """
        Find a natural section boundary near the target position.
        
        Args:
            text: The text to search in
            target_position: The approximate position to find a boundary
            
        Returns:
            The actual position of a natural boundary
        """
        if not self.preserve_sections:
            return target_position
        
        # Get lines around the target position
        lines = text[:target_position].splitlines(True)
        
        # Try to find a section boundary in the last few lines
        for i in range(len(lines) - 1, max(0, len(lines) - 10), -1):
            line = lines[i]
            
            # Check if this line matches any section boundary patterns
            if any(re.match(pattern, line.strip()) for pattern in self.section_markers):
                return sum(len(l) for l in lines[:i+1])
            
            # Also use paragraph breaks (empty lines) as potential boundaries
            if line.strip() == "" and i > 0 and lines[i-1].strip() != "":
                return sum(len(l) for l in lines[:i+1])
        
        # If no boundary found, just use the target position
        return target_position
    
    def chunk_content(self, content: str) -> List[str]:
        """
        Split large content into optimally sized chunks.
        
        Args:
            content: The content to split
            
        Returns:
            List of content chunks
        """
        if self.estimate_tokens(content) <= self.max_chunk_size:
            return [content]
        
        chunks = []
        start_pos = 0
        
        while start_pos < len(content):
            # Calculate where this chunk should end
            raw_end_pos = min(len(content), start_pos + (self.max_chunk_size * 4))  # 4 chars per token
            
            # Find a natural boundary near that position
            actual_end_pos = self.find_section_boundary(content[start_pos:], raw_end_pos - start_pos)
            actual_end_pos += start_pos
            
            # Add the chunk
            chunks.append(content[start_pos:actual_end_pos])
            
            # Set the start position for the next chunk, with overlap
            if actual_end_pos >= len(content):
                break
                
            # Calculate overlap position
            overlap_pos = max(start_pos, actual_end_pos - (self.overlap * 4))  # 4 chars per token
            start_pos = overlap_pos
        
        return chunks


class JSONResponseParser:
    """Robust parser for JSON responses from DeepSeek API."""
    
    @staticmethod
    def extract_json(text: str) -> Dict[str, Any]:
        """
        Extract and parse JSON from a text response, even if the JSON is embedded in other text.
        
        Args:
            text: Text response potentially containing JSON
            
        Returns:
            Parsed JSON object
        """
        # Try parsing the entire response as JSON first
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass
        
        # Look for JSON inside markdown code blocks
        json_block_pattern = r"```(?:json)?\s*([\s\S]+?)\s*```"
        matches = re.findall(json_block_pattern, text)
        
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        # Look for JSON objects directly in the text with a more permissive regex
        json_pattern = r"\{[\s\S]*?\}"
        matches = re.findall(json_pattern, text)
        
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        # Try to repair and extract the JSON by identifying what appears to be JSON content
        try:
            repaired_json = JSONResponseParser._repair_json(text)
            if repaired_json:
                return repaired_json
        except Exception:
            pass
        
        # If all attempts fail, return an empty dict or raise an exception
        raise ValueError("Could not extract valid JSON from response")
    
    @staticmethod
    def _repair_json(text: str) -> Optional[Dict[str, Any]]:
        """
        Attempt to repair malformed JSON.
        
        Args:
            text: Text containing potentially malformed JSON
            
        Returns:
            Repaired JSON object or None if repair failed
        """
        # Find what looks like the start of a JSON object
        start_idx = text.find('{')
        if start_idx == -1:
            return None
        
        # Find what looks like the end of the JSON object
        end_idx = text.rfind('}')
        if end_idx == -1 or end_idx < start_idx:
            return None
        
        json_text = text[start_idx:end_idx+1]
        
        # Try some common JSON repairs
        try:
            # Replace single quotes with double quotes
            json_text = re.sub(r"'([^']*)':", r'"\1":', json_text)
            
            # Add quotes around unquoted keys
            json_text = re.sub(r'([{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', json_text)
            
            # Remove trailing commas
            json_text = re.sub(r',\s*}', '}', json_text)
            json_text = re.sub(r',\s*]', ']', json_text)
            
            return json.loads(json_text)
        except json.JSONDecodeError:
            return None


class DeepSeekClient(LLM):
    """Enhanced client for DeepSeek API with advanced features."""
    
    def __init__(self, model_id: str, max_retries: int = 5, retry_delay: float = 1.0):
        """
        Initialize the enhanced DeepSeek client.
        
        Args:
            model_id: Model identifier (e.g. "deepseek/deepseek-coder")
            max_retries: Maximum number of retries for failed requests
            retry_delay: Base delay in seconds between retries (will use exponential backoff)
        """
        super().__init__(model_id)
        
        # Extract the model name from the model_id (remove provider prefix)
        if "/" in model_id:
            self.model = model_id.split("/")[1]
        else:
            self.model = model_id
        
        # Get configuration from config_loader
        self.api_key = get_api_key('deepseek', 'DEEPSEEK_API_KEY')
        
        # Get DeepSeek configuration
        deepseek_config = get_service_config('deepseek')
        self.base_url = deepseek_config.get('base_url', "https://api.deepseek.com/v1")
        
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.circuit_breaker = CircuitBreaker()
        
        # For handling large contexts and prompts
        self.content_chunker = ContentChunker()
        
        # For robust JSON parsing
        self.json_parser = JSONResponseParser()
        
        # Model token limits (conservatively set)
        self.token_limits = {
            "deepseek-chat": 28000,  # Actually 32k but leave buffer
            "deepseek-coder": 28000,
            "deepseek-reasoner": 28000,
        }
        
        self.default_token_limit = 8000
        
        # Verify API key exists
        if not self.api_key:
            error_msg = "DeepSeek API key not found in config.json or environment variables. Please add it to the config file."
            logger.error(error_msg)
            raise ValueError(error_msg)
            
        # Initialize OpenAI client if available
        if HAS_OPENAI:
            self.client = AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            logger.info(f"Using OpenAI SDK for DeepSeek API with model: {self.model}")
        else:
            logger.warning("OpenAI SDK not found. Using direct HTTP requests which may be less reliable.")
            self.client = None
    
    def _get_model_token_limit(self) -> int:
        """
        Get the token limit for the current model.
        
        Returns:
            Token limit as an integer
        """
        # Check each known model name to find a match
        for model_name, token_limit in self.token_limits.items():
            if model_name in self.model:
                return token_limit
        
        # If no match, return default limit
        return self.default_token_limit
    
    async def count_tokens(self, text: str) -> int:
        """
        Count the number of tokens in text.
        
        Args:
            text: The text to count tokens for
            
        Returns:
            Token count (estimated)
        """
        # DeepSeek doesn't provide a token counting endpoint, use approximation
        return self.content_chunker.estimate_tokens(text)
    
    async def _process_json_response(self, 
                                     prompt: str,
                                     json_format_prompt: str) -> Dict[str, Any]:
        """
        Process a JSON response with robust parsing and validation.
        
        Args:
            prompt: The original prompt
            json_format_prompt: Prompt instructing the model to respond with JSON
            
        Returns:
            Parsed JSON object
        """
        full_prompt = f"{json_format_prompt}\n\n{prompt}"
        
        for attempt in range(3):
            try:
                response_text = await self._generate_raw(full_prompt)
                
                # Try to parse the JSON response
                result = self.json_parser.extract_json(response_text)
                
                # Validation successful
                return result
                
            except ValueError as e:
                logger.warning(f"JSON parsing failed (attempt {attempt+1}/3): {e}")
                
                if attempt == 2:  # Last attempt failed
                    logger.error(f"All JSON parsing attempts failed. Last response: {response_text[:500]}...")
                    raise ValueError(f"Failed to parse JSON response after 3 attempts: {e}")
                
                # Add more explicit instructions for the next attempt
                json_format_prompt = (
                    "You MUST respond with VALID JSON only. No text before or after the JSON. "
                    "Use double quotes for keys and string values. The JSON must be properly formatted "
                    "with no syntax errors. The response should be a parseable JSON object that looks like "
                    "this format: {\"key1\": \"value1\", \"key2\": value2, \"key3\": [1, 2, 3]}"
                )
    
    async def generate_json(self, 
                           prompt: str, 
                           json_schema: Optional[Dict[str, Any]] = None,
                           project_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a JSON response from DeepSeek API with robust parsing.
        
        Args:
            prompt: Input text prompt
            json_schema: Optional JSON schema to validate against
            project_name: Optional project name for logging
            
        Returns:
            Parsed JSON response
        """
        # Create a JSON-focused prompt
        if json_schema:
            schema_str = json.dumps(json_schema, indent=2)
            json_format_prompt = (
                f"You must respond with a JSON object that conforms to this schema:\n\n{schema_str}\n\n"
                "The response should contain ONLY the JSON object, nothing else. "
                "Do not include explanations, code blocks, or any text outside the JSON object."
            )
        else:
            json_format_prompt = (
                "You must respond with a JSON object only. No text before or after the JSON. "
                "Use double quotes for keys and string values. The JSON must be properly formatted."
            )
        
        try:
            return await self._process_json_response(prompt, json_format_prompt)
        except Exception as e:
            logger.error(f"Error generating JSON response: {e}")
            # Return a minimal valid JSON with error information
            return {
                "error": True,
                "message": str(e),
                "partial_result": None
            }
    
    async def _generate_raw(self, 
                           prompt: str,
                           system_message: Optional[str] = None,
                           max_tokens: Optional[int] = None) -> str:
        """
        Generate raw text response from DeepSeek API without additional processing.
        
        Args:
            prompt: Input text prompt
            system_message: Optional system message
            max_tokens: Optional maximum tokens for response
            
        Returns:
            Raw text response
        """
        # Check circuit breaker before attempting request
        try:
            await self.circuit_breaker.check()
        except Exception as e:
            logger.error(f"DeepSeek circuit breaker prevented request: {e}")
            raise
            
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": prompt})
        
        # Estimate token count for this request
        prompt_tokens = await self.count_tokens(prompt)
        system_tokens = await self.count_tokens(system_message or "")
        total_tokens = prompt_tokens + system_tokens
        
        # Log token usage
        logger.info(f"Estimated tokens for DeepSeek request: {total_tokens} (prompt: {prompt_tokens}, system: {system_tokens})")
        
        # Check if we need to chunk the request
        model_token_limit = self._get_model_token_limit()
        if total_tokens > model_token_limit * 0.8:  # 80% of limit to be safe
            logger.warning(f"Request exceeds 80% of token limit ({total_tokens} > {model_token_limit * 0.8}), attempting chunking")
            return await self._handle_large_request(prompt, system_message, max_tokens)
        
        # Use OpenAI SDK if available (preferred method)
        if HAS_OPENAI and self.client:
            return await self._generate_with_openai_sdk(messages, max_tokens)
        else:
            return await self._generate_with_aiohttp(messages, max_tokens)
    
    async def _handle_large_request(self, 
                                   prompt: str,
                                   system_message: Optional[str] = None,
                                   max_tokens: Optional[int] = None) -> str:
        """
        Handle a request that exceeds token limits by chunking.
        
        Args:
            prompt: Input text prompt
            system_message: Optional system message
            max_tokens: Optional maximum tokens for response
            
        Returns:
            Combined response from chunked requests
        """
        # Use content chunker to split the prompt
        prompt_chunks = self.content_chunker.chunk_content(prompt)
        
        if len(prompt_chunks) == 1:
            # If there's only one chunk after all, process normally
            messages = []
            if system_message:
                messages.append({"role": "system", "content": system_message})
            messages.append({"role": "user", "content": prompt})
            
            if HAS_OPENAI and self.client:
                return await self._generate_with_openai_sdk(messages, max_tokens)
            else:
                return await self._generate_with_aiohttp(messages, max_tokens)
        
        # Process each chunk and combine the results
        responses = []
        for i, chunk in enumerate(prompt_chunks):
            try:
                # Add chunk information to system message
                chunk_system_msg = (
                    f"{system_message or ''}\n\n"
                    f"This is chunk {i+1} of {len(prompt_chunks)} from a larger prompt. "
                    f"Process this part independently."
                )
                
                messages = [
                    {"role": "system", "content": chunk_system_msg},
                    {"role": "user", "content": chunk}
                ]
                
                # Generate response for this chunk
                if HAS_OPENAI and self.client:
                    chunk_response = await self._generate_with_openai_sdk(messages, max_tokens)
                else:
                    chunk_response = await self._generate_with_aiohttp(messages, max_tokens)
                
                responses.append(chunk_response)
                
            except Exception as e:
                logger.error(f"Error processing chunk {i+1}: {e}")
                responses.append(f"[Error processing chunk {i+1}: {e}]")
        
        # Combine the chunked responses
        combined_response = "\n\n".join(responses)
        
        # If there are multiple chunks, summarize the combined response
        if len(responses) > 1:
            try:
                summary_prompt = (
                    "Below are multiple responses to chunks of a larger prompt. "
                    "Synthesize these into a coherent, unified response:\n\n"
                    f"{combined_response}"
                )
                
                summary_messages = [
                    {"role": "system", "content": "Synthesize the following responses into a coherent unified response."},
                    {"role": "user", "content": summary_prompt}
                ]
                
                # Generate a summarized response
                if HAS_OPENAI and self.client:
                    return await self._generate_with_openai_sdk(summary_messages, max_tokens)
                else:
                    return await self._generate_with_aiohttp(summary_messages, max_tokens)
                
            except Exception as e:
                logger.error(f"Error summarizing chunked responses: {e}")
                # Return combined responses if summarization fails
                return combined_response
        
        return combined_response
            
    async def generate(self, prompt: str, project_name: Optional[str] = None) -> str:
        """
        Generate text using DeepSeek API with retry logic for network errors and rate limits.
        
        Args:
            prompt: Input text prompt
            project_name: Optional project name for logging
            
        Returns:
            Generated text response
        """
        try:
            project_context = f"Project: {project_name}" if project_name else "No specific project"
            system_message = (
                f"You are a helpful AI assistant working on the following project: {project_context}. "
                "Provide accurate, helpful responses focused on code implementation and technical details."
            )
            
            return await self._generate_raw(prompt, system_message)
            
        except Exception as e:
            logger.error(f"Error in DeepSeek generate: {e}")
            raise
    
    async def _generate_with_openai_sdk(self, 
                                       messages: List[Dict[str, str]],
                                       max_tokens: Optional[int] = None) -> str:
        """
        Generate text using OpenAI SDK.
        
        Args:
            messages: List of message objects
            max_tokens: Optional maximum tokens for response
            
        Returns:
            Generated text
        """
        retries = 0
        while retries < self.max_retries:
            try:
                completion_args = {
                    "model": self.model,
                    "messages": messages,
                    "stream": False
                }
                
                if max_tokens:
                    completion_args["max_tokens"] = max_tokens
                
                response = await self.client.chat.completions.create(**completion_args)
                
                # Update token usage
                if hasattr(response, 'usage'):
                    self.update_token_usage(
                        response.usage.prompt_tokens,
                        response.usage.completion_tokens
                    )
                
                self.circuit_breaker.record_success()
                return response.choices[0].message.content
                
            except OpenAIError as e:
                retries += 1
                
                # Check if it's a rate limit error
                is_rate_limit = "rate_limit" in str(e).lower() or "429" in str(e)
                self.circuit_breaker.record_failure(is_rate_limit=is_rate_limit)
                
                if retries >= self.max_retries:
                    logger.error(f"Failed after {self.max_retries} retries. Error: {e}")
                    raise
                
                # Intelligent backoff strategy with jitter
                error_str = str(e).lower()
                is_timeout = any(term in error_str for term in ["timeout", "timed out", "connection"])
                is_server_error = any(term in error_str for term in ["500", "502", "503", "504", "server error"])

                if is_rate_limit:
                    # Exponential backoff with jitter for rate limits
                    base_wait = 60 + (retries * 30)  # Start at 60s, increase by 30s each retry
                    import random
                    jitter = random.uniform(0.8, 1.2)  # Add randomness to avoid thundering herd
                    wait_time = base_wait * jitter
                    logger.warning(f"Rate limit detected. Waiting {wait_time:.1f}s (attempt {retries}/{self.max_retries})")
                elif is_timeout:
                    # Shorter wait for timeouts
                    wait_time = self.retry_delay * (2 ** (retries - 1))
                    logger.warning(f"Timeout error. Retrying in {wait_time:.1f}s (attempt {retries}/{self.max_retries})")
                elif is_server_error:
                    # Medium wait for server errors
                    wait_time = self.retry_delay * (3 ** (retries - 1))
                    logger.warning(f"Server error. Retrying in {wait_time:.1f}s (attempt {retries}/{self.max_retries})")
                else:
                    # Standard exponential backoff for other errors
                    wait_time = self.retry_delay * (2 ** (retries - 1))
                    logger.warning(f"API error: {e}. Retrying in {wait_time:.1f}s (attempt {retries}/{self.max_retries})")

                await asyncio.sleep(wait_time)
                
            except Exception as e:
                self.circuit_breaker.record_failure()
                logger.error(f"Unrecoverable error in DeepSeek generate: {e}")
                raise
    
    async def _generate_with_aiohttp(self, 
                                    messages: List[Dict[str, str]],
                                    max_tokens: Optional[int] = None) -> str:
        """
        Generate text using direct HTTP requests with aiohttp.
        
        Args:
            messages: List of message objects
            max_tokens: Optional maximum tokens for response
            
        Returns:
            Generated text
        """
        request_url = f"{self.base_url}/chat/completions"
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        request_body = {
            "model": self.model,
            "messages": messages
        }
        
        if max_tokens:
            request_body["max_tokens"] = max_tokens
        
        retries = 0
        while retries < self.max_retries:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(request_url, headers=headers, json=request_body) as response:
                        response_json = await response.json()
                        
                        if response.status != 200:
                            # Handle API errors
                            is_rate_limit = response.status == 429
                            error_message = response_json.get("error", {}).get("message", "Unknown error")
                            
                            self.circuit_breaker.record_failure(is_rate_limit=is_rate_limit)
                            
                            if retries >= self.max_retries - 1:
                                logger.error(f"API error: {error_message} (HTTP {response.status})")
                                raise Exception(f"DeepSeek API error: {error_message}")
                            
                            # Calculate backoff time
                            wait_time = self.retry_delay * (4 ** retries if is_rate_limit else 2 ** retries)
                            if is_rate_limit:
                                wait_time = max(wait_time, 60)
                            
                            logger.warning(f"API error: {error_message} (HTTP {response.status}). Retrying in {wait_time:.2f}s")
                            await asyncio.sleep(wait_time)
                            retries += 1
                            continue
                        
                        # Update token usage if available
                        if "usage" in response_json:
                            self.update_token_usage(
                                response_json["usage"].get("prompt_tokens", 0),
                                response_json["usage"].get("completion_tokens", 0)
                            )
                        
                        # Extract the generated text
                        if "choices" in response_json and len(response_json["choices"]) > 0:
                            if "message" in response_json["choices"][0]:
                                generated_text = response_json["choices"][0]["message"].get("content", "")
                            else:
                                generated_text = response_json["choices"][0].get("text", "")
                                
                            self.circuit_breaker.record_success()
                            return generated_text
                        
                        raise Exception("Invalid response format from DeepSeek API")
                        
            except aiohttp.ClientError as e:
                retries += 1
                self.circuit_breaker.record_failure()
                
                if retries >= self.max_retries:
                    logger.error(f"Failed after {self.max_retries} retries. Error: {e}")
                    raise
                
                wait_time = self.retry_delay * (2 ** (retries - 1))
                logger.warning(f"Network error: {e}. Retrying in {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                self.circuit_breaker.record_failure()
                logger.error(f"Unrecoverable error in DeepSeek generate: {e}")
                raise 