"""
Base LLM class for integrating with different model providers.
"""
import os
import json
import logging
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

import httpx
from jinja2 import Environment, FileSystemLoader
from dotenv import load_dotenv

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLM(ABC):
    """
    Abstract base class for LLM integration.
    """
    def __init__(self, model_id: str):
        """
        Initialize the LLM with a model ID.
        
        Args:
            model_id: The ID of the model to use
        """
        self.model_id = model_id
        self.provider = self._get_provider_from_model_id(model_id)
        self.env = Environment(loader=FileSystemLoader("templates"))
        
        self.token_usage = {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
        }
    
    def _get_provider_from_model_id(self, model_id: str) -> str:
        """
        Extract the provider from the model ID.
        
        Args:
            model_id: The ID of the model
            
        Returns:
            The provider name
        """
        if "/" in model_id:
            return model_id.split("/")[0]
        return "unknown"
    
    @abstractmethod
    async def generate(self, prompt: str, project_name: str) -> str:
        """
        Generate a response from the LLM.
        
        Args:
            prompt: The prompt to send to the LLM
            project_name: The name of the project
            
        Returns:
            The generated response
        """
        pass
    
    @abstractmethod
    async def count_tokens(self, text: str) -> int:
        """
        Count the number of tokens in a text.
        
        Args:
            text: The text to count tokens for
            
        Returns:
            The number of tokens
        """
        pass
    
    def render_prompt(self, template_name: str, **kwargs) -> str:
        """
        Render a prompt template.
        
        Args:
            template_name: The name of the template
            **kwargs: Variables to pass to the template
            
        Returns:
            The rendered prompt
        """
        template = self.env.get_template(f"{template_name}.jinja2")
        return template.render(**kwargs)
    
    def update_token_usage(self, prompt_tokens: int, completion_tokens: int) -> None:
        """
        Update token usage statistics.
        
        Args:
            prompt_tokens: The number of tokens in the prompt
            completion_tokens: The number of tokens in the completion
        """
        self.token_usage["prompt_tokens"] += prompt_tokens
        self.token_usage["completion_tokens"] += completion_tokens
        self.token_usage["total_tokens"] = (
            self.token_usage["prompt_tokens"] + self.token_usage["completion_tokens"]
        )
    
    def get_token_usage(self) -> Dict[str, int]:
        """
        Get the current token usage.
        
        Returns:
            A dictionary with token usage statistics
        """
        return self.token_usage
    
    @staticmethod
    def create(model_id: str) -> 'LLM':
        """
        Factory method to create an LLM instance based on the model ID.
        
        Args:
            model_id: The ID of the model
            
        Returns:
            An LLM instance
        """
        provider = model_id.split("/")[0] if "/" in model_id else "unknown"
        
        # Prefer DeepSeek when specified (primary model)
        if provider == "deepseek":
            try:
                from .deepseek_client import DeepSeekClient
                return DeepSeekClient(model_id)
            except Exception as e:
                logger.warning(f"Error creating DeepSeek client, falling back to OpenAI: {e}")
                from .openai_client import OpenAIClient
                return OpenAIClient("openai/gpt-4o-mini")  # Fall back to OpenAI
        # OpenAI support
        elif provider == "openai":
            from .openai_client import OpenAIClient
            return OpenAIClient(model_id)
        # Try LM Studio next as it's locally available
        elif provider == "lm-studio":
            try:
                from .lm_studio_client import LMStudioClient
                return LMStudioClient(model_id)
            except Exception as e:
                logger.warning(f"Error creating LM Studio client, falling back to DeepSeek: {e}")
                from .deepseek_client import DeepSeekClient
                return DeepSeekClient("deepseek/deepseek-coder")  # Fall back to DeepSeek
        # Only try Ollama if specifically requested
        elif provider == "ollama":
            try:
                from .ollama_client import OllamaClient
                return OllamaClient(model_id)
            except Exception as e:
                logger.warning(f"Error creating Ollama client, falling back to DeepSeek: {e}")
                from .deepseek_client import DeepSeekClient
                return DeepSeekClient("deepseek/deepseek-coder")  # Fall back to DeepSeek
        else:
            logger.warning(f"Unsupported provider: {provider}, using DeepSeek instead")
            from .deepseek_client import DeepSeekClient
            return DeepSeekClient("deepseek/deepseek-coder")  # Default to DeepSeek
