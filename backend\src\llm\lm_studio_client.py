"""
LM Studio client for LLM integration.
"""
import os
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Union

import httpx
from dotenv import load_dotenv

from .llm import LLM

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LMStudioClient(LLM):
    """
    LM Studio client for LLM integration.
    """
    def __init__(self, model_id: str):
        """
        Initialize the LM Studio client.
        
        Args:
            model_id: The ID of the model to use
        """
        super().__init__(model_id)
        
        self.model_name = model_id.split("/")[1] if "/" in model_id else model_id
        
        self.api_url = os.getenv("LM_STUDIO_BASE_URL", "http://localhost:1234")
    
    async def generate(self, prompt: str, project_name: str) -> str:
        """
        Generate a response from the LM Studio model.
        
        Args:
            prompt: The prompt to send to the model
            project_name: The name of the project
            
        Returns:
            The generated response
        """
        try:
            logger.info(f"Sending request to LM Studio API with model: {self.model_name}")
            
            payload = {
                "messages": [
                    {"role": "system", "content": f"You are an AI assistant for project {project_name}. Provide detailed and accurate responses."},
                    {"role": "user", "content": prompt}
                ],
                "model": self.model_name,
                "stream": False,
                "temperature": 0.7,
                "max_tokens": -1  # Generate up to model capacity
            }
            
            async with httpx.AsyncClient(timeout=90.0) as client:
                response = await client.post(
                    f"{self.api_url}/v1/chat/completions",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                response.raise_for_status()
                
                response_data = response.json()
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    response_text = response_data["choices"][0]["message"]["content"]
                    
                    # Update token usage
                    if "usage" in response_data:
                        self.token_usage["prompt_tokens"] = response_data["usage"].get("prompt_tokens", 0)
                        self.token_usage["completion_tokens"] = response_data["usage"].get("completion_tokens", 0)
                        self.token_usage["total_tokens"] = response_data["usage"].get("total_tokens", 0)
                    else:
                        # Estimate token usage
                        prompt_tokens = len(prompt.split())
                        completion_tokens = len(response_text.split())
                        self.token_usage["prompt_tokens"] += prompt_tokens
                        self.token_usage["completion_tokens"] += completion_tokens
                        self.token_usage["total_tokens"] += prompt_tokens + completion_tokens
                    
                    return response_text
                else:
                    logger.error(f"Unexpected response format from LM Studio: {response_data}")
                    raise Exception("Unexpected response format from LM Studio API")
                
        except (httpx.HTTPError, httpx.TimeoutException, httpx.ConnectError) as e:
            logger.error(f"Error in LM Studio generate: {e}")
            # Fall back to OpenAI if LM Studio is not available
            try:
                logger.warning(f"LM Studio server not available. Falling back to OpenAI...")
                from .openai_client import OpenAIClient
                openai_client = OpenAIClient("openai/gpt-4o-mini")
                return await openai_client.generate(prompt, project_name)
            except Exception as fallback_error:
                logger.error(f"Error in OpenAI fallback: {fallback_error}")
                raise Exception(f"Failed to generate response from LM Studio and fallback: {e}, {fallback_error}")
        except Exception as e:
            logger.error(f"Error in LM Studio generate: {e}")
            raise
    
    async def count_tokens(self, text: str) -> int:
        """
        Count the number of tokens in a text.
        
        Args:
            text: The text to count tokens for
            
        Returns:
            The number of tokens
        """
        return len(text) // 4
