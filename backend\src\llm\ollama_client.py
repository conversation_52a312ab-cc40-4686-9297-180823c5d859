"""
Ollama client for LLM integration.
"""
import os
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Union

import httpx
from dotenv import load_dotenv

from .llm import LLM

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OllamaClient(LLM):
    """
    Ollama client for LLM integration.
    """
    def __init__(self, model_id: str):
        """
        Initialize the Ollama client.
        
        Args:
            model_id: The ID of the model to use
        """
        super().__init__(model_id)
        
        self.model_name = model_id.split("/")[1] if "/" in model_id else model_id
        
        self.api_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    
    async def generate(self, prompt: str, project_name: str) -> str:
        """
        Generate a response from the Ollama model.
        
        Args:
            prompt: The prompt to send to the model
            project_name: The name of the project
            
        Returns:
            The generated response
        """
        # Set up headers and payload
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model_name,
            "prompt": prompt,
            "stream": False
        }
        
        # API endpoint for Ollama
        url = f"{self.api_url}/api/generate"
        
        try:
            async with httpx.AsyncClient(timeout=90.0) as client:
                logger.info(f"Sending request to Ollama API with model: {self.model_name}")
                
                # Make the request to Ollama API
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()
                
                # Parse the response
                data = response.json()
                
                # Extract the generated text from the response
                generated_text = data.get("response", "")
                
                # Compute token usage (approximation for Ollama)
                prompt_tokens = len(prompt.split())
                completion_tokens = len(generated_text.split())
                self.token_usage["prompt_tokens"] += prompt_tokens
                self.token_usage["completion_tokens"] += completion_tokens
                self.token_usage["total_tokens"] += prompt_tokens + completion_tokens
                
                return generated_text
                
        except (httpx.HTTPError, httpx.TimeoutException, httpx.ConnectError) as e:
            logger.error(f"Error in Ollama generate: {e}")
            # Fall back to OpenAI if Ollama is not available
            try:
                logger.warning(f"Ollama server not available. Falling back to OpenAI...")
                from .openai_client import OpenAIClient
                openai_client = OpenAIClient("openai/gpt-4o-mini")
                return await openai_client.generate(prompt, project_name)
            except Exception as fallback_error:
                logger.error(f"Error in OpenAI fallback: {fallback_error}")
                raise Exception(f"Failed to generate response from Ollama and fallback: {e}, {fallback_error}")
        except Exception as e:
            logger.error(f"Error in Ollama generate: {e}")
            raise
    
    async def count_tokens(self, text: str) -> int:
        """
        Count the number of tokens in a text.
        
        Args:
            text: The text to count tokens for
            
        Returns:
            The number of tokens
        """
        return len(text) // 4
