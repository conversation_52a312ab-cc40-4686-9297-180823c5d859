"""
OpenAI client for LLM integration.
"""
import os
import sys
import logging
import asyncio
from typing import Dict, Any, List, Optional, Union

import httpx
from openai import AsyncOpenAI
from dotenv import load_dotenv

from .llm import LLM

# Use the config_loader from backend folder
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config_loader import get_api_key, get_service_config

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenAIClient(LLM):
    """
    OpenAI client for LLM integration.
    """
    def __init__(self, model_id: str):
        """
        Initialize the OpenAI client.
        
        Args:
            model_id: The ID of the model to use
        """
        super().__init__(model_id)
        
        self.model_name = model_id.split("/")[1] if "/" in model_id else model_id
        
        # Get API key from config
        api_key = get_api_key('openai', 'OPENAI_API_KEY')
        
        if not api_key:
            logger.error("OpenAI API key not found in config.json or environment variables")
            raise ValueError("OpenAI API key not found. Please add it to config.json or set as environment variable.")
        
        self.client = AsyncOpenAI(api_key=api_key)
    
    async def generate(self, prompt: str, project_name: str) -> str:
        """
        Generate a response from the OpenAI model.
        
        Args:
            prompt: The prompt to send to the model
            project_name: The name of the project
            
        Returns:
            The generated response
        """
        try:
            prompt_tokens = await self.count_tokens(prompt)
            
            logger.info(f"Sending request to OpenAI ({self.model_name})")
            
            # OpenAI SDK does not expose a timeout param directly in chat.completions.create as of now.
            # If using httpx under the hood, set timeout to 2400 seconds. Otherwise, ensure server/proxy allows long requests.
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=4000,
                top_p=1.0,
                frequency_penalty=0.0,
                presence_penalty=0.0
                # timeout=2400  # Uncomment if/when OpenAI SDK supports this
            )
            
            response_text = response.choices[0].message.content
            
            completion_tokens = await self.count_tokens(response_text)
            self.update_token_usage(prompt_tokens, completion_tokens)
            
            logger.info(f"Token usage: {self.get_token_usage()}")
            
            return response_text
        
        except Exception as e:
            logger.error(f"Error generating response from OpenAI: {e}")
            return f"Error generating response: {str(e)}"
    
    async def count_tokens(self, text: str) -> int:
        """
        Count the number of tokens in a text.
        
        Args:
            text: The text to count tokens for
            
        Returns:
            The number of tokens
        """
        return len(text) // 4
