"""
Enhanced SearxNG client for web research integration.
Provides robust search capabilities with automatic retries and error handling.
"""
import httpx
import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, Union

logger = logging.getLogger(__name__)

class SearxNGClient:
    """
    Client for interacting with SearxNG search API.
    Provides enhanced search capabilities and result processing.
    """
    def __init__(self, searxng_url: str = "http://localhost:8888/search"):
        """
        Initialize the SearxNG client with the API URL.
        
        Args:
            searxng_url: URL of the SearxNG instance (default: http://localhost:8888/search)
        """
        self.searxng_url = searxng_url
        self.search_cache = {}
        self.max_retries = 3
        self.retry_delay = 2  # seconds
        
    async def search(self, query: str, page: int = 1, max_results: int = 5) -> Dict[str, Any]:
        """
        Perform a search query with the SearxNG API.
        
        Args:
            query: The search query
            page: Page number for results (default: 1)
            max_results: Maximum number of results to return (default: 5)
            
        Returns:
            Dictionary with search results and metadata
        """
        cache_key = f"{query}::{page}::{max_results}"
        if cache_key in self.search_cache:
            logger.info(f"Using cached results for query: {query}")
            return self.search_cache[cache_key]
        
        params = {
            "q": query,
            "format": "json",
            "pageno": page,
            "language": "en",
            "time_range": "",  # Empty for all time, can be: day, week, month, year
            "safesearch": 0,   # 0=None, 1=Moderate, 2=Strict
            "categories": "general"
        }
        
        for attempt in range(self.max_retries):
            try:
                logger.info(f"Searching SearxNG for: {query} (attempt {attempt+1})")
                async with httpx.AsyncClient(timeout=60.0) as client:
                    resp = await client.get(self.searxng_url, params=params)
                    resp.raise_for_status()
                    data = resp.json()
                    
                    # Process the results
                    results = data.get("results", [])
                    infoboxes = data.get("infoboxes", [])
                    answers = data.get("answers", [])
                    suggestions = data.get("suggestions", [])
                    
                    # Limit to max_results
                    results = results[:max_results]
                    
                    # Create structured response
                    response = {
                        "query": query,
                        "results": results,
                        "answers": answers,
                        "infoboxes": infoboxes,
                        "suggestions": suggestions,
                        "total_results": len(data.get("results", [])),
                        "page": page
                    }
                    
                    # Cache the results
                    self.search_cache[cache_key] = response
                    
                    return response
                    
            except Exception as e:
                logger.warning(f"Search error (attempt {attempt+1}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
        
        # If we've exhausted all retries, return an empty result
        logger.error(f"All search attempts failed for query: {query}")
        return {
            "query": query,
            "results": [],
            "answers": [],
            "infoboxes": [],
            "suggestions": [],
            "total_results": 0,
            "page": page,
            "error": "Search failed after multiple attempts"
        }
    
    def format_results_as_text(self, search_result: Dict[str, Any]) -> str:
        """
        Format search results as a readable text string.
        
        Args:
            search_result: The search result dictionary
            
        Returns:
            Formatted text of the search results
        """
        parts = [f"Search results for: {search_result['query']}"]
        
        # Add answers if available
        if search_result.get("answers"):
            parts.append("\nDirect Answers:")
            for answer in search_result["answers"]:
                parts.append(f"- {answer}")
        
        # Add main results
        if search_result.get("results"):
            parts.append("\nWeb Results:")
            for i, result in enumerate(search_result["results"]):
                parts.append(f"{i+1}. {result.get('title', 'No title')}")
                parts.append(f"   URL: {result.get('url', 'No URL')}")
                parts.append(f"   {result.get('content', 'No description')}")
                parts.append("")
        
        # Add suggestions if available and no results found
        if not search_result.get("results") and search_result.get("suggestions"):
            parts.append("\nSuggestions:")
            for suggestion in search_result["suggestions"]:
                parts.append(f"- {suggestion}")
        
        # Add error if present
        if search_result.get("error"):
            parts.append(f"\nError: {search_result['error']}")
        
        return "\n".join(parts)

# Create a single client instance for reuse
_searxng_client = None

def get_searxng_client() -> SearxNGClient:
    """
    Get or create the SearxNG client singleton.
    
    Returns:
        SearxNG client instance
    """
    global _searxng_client
    if _searxng_client is None:
        _searxng_client = SearxNGClient()
    return _searxng_client

async def searxng_search(query: str, searxng_url: str = "http://localhost:8888/search") -> str:
    """
    Perform a search query with the SearxNG API and return results as text.
    This maintains backward compatibility with existing code.
    
    Args:
        query: The search query
        searxng_url: URL of the SearxNG instance
        
    Returns:
        Text representation of search results
    """
    client = get_searxng_client()
    if searxng_url != client.searxng_url:
        client.searxng_url = searxng_url
        
    results = await client.search(query)
    return client.format_results_as_text(results)

async def searxng_search_structured(query: str, page: int = 1, max_results: int = 5) -> Dict[str, Any]:
    """
    Perform a search query with the SearxNG API and return structured results.
    
    Args:
        query: The search query
        page: Page number for results
        max_results: Maximum number of results to return
        
    Returns:
        Dictionary with search results and metadata
    """
    client = get_searxng_client()
    return await client.search(query, page, max_results) 