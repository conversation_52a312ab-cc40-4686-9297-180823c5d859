"""
Main FastAPI application for the Autonomous AI Software Development Agent.
"""
import os
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional
import traceback
import sys

# Add the parent directory to sys.path for imports to work
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, HTTPException, Depends, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import socketio
from pydantic import BaseModel
from dotenv import load_dotenv
from fastapi.middleware.wsgi import WSGIMiddleware
from starlette.middleware.wsgi import WSGIMiddleware as StarletteWSGIMiddleware  # fallback for some FastAPI versions

from agents.agent import Agent
from project import ProjectManager
from state import AgentState
from socket_instance import set_socket_instance, emit_message, emit_agent_message, emit_agent_typing, emit_agent_stream_token, emit_message_reaction
# from automation.executor import AutomationExecutor  # Temporarily disabled
from feedback.feedback_store import FeedbackStore
from api.auto_agent_api import router as auto_agent_router
from api.demo_runner_api import router as demo_runner_router  # Import the new demo runner API
from api.auto_agent_routes import router as auto_agent_routes_router  # Import the enhanced auto agent routes
from api.intelligent_agent_api import router as intelligent_agent_router  # Import the new intelligent agent API

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Autonomous Agent API")

# Static files directory
static_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "static")
os.makedirs(static_dir, exist_ok=True)  # Create static directory if it doesn't exist

# Mount static files directory
app.mount("/static", StaticFiles(directory=static_dir), name="static")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:4200"],  # Angular default port
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the AutoAgent API router
app.include_router(auto_agent_router)

# Include the Demo Runner API router
app.include_router(demo_runner_router)

# Include the enhanced AutoAgent routes with additional project execution capabilities
app.include_router(auto_agent_routes_router, prefix="/api/auto-agent", tags=["auto-agent"])

# Include the new Intelligent Agent API router
app.include_router(intelligent_agent_router)

sio = socketio.AsyncServer(async_mode="asgi", cors_allowed_origins=["http://localhost:4200"])
socket_app = socketio.ASGIApp(sio, app)

set_socket_instance(sio)

project_manager = ProjectManager()
agent_state = AgentState()

class ProjectRequest(BaseModel):
    name: str
    description: Optional[str] = None

class MessageRequest(BaseModel):
    project_name: str
    message: str
    openai_model_id: str = "openai/gpt-4"
    local_llm_model_id: str = "lm-studio/mistral-nemo-instruct-2407"
    streaming_enabled: bool = True

class ConfigRequest(BaseModel):
    openai_api_key: Optional[str] = None
    ollama_base_url: Optional[str] = None
    lm_studio_base_url: Optional[str] = None


class FeedbackRequest(BaseModel):
    project_name: str
    task_type: str
    rating: int  # 1 (positive), -1 (negative), 0 (neutral)
    task_description: Optional[str] = None
    model_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@sio.event
async def connect(sid, environ):
    """Handle client connection."""
    logger.info(f"Client connected: {sid}")
    await sio.emit("connection_response", {"status": "connected"})

@sio.event
async def disconnect(sid):
    """Handle client disconnection."""
    logger.info(f"Client disconnected: {sid}")

@sio.event
async def user_message(sid, data):
    """Handle user messages and start the agent process."""
    try:
        message = data.get("message")
        project_name = data.get("project_name")
        openai_model_id = data.get("openai_model_id", "openai/gpt-4")
        local_llm_model_id = data.get("local_llm_model_id", "lm-studio/mistral-nemo-instruct-2407")
        streaming_enabled = data.get("streaming_enabled", True)
        
        logger.info(f"Received message from user: {message}")
        
        project_manager.add_message_from_user(project_name, message)
        
        if agent_state.is_agent_active(project_name):
            await emit_agent_message(
                project_name,
                "I'm already working on a task for this project. Please wait until I'm finished."
            )
            return
        
        agent = Agent(
            openai_model_id=openai_model_id,
            local_llm_model_id=local_llm_model_id
        )
        
        # Show typing indicator
        await emit_agent_typing(project_name, True)
        
        # Create task for agent execution with streaming support
        task_params = {
            "agent": agent,
            "message": message,
            "project_name": project_name,
            "streaming_enabled": streaming_enabled
        }
        
        asyncio.create_task(execute_agent(**task_params))
        
        # First message is non-streaming to indicate the agent is working
        if not streaming_enabled:
            await emit_agent_message(project_name, "I'm analyzing your request...")
    
    except Exception as e:
        logger.error(f"Error processing user message: {e}")
        await emit_agent_message(project_name, f"An error occurred: {str(e)}")
        await emit_agent_typing(project_name, False)

async def execute_agent(agent, message, project_name, streaming_enabled=True):
    """
    Execute the agent with a message.
    
    Args:
        agent: Agent instance
        message: Message to process
        project_name: Name of the project
        streaming_enabled: Whether to stream the response
    """
    agent_state.set_agent_active(project_name, True)
    
    try:
        # Retrieve context memory for the project
        context_memory = project_manager.get_context_memory(project_name)
        context_str = ""
        
        if context_memory:
            context_str = "Previous conversation context:\n"
            for entry in context_memory:
                context_str += f"User: {entry['question']}\nAI: {entry['answer']}\n\n"
        
        # Combine context with the new message
        enhanced_message = message
        if context_str:
            enhanced_message = f"{context_str}\nNew message: {message}"
        
        if streaming_enabled:
            # For streaming responses
            async def stream_callback(chunk):
                if chunk:
                    await emit_agent_stream_token(project_name, chunk)
            
            # Start with an empty agent message that will be built up
            empty_message = project_manager.add_message_from_agent(project_name, "")
            message_id = empty_message.get("id", "")
            full_response = ""
            
            # Execute the agent with streaming callback
            result = await agent.execute_with_streaming(
                enhanced_message, 
                project_name, 
                stream_callback=stream_callback
            )
            
            # Update the message with the full response
            if "response" in result:
                full_response = result["response"]
                # Update the message in the project data
                for msg in project_manager.projects[project_name].get("messages", []):
                    if msg.get("id") == message_id:
                        msg["content"] = full_response
                        break
                project_manager._save_project_metadata(project_name, project_manager.projects[project_name])
            
        else:
            # For non-streaming responses
            result = await agent.execute(enhanced_message, project_name, progress_callback=None)
            
            if "response" in result:
                await emit_agent_message(project_name, result["response"])
                project_manager.add_message_from_agent(project_name, result["response"])
        
        # Stop typing indicator
        await emit_agent_typing(project_name, False)
        await emit_message('agent_complete', {'project_name': project_name})

        # Optionally emit thinking process for debugging
        if "thinking" in result:
            await emit_message('agent_thinking', {'project_name': project_name, 'thinking': result["thinking"]})
        
    except Exception as e:
        logger.error(f"Error executing agent: {e}")
        traceback_str = traceback.format_exc()
        logger.error(f"Traceback: {traceback_str}")
        
        await emit_agent_message(
            project_name,
            f"I encountered an error while processing your request: {str(e)}"
        )
        await emit_agent_typing(project_name, False)
    finally:
        agent_state.set_agent_active(project_name, False)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Autonomous Agent API is running"}

@app.get("/api/models")
async def get_models():
    """Get available LLM models."""
    return {
        "models": [
            {"id": "openai/gpt-4", "name": "GPT-4 (OpenAI)"},
            {"id": "openai/gpt-3.5-turbo", "name": "GPT-3.5 Turbo (OpenAI)"},
            {"id": "lm-studio/mistral-nemo-instruct-2407", "name": "Mistral Nemo (LM Studio)"}
        ]
    }

@app.get("/projects")
async def get_projects():
    """Get list of projects."""
    projects = project_manager.get_projects()
    response_projects = []
    for project in projects:
        proj = {k: v for k, v in project.items() if k != "path"}
        if "name" in proj:
            proj["path"] = project["path"]
        response_projects.append(proj)
    return {"projects": response_projects}

@app.post("/projects")
async def create_project(project_request: ProjectRequest):
    """Create a new project."""
    try:
        # Create the project first
        project = project_manager.create_project(project_request.name)
        
        # Use the new intelligent agent for project setup
        setup_result = {
            'success': True,
            'port': 4200,
            'project_dir': os.path.join(project_manager.projects_dir, project_request.name)
        }
        
        if setup_result['success']:
            # Update project with the port information
            project['port'] = setup_result.get('port', 4200)
            project['actual_dir'] = setup_result.get('project_dir', os.path.join(project_manager.projects_dir, project_request.name))
            project_manager._save_project_metadata(project_request.name, project)
            
            # Emit socket event to notify frontend
            await emit_message('project_setup_complete', {
                'project_name': project_request.name,
                'port': setup_result.get('port', 4200),
                'project_dir': setup_result.get('project_dir', os.path.join(project_manager.projects_dir, project_request.name))
            })
            
            # Also emit agent_complete to trigger code-server display
            await emit_message('agent_complete', {
                'project_name': project_request.name
            })
            
            return {
                "message": f"Project {project_request.name} created and set up successfully",
                "project": project,
                "setup": setup_result
            }
        else:
            error_msg = setup_result.get('error', 'Unknown error')
            logger.error(f"Project setup failed: {error_msg}")
            return {
                "message": f"Project {project_request.name} created but setup failed: {error_msg}",
                "project": project,
                "setup": setup_result
            }
    except Exception as e:
        logger.error(f"Error creating project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create project: {str(e)}")

@app.get("/projects/{project_name}")
async def get_project(project_name: str):
    """Get project details."""
    project = project_manager.get_project(project_name)
    if not project:
        raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
    # Remove any existing 'path' and set it fresh
    response = {k: v for k, v in project.items() if k != "path"}
    response["path"] = project["path"]
    return {"project": response}

@app.delete("/projects/{project_name}")
async def delete_project(project_name: str):
    """Delete a project."""
    success = project_manager.delete_project(project_name)
    if not success:
        raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
    return {"message": f"Project {project_name} deleted successfully"}

@app.get("/projects/{project_name}/files")
async def get_project_files(project_name: str):
    """Get project files."""
    files = project_manager.get_project_files(project_name)
    return {"files": files}

@app.get("/projects/{project_name}/files/{file_path:path}")
async def get_file_content(project_name: str, file_path: str):
    """Get file content."""
    content = project_manager.get_file_content(project_name, file_path)
    if content is None:
        raise HTTPException(status_code=404, detail=f"File {file_path} not found in project {project_name}")
    return {"content": content}

@app.get("/projects/{project_name}/preview/{file_path:path}")
async def preview_file(project_name: str, file_path: str):
    """Preview HTML file content."""
    from fastapi.responses import HTMLResponse
    
    content = project_manager.get_file_content(project_name, file_path)
    if content is None:
        raise HTTPException(status_code=404, detail=f"File {file_path} not found in project {project_name}")
    
    return HTMLResponse(content=content)

@app.put("/projects/{project_name}/files/{file_path:path}")
async def update_file_content(project_name: str, file_path: str, request: Request):
    """Update file content."""
    body = await request.json()
    content = body.get("content")
    if content is None:
        raise HTTPException(status_code=400, detail="Content is required")
    
    file = project_manager.add_file_to_project(project_name, file_path, content)
    return {"message": f"File {file_path} updated successfully", "file": file}

@app.delete("/projects/{project_name}/files/{file_path:path}")
async def delete_file(project_name: str, file_path: str):
    """Delete a file."""
    success = project_manager.delete_file(project_name, file_path)
    if not success:
        raise HTTPException(status_code=404, detail=f"File {file_path} not found in project {project_name}")
    return {"message": f"File {file_path} deleted successfully"}

@app.post("/projects/{project_name}/open-explorer")
async def open_in_explorer(project_name: str):
    """Open the project directory in file explorer."""
    # Normalize the path to avoid slash issues
    project_path = os.path.normpath(os.path.abspath(os.path.join(project_manager.projects_dir, project_name)))
    if not os.path.exists(project_path):
        raise HTTPException(status_code=404, detail=f"Project directory {project_name} not found (checked path: {project_path})")
    if os.name == 'nt':  # Windows
        os.startfile(project_path)
    elif os.name == 'posix':  # macOS, Linux
        import subprocess
        if os.path.exists('/usr/bin/xdg-open'):
            subprocess.Popen(['xdg-open', project_path])
        elif os.path.exists('/usr/bin/open'):
            subprocess.Popen(['open', project_path])
        else:
            raise HTTPException(status_code=500, detail="Could not find file explorer command")
    else:
        raise HTTPException(status_code=500, detail=f"Unsupported OS: {os.name}")
    return {"message": f"Opened project {project_name} directory in file explorer"}

@app.get("/projects/{project_name}/messages")
async def get_project_messages(project_name: str):
    """Get project messages and chat expanded state."""
    messages = project_manager.get_project_messages(project_name)
    
    # Get the chat expanded state if it exists
    chat_expanded = False
    if project_name in project_manager.projects:
        chat_expanded = project_manager.projects[project_name].get("chat_expanded", False)
    
    return {
        "messages": messages,
        "chatExpanded": chat_expanded
    }

@app.post("/projects/{project_name}/messages")
async def add_message(project_name: str, message_request: MessageRequest):
    """Add a message to a project and start the agent."""
    project = project_manager.get_project(project_name)
    if not project:
        raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
    
    message = project_manager.add_message_from_user(project_name, message_request.message)
    
    # Initialize the agent with the model IDs from the request
    agent = Agent(
        openai_model_id=message_request.openai_model_id,
        local_llm_model_id=message_request.local_llm_model_id
    )
    asyncio.create_task(execute_agent(agent, message_request.message, project_name, message_request.streaming_enabled))
    return {"message": "Message added and agent started", "message_data": message}

@app.get("/api/config")
async def get_config():
    """Get current configuration."""
    return {
        "openai_api_key": "***" if os.getenv("OPENAI_API_KEY") else None,
        "ollama_base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
        "lm_studio_base_url": os.getenv("LM_STUDIO_BASE_URL", "http://localhost:1234")
    }

@app.put("/config")
async def update_config(config_request: ConfigRequest):
    """Update configuration."""
    
    if config_request.openai_api_key:
        os.environ["OPENAI_API_KEY"] = config_request.openai_api_key
    
    if config_request.ollama_base_url:
        os.environ["OLLAMA_BASE_URL"] = config_request.ollama_base_url
    
    if config_request.lm_studio_base_url:
        os.environ["LM_STUDIO_BASE_URL"] = config_request.lm_studio_base_url
    
    return {"message": "Configuration updated successfully"}

@app.post("/api/models/test")
async def test_model_connection(request: Request):
    """Test model connection."""
    body = await request.json()
    model_id = body.get("model_id")
    
    if not model_id:
        raise HTTPException(status_code=400, detail="Model ID is required")
    
    try:
        from llm.llm import LLM
        llm = LLM.create(model_id)
        response = await llm.generate("Hello, this is a test message.", "test")
        return {"success": True, "response": response}
    except Exception as e:
        logger.error(f"Error testing model connection: {e}")
        return {"success": False, "error": str(e)}

@app.post("/projects/{project_name}/reset")
async def reset_project(project_name: str):
    """Reset a project (delete all contents but keep the folder)."""
    success = project_manager.reset_project(project_name)
    if not success:
        raise HTTPException(status_code=404, detail=f"Project {project_name} not found or could not be reset")
    return {"message": f"Project {project_name} reset successfully"}

@app.post("/projects/{project_name}/export-chat")
async def export_project_chat(project_name: str):
    """Export the chat history to a text file in exports/ folder."""
    import datetime
    messages = project_manager.get_project_messages(project_name)
    if not messages:
        raise HTTPException(status_code=404, detail=f"No messages found for project {project_name}")
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    export_dir = os.path.join(project_manager.projects_dir, project_name, 'exports')
    os.makedirs(export_dir, exist_ok=True)
    file_name = f"{project_name}_{timestamp}.txt"
    file_path = os.path.join(export_dir, file_name)
    with open(file_path, 'w', encoding='utf-8') as f:
        for msg in messages:
            sender = 'You' if msg.get('sender') == 'user' else 'AI'
            content = msg.get('content', msg.get('message', ''))
            f.write(f"[{sender}] {content}\n")
    return {"message": "Chat exported successfully", "file_path": file_path}

@app.post("/projects/{project_name}/messages/save")
async def save_project_messages(project_name: str, request: Request):
    """Save project messages and chat expanded state."""
    body = await request.json()
    messages = body.get("messages", [])
    chat_expanded = body.get("chatExpanded", False)
    
    if not isinstance(messages, list):
        raise HTTPException(status_code=400, detail="Messages must be a list")
    
    try:
        # Update the project data with the messages
        if project_name not in project_manager.projects:
            raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
        
        # Update the project data
        project_manager.projects[project_name]["messages"] = messages
        # Store the chat expanded state
        project_manager.projects[project_name]["chat_expanded"] = chat_expanded
        
        # Save the project data to disk (ensure this method exists)
        project_manager._save_project_metadata(project_name, project_manager.projects[project_name])
        
        return {"message": f"Saved {len(messages)} messages for project {project_name}"}
    except Exception as e:
        logger.error(f"Error saving project messages: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to save messages: {str(e)}")


# Feedback System Endpoints

@app.post("/feedback", response_model=Dict[str, Any])
async def submit_feedback(feedback_request: FeedbackRequest):
    """Submit user feedback for a task."""
    try:
        feedback_store = FeedbackStore()
        success = feedback_store.store_feedback(
            project_name=feedback_request.project_name,
            task_type=feedback_request.task_type,
            rating=feedback_request.rating,
            task_description=feedback_request.task_description,
            model_id=feedback_request.model_id,
            metadata=feedback_request.metadata
        )
        
        if success:
            return {"success": True, "message": "Feedback stored successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to store feedback")
    except Exception as e:
        logger.error(f"Error submitting feedback: {e}")
        raise HTTPException(status_code=500, detail=f"Error submitting feedback: {str(e)}")


@app.get("/feedback/summary", response_model=Dict[str, Any])
async def get_feedback_summary(project_name: Optional[str] = None):
    """Get a summary of user feedback."""
    try:
        feedback_store = FeedbackStore()
        summary = feedback_store.get_feedback_summary(project_name)
        return {"success": True, "summary": summary}
    except Exception as e:
        logger.error(f"Error getting feedback summary: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting feedback summary: {str(e)}")


@app.get("/feedback/preferences", response_model=Dict[str, Any])
async def get_user_preferences():
    """Get learned user preferences based on feedback."""
    try:
        feedback_store = FeedbackStore()
        preferences = feedback_store.get_user_preferences()
        return {"success": True, "preferences": preferences}
    except Exception as e:
        logger.error(f"Error getting user preferences: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting user preferences: {str(e)}")


@app.get("/feedback/model_recommendation", response_model=Dict[str, Any])
async def get_model_recommendation(task_type: str):
    """Get recommended model for a specific task type based on feedback."""
    try:
        feedback_store = FeedbackStore()
        model_id = feedback_store.get_model_recommendation(task_type)
        return {"success": True, "recommended_model": model_id}
    except Exception as e:
        logger.error(f"Error getting model recommendation: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting model recommendation: {str(e)}")

# Add new API endpoints
@app.post("/projects/{project_name}/messages/{message_id}/reaction")
async def add_message_reaction(project_name: str, message_id: str, request: Request):
    """Add a reaction to a message."""
    body = await request.json()
    reaction = body.get("reaction")
    
    if not reaction:
        raise HTTPException(status_code=400, detail="Reaction is required")
    
    result = project_manager.add_message_reaction(project_name, message_id, reaction)
    
    if result:
        # Emit socket event
        await emit_message_reaction(project_name, message_id, reaction)
        return {"message": "Reaction added successfully"}
    else:
        raise HTTPException(status_code=404, detail=f"Message {message_id} not found")

@app.get("/projects/{project_name}/context-memory")
async def get_context_memory(project_name: str, limit: int = 5):
    """Get context memory for the project."""
    context_memory = project_manager.get_context_memory(project_name, limit)
    return {"context_memory": context_memory}

@app.post("/projects/{project_name}/context-memory/reset")
async def reset_context_memory(project_name: str):
    """Reset context memory for the project."""
    if project_name not in project_manager.projects:
        raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
    
    # Reset context memory
    project_manager.projects[project_name]["context_memory"] = []
    project_manager._save_project_metadata(project_name, project_manager.projects[project_name])
    
    return {"message": "Context memory reset successfully"}

# New endpoint for fully autonomous execution
@app.post("/projects/{project_name}/autonomous-execute")
async def autonomous_execute(project_name: str, request: Request):
    """
    Execute a fully autonomous end-to-end project creation process.
    
    This endpoint triggers the AutoAgent's fully automated workflow with zero human intervention.
    The process includes research, planning, coding, testing, and validation phases.
    """
    try:
        body = await request.json()
        prompt = body.get("prompt")
        openai_model_id = body.get("openai_model_id", "openai/gpt-4o")
        local_llm_model_id = body.get("local_llm_model_id") 
        
        if not prompt:
            raise HTTPException(status_code=400, detail="Project prompt is required")
            
        # Initialize AutoAgent
        from agents.auto_agent import AutoAgent
        auto_agent = AutoAgent(
            openai_model_id=openai_model_id,
            local_llm_model_id=local_llm_model_id
        )
        
        # Start the process as a background task
        task = asyncio.create_task(
            auto_agent.create_project(
                project_name=project_name,
                prompt=prompt,
                callbacks={
                    'progress': None,  # Implement if needed
                    'stream': None,    # Implement if needed
                    'completion': None # Implement if needed
                }
            )
        )
        
        # Return immediately with task info
        return {
            "message": f"Fully autonomous execution started for project {project_name}",
            "project_name": project_name,
            "status": "running"
        }
        
    except Exception as e:
        logger.error(f"Error starting autonomous execution: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start autonomous execution: {str(e)}")

# Mount the FastAPI app at '/api' so docs are available at /api/docs, while keeping Socket.IO as the main app
try:
    socket_app.mount('/api', app)
except AttributeError:
    # For older versions of FastAPI/Starlette
    socket_app.mount('/api', StarletteWSGIMiddleware(app))

# Add demo runner page route
@app.get("/demos", response_class=FileResponse)
async def get_demo_runner():
    """Serve the demo runner HTML page."""
    return FileResponse(os.path.join(static_dir, "demo_runner.html"))

# Add root endpoint to serve index.html
@app.get("/", response_class=FileResponse)
async def get_index():
    """Serve the index HTML page."""
    return FileResponse(os.path.join(static_dir, "index.html"))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:socket_app", host="0.0.0.0", port=8000, reload=True)
