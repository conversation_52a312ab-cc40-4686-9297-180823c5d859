"""
Project management functionality for the Autonomous Agent.
"""
import os
import json
import shutil
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import re
from pathlib import Path
import time
import sqlite3

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProjectManager:
    """
    Manages project state and files.
    """
    def __init__(self, projects_dir: str = None):
        """
        Initialize the ProjectManager.
        
        Args:
            projects_dir: Directory to store projects
        """
        self.projects_dir = projects_dir or os.path.join(os.getcwd(), "projects")
        # Convert to Path object and resolve
        self.projects_dir = str(Path(self.projects_dir).resolve())
        # Ensure forward slashes
        self.projects_dir = self.projects_dir.replace("\\", "/")
        
        os.makedirs(self.projects_dir, exist_ok=True)
        
        self.projects = self._load_projects()
    
    def _normalize_path(self, path: str) -> str:
        """
        Normalize a path to use forward slashes and resolve any relative components.
        
        Args:
            path: Path to normalize
            
        Returns:
            Normalized path using forward slashes
        """
        # Convert to Path object and resolve
        normalized = str(Path(path).resolve())
        # Ensure forward slashes
        return normalized.replace("\\", "/")

    def _join_paths(self, *paths: str) -> str:
        """
        Join paths and ensure forward slashes.
        
        Args:
            *paths: Path components to join
            
        Returns:
            Joined path with forward slashes
        """
        # Join paths using Path
        joined = str(Path(*paths))
        # Ensure forward slashes
        return joined.replace("\\", "/")

    def _normalize_project_data(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively normalize all paths in project data to use forward slashes.
        
        Args:
            project_data: Project metadata dictionary
            
        Returns:
            Normalized project data with consistent forward slashes
        """
        if not isinstance(project_data, dict):
            return project_data
            
        normalized = {}
        for key, value in project_data.items():
            if key in ["path", "full_path"] and isinstance(value, str):
                normalized[key] = value.replace("\\", "/")
            elif isinstance(value, list):
                normalized[key] = [self._normalize_project_data(item) if isinstance(item, dict) else item for item in value]
            elif isinstance(value, dict):
                normalized[key] = self._normalize_project_data(value)
            else:
                normalized[key] = value
        return normalized

    def _load_projects(self) -> Dict[str, Dict[str, Any]]:
        """
        Load existing projects from disk.
        
        Returns:
            Dictionary of projects
        """
        projects = {}
        
        if not os.path.exists(self.projects_dir):
            return projects
        
        for project_name in os.listdir(self.projects_dir):
            project_dir = self._join_paths(self.projects_dir, project_name)
            
            if not os.path.isdir(project_dir):
                continue
            
            metadata_path = self._join_paths(project_dir, "metadata.json")
            if os.path.exists(metadata_path):
                try:
                    with open(metadata_path, "r") as f:
                        metadata = json.load(f)
                    metadata["path"] = project_dir
                    projects[project_name] = metadata
                except Exception as e:
                    logger.error(f"Error loading project metadata for {project_name}: {e}")
            else:
                projects[project_name] = {
                    "name": project_name,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "files": [],
                    "messages": [],
                    "path": project_dir
                }
                
                self._save_project_metadata(project_name, projects[project_name])
        
        return projects
    
    def _save_project_metadata(self, project_name: str, metadata: Dict[str, Any]) -> None:
        """
        Save project metadata to disk.
        
        Args:
            project_name: Name of the project
            metadata: Project metadata
        """
        project_dir = self._join_paths(self.projects_dir, project_name)
        metadata_path = self._join_paths(project_dir, "metadata.json")
        
        # Create a copy of the metadata to avoid modifying the original
        # We don't want to save certain fields like 'path'
        metadata_copy = metadata.copy()
        if 'path' in metadata_copy:
            del metadata_copy['path']
        
        try:
            with open(metadata_path, "w") as f:
                json.dump(metadata_copy, f, indent=2)
                logger.debug(f"[ProjectManager] Saved metadata for {project_name}")
        except Exception as e:
            logger.error(f"Error saving project metadata for {project_name}: {e}")
    
    def create_project(self, project_name: str) -> Dict[str, Any]:
        """
        Create a new project.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Project metadata
        """
        logger.info(f"[ProjectManager] Creating project: {project_name}")
        if project_name in self.projects:
            logger.warning(f"[ProjectManager] Project {project_name} already exists")
            return self.projects[project_name]
        project_dir = self._join_paths(self.projects_dir, project_name)
        logger.debug(f"[ProjectManager] project_dir: {project_dir}")
        os.makedirs(project_dir, exist_ok=True)
        os.makedirs(self._join_paths(project_dir, "src"), exist_ok=True)
        os.makedirs(self._join_paths(project_dir, "docs"), exist_ok=True)
        metadata = {
            "name": project_name,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "files": [],
            "messages": [],
            "path": project_dir
        }
        logger.debug(f"[ProjectManager] metadata: {metadata}")
        self._save_project_metadata(project_name, metadata)
        self.projects[project_name] = metadata
        readme_path = self._join_paths(project_dir, "README.md")
        logger.debug(f"[ProjectManager] readme_path: {readme_path}")
        with open(readme_path, "w", encoding="utf-8") as f:
            f.write(f"# {project_name}\n\nThis is the default README for the {project_name} project.\n")
        self.add_file_to_project(project_name, "README.md", f"# {project_name}\n\nThis is the default README for the {project_name} project.\n")
        logger.info(f"[ProjectManager] Created project {project_name}")
        return metadata
    
    def create_project_if_not_exists(self, project_name: str) -> Dict[str, Any]:
        """
        Create a project if it doesn't exist.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Project metadata
        """
        if project_name in self.projects:
            return self.projects[project_name]
        
        return self.create_project(project_name)
    
    def get_project(self, project_name: str) -> Optional[Dict[str, Any]]:
        """
        Get project metadata.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Project metadata or None if project doesn't exist
        """
        project = self.projects.get(project_name)
        if project:
            project_dir = self._join_paths(self.projects_dir, project_name)
            project["path"] = project_dir
            project = self._normalize_project_data(project)
        return project
    
    def get_projects(self) -> List[Dict[str, Any]]:
        """
        Get all projects.
        
        Returns:
            List of project metadata
        """
        projects = []
        for project in self.projects.values():
            if "name" in project:
                project_copy = project.copy()
                project_dir = self._join_paths(self.projects_dir, project["name"])
                project_copy["path"] = project_dir
                normalized_project = self._normalize_project_data(project_copy)
                projects.append(normalized_project)
        return projects

    def get_project_dir(self, project_name: str) -> str:
        """
        Get the directory path for a project.

        Args:
            project_name: Name of the project

        Returns:
            Project directory path
        """
        return self._join_paths(self.projects_dir, project_name)

    def _is_code_file(self, file_path: str) -> bool:
        """
        Check if a file is a code file based on its extension.

        Args:
            file_path: Path to the file

        Returns:
            True if it's a code file, False otherwise
        """
        code_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.html', '.css', '.scss', '.sass',
            '.java', '.cpp', '.c', '.h', '.hpp', '.cs', '.php', '.rb', '.go',
            '.rs', '.swift', '.kt', '.dart', '.vue', '.svelte', '.json', '.xml',
            '.yaml', '.yml', '.sql', '.sh', '.bat', '.ps1', '.dockerfile'
        }

        _, ext = os.path.splitext(file_path.lower())
        return ext in code_extensions

    def _is_content_more_complete(self, new_content: str, existing_content: str) -> bool:
        """
        Check if new content is more complete than existing content.

        Args:
            new_content: New file content
            existing_content: Existing file content

        Returns:
            True if new content is more complete, False otherwise
        """
        if not existing_content:
            return True

        if not new_content:
            return False

        # Simple heuristic: longer content with more non-whitespace characters is more complete
        new_meaningful = len(new_content.strip().replace(' ', '').replace('\n', '').replace('\t', ''))
        existing_meaningful = len(existing_content.strip().replace(' ', '').replace('\n', '').replace('\t', ''))

        return new_meaningful > existing_meaningful

    def delete_project(self, project_name: str) -> bool:
        """
        Delete a project.
        
        Args:
            project_name: Name of the project
            
        Returns:
            True if project was deleted, False otherwise
        """
        if project_name not in self.projects:
            logger.warning(f"Project {project_name} doesn't exist")
            return False
        
        project_dir = self._join_paths(self.projects_dir, project_name)
        try:
            shutil.rmtree(project_dir)
        except Exception as e:
            logger.error(f"Error deleting project directory for {project_name}: {e}")
            return False
        
        del self.projects[project_name]
        
        logger.info(f"Deleted project {project_name}")
        
        return True
    
    def get_project_files(self, project_name: str) -> List[Dict[str, Any]]:
        """
        Get project files.
        
        Args:
            project_name: Name of the project
            
        Returns:
            List of file metadata
        """
        if project_name not in self.projects:
            logger.warning(f"Project {project_name} doesn't exist")
            return []
        
        files = self.projects[project_name].get("files", [])
        # Normalize all paths to use forward slashes
        for file in files:
            if "path" in file:
                file["path"] = file["path"].replace("\\", "/")
            if "full_path" in file:
                file["full_path"] = file["full_path"].replace("\\", "/")
        return files
    
    def _sanitize_path(self, path: str) -> str:
        """
        Sanitize a path to prevent directory traversal and ensure it's valid.
        
        Args:
            path: Path to sanitize
            
        Returns:
            Sanitized path
        """
        # Convert backslashes to forward slashes for consistency
        path = path.replace("\\", "/")
        
        # Remove any attempt to traverse directories
        path = path.replace("../", "").replace("..\\", "")
        
        # Remove any starting slash or backslash
        while path.startswith("/") or path.startswith("\\"):
            path = path[1:]
        
        # Handle special cases like "./" at the beginning
        if path.startswith("./"):
            path = path[2:]
        
        # Detect framework type based on file extension and path components
        framework_type = "unknown"
        
        # Detect Angular
        if re.search(r'\.(component|service|module|directive|pipe|guard)\.(ts|js|html|css|scss)$', path):
            framework_type = "angular"
        # Detect React
        elif re.search(r'\.(jsx|tsx)$', path):
            framework_type = "react"
        # Detect Vue
        elif path.endswith('.vue'):
            framework_type = "vue"
        # Detect Express/Node
        elif re.search(r'(route|controller|middleware|model)\.js$', path):
            framework_type = "express"
        # Detect Django/Flask
        elif (re.search(r'views\.py$|urls\.py$|models\.py$', path) or 
              re.search(r'templates/.*\.html$', path)):
            framework_type = "django_flask"
            
        # Handle framework-specific paths
        if framework_type == "angular":
            # Angular component structure
            if re.search(r'\.component\.(ts|html|scss|css)$', path):
                # Extract component name from path
                match = re.search(r'([a-zA-Z0-9_-]+)\.component', path)
                if match:
                    component_name = match.group(1)
                    filename = os.path.basename(path)
                    return f"src/app/components/{component_name}/{filename}"
                
            # Angular services, modules, etc.
            elif re.search(r'\.service\.ts$', path):
                return f"src/app/services/{os.path.basename(path)}"
            elif re.search(r'\.module\.ts$', path):
                return f"src/app/{os.path.basename(path)}"
            elif re.search(r'\.directive\.ts$', path):
                return f"src/app/directives/{os.path.basename(path)}"
            elif re.search(r'\.pipe\.ts$', path):
                return f"src/app/pipes/{os.path.basename(path)}"
            elif re.search(r'\.guard\.ts$', path):
                return f"src/app/guards/{os.path.basename(path)}"
            elif re.search(r'\.model\.ts$', path) or re.search(r'\.interface\.ts$', path):
                return f"src/app/models/{os.path.basename(path)}"
                
        elif framework_type == "react":
            # React component structure
            if not path.startswith('src/'):
                if re.search(r'(container|page|view|screen)\.(jsx|tsx)$', path.lower()):
                    return f"src/containers/{os.path.basename(path)}"
                else:
                    return f"src/components/{os.path.basename(path)}"
                    
        elif framework_type == "vue":
            # Vue component structure
            if not path.startswith('src/'):
                if re.search(r'(page|view|screen)\.vue$', path.lower()):
                    return f"src/views/{os.path.basename(path)}"
                else:
                    return f"src/components/{os.path.basename(path)}"
                    
        elif framework_type == "express":
            # Express/Node.js structure
            file_type = ""
            if "route" in path:
                file_type = "routes"
            elif "controller" in path:
                file_type = "controllers"
            elif "middleware" in path:
                file_type = "middleware"
            elif "model" in path:
                file_type = "models"
            
            if file_type and not path.startswith(f'src/{file_type}'):
                return f"src/{file_type}/{os.path.basename(path)}"
                
        elif framework_type == "django_flask":
            # Django/Flask structure
            if "models.py" in path and not path.startswith('app/'):
                return f"app/models/{os.path.basename(path)}"
            elif "views.py" in path and not path.startswith('app/'):
                return f"app/views/{os.path.basename(path)}"
            elif path.endswith('.html') and not path.startswith('app/templates/'):
                return f"app/templates/{os.path.basename(path)}"
                
        # General case for web assets
        if not path.startswith('src/') and not path.startswith('public/'):
            if path.endswith(('.css', '.scss', '.sass', '.less')):
                return f"src/assets/styles/{os.path.basename(path)}"
            elif path.endswith(('.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico')):
                return f"src/assets/images/{os.path.basename(path)}"
            elif path.endswith('.js') and not any(x in path for x in ['component', 'service', 'directive']):
                return f"src/utils/{os.path.basename(path)}"
        
        # Convert back to OS-specific path
        return path.replace("/", os.sep)

    def add_file_to_project(self, project_name: str, file_path: str, content: Any) -> Dict[str, Any]:
        """
        Add a file to a project.
        
        Args:
            project_name: Name of the project
            file_path: Path to the file relative to the project root
            content: Content of the file
            
        Returns:
            Result of the operation
        """
        if project_name not in self.projects:
            return {"error": f"Project {project_name} does not exist"}
        
        # Sanitize the file path
        file_path = self._sanitize_path(file_path)
        
        project_dir = self._join_paths(self.projects_dir, project_name)
        
        # Fix duplicate project directory issue
        # Check if the path contains project_name/project_name pattern
        path_parts = file_path.split(os.sep)
        if path_parts and path_parts[0] == project_name:
            # Remove the first part of the path to avoid nesting
            file_path = os.sep.join(path_parts[1:])
            logger.info(f"Adjusted path to avoid nesting: {file_path}")
        
        # Detect framework type from existing files if not already determined
        framework_info = self._detect_framework_from_files(project_name)
        
        # Apply framework-specific path adjustments based on detected framework
        if framework_info.get("is_angular", False):
            # Ensure Angular components are in src/app/components
            if re.search(r'\.component\.(ts|html|scss|css)$', file_path) and not file_path.startswith('src/app/components'):
                component_name = re.search(r'([a-zA-Z0-9_-]+)\.component', file_path)
                if component_name:
                    file_path = f"src/app/components/{component_name.group(1)}/{os.path.basename(file_path)}"
            
            # Ensure Angular services are in src/app/services
            elif re.search(r'\.service\.ts$', file_path) and not file_path.startswith('src/app/services'):
                file_path = f"src/app/services/{os.path.basename(file_path)}"
                
            # Ensure other Angular files are in appropriate directories
            elif (not file_path.startswith('src/app/') and 
                  (re.search(r'\.module\.ts$', file_path) or 
                   re.search(r'\.directive\.ts$', file_path) or
                   re.search(r'\.pipe\.ts$', file_path) or
                   re.search(r'\.guard\.ts$', file_path))):
                file_type = ""
                if ".module." in file_path:
                    file_type = ""  # root app directory
                elif ".directive." in file_path:
                    file_type = "directives"
                elif ".pipe." in file_path:
                    file_type = "pipes"
                elif ".guard." in file_path:
                    file_type = "guards"
                
                if file_type:
                    file_path = f"src/app/{file_type}/{os.path.basename(file_path)}"
                else:
                    file_path = f"src/app/{os.path.basename(file_path)}"
                    
        elif framework_info.get("is_react", False):
            # Ensure React components are in src/components
            if re.search(r'\.(jsx|tsx)$', file_path):
                if not file_path.startswith('src/'):
                    if re.search(r'(container|page|view|screen)\.(jsx|tsx)$', file_path.lower()):
                        file_path = f"src/containers/{os.path.basename(file_path)}"
                    else:
                        file_path = f"src/components/{os.path.basename(file_path)}"
            
            # Ensure hooks are in src/hooks
            elif re.search(r'\.hook\.(js|ts)$', file_path) and not file_path.startswith('src/hooks'):
                file_path = f"src/hooks/{os.path.basename(file_path)}"
                
            # Ensure context/store files are in src/context or src/store
            elif re.search(r'(context|store)\.(js|ts)$', file_path):
                if 'context' in file_path.lower():
                    file_path = f"src/context/{os.path.basename(file_path)}"
                elif 'store' in file_path.lower():
                    file_path = f"src/store/{os.path.basename(file_path)}"
                    
        elif framework_info.get("is_vue", False):
            # Ensure Vue components are in src/components
            if file_path.endswith('.vue') and not file_path.startswith('src/'):
                if re.search(r'(page|view|screen)\.vue$', file_path.lower()):
                    file_path = f"src/views/{os.path.basename(file_path)}"
                else:
                    file_path = f"src/components/{os.path.basename(file_path)}"
            
            # Ensure store modules are in src/store
            elif re.search(r'\.store\.(js|ts)$', file_path) and not file_path.startswith('src/store'):
                file_path = f"src/store/{os.path.basename(file_path)}"
                
        elif framework_info.get("is_express", False):
            # Ensure Express/Node.js modules are in correct directories
            if not file_path.startswith('src/'):
                if re.search(r'route[s]?\.js$', file_path):
                    file_path = f"src/routes/{os.path.basename(file_path)}"
                elif re.search(r'controller[s]?\.js$', file_path):
                    file_path = f"src/controllers/{os.path.basename(file_path)}"
                elif re.search(r'model[s]?\.js$', file_path):
                    file_path = f"src/models/{os.path.basename(file_path)}"
                elif re.search(r'middleware[s]?\.js$', file_path):
                    file_path = f"src/middleware/{os.path.basename(file_path)}"
        
        # Create full path to the file
        file_full_path = self._join_paths(project_dir, file_path)
        
        # Security check: ensure the file path stays within the project directory
        real_project_dir = os.path.realpath(project_dir)
        real_file_path = os.path.realpath(file_full_path)
        if not real_file_path.startswith(real_project_dir):
            file_path = os.path.basename(file_path)
            file_full_path = self._join_paths(project_dir, file_path)
            logger.warning(f"Attempted path traversal detected. Restricting to: {file_path}")
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(file_full_path), exist_ok=True)
        
        try:
            # Check if the file already exists and handle it intelligently
            if os.path.exists(file_full_path):
                existing_content = None
                try:
                    # Try to read the existing content
                    with open(file_full_path, "r", encoding="utf-8") as f:
                        existing_content = f.read()
                except Exception as e:
                    logger.warning(f"Could not read existing file {file_path}: {e}")
                
                if existing_content is not None:
                    # For code files, attempt to merge changes intelligently
                    if self._is_code_file(file_path):
                        merged_content = self._smart_merge_code(file_path, existing_content, content)
                        content = merged_content
                        logger.info(f"Smart-merged changes for existing file: {file_path}")
                    else:
                        # For non-code files, check which is more feature-complete
                        if self._is_content_more_complete(existing_content, content):
                            # Keep existing file if it appears more complete
                            logger.info(f"Keeping existing file {file_path} as it appears more complete")
                            
                            # Add to project files list if not already there
                            project_files = self.projects[project_name].get("files", [])
                            file_found = False
                            
                            for file in project_files:
                                if file["path"] == file_path:
                                    file["content"] = existing_content
                                    file_found = True
                                    break
                            
                            if not file_found:
                                project_files.append({"path": file_path, "content": existing_content})
                                self.projects[project_name]["files"] = project_files
                            
                            return {"success": True, "message": f"Kept existing file {file_path} in project {project_name}"}
            
            # Check if file is binary content
            is_binary = False
            if isinstance(content, bytes):
                is_binary = True
            elif isinstance(content, str) and "\0" in content:
                is_binary = True
                content = content.encode("utf-8")
            
            # Write content to file
            mode = "wb" if is_binary else "w"
            encoding = None if is_binary else "utf-8"
            
            with open(file_full_path, mode, encoding=encoding) as f:
                f.write(content)
            
            # Add file to project
            project_files = self.projects[project_name].get("files", [])
            file_found = False
            
            for file in project_files:
                if file["path"] == file_path:
                    file["content"] = content
                    file_found = True
                    break
            
            if not file_found:
                project_files.append({"path": file_path, "content": content})
                self.projects[project_name]["files"] = project_files
            
            # Add entry to filesystem table if it exists
            try:
                # The SQL operation may fail if conn is not initialized yet
                if hasattr(self, 'conn'):
                    cursor = self.conn.cursor()
                    cursor.execute(
                        "INSERT OR REPLACE INTO files (project, path, timestamp) VALUES (?, ?, ?)",
                        (project_name, file_path, datetime.now().isoformat())
                    )
                    self.conn.commit()
            except (AttributeError, sqlite3.Error) as e:
                logger.debug(f"Skipping SQL operation: {e}")
            
            return {"success": True, "message": f"File {file_path} added to project {project_name}"}
        except Exception as e:
            logger.error(f"Error adding file {file_path} to project {project_name}: {e}")
            return {"error": str(e)}
            
    def _detect_framework_from_files(self, project_name: str) -> Dict[str, bool]:
        """
        Detect framework from existing project files.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Dictionary with framework detection flags
        """
        framework_info = {
            "is_angular": False,
            "is_react": False,
            "is_vue": False,
            "is_express": False,
            "is_django": False,
            "is_flask": False
        }
        
        if project_name not in self.projects:
            return framework_info
            
        project_files = self.projects[project_name].get("files", [])
        project_dir = self._join_paths(self.projects_dir, project_name)
        
        # Check for framework-specific file patterns
        for file in project_files:
            path = file.get("path", "")
            
            # Angular detection
            if re.search(r'\.component\.(ts|html|scss|css)$', path) or re.search(r'\.module\.ts$', path):
                framework_info["is_angular"] = True
                
            # React detection
            elif re.search(r'\.(jsx|tsx)$', path) or path.endswith('react.js'):
                framework_info["is_react"] = True
                
            # Vue detection
            elif path.endswith('.vue'):
                framework_info["is_vue"] = True
                
            # Express/Node detection
            elif re.search(r'express\.js$|app\.js$|server\.js$', path) or re.search(r'route[s]?\.js$', path):
                framework_info["is_express"] = True
                
            # Django detection
            elif re.search(r'settings\.py$|urls\.py$|wsgi\.py$', path):
                framework_info["is_django"] = True
                
            # Flask detection
            elif path.endswith('app.py') or path.endswith('flask_app.py'):
                framework_info["is_flask"] = True
        
        # Check specific files on disk if project files list isn't conclusive
        if not any(framework_info.values()):
            # Check for package.json to identify JavaScript frameworks
            package_json_path = self._join_paths(project_dir, "package.json")
            if os.path.exists(package_json_path):
                try:
                    with open(package_json_path, "r") as f:
                        package_data = json.load(f)
                    
                    dependencies = {
                        **package_data.get("dependencies", {}),
                        **package_data.get("devDependencies", {})
                    }
                    
                    if "@angular/core" in dependencies:
                        framework_info["is_angular"] = True
                    elif "react" in dependencies:
                        framework_info["is_react"] = True
                    elif "vue" in dependencies:
                        framework_info["is_vue"] = True
                    elif "express" in dependencies:
                        framework_info["is_express"] = True
                except Exception as e:
                    logger.error(f"Error reading package.json: {e}")
            
            # Check for requirements.txt to identify Python frameworks
            requirements_path = self._join_paths(project_dir, "requirements.txt")
            if os.path.exists(requirements_path):
                try:
                    with open(requirements_path, "r") as f:
                        content = f.read().lower()
                        if "django" in content:
                            framework_info["is_django"] = True
                        elif "flask" in content:
                            framework_info["is_flask"] = True
                except Exception as e:
                    logger.error(f"Error reading requirements.txt: {e}")
        
        return framework_info
    
    def get_file_content(self, project_name: str, file_path: str) -> Optional[str]:
        """
        Get file content.
        
        Args:
            project_name: Name of the project
            file_path: Path to the file (relative to project directory)
            
        Returns:
            File content or None if file doesn't exist
        """
        if project_name not in self.projects:
            logger.warning(f"Project {project_name} doesn't exist")
            return None
        
        project_files = self.projects[project_name].get("files", [])
        for file in project_files:
            if file["path"] == file_path:
                return file.get("content")
        
        project_dir = self._join_paths(self.projects_dir, project_name)
        file_path_full = self._join_paths(project_dir, file_path)
        
        if os.path.exists(file_path_full):
            # Skip binary files that are likely to cause encoding issues
            problematic_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.ttf', '.eot', '.bin', '.exe', '.dll']
            if any(file_path_full.lower().endswith(ext) for ext in problematic_extensions):
                logger.warning(f"Skipping binary file: {file_path}")
                return None
            
            # Try different encodings
            encodings_to_try = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            
            for encoding in encodings_to_try:
                try:
                    with open(file_path_full, "r", encoding=encoding) as f:
                        content = f.read()
                    return content
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.error(f"Error reading file {file_path}: {e}")
                    return None
                
            # If all encodings failed
            logger.warning(f"Could not decode file {file_path} with any encoding")
            return None
        else:
            return None
    
    def delete_file(self, project_name: str, file_path: str) -> bool:
        """
        Delete a file from a project.
        
        Args:
            project_name: Name of the project
            file_path: Path to the file (relative to project directory)
            
        Returns:
            True if file was deleted, False otherwise
        """
        if project_name not in self.projects:
            logger.warning(f"Project {project_name} doesn't exist")
            return False
        
        project_files = self.projects[project_name].get("files", [])
        for i, file in enumerate(project_files):
            if file["path"] == file_path:
                project_files.pop(i)
                
                self.projects[project_name]["files"] = project_files
                self.projects[project_name]["updated_at"] = datetime.now().isoformat()
                
                self._save_project_metadata(project_name, self.projects[project_name])
                
                project_dir = self._join_paths(self.projects_dir, project_name)
                file_path_full = self._join_paths(project_dir, file_path)
                
                if os.path.exists(file_path_full):
                    try:
                        os.remove(file_path_full)
                    except Exception as e:
                        logger.error(f"Error deleting file {file_path} for project {project_name}: {e}")
                        return False
                
                logger.info(f"Deleted file {file_path} from project {project_name}")
                
                return True
        
        logger.warning(f"File {file_path} not found in project {project_name}")
        
        return False
    
    def get_project_messages(self, project_name: str) -> List[Dict[str, Any]]:
        """
        Get project messages.
        
        Args:
            project_name: Name of the project
            
        Returns:
            List of messages
        """
        if project_name not in self.projects:
            logger.warning(f"Project {project_name} doesn't exist")
            return []
        
        return self.projects[project_name].get("messages", [])
    
    def add_message_from_user(self, project_name: str, content: str) -> Dict[str, Any]:
        """
        Add a message from the user to a project.
        
        Args:
            project_name: Name of the project
            content: Message content
            
        Returns:
            Message metadata
        """
        return self._add_message(project_name, content, from_agent=False)
    
    def add_message_from_agent(self, project_name: str, content: str) -> Dict[str, Any]:
        """
        Add a message from the agent to a project.
        
        Args:
            project_name: Name of the project
            content: Message content
            
        Returns:
            Message metadata
        """
        return self._add_message(project_name, content, from_agent=True)
    
    def _add_message(self, project_name: str, content: str, from_agent: bool) -> Dict[str, Any]:
        """
        Add a message to a project.
        
        Args:
            project_name: Name of the project
            content: Message content
            from_agent: Whether the message is from the agent
            
        Returns:
            Message metadata
        """
        if project_name not in self.projects:
            logger.warning(f"Project {project_name} doesn't exist")
            return {}
        
        message_id = f"{int(time.time() * 1000)}-{len(self.projects[project_name].get('messages', []))}"
        message = {
            "id": message_id,
            "content": content,
            "from_agent": from_agent,
            "sender": "agent" if from_agent else "user",
            "timestamp": datetime.now().isoformat(),
            "reactions": [],
            "references": []  # For citing sources or referring to previous messages
        }
        
        project_messages = self.projects[project_name].get("messages", [])
        project_messages.append(message)
        
        self.projects[project_name]["messages"] = project_messages
        self.projects[project_name]["updated_at"] = datetime.now().isoformat()
        
        # Update context memory if this is an agent message responding to a user query
        if from_agent and project_messages and len(project_messages) >= 2:
            user_message = project_messages[-2] if project_messages[-2].get("sender") == "user" else None
            if user_message:
                self._update_context_memory(project_name, user_message, message)
        
        self._save_project_metadata(project_name, self.projects[project_name])
        
        logger.info(f"Added message to project {project_name}")
        
        return message
    
    def add_message_reaction(self, project_name: str, message_id: str, reaction: str) -> bool:
        """
        Add a reaction to a message.
        
        Args:
            project_name: Name of the project
            message_id: ID of the message
            reaction: Reaction type (like, dislike, etc.)
            
        Returns:
            True if successful, False otherwise
        """
        if project_name not in self.projects:
            logger.warning(f"Project {project_name} doesn't exist")
            return False
        
        project_messages = self.projects[project_name].get("messages", [])
        
        for message in project_messages:
            if message.get("id") == message_id:
                # Check if the reaction already exists
                existing_reactions = message.get("reactions", [])
                if reaction not in existing_reactions:
                    existing_reactions.append(reaction)
                    message["reactions"] = existing_reactions
                    self._save_project_metadata(project_name, self.projects[project_name])
                    logger.info(f"Added reaction {reaction} to message {message_id} in project {project_name}")
                    return True
                return True  # Reaction already exists
        
        logger.warning(f"Message {message_id} not found in project {project_name}")
        return False
    
    def _update_context_memory(self, project_name: str, user_message: Dict[str, Any], agent_message: Dict[str, Any]) -> None:
        """
        Update the context memory for the project.
        
        Args:
            project_name: Name of the project
            user_message: User message
            agent_message: Agent message
        """
        if "context_memory" not in self.projects[project_name]:
            self.projects[project_name]["context_memory"] = []
        
        # Add this Q&A pair to context memory
        memory_entry = {
            "question": user_message.get("content", ""),
            "answer": agent_message.get("content", ""),
            "timestamp": datetime.now().isoformat()
        }
        
        context_memory = self.projects[project_name]["context_memory"]
        context_memory.append(memory_entry)
        
        # Limit context memory size to last 10 entries
        if len(context_memory) > 10:
            context_memory = context_memory[-10:]
        
        self.projects[project_name]["context_memory"] = context_memory
    
    def get_context_memory(self, project_name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get context memory for the project.
        
        Args:
            project_name: Name of the project
            limit: Maximum number of memory entries to return
            
        Returns:
            List of context memory entries
        """
        if project_name not in self.projects:
            logger.warning(f"Project {project_name} doesn't exist")
            return []
        
        context_memory = self.projects[project_name].get("context_memory", [])
        
        # Return the most recent entries, limited by the limit parameter
        return context_memory[-limit:] if limit > 0 else context_memory
    
    def reset_project(self, project_name: str) -> bool:
        """
        Delete all contents of the project folder but leave the folder itself.
        Args:
            project_name: Name of the project
        Returns:
            True if reset was successful, False otherwise
        """
        if project_name not in self.projects:
            logger.warning(f"Project {project_name} doesn't exist")
            return False
        project_dir = self._join_paths(self.projects_dir, project_name)
        try:
            for item in os.listdir(project_dir):
                item_path = self._join_paths(project_dir, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                else:
                    os.remove(item_path)
            # Optionally, reinitialize metadata
            self.projects[project_name] = {
                "name": project_name,
                "created_at": self.projects[project_name].get("created_at", datetime.now().isoformat()),
                "updated_at": datetime.now().isoformat(),
                "files": [],
                "messages": []
            }
            self._save_project_metadata(project_name, self.projects[project_name])
            logger.info(f"Reset project {project_name}")
            return True
        except Exception as e:
            logger.error(f"Error resetting project directory for {project_name}: {e}")
            return False
