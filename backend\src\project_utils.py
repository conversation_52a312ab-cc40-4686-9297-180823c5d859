import os
import json
import shutil
import logging
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)

def fix_nested_project_structure(project_root: str) -> Dict[str, int]:
    """
    Checks for and fixes common project structure issues:
    1. Nested project folders (project_name/project_name)
    2. Duplicate project_memory.json files
    3. Incorrectly nested source files
    
    Args:
        project_root: The root directory of the project
        
    Returns:
        Dictionary with counts of issues fixed
    """
    if not os.path.exists(project_root) or not os.path.isdir(project_root):
        logger.error(f"Project root does not exist or is not a directory: {project_root}")
        return {"error": f"Invalid project root: {project_root}"}
    
    stats = {
        "nested_folders_fixed": 0,
        "duplicate_memory_files_fixed": 0,
        "src_structure_fixed": 0,
        "total_fixes": 0
    }
    
    # Get the project name from the folder name
    project_name = os.path.basename(project_root)
    logger.info(f"Checking project structure for: {project_name} in {project_root}")
    
    # 1. Check for nested project folder
    nested_dir = os.path.join(project_root, project_name)
    if os.path.exists(nested_dir) and os.path.isdir(nested_dir):
        logger.info(f"Detected nested project directory: {nested_dir}")
        
        # Move all files from nested directory to parent
        for item in os.listdir(nested_dir):
            source = os.path.join(nested_dir, item)
            target = os.path.join(project_root, item)
            
            # Don't overwrite existing files
            if os.path.exists(target):
                logger.warning(f"Skipping existing item: {target}")
                continue
                
            try:
                if os.path.isdir(source):
                    shutil.copytree(source, target)
                else:
                    shutil.copy2(source, target)
                logger.info(f"Moved {source} to {target}")
            except Exception as e:
                logger.error(f"Error moving {source} to {target}: {e}")
        
        # Remove the nested directory after everything is moved
        try:
            shutil.rmtree(nested_dir)
            logger.info(f"Removed nested directory: {nested_dir}")
            stats["nested_folders_fixed"] += 1
            stats["total_fixes"] += 1
        except Exception as e:
            logger.error(f"Error removing nested directory: {e}")
    
    # 2. Check for duplicate project_memory.json files
    root_memory_file = os.path.join(project_root, "project_memory.json")
    memory_files = []
    
    # Find all project_memory.json files
    for root, dirs, files in os.walk(project_root):
        if "project_memory.json" in files:
            memory_file_path = os.path.join(root, "project_memory.json")
            if memory_file_path != root_memory_file:  # Skip the one at the project root
                memory_files.append(memory_file_path)
    
    # If we have duplicates and no memory file at the root
    if memory_files and not os.path.exists(root_memory_file):
        try:
            # Use the most recently modified file
            newest_file = max(memory_files, key=os.path.getmtime)
            logger.info(f"Using newest memory file: {newest_file}")
            
            # Copy the newest file to the root
            shutil.copy2(newest_file, root_memory_file)
            logger.info(f"Copied {newest_file} to {root_memory_file}")
            
            stats["duplicate_memory_files_fixed"] += 1
            stats["total_fixes"] += 1
        except Exception as e:
            logger.error(f"Error copying memory file: {e}")
    
    # If we have duplicates and a memory file at the root
    elif memory_files and os.path.exists(root_memory_file):
        try:
            # Load the root memory file
            with open(root_memory_file, "r", encoding="utf-8") as f:
                root_data = json.load(f)
            
            # Load each additional memory file and merge data
            for mem_file in memory_files:
                try:
                    with open(mem_file, "r", encoding="utf-8") as f:
                        additional_data = json.load(f)
                    
                    # Merge data - prefer root data but add any missing keys
                    for key, value in additional_data.items():
                        if key not in root_data:
                            root_data[key] = value
                    
                    # Delete the duplicate file
                    os.remove(mem_file)
                    logger.info(f"Merged and deleted duplicate memory file: {mem_file}")
                except Exception as e:
                    logger.error(f"Error processing memory file {mem_file}: {e}")
            
            # Save the merged data back to the root memory file
            with open(root_memory_file, "w", encoding="utf-8") as f:
                json.dump(root_data, f, indent=2)
            
            stats["duplicate_memory_files_fixed"] += len(memory_files)
            stats["total_fixes"] += len(memory_files)
        except Exception as e:
            logger.error(f"Error merging memory files: {e}")
    
    # 3. Check if src directory exists inside a nested structure
    nested_src = os.path.join(project_root, project_name, "src")
    root_src = os.path.join(project_root, "src")
    
    if os.path.exists(nested_src) and not os.path.exists(root_src):
        try:
            # Move the src directory to the project root
            shutil.move(nested_src, root_src)
            logger.info(f"Moved nested src directory from {nested_src} to {root_src}")
            stats["src_structure_fixed"] += 1
            stats["total_fixes"] += 1
        except Exception as e:
            logger.error(f"Error moving src directory: {e}")
    
    logger.info(f"Project structure check completed. Total fixes: {stats['total_fixes']}")
    return stats


def get_canonical_project_path(project_root: str, relative_path: str) -> str:
    """
    Converts a potentially problematic relative path to a canonical path
    within the project structure.
    
    Args:
        project_root: The root directory of the project
        relative_path: The relative path that may need normalization
        
    Returns:
        Canonical path within the project
    """
    if not relative_path:
        return project_root
    
    # Normalize path (remove .. and extra slashes)
    normalized_path = os.path.normpath(relative_path)
    
    # Get project name
    project_name = os.path.basename(project_root)
    
    # Remove project name prefix if present
    path_parts = normalized_path.split(os.sep)
    if path_parts and path_parts[0] == project_name:
        path_parts = path_parts[1:]
        normalized_path = os.path.join(*path_parts) if path_parts else ""
    
    # Join with project root
    return os.path.join(project_root, normalized_path)


def find_all_components(project_root: str) -> List[Dict[str, str]]:
    """
    Find all components in the project by analyzing the directory structure.
    This is useful for rebuilding project context.
    
    Args:
        project_root: The root directory of the project
        
    Returns:
        List of component information
    """
    components = []
    
    # Skip these directories
    exclude_dirs = ["node_modules", ".git", "__pycache__", "dist", "build", "target"]
    
    # File patterns to detect component types
    component_patterns = {
        "angular-component": [".component.ts", ".component.html", ".component.scss", ".component.css"],
        "angular-service": [".service.ts"],
        "angular-module": [".module.ts"],
        "react-component": [".jsx", ".tsx"],
        "vue-component": [".vue"],
        "model": [".model.ts", "dto.ts", ".entity.ts", ".interface.ts"],
        "api-service": ["api.ts", "client.ts", "http.ts"]
    }
    
    # Walk through the project directory
    for root, dirs, files in os.walk(project_root):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        # Check each file
        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, project_root)
            
            # Determine component type based on file patterns
            component_type = "unknown"
            for ctype, patterns in component_patterns.items():
                if any(file.endswith(pattern) for pattern in patterns):
                    component_type = ctype
                    break
            
            # Only include source code files
            file_ext = os.path.splitext(file)[1].lower()
            if file_ext in [".ts", ".js", ".jsx", ".tsx", ".vue", ".html", ".css", ".scss"]:
                components.append({
                    "name": os.path.splitext(file)[0],
                    "type": component_type,
                    "path": rel_path
                })
    
    return components 