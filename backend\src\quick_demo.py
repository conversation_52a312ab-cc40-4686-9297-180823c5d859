#!/usr/bin/env python
"""
Quick demonstration of the enhanced autonomous agent capabilities.
"""

import os
import sys
import json
import asyncio
import logging
from pathlib import Path

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agents.auto_agent import AutoAgent
from src.project import ProjectManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("QuickDemo")

# Calculator app description
CALCULATOR_PROMPT = """
Create a calculator application with Angular. The calculator should have:
1. Basic arithmetic operations (addition, subtraction, multiplication, division)
2. A clear button to reset the calculator
3. A display to show the current calculation
4. A clean, responsive interface
"""

async def main():
    """Main function."""
    logger.info("Starting quick demo of enhanced autonomous agent capabilities")
    
    # Initialize the AutoAgent
    agent = AutoAgent(openai_model_id="openai/gpt-4o")
    
    # Project name
    project_name = "CalculatorDemo"
    
    # Print instructions
    print("\n" + "=" * 50)
    print("AUTONOMOUS CALCULATOR APP CREATION")
    print("=" * 50)
    print(f"Project name: {project_name}")
    print(f"Model: openai/gpt-4o")
    print("=" * 50 + "\n")
    
    # Define a simple callback for streaming updates
    async def stream_callback(text):
        print(text, end="", flush=True)
    
    # Start the autonomous project creation
    result = await agent.create_project(
        project_name=project_name,
        prompt=CALCULATOR_PROMPT,
        callbacks={
            'stream': stream_callback
        }
    )
    
    # Print completion message
    print("\n" + "=" * 50)
    print("CREATION COMPLETE")
    print("=" * 50)
    
    # Get project info
    project_manager = ProjectManager()
    project_dir = os.path.join(project_manager.projects_dir, project_name)
    
    # Check if project exists
    if os.path.exists(project_dir):
        # Count files
        file_count = 0
        key_files = []
        for root, _, files in os.walk(project_dir):
            for file in files:
                file_count += 1
                rel_path = os.path.relpath(os.path.join(root, file), project_dir)
                if any(keyword in rel_path.lower() for keyword in ['calculator', 'component', 'service', 'app.module']):
                    key_files.append(rel_path)
        
        print(f"\nProject created at: {project_dir}")
        print(f"Total files created: {file_count}")
        
        if key_files:
            print("\nKey calculator files created:")
            for file in sorted(key_files)[:10]:  # Show top 10 files
                print(f"  - {file}")
            
            if len(key_files) > 10:
                print(f"  - ... and {len(key_files) - 10} more")
        
        print("\nTo run the calculator app:")
        print(f"  1. cd {project_dir}")
        print(f"  2. npm install")
        print(f"  3. npm start")
        print(f"  4. Open browser to http://localhost:4200/")
    else:
        print(f"\nFailed to create project at {project_dir}")
    
    return 0

if __name__ == "__main__":
    asyncio.run(main()) 