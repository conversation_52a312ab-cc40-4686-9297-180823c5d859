"""
Socket.IO instance for real-time communication.
"""
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

sio = None

def set_socket_instance(socket_instance):
    """
    Set the global socket instance.
    
    Args:
        socket_instance: Socket.IO instance
    """
    global sio
    sio = socket_instance
    logger.info("Socket instance set")

async def emit_message(event: str, data: Dict[str, Any], room: Optional[str] = None) -> None:
    """
    Emit a message to connected clients.
    
    Args:
        event: Event name
        data: Event data
        room: Room to emit to (optional)
    """
    global sio
    
    if sio is None:
        logger.warning(f"Socket instance not set, cannot emit {event}")
        return
    
    try:
        if room:
            await sio.emit(event, data, room=room)
        else:
            await sio.emit(event, data)
        
        logger.debug(f"Emitted {event} event")
    except Exception as e:
        logger.error(f"Error emitting {event} event: {e}")

async def emit_agent_message(project_name: str, message: str, message_type: str = "agent") -> None:
    """
    Emit an agent message.
    
    Args:
        project_name: Name of the project
        message: Message content
        message_type: Type of message (agent, user, system, etc.)
    """
    symbol = get_symbol_for_message_type(message_type)
    
    await emit_message("agent_message", {
        "project_name": project_name,
        "message": f"{symbol} {message}",
        "message_type": message_type,
        "timestamp": datetime.now().isoformat()
    })
    
    logger.info(f"[SocketIO] Emitted {message_type} message to {project_name}: {message[:100]}{'...' if len(message) > 100 else ''}")

async def emit_api_call(project_name: str, api_type: str, request_data: Dict[str, Any], response_data: Dict[str, Any] = None, error: str = None) -> None:
    """
    Emit an API call event with full request and response details.
    
    Args:
        project_name: Name of the project
        api_type: Type of API (browser, llm_studio, openai, etc.)
        request_data: Request data sent to the API
        response_data: Response data received from the API
        error: Error message if the API call failed
    """
    symbol = get_symbol_for_message_type(api_type)
    
    message = f"API Call: {api_type}\n"
    message += f"Request: {str(request_data)[:300]}{'...' if len(str(request_data)) > 300 else ''}\n"
    
    if response_data:
        message += f"Response: {str(response_data)[:300]}{'...' if len(str(response_data)) > 300 else ''}"
    
    if error:
        message += f"Error: {error}"
    
    await emit_message("agent_message", {
        "project_name": project_name,
        "message": f"{symbol} {message}",
        "message_type": api_type,
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "api_type": api_type,
            "request": request_data,
            "response": response_data,
            "error": error
        }
    })
    
    logger.info(f"[SocketIO] Emitted {api_type} API call to {project_name}")

async def emit_browser_search(project_name: str, query: str, results: Dict[str, Any] = None, error: str = None) -> None:
    """
    Emit a browser search event with query and results.
    
    Args:
        project_name: Name of the project
        query: Search query
        results: Search results
        error: Error message if the search failed
    """
    symbol = get_symbol_for_message_type("browser")
    
    message = f"Browser Search: {query}\n"
    
    if results:
        message += f"Results: {str(results)[:300]}{'...' if len(str(results)) > 300 else ''}"
    
    if error:
        message += f"Error: {error}"
    
    await emit_message("agent_message", {
        "project_name": project_name,
        "message": f"{symbol} {message}",
        "message_type": "browser",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "query": query,
            "results": results,
            "error": error
        }
    })
    
    logger.info(f"[SocketIO] Emitted browser search to {project_name}: {query}")

async def emit_llm_call(project_name: str, model_type: str, model_id: str, prompt: str, response: str = None, error: str = None, timing: float = None) -> None:
    """
    Emit an LLM call event with prompt and response.
    
    Args:
        project_name: Name of the project
        model_type: Type of model (openai, lm_studio, etc.)
        model_id: ID of the model
        prompt: Prompt sent to the model
        response: Response received from the model
        error: Error message if the call failed
        timing: Execution time in seconds
    """
    symbol = get_symbol_for_message_type(f"llm_{model_type}")
    
    message = f"LLM Call ({model_type}/{model_id}):\n"
    message += f"Prompt: {prompt[:300]}{'...' if len(prompt) > 300 else ''}\n"
    
    if response:
        message += f"Response: {response[:300]}{'...' if len(response) > 300 else ''}"
    
    if error:
        message += f"Error: {error}"
    
    if timing:
        timing_ms = int(timing * 1000)
        message += f"\nExecution time: {timing_ms}ms"
    
    await emit_message("agent_message", {
        "project_name": project_name,
        "message": f"{symbol} {message}",
        "message_type": f"llm_{model_type}",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "model_type": model_type,
            "model_id": model_id,
            "prompt": prompt,
            "response": response,
            "error": error,
            "executionTime": timing_ms if timing else None
        }
    })
    
    logger.info(f"[SocketIO] Emitted {model_type} LLM call to {project_name}")

async def emit_terminal_command(project_name: str, command: str, output: str = None, error: str = None) -> None:
    """
    Emit a terminal command event with command and output.
    
    Args:
        project_name: Name of the project
        command: Command executed
        output: Command output
        error: Error message if the command failed
    """
    symbol = get_symbol_for_message_type("terminal")
    
    message = f"Terminal Command: {command}\n"
    
    if output:
        message += f"Output: {output[:300]}{'...' if len(output) > 300 else ''}"
    
    if error:
        message += f"Error: {error}"
    
    await emit_message("agent_message", {
        "project_name": project_name,
        "message": f"{symbol} {message}",
        "message_type": "terminal",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "command": command,
            "output": output,
            "error": error
        }
    })
    
    logger.info(f"[SocketIO] Emitted terminal command to {project_name}: {command}")

async def emit_agent_file_update(project_name: str, file_path: str, content: str) -> None:
    """
    Emit a file update from the agent.
    
    Args:
        project_name: Name of the project
        file_path: Path to the file being updated
        content: New content of the file
    """
    symbol = get_symbol_for_message_type("code")
    
    message = f"Updated file: {file_path}"
    
    await emit_message("agent_file_update", {
        "project_name": project_name,
        "file_path": file_path,
        "content": content,
        "timestamp": datetime.now().isoformat()
    })
    
    # Also emit as an agent message for UI display
    await emit_agent_message(
        project_name,
        f"{symbol} {message}",
        message_type="code"
    )
    
    logger.info(f"[SocketIO] Emitted file update for {project_name}: {file_path}")

async def emit_code_generation_message(project_name: str, file_path: str, action: str) -> None:
    """
    Emit a code generation message.
    
    Args:
        project_name: Name of the project
        file_path: Path of the file being generated/updated
        action: Action being performed (generating, generated, failed, etc.)
    """
    symbol = get_symbol_for_message_type("code")
    
    message = f"Code {action.capitalize()}: {file_path}"
    
    await emit_message("agent_message", {
        "project_name": project_name,
        "message": f"{symbol} {message}",
        "message_type": "code",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "file_path": file_path,
            "action": action
        }
    })
    
    logger.info(f"[SocketIO] Emitted code {action} for {project_name}: {file_path}")

async def emit_cursor_message(project_name: str, message: str) -> None:
    """
    Emit a cursor message.
    
    Args:
        project_name: Name of the project
        message: Message content
    """
    symbol = get_symbol_for_message_type("cursor")
    
    await emit_message("agent_message", {
        "project_name": project_name,
        "message": f"{symbol} {message}",
        "message_type": "cursor",
        "timestamp": datetime.now().isoformat()
    })
    
    logger.info(f"[SocketIO] Emitted cursor message to {project_name}: {message[:100]}{'...' if len(message) > 100 else ''}")

async def emit_plan(project_name: str, plan: Dict[str, Any]) -> None:
    """
    Emit a plan event with full plan details.
    
    Args:
        project_name: Name of the project
        plan: Plan details
    """
    symbol = get_symbol_for_message_type("plan")
    
    message = f"Plan & Reasoning:\n"
    if "description" in plan:
        message += f"{plan['description']}\n\n"
    
    if "steps" in plan:
        message += "Steps:\n"
        for i, step in enumerate(plan["steps"]):
            message += f"{i+1}. {step.get('description', 'Unknown step')}\n"
    
    await emit_message("agent_message", {
        "project_name": project_name,
        "message": f"{symbol} {message}",
        "message_type": "plan",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "plan": plan
        }
    })
    
    logger.info(f"[SocketIO] Emitted plan to {project_name}")

def get_symbol_for_message_type(message_type: str) -> str:
    """
    Get the appropriate symbol for a message type.
    
    Args:
        message_type: Type of message
        
    Returns:
        Symbol string
    """
    symbols = {
        "user": "👤",
        "agent": "🤖",
        "cursor": "🤖",
        "terminal": "💻",
        "browser": "🔍",
        "plan": "📋",
        "code": "📝",
        "system": "⚙️",
        "error": "⚠️",
        "llm_openai": "🧠",
        "llm_local_llm": "💻",
        "llm_lm_studio": "🧠",
        "llm": "🧠",
        "openai": "🧠",
        "lm_studio": "🧠"
    }
    
    return symbols.get(message_type, "🔵")

async def emit_agent_typing(project_name: str, is_typing: bool = True) -> None:
    """
    Emit agent typing status.
    
    Args:
        project_name: Project name
        is_typing: Typing status (True or False)
    """
    data = {
        "project_name": project_name,
        "typing": is_typing,
        "timestamp": datetime.now().isoformat()
    }
    
    await emit_message("agent_typing", data)

async def emit_agent_status(project_name: str, active: bool, stage: str = "", progress: int = 0, message: str = "", stages: Dict[str, Any] = None) -> None:
    """
    Emit agent workflow status updates to track progress in the UI.
    
    Args:
        project_name: Project name
        active: Whether the agent is actively working
        stage: Current workflow stage (setup, research, planning, implementation, testing, validation)
        progress: Overall progress percentage (0-100)
        message: Current status message
        stages: Detailed status of individual workflow stages
    """
    data = {
        "project_name": project_name,
        "active": active,
        "stage": stage,
        "progress": progress,
        "message": message,
        "timestamp": datetime.now().isoformat()
    }
    
    if stages:
        data["stages"] = stages
    
    await emit_message("agent_status", data)

async def emit_agent_stream_token(project_name: str, token: str) -> None:
    """
    Emit a streaming token from the agent.
    
    Args:
        project_name: Name of the project
        token: Token to stream
    """
    await emit_message("agent_stream_token", {
        "project_name": project_name,
        "token": token,
        "timestamp": datetime.now().isoformat()
    })

async def emit_message_reaction(project_name: str, message_id: str, reaction: str) -> None:
    """
    Emit a message reaction.
    
    Args:
        project_name: Name of the project
        message_id: ID of the message being reacted to
        reaction: Reaction type
    """
    await emit_message("message_reaction", {
        "project_name": project_name,
        "message_id": message_id,
        "reaction": reaction,
        "timestamp": datetime.now().isoformat()
    })
    
    logger.debug(f"[SocketIO] Emitted {reaction} reaction to message {message_id} for {project_name}")

async def emit_agent_thinking(project_name: str, thinking: str) -> None:
    """
    Emit agent's thought process for debugging/transparency.
    
    Args:
        project_name: Name of the project
        thinking: Agent's thought process
    """
    await emit_message("agent_thinking", {
        "project_name": project_name,
        "thinking": thinking,
        "timestamp": datetime.now().isoformat()
    })
    
    logger.debug(f"[SocketIO] Emitted agent thinking for {project_name}: {thinking[:100]}...")

async def emit_agent_complete(project_name: str, status: str, results: Dict[str, Any] = None) -> None:
    """
    Emit agent complete event.
    
    Args:
        project_name: Name of the project
        status: Completion status
        results: Results of agent execution
    """
    await emit_message("agent_complete", {
        "project_name": project_name,
        "status": status,
        "results": results,
        "timestamp": datetime.now().isoformat()
    })
    
    logger.info(f"[SocketIO] Emitted agent complete for {project_name}: {status}")
