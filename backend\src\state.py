"""
State management for the Autonomous Agent.
"""
import os
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentState:
    """
    Manages the state of agents and projects.
    """
    def __init__(self, state_file: str = None):
        """
        Initialize the AgentState.
        
        Args:
            state_file: Path to the state file
        """
        self.state_file = state_file or os.path.join(os.getcwd(), "agent_state.json")
        self.state = self._load_state()
    
    def _load_state(self) -> Dict[str, Any]:
        """
        Load state from disk.
        
        Returns:
            State dictionary
        """
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, "r") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading state: {e}")
        
        return {
            "projects": {},
            "token_usage": {
                "total": 0,
                "by_model": {}
            }
        }
    
    def _save_state(self) -> None:
        """
        Save state to disk.
        """
        try:
            with open(self.state_file, "w") as f:
                json.dump(self.state, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving state: {e}")
    
    def get_project_state(self, project_name: str) -> Dict[str, Any]:
        """
        Get the state of a project.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Project state
        """
        projects = self.state.get("projects", {})
        
        if project_name not in projects:
            projects[project_name] = {
                "active": False,
                "completed": False,
                "current_step": None,
                "token_usage": 0,
                "internal_monologue": "",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            self.state["projects"] = projects
            self._save_state()
        
        return projects[project_name]
    
    def set_agent_active(self, project_name: str, active: bool) -> None:
        """
        Set whether an agent is active for a project.
        
        Args:
            project_name: Name of the project
            active: Whether the agent is active
        """
        project_state = self.get_project_state(project_name)
        project_state["active"] = active
        project_state["updated_at"] = datetime.now().isoformat()
        
        self._save_state()
    
    def is_agent_active(self, project_name: str) -> bool:
        """
        Check if an agent is active for a project.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Whether the agent is active
        """
        project_state = self.get_project_state(project_name)
        return project_state.get("active", False)
    
    def set_agent_completed(self, project_name: str, completed: bool) -> None:
        """
        Set whether an agent has completed a project.
        
        Args:
            project_name: Name of the project
            completed: Whether the agent has completed the project
        """
        project_state = self.get_project_state(project_name)
        project_state["completed"] = completed
        project_state["updated_at"] = datetime.now().isoformat()
        
        self._save_state()
    
    def is_agent_completed(self, project_name: str) -> bool:
        """
        Check if an agent has completed a project.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Whether the agent has completed the project
        """
        project_state = self.get_project_state(project_name)
        return project_state.get("completed", False)
    
    def set_current_step(self, project_name: str, step: str) -> None:
        """
        Set the current step for a project.
        
        Args:
            project_name: Name of the project
            step: Current step
        """
        project_state = self.get_project_state(project_name)
        project_state["current_step"] = step
        project_state["updated_at"] = datetime.now().isoformat()
        
        self._save_state()
    
    def get_current_step(self, project_name: str) -> Optional[str]:
        """
        Get the current step for a project.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Current step
        """
        project_state = self.get_project_state(project_name)
        return project_state.get("current_step")
    
    def add_token_usage(self, project_name: str, model: str, tokens: int) -> None:
        """
        Add token usage for a project and model.
        
        Args:
            project_name: Name of the project
            model: Model name
            tokens: Number of tokens
        """
        project_state = self.get_project_state(project_name)
        project_state["token_usage"] = project_state.get("token_usage", 0) + tokens
        project_state["updated_at"] = datetime.now().isoformat()
        
        self.state["token_usage"]["total"] = self.state["token_usage"].get("total", 0) + tokens
        
        by_model = self.state["token_usage"].get("by_model", {})
        by_model[model] = by_model.get(model, 0) + tokens
        self.state["token_usage"]["by_model"] = by_model
        
        self._save_state()
    
    def get_token_usage(self, project_name: str = None) -> Dict[str, Any]:
        """
        Get token usage.
        
        Args:
            project_name: Name of the project (optional)
            
        Returns:
            Token usage statistics
        """
        if project_name:
            project_state = self.get_project_state(project_name)
            return {
                "project": project_name,
                "tokens": project_state.get("token_usage", 0)
            }
        
        return self.state.get("token_usage", {})
    
    def set_internal_monologue(self, project_name: str, monologue: str) -> None:
        """
        Set the internal monologue for a project.
        
        Args:
            project_name: Name of the project
            monologue: Internal monologue
        """
        project_state = self.get_project_state(project_name)
        project_state["internal_monologue"] = monologue
        project_state["updated_at"] = datetime.now().isoformat()
        
        self._save_state()
    
    def get_internal_monologue(self, project_name: str) -> str:
        """
        Get the internal monologue for a project.
        
        Args:
            project_name: Name of the project
            
        Returns:
            Internal monologue
        """
        project_state = self.get_project_state(project_name)
        return project_state.get("internal_monologue", "")
    
    def append_internal_monologue(self, project_name: str, monologue: str) -> None:
        """
        Append to the internal monologue for a project.
        
        Args:
            project_name: Name of the project
            monologue: Internal monologue to append
        """
        current_monologue = self.get_internal_monologue(project_name)
        new_monologue = current_monologue + "\n" + monologue if current_monologue else monologue
        self.set_internal_monologue(project_name, new_monologue)
