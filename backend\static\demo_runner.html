<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autonomous Agent Demo Runner</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f8fa;
        }
        
        h1, h2 {
            color: #2c3e50;
        }
        
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .demo-options {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .demo-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            flex: 1;
            min-width: 300px;
            transition: transform 0.2s;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
        }
        
        .demo-card h3 {
            margin-top: 0;
            color: #3498db;
        }
        
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        
        input[type="text"] {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .output-container {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #34495e;
            color: white;
            padding: 10px 15px;
            border-radius: 4px 4px 0 0;
            margin-top: 20px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-dot.idle {
            background-color: #95a5a6;
        }
        
        .status-dot.running {
            background-color: #2ecc71;
            animation: blink 1s infinite;
        }
        
        .status-dot.error {
            background-color: #e74c3c;
        }
        
        @keyframes blink {
            50% {
                opacity: 0.5;
            }
        }
    </style>
</head>
<body>
    <h1>Autonomous Agent Demo Runner</h1>
    <p>Use the buttons below to run demos of the autonomous agent capabilities.</p>
    
    <div class="card">
        <h2>Project Name</h2>
        <input type="text" id="project-name" placeholder="Enter a project name (optional)" value="DemoProject">
    </div>
    
    <div class="demo-options">
        <div class="demo-card">
            <h3>Quick Demo</h3>
            <p>A simplified demonstration of the autonomous agent's capabilities. This is the fastest option.</p>
            <p><strong>Duration:</strong> ~2-3 minutes</p>
            <button id="quick-demo-btn">Run Quick Demo</button>
        </div>
        
        <div class="demo-card">
            <h3>Calculator Demo</h3>
            <p>Creates a fully functional calculator application with Angular. Demonstrates complete end-to-end project creation.</p>
            <p><strong>Duration:</strong> ~5-7 minutes</p>
            <button id="calculator-demo-btn">Run Calculator Demo</button>
        </div>
        
        <div class="demo-card">
            <h3>Utilities Demo</h3>
            <p>Creates a more complex calculator application with additional features like memory functions and a history of calculations.</p>
            <p><strong>Duration:</strong> ~8-10 minutes</p>
            <button id="utilities-demo-btn">Run Utilities Demo</button>
        </div>
    </div>
    
    <div class="status-bar">
        <div class="status-indicator">
            <div class="status-dot idle" id="status-dot"></div>
            <span id="status-text">Idle</span>
        </div>
        <div>
            <button id="stop-demo-btn" disabled>Stop Demo</button>
        </div>
    </div>
    
    <div class="output-container" id="output"></div>
    
    <script>
        // Variables to track state
        let runningProcess = null;
        let statusCheckInterval = null;
        
        // DOM elements
        const quickDemoBtn = document.getElementById('quick-demo-btn');
        const calculatorDemoBtn = document.getElementById('calculator-demo-btn');
        const utilitiesDemoBtn = document.getElementById('utilities-demo-btn');
        const stopDemoBtn = document.getElementById('stop-demo-btn');
        const projectNameInput = document.getElementById('project-name');
        const outputDiv = document.getElementById('output');
        const statusDot = document.getElementById('status-dot');
        const statusText = document.getElementById('status-text');
        
        // Helper functions
        function setStatus(status) {
            statusDot.className = `status-dot ${status}`;
            statusText.innerText = status === 'idle' ? 'Idle' : 
                                   status === 'running' ? 'Running' : 'Error';
            
            // Update button states
            const buttonsDisabled = status !== 'idle';
            quickDemoBtn.disabled = buttonsDisabled;
            calculatorDemoBtn.disabled = buttonsDisabled;
            utilitiesDemoBtn.disabled = buttonsDisabled;
            stopDemoBtn.disabled = !buttonsDisabled;
        }
        
        function appendOutput(text, error = false) {
            const span = document.createElement('span');
            span.innerText = text;
            if (error) {
                span.style.color = '#e74c3c';
            }
            outputDiv.appendChild(span);
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }
        
        function clearOutput() {
            outputDiv.innerHTML = '';
        }
        
        // Start a demo
        async function startDemo(demoType) {
            // Get the project name
            const projectName = projectNameInput.value.trim() || `Demo${demoType.charAt(0).toUpperCase() + demoType.slice(1)}`;
            
            // Clear previous output
            clearOutput();
            
            // Update UI
            setStatus('running');
            appendOutput(`Starting ${demoType} demo with project name: ${projectName}\n\n`);
            
            try {
                // Call the API to run the demo
                const response = await fetch(`/api/demos/run/${demoType}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ project_name: projectName })
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to start demo: ${response.statusText}`);
                }
                
                const data = await response.json();
                runningProcess = data.process_id;
                
                appendOutput(`Demo started with process ID: ${runningProcess}\n\n`);
                
                // Start polling for status updates
                statusCheckInterval = setInterval(checkDemoStatus, 1000);
                
            } catch (error) {
                appendOutput(`Error: ${error.message}\n`, true);
                setStatus('error');
            }
        }
        
        // Check the status of a running demo
        async function checkDemoStatus() {
            if (!runningProcess) return;
            
            try {
                const response = await fetch(`/api/demos/status/${runningProcess}`);
                
                if (!response.ok) {
                    throw new Error(`Failed to get status: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // Process output if available
                if (data.output) {
                    appendOutput(data.output);
                }
                
                // Check if the process is still running
                if (!data.is_running) {
                    clearInterval(statusCheckInterval);
                    
                    if (data.exit_code === 0) {
                        appendOutput('\n\nDemo completed successfully.\n', false);
                        setStatus('idle');
                    } else {
                        appendOutput(`\n\nDemo exited with code: ${data.exit_code}\n`, true);
                        setStatus('error');
                    }
                    
                    runningProcess = null;
                }
                
            } catch (error) {
                appendOutput(`Error checking status: ${error.message}\n`, true);
                clearInterval(statusCheckInterval);
                setStatus('error');
                runningProcess = null;
            }
        }
        
        // Stop a running demo
        async function stopDemo() {
            if (!runningProcess) return;
            
            try {
                const response = await fetch(`/api/demos/stop/${runningProcess}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to stop demo: ${response.statusText}`);
                }
                
                clearInterval(statusCheckInterval);
                appendOutput('\n\nDemo stopped by user.\n');
                setStatus('idle');
                runningProcess = null;
                
            } catch (error) {
                appendOutput(`Error stopping demo: ${error.message}\n`, true);
                setStatus('error');
            }
        }
        
        // Event listeners for buttons
        quickDemoBtn.addEventListener('click', () => startDemo('quick'));
        calculatorDemoBtn.addEventListener('click', () => startDemo('calculator'));
        utilitiesDemoBtn.addEventListener('click', () => startDemo('utilities'));
        stopDemoBtn.addEventListener('click', stopDemo);
        
        // Initial state
        setStatus('idle');
        appendOutput('Ready to run demos. Choose one from the options above.\n');
    </script>
</body>
</html> 