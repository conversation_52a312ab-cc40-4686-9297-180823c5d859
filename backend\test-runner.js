const express = require('express');
const puppeteer = require('puppeteer');
const playwright = require('playwright');
const bodyParser = require('body-parser');

const app = express();
const port = 4000;

app.use(bodyParser.json({ limit: '1mb' }));

app.post('/run-script', async (req, res) => {
  const { script, runner } = req.body;
  let result = '';
  let screenshot = null;
  let logs = [];
  try {
    if (runner === 'playwright') {
      const browser = await playwright.chromium.launch();
      const context = await browser.newContext();
      const page = await context.newPage();
      page.on('console', msg => logs.push(msg.text()));
      const fn = new Function('page', script);
      result = await fn(page);
      screenshot = await page.screenshot({ type: 'png', encoding: 'base64' });
      await browser.close();
    } else {
      const browser = await puppeteer.launch();
      const page = await browser.newPage();
      page.on('console', msg => logs.push(msg.text()));
      const fn = new Function('page', script);
      result = await fn(page);
      screenshot = await page.screenshot({ encoding: 'base64' });
      await browser.close();
    }
    res.json({ result, logs, screenshot: screenshot ? `data:image/png;base64,${screenshot}` : null });
  } catch (err) {
    res.status(500).json({ error: err.message, logs });
  }
});

app.listen(port, () => {
  console.log(`Dynamic test runner API listening at http://localhost:${port}`);
}); 