import json
import sys
import requests

with open('updated_index.html', 'r') as f:
    content = f.read()

payload = {
    'content': content
}

json_payload = json.dumps(payload)

response = requests.put(
    'http://localhost:8000/api/projects/TestProject/files/index.html',
    headers={'Content-Type': 'application/json'},
    data=json_payload
)

print(f"Status code: {response.status_code}")
print(response.text)
