"""
Main Agent class for the Autonomous AI Software Development Agent.
"""
import logging
import asyncio
import os
import re
import json
import time
import requests
from bs4 import BeautifulSoup
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path
import shlex
import shutil
import fnmatch
import inspect

from llm.llm import LLM

from browser_integration import BrowserIntegration
from vscode_integration import VSCodeIntegration
from project_manager import ProjectManager

# Import socketio instance for emitting messages to frontend
# from main import sio

try:
    from backend.src.socket_instance import emit_agent_file_update, emit_agent_message, emit_plan, emit_llm_call, emit_agent_thinking, emit_agent_complete
except ImportError:
    emit_agent_file_update = None

logger = logging.getLogger(__name__)

# Add CodeAnalyzer class for architectural understanding
class CodeAnalyzer:
    """
    Analyzes code structure, dependencies, and architecture to provide deeper
    understanding of complex projects.
    """
    def __init__(self, project_dir):
        self.project_dir = project_dir
        self.dependency_graph = {}  # Maps files to their dependencies
        self.component_graph = {}   # Maps components to their relationships
        self.api_endpoints = []     # List of API endpoints detected
        self.imports_map = {}       # Maps import statements to files
        
    def analyze_project_structure(self):
        """Analyze the entire project structure and build dependency maps"""
        # Walk through project directory and analyze code files
        for root, dirs, files in os.walk(self.project_dir):
            for file in files:
                if file.endswith(('.ts', '.js', '.html', '.css', '.scss')):
                    file_path = os.path.join(root, file)
                    self.analyze_file(file_path)
        
        # Build higher-level component relationships after analyzing all files
        self.build_component_graph()
        
        return {
            'dependency_graph': self.dependency_graph,
            'component_graph': self.component_graph,
            'api_endpoints': self.api_endpoints,
            'imports_map': self.imports_map
        }
    
    def analyze_file(self, file_path):
        """Analyze a single file to extract imports, dependencies, and structure"""
        rel_path = os.path.relpath(file_path, self.project_dir)
        
        # Skip node_modules directory files to avoid encoding issues
        if 'node_modules' in file_path.replace('\\', '/').split('/'):
            return
        
        # Skip files that are likely to cause encoding issues
        problematic_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.ttf', '.eot', '.bin', '.exe', '.dll']
        if any(file_path.lower().endswith(ext) for ext in problematic_extensions):
            return
        
        # Try different encodings
        encodings_to_try = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        file_content = None
        
        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    file_content = f.read()
                    break  # If successful, break out of the loop
            except (UnicodeDecodeError, IOError):
                continue  # Try the next encoding
        
        if file_content is None:
            # If all encodings failed, skip this file
            logger.warning(f"Could not decode file {file_path} with any encoding")
            return
            
        # Different analysis methods based on file type
        if file_path.endswith('.ts') or file_path.endswith('.js'):
            self._analyze_typescript_file(rel_path, file_content)
        elif file_path.endswith('.html'):
            self._analyze_html_file(rel_path, file_content)
        elif file_path.endswith('.css') or file_path.endswith('.scss') or file_path.endswith('.sass'):
            self._analyze_style_file(rel_path, file_content)
        elif file_path.endswith('.json'):
            self._analyze_json_file(rel_path, file_content)
        elif file_path.endswith('.py'):
            self._analyze_python_file(rel_path, file_content)
        else:
            # Generic file analysis
            self._analyze_generic_file(rel_path, file_content)
    
    def _analyze_typescript_file(self, file_path, content):
        """Analyze TypeScript/JavaScript file for imports, classes, and dependencies"""
        # Extract imports
        import_pattern = re.compile(r'import\s+{?\s*([^}]*)\s*}?\s+from\s+[\'"]([^\'"]+)[\'"];?', re.MULTILINE)
        imports = []
        
        for match in import_pattern.finditer(content):
            imported_items = [item.strip() for item in match.group(1).split(',')]
            import_source = match.group(2)
            imports.append({
                'source': import_source,
                'items': imported_items
            })
            
        # Detect Angular components/services/modules
        component_pattern = re.compile(r'@Component\(\s*{([^}]+)}\s*\)')
        service_pattern = re.compile(r'@Injectable\(\s*{([^}]+)}\s*\)')
        module_pattern = re.compile(r'@NgModule\(\s*{([^}]+)}\s*\)')
        
        # Extract component metadata
        component_match = component_pattern.search(content)
        if component_match:
            # This is an Angular component
            # Extract selector and templateUrl if available
            selector_match = re.search(r'selector\s*:\s*[\'"]([^\'"]+)[\'"]', component_match.group(1))
            template_match = re.search(r'templateUrl\s*:\s*[\'"]([^\'"]+)[\'"]', component_match.group(1))
            
            component_info = {
                'type': 'component',
                'file_path': file_path,
                'selector': selector_match.group(1) if selector_match else None,
                'template': template_match.group(1) if template_match else None,
                'imports': imports
            }
            
            self.component_graph[file_path] = component_info
        
        # Extract service metadata
        service_match = service_pattern.search(content)
        if service_match:
            # This is an Angular service
            service_info = {
                'type': 'service',
                'file_path': file_path,
                'imports': imports
            }
            
            self.component_graph[file_path] = service_info
        
        # Extract module metadata
        module_match = module_pattern.search(content)
        if module_match:
            # This is an Angular module
            # Extract declarations, imports, providers
            declarations_match = re.search(r'declarations\s*:\s*\[(.*?)\]', module_match.group(1), re.DOTALL)
            imports_match = re.search(r'imports\s*:\s*\[(.*?)\]', module_match.group(1), re.DOTALL)
            providers_match = re.search(r'providers\s*:\s*\[(.*?)\]', module_match.group(1), re.DOTALL)
            
            module_info = {
                'type': 'module',
                'file_path': file_path,
                'declarations': self._parse_array_items(declarations_match.group(1) if declarations_match else ""),
                'module_imports': self._parse_array_items(imports_match.group(1) if imports_match else ""),
                'providers': self._parse_array_items(providers_match.group(1) if providers_match else ""),
                'imports': imports
            }
            
            self.component_graph[file_path] = module_info
        
        # Extract API endpoints (for services)
        http_methods = ['get', 'post', 'put', 'delete', 'patch']
        for method in http_methods:
            pattern = rf'\.{method}\s*\(\s*[\'"]([^\'"]+)[\'"]'
            for match in re.finditer(pattern, content, re.IGNORECASE):
                endpoint = match.group(1)
                self.api_endpoints.append({
                    'file': file_path,
                    'method': method.upper(),
                    'endpoint': endpoint
                })
        
        # Store imports
        self.imports_map[file_path] = imports
        
        # Store in dependency graph
        self.dependency_graph[file_path] = {
            'imports': imports,
            'is_component': bool(component_match),
            'is_service': bool(service_match),
            'is_module': bool(module_match)
        }
    
    def _analyze_html_file(self, file_path, content):
        """Analyze HTML template files for component usage and dependencies"""
        # Extract component selectors used in the template
        # This is a simple version - a more comprehensive version would use an HTML parser
        component_pattern = re.compile(r'<(app-[a-zA-Z0-9-]+)[^>]*>')
        component_usages = []
        
        for match in component_pattern.finditer(content):
            component_selector = match.group(1)
            component_usages.append(component_selector)
        
        # Store in dependency graph
        self.dependency_graph[file_path] = {
            'component_usages': component_usages,
            'is_template': True
        }
    
    def _analyze_style_file(self, file_path, content):
        """Analyze CSS/SCSS files for styles and patterns"""
        # Extract selectors
        selector_pattern = re.compile(r'([.#][a-zA-Z0-9_-]+)\s*{', re.MULTILINE)
        selectors = [match.group(1) for match in selector_pattern.finditer(content)]
        
        # Store in dependency graph
        self.dependency_graph[file_path] = {
            'selectors': selectors,
            'is_style': True
        }
    
    def _parse_array_items(self, array_content):
        """Parse items from Angular module arrays like declarations, imports, providers"""
        # Split by commas but ignore commas within brackets/parentheses
        if not array_content.strip():
            return []
            
        items = []
        current_item = ""
        bracket_level = 0
        
        for char in array_content:
            if char == ',' and bracket_level == 0:
                items.append(current_item.strip())
                current_item = ""
            else:
                current_item += char
                if char in '[{(':
                    bracket_level += 1
                elif char in ']})':
                    bracket_level -= 1
        
        if current_item.strip():
            items.append(current_item.strip())
            
        return items
    
    def build_component_graph(self):
        """
        Build a graph of component relationships based on the analyzed files.
        Shows which components use which services, and which components include other components.
        """
        # Map selectors to component files
        selector_to_file = {}
        for file_path, component_info in self.component_graph.items():
            if component_info.get('type') == 'component' and component_info.get('selector'):
                selector_to_file[component_info['selector']] = file_path
        
        # Analyze templates to find component usage
        for file_path, file_info in self.dependency_graph.items():
            if file_info.get('is_template'):
                # Find which component this template belongs to
                for component_path, component_info in self.component_graph.items():
                    if component_info.get('type') == 'component' and component_info.get('template'):
                        template_path = os.path.join(os.path.dirname(component_path), component_info['template'])
                        template_path = os.path.normpath(template_path)
                        
                        if os.path.normpath(file_path) == template_path:
                            # This template belongs to this component
                            # Add child components as dependencies
                            used_components = []
                            for selector in file_info.get('component_usages', []):
                                if selector in selector_to_file:
                                    used_components.append(selector_to_file[selector])
                            
                            if 'used_components' not in component_info:
                                component_info['used_components'] = []
                            
                            component_info['used_components'].extend(used_components)
    
    def get_component_dependencies(self, component_file):
        """Get all dependencies of a specific component"""
        if component_file not in self.component_graph:
            return None
            
        component_info = self.component_graph[component_file]
        
        # Direct imports
        dependencies = {
            'imports': component_info.get('imports', []),
            'used_components': component_info.get('used_components', []),
            'injectable_dependencies': []
        }
        
        # Extract injectable dependencies from constructor
        if component_file in self.dependency_graph:
            with open(os.path.join(self.project_dir, component_file), 'r', encoding='utf-8') as f:
                content = f.read()
                constructor_pattern = re.compile(r'constructor\s*\(\s*(.*?)\s*\)', re.DOTALL)
                constructor_match = constructor_pattern.search(content)
                
                if constructor_match:
                    constructor_params = constructor_match.group(1)
                    # Extract parameters with type annotations
                    param_pattern = re.compile(r'(?:private|public|protected)?\s*(\w+)\s*:\s*(\w+)')
                    for param_match in param_pattern.finditer(constructor_params):
                        param_name = param_match.group(1)
                        param_type = param_match.group(2)
                        dependencies['injectable_dependencies'].append({
                            'name': param_name,
                            'type': param_type
                        })
        
        return dependencies
    
    def generate_architecture_summary(self):
        """Generate a human-readable summary of the application architecture"""
        components = [info for file, info in self.component_graph.items() if info.get('type') == 'component']
        services = [info for file, info in self.component_graph.items() if info.get('type') == 'service']
        modules = [info for file, info in self.component_graph.items() if info.get('type') == 'module']
        
        summary = []
        summary.append(f"# Application Architecture Summary")
        summary.append(f"\n## Overview")
        summary.append(f"- Components: {len(components)}")
        summary.append(f"- Services: {len(services)}")
        summary.append(f"- Modules: {len(modules)}")
        summary.append(f"- API Endpoints: {len(self.api_endpoints)}")
        
        if modules:
            summary.append(f"\n## Modules")
            for module in modules:
                summary.append(f"- {os.path.basename(module['file_path'])}")
                if module.get('declarations'):
                    summary.append(f"  - Declarations: {len(module.get('declarations', []))} components")
                if module.get('providers'):
                    summary.append(f"  - Providers: {len(module.get('providers', []))} services")
        
        if components:
            summary.append(f"\n## Key Components")
            for component in components[:5]:  # Show top 5 components
                summary.append(f"- {os.path.basename(component['file_path'])}")
                if component.get('selector'):
                    summary.append(f"  - Selector: {component['selector']}")
                if component.get('used_components'):
                    summary.append(f"  - Uses {len(component.get('used_components', []))} child components")
        
        if services:
            summary.append(f"\n## Services")
            for service in services[:5]:  # Show top 5 services
                summary.append(f"- {os.path.basename(service['file_path'])}")
        
        if self.api_endpoints:
            summary.append(f"\n## API Endpoints")
            for endpoint in self.api_endpoints[:5]:  # Show top 5 endpoints
                summary.append(f"- {endpoint['method']} {endpoint['endpoint']}")
                
        return "\n".join(summary)

# Add ProjectMemory class
class ProjectMemory:
    """
    Tracks the progress, files, commands, and requirement completion for a project.
    Provides a persistent memory of project state that can be used by the agent.
    """
    def __init__(self, project_name, initial_requirements=None):
        self.project_name = project_name
        self.requirements = initial_requirements or []
        self.files_created = []
        self.files_modified = []
        self.commands_executed = []
        self.errors_encountered = []
        self.requirements_status = {}  # Map of requirement -> completion status
        self.timestamp = time.time()
        self.metadata = {}
        
    def add_file_created(self, file_path, content_summary=None):
        """Record a file creation event"""
        self.files_created.append({
            "path": file_path,
            "timestamp": time.time(),
            "content_summary": content_summary
        })
    
    def add_file_modified(self, file_path, changes=None):
        """Record a file modification event"""
        self.files_modified.append({
            "path": file_path,
            "timestamp": time.time(),
            "changes": changes
        })
    
    def add_command_executed(self, command, output=None, status=None):
        """Record a command execution event"""
        self.commands_executed.append({
            "command": command,
            "timestamp": time.time(),
            "output": output,
            "status": status
        })
    
    def add_error(self, error_message, context=None):
        """Record an error event"""
        self.errors_encountered.append({
            "message": error_message,
            "timestamp": time.time(),
            "context": context
        })
    
    def update_requirement_status(self, requirement, status, notes=None):
        """Update the status of a specific requirement"""
        # Convert requirement to string if it's a dict or other non-hashable type
        req_key = str(requirement) if not isinstance(requirement, str) else requirement
        
        self.requirements_status[req_key] = {
            "status": status,  # e.g. "completed", "in_progress", "not_started", "failed"
            "timestamp": time.time(),
            "notes": notes
        }
    
    def analyze_completion(self):
        """Analyze the overall completion status of the project"""
        total_reqs = len(self.requirements)
        completed_reqs = len([r for r in self.requirements_status.values() if r.get("status") == "completed"])
        
        return {
            "total_requirements": total_reqs,
            "completed_requirements": completed_reqs,
            "completion_percentage": (completed_reqs / total_reqs * 100) if total_reqs > 0 else 0,
            "files_created": len(self.files_created),
            "files_modified": len(self.files_modified),
            "commands_executed": len(self.commands_executed),
            "errors_encountered": len(self.errors_encountered)
        }
    
    def to_json(self):
        """Convert memory to JSON for storage"""
        return {
            "project_name": self.project_name,
            "requirements": self.requirements,
            "files_created": self.files_created,
            "files_modified": self.files_modified,
            "commands_executed": self.commands_executed,
            "errors_encountered": self.errors_encountered,
            "requirements_status": self.requirements_status,
            "timestamp": self.timestamp,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_json(cls, json_data):
        """Create a ProjectMemory instance from JSON"""
        memory = cls(json_data["project_name"])
        memory.requirements = json_data.get("requirements", [])
        memory.files_created = json_data.get("files_created", [])
        memory.files_modified = json_data.get("files_modified", [])
        memory.commands_executed = json_data.get("commands_executed", [])
        memory.errors_encountered = json_data.get("errors_encountered", [])
        memory.requirements_status = json_data.get("requirements_status", {})
        memory.timestamp = json_data.get("timestamp", time.time())
        memory.metadata = json_data.get("metadata", {})
        return memory
    
    def save_to_file(self, project_dir):
        """Save memory to a file in the project directory"""
        memory_path = os.path.join(project_dir, "project_memory.json")
        with open(memory_path, "w") as f:
            json.dump(self.to_json(), f, indent=2)
        return memory_path
    
    @classmethod
    def load_from_file(cls, project_dir):
        """Load memory from a file in the project directory"""
        memory_path = os.path.join(project_dir, "project_memory.json")
        if os.path.exists(memory_path):
            with open(memory_path, "r") as f:
                return cls.from_json(json.load(f))
        return None
    
    def generate_report(self):
        """Generate a human-readable report of project progress"""
        completion = self.analyze_completion()
        
        report = [
            f"# Project Memory Report: {self.project_name}",
            f"Generated on: {time.ctime()}",
            "",
            f"## Project Completion",
            f"- Requirements: {completion['completed_requirements']}/{completion['total_requirements']} ({completion['completion_percentage']:.1f}%)",
            f"- Files Created: {completion['files_created']}",
            f"- Files Modified: {completion['files_modified']}",
            f"- Commands Executed: {completion['commands_executed']}",
            f"- Errors Encountered: {completion['errors_encountered']}",
            "",
            "## Requirements Status"
        ]
        
        for req_key, status in self.requirements_status.items():
            # Display the requirement key (which may be a stringified dict)
            report.append(f"- [{status['status'].upper()}] {req_key}")
            if status.get("notes"):
                report.append(f"  Notes: {status['notes']}")
        
        report.extend([
            "",
            "## Files Created"
        ])
        
        for file in self.files_created:
            report.append(f"- {file['path']} (Created: {time.ctime(file['timestamp'])})")
        
        report.extend([
            "",
            "## Commands Executed"
        ])
        
        for cmd in self.commands_executed:
            report.append(f"- `{cmd['command']}` (Status: {cmd.get('status', 'unknown')})")
        
        return "\n".join(report)

class Agent:
    """
    Main Agent class that coordinates the execution of tasks.
    """
    def __init__(self, model_id: str = "openai/gpt-4o-mini", project_manager: Optional[ProjectManager] = None):
        """
        Initialize the Agent with a specific model.
        
        Args:
            model_id: The ID of the model to use for generation.
            project_manager: ProjectManager instance to use.
        """
        self.model_id = model_id
        self.llm = LLM.create(model_id)
        self.project_manager = project_manager or ProjectManager()
        self.browser = BrowserIntegration()
        self.vscode = VSCodeIntegration()
        self.project_memories = {}  # Map of project_name -> ProjectMemory
        self.code_analyzers = {}    # Map of project_name -> CodeAnalyzer
        
        # Common patterns to ignore in file operations
        self.ignore_patterns = [
            'node_modules', '.git', 'dist', 'build', '.cache', 
            '__pycache__', '.pytest_cache', '.angular', 'coverage',
            '.next', '.nuxt', '.output', '.vscode', '.idea',
            '*.log', '*.lock', '*.min.js', '*.min.css',
            '.DS_Store', 'Thumbs.db'
        ]
        
        self.task_handlers = {
            "create_file": self._handle_create_file,
            "update_file": self._handle_update_file,
            "search_web": self._handle_search_web,
            "run_tests": self._handle_run_tests,
            "open_vscode": self._handle_open_vscode,
            "install_package": self._handle_install_package,
            "analyze_code": self._handle_analyze_code,
            "fix_bugs": self._handle_fix_bugs,
            "create_project": self._handle_create_project
        }
        
        logger.info(f"Initialized Agent with model: {model_id}")
    
    async def execute(self, message: str, project_name: str, sio) -> Dict[str, Any]:
        """
        Execute a task based on the user message.
        
        Args:
            message: The user message containing the task description.
            project_name: The name of the project to work on.
            sio: The socketio instance for emitting messages to frontend
            
        Returns:
            A dictionary containing the execution results.
        """
        logger.info(f"Executing task for project {project_name}: {message}")
        
        project_dir = self.project_manager.get_project_dir(project_name)
        if not project_dir:
            logger.error(f"Project {project_name} not found")
            return {
                "status": "error",
                "message": f"Project {project_name} not found",
                "project_name": project_name
            }
        
        # Emit the user prompt to the chat for transparency
        await emit_agent_message(
            project_name=project_name,
            message=f"[Prompt to LLM]: {message}",
            message_type="user"
        )
        
        # Parse the task to determine what to do
        task_type, task_details = await self._parse_task(message, project_name)
        
        # Emit the thinking process for debugging
        thinking = f"Task type: {task_type}\nTask details: {str(task_details)[:500]}"
        await emit_agent_thinking(project_name, thinking)
        
        # Process the parsed task
        if task_type == "create_project":
            result = await self._handle_create_project(task_details, project_name, project_dir)
        elif task_type == "modify_code":
            result = await self._modify_code(task_details, project_name, project_dir)
        elif task_type == "run_commands":
            result = await self._run_commands(task_details, project_name, project_dir)
        elif task_type == "search_web":
            result = await self._handle_search_web(task_details, project_name)
        else:
            result = {
                "status": "error",
                "message": f"Unknown task type: {task_type}",
                "task_type": task_type
            }
        
        # Emit completion status
        await emit_agent_complete(project_name, result.get("status", "error"), result)
        
        return result
    
    async def _parse_task(self, message: str, project_name: str) -> Tuple[str, Dict[str, Any]]:
        """
        Parse the user message to determine the task type and details.
        
        Args:
            message: The user message
            project_name: The project name
            
        Returns:
            A tuple of (task_type, task_details)
        """
        from socket_instance import emit_llm_call
        
        # Try to parse with OpenAI
        try:
            # Log the call to OpenAI
            logger.info(f"Sending request to OpenAI for task parsing: {message[:100]}...")
            
            # Emit the LLM call for transparency
            await emit_llm_call(
                project_name=project_name,
                model_type="openai",
                model_id=self.model_id,
                prompt=f"Parse the following task request: {message}",
            )
            
            # Call the OpenAI API to parse the task
            response = await self.llm.generate(
                prompt=f"Parse the following task request: {message}",
                context=f"You are a task parsing assistant. Given a user request, determine the type of task and extract relevant details."
            )
            
            # Emit the LLM response for transparency
            await emit_llm_call(
                project_name=project_name,
                model_type="openai",
                model_id=self.model_id,
                prompt=f"Parse the following task request: {message}",
                response=response
            )
            
            # Try to parse the response as JSON
            try:
                task_details = json.loads(response)
                task_type = task_details.get("task_type", "unknown")
            except json.JSONDecodeError:
                # If not valid JSON, use heuristics to determine task type
                task_type = self._determine_task_type(message)
                task_details = {"message": message}
            
            return task_type, task_details
            
        except Exception as e:
            logger.error(f"Error parsing task with OpenAI: {e}")
            
            # Fall back to heuristic approach
            task_type = self._determine_task_type(message)
            task_details = {"message": message}
            
            return task_type, task_details
    
    async def _handle_create_file(self, details: Dict[str, Any], project_name: str, project_dir: str) -> Dict[str, Any]:
        """Handle creating a file."""
        file_path = details.get("file_path", "")
        content = details.get("content", "")
        
        if not file_path:
            return {"status": "error", "message": "File path not provided", "project_name": project_name}
        
        try:
            file_info = self.project_manager.add_file_to_project(project_name, file_path, content)
            
            return {
                "status": "success",
                "message": f"Created file {file_path}",
                "file": file_info,
                "project_name": project_name
            }
        except Exception as e:
            logger.error(f"Error creating file {file_path}: {e}")
            return {"status": "error", "message": f"Error creating file {file_path}: {str(e)}", "project_name": project_name}
    
    async def _handle_update_file(self, details: Dict[str, Any], project_name: str, project_dir: str) -> Dict[str, Any]:
        """Handle updating a file."""
        file_path = details.get("file_path", "")
        content = details.get("content", "")
        
        if not file_path:
            return {"status": "error", "message": "File path not provided", "project_name": project_name}
        
        try:
            file_info = self.project_manager.add_file_to_project(project_name, file_path, content)
            
            return {
                "status": "success",
                "message": f"Updated file {file_path}",
                "file": file_info,
                "project_name": project_name
            }
        except Exception as e:
            logger.error(f"Error updating file {file_path}: {e}")
            return {"status": "error", "message": f"Error updating file {file_path}: {str(e)}", "project_name": project_name}
    
    async def _handle_search_web(self, details: Dict[str, Any], project_name: str) -> Dict[str, Any]:
        """
        Search the web for information based on the task details.
        
        Args:
            details: Details for web search
            project_name: The project name
            
        Returns:
            Dictionary with search results
        """
        from socket_instance import emit_browser_search, emit_agent_message
        
        # Extract the search query
        query = details.get("query", details.get("message", ""))
        
        # Log the search
        logger.info(f"Searching the web for: {query}")
        
        # Emit search start message
        await emit_agent_message(
            project_name=project_name,
            message=f"Searching the web for: {query}",
            message_type="agent"
        )
        
        # Perform the search
        try:
            # Use the browser integration to search
            results = await self.browser.api_search(
                query=query,
                api_engine="searxng",  # Use SearxNG as default
                project_name=project_name
            )
            
            # Check for errors
            if "error" in results:
                logger.error(f"Error searching the web: {results['error']}")
                await emit_agent_message(
                    project_name=project_name,
                    message=f"Error searching the web: {results['error']}",
                    message_type="error"
                )
                return {
                    "status": "error",
                    "message": f"Error searching the web: {results['error']}"
                }
            
            # Return the results
            return {
                "status": "success",
                "message": f"Web search completed for: {query}",
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Error searching the web: {e}")
            
            # Emit error message
            await emit_agent_message(
                project_name=project_name,
                message=f"Error searching the web: {str(e)}",
                message_type="error"
            )
            
            return {
                "status": "error",
                "message": f"Error searching the web: {str(e)}"
            }

    async def _fetch_and_summarize_url(self, url: str) -> str:
        """Fetch the main text content from a URL and summarize it using the LLM."""
        try:
            headers = {"User-Agent": "Mozilla/5.0 (compatible; AutonomousAI/1.0)"}
            resp = requests.get(url, headers=headers, timeout=10)
            if resp.status_code != 200:
                return f"(Failed to fetch: HTTP {resp.status_code})"
            soup = BeautifulSoup(resp.text, "html.parser")
            # Try to extract main content
            paragraphs = soup.find_all('p')
            text = '\n'.join(p.get_text() for p in paragraphs)
            if not text.strip():
                return "(No readable text found)"
            # Limit to first 2000 characters for summarization
            text = text[:2000]
            prompt = f"Summarize the following web page content in 5-7 sentences for a technical audience:\n\n{text}"
            summary = await self.llm.generate(prompt)
            return summary.strip()
        except Exception as e:
            return f"(Error fetching/summarizing: {e})"
    
    async def _handle_run_tests(self, details: Dict[str, Any], project_name: str, project_dir: str) -> Dict[str, Any]:
        """Handle running tests."""
        test_dir = details.get("test_dir", "tests")
        test_file = details.get("test_file", None)
        
        try:
            from integration import Integration
            from autonomous_testing import AutonomousTesting
            
            integration = Integration()
            testing = AutonomousTesting(integration=integration)
            
            test_results = await asyncio.to_thread(
                testing.run_autonomous_testing,
                project_dir=project_dir,
                test_dir=test_dir,
                browser_url="http://localhost:4200"
            )
            
            if test_results["success"]:
                message = "Tests passed successfully."
            else:
                message = f"Tests failed. {len(test_results.get('errors', []))} errors found."
                if test_results.get("errors"):
                    message += "\n\nErrors:\n" + "\n".join([str(err) for err in test_results.get("errors", [])])
            
            return {
                "status": "success" if test_results["success"] else "error",
                "message": message,
                "test_results": test_results,
                "project_name": project_name
            }
        except Exception as e:
            logger.error(f"Error running tests: {e}")
            return {"status": "error", "message": f"Error running tests: {str(e)}", "project_name": project_name}
    
    async def _handle_open_vscode(self, details: Dict[str, Any], project_name: str, project_dir: str) -> Dict[str, Any]:
        """Handle opening VS Code."""
        try:
            file_path = details.get("file_path")
            line = details.get("line")
            column = details.get("column")
            
            if file_path:
                success = self.vscode.open_file(
                    os.path.join(project_dir, file_path),
                    line=line,
                    column=column,
                    wait=True
                )
                
                if success:
                    return {
                        "status": "success",
                        "message": f"Opened file {file_path} in VS Code",
                        "project_name": project_name,
                        "file_path": file_path
                    }
                else:
                    return {
                        "status": "error",
                        "message": f"Failed to open file {file_path} in VS Code",
                        "project_name": project_name
                    }
            else:
                success = self.vscode.open_folder(project_dir)
                
                if success:
                    self.vscode.create_workspace_settings(project_dir)
                    self.vscode.create_tasks_file(project_dir)
                    self.vscode.create_terminal_profile(project_dir)
                    self.vscode.install_recommended_extensions()
                    
                    return {
                        "status": "success",
                        "message": f"Opened project {project_name} in VS Code and configured workspace",
                        "project_name": project_name
                    }
                else:
                    return {
                        "status": "error",
                        "message": f"Failed to open project {project_name} in VS Code",
                        "project_name": project_name
                    }
        except Exception as e:
            logger.error(f"Error opening VS Code: {e}")
            return {"status": "error", "message": f"Error opening VS Code: {str(e)}", "project_name": project_name}
    
    async def _handle_install_package(self, details: Dict[str, Any], project_name: str, project_dir: str) -> Dict[str, Any]:
        """Handle installing a package."""
        package_name = details.get("package_name", "")
        package_manager = details.get("package_manager", "pip")
        
        if not package_name:
            return {"status": "error", "message": "Package name not provided", "project_name": project_name}
        
        try:
            from terminal_integration import TerminalIntegration
            
            terminal = TerminalIntegration()
            
            if package_manager == "pip":
                cmd = f"pip install {package_name}"
            elif package_manager == "npm":
                cmd = f"npm install {package_name}"
            else:
                return {"status": "error", "message": f"Unsupported package manager: {package_manager}", "project_name": project_name}
            
            result = await terminal.run_command(cmd, cwd=project_dir, project_name=project_name)
            output = result.get("output", "")
            error = result.get("error", "")
            
            if error or "error" in output.lower() or "exception" in output.lower():
                return {
                    "status": "error",
                    "message": f"Error installing package {package_name}:\n{output}",
                    "project_name": project_name
                }
            
            return {
                "status": "success",
                "message": f"Installed package {package_name}",
                "output": output,
                "project_name": project_name
            }
        except Exception as e:
            logger.error(f"Error installing package {package_name}: {e}")
            return {"status": "error", "message": f"Error installing package {package_name}: {str(e)}", "project_name": project_name}
    
    async def _handle_analyze_code(self, details: Dict[str, Any], project_name: str, project_dir: str) -> Dict[str, Any]:
        """Handle analyzing code."""
        file_path = details.get("file_path", "")
        
        if not file_path:
            return {"status": "error", "message": "File path not provided", "project_name": project_name}
        
        try:
            content = self.project_manager.get_file_content(project_name, file_path)
            
            if not content:
                return {"status": "error", "message": f"File {file_path} not found", "project_name": project_name}
            
            prompt = f"""Here is the current code for the Angular component '{file_path}':

TypeScript (TS):
```
{content}
```

Please provide:
1. A summary of what the code does
2. Any potential issues or bugs
3. Suggestions for improvement
4. Code quality assessment
"""
            
            analysis = await self.llm.generate(prompt)
            
            return {
                "status": "success",
                "message": analysis,
                "file_path": file_path,
                "project_name": project_name
            }
        except Exception as e:
            logger.error(f"Error analyzing code in {file_path}: {e}")
            return {"status": "error", "message": f"Error analyzing code: {str(e)}", "project_name": project_name}
    
    async def _handle_fix_bugs(self, details: Dict[str, Any], project_name: str, project_dir: str) -> Dict[str, Any]:
        """Handle fixing bugs."""
        file_path = details.get("file_path", "")
        bug_description = details.get("bug_description", "")
        
        if not file_path:
            return {"status": "error", "message": "File path not provided", "project_name": project_name}
        
        try:
            content = self.project_manager.get_file_content(project_name, file_path)
            
            if not content:
                return {"status": "error", "message": f"File {file_path} not found", "project_name": project_name}
            
            prompt = f"""
            Fix the following bug in the code:
            
            Bug description: {bug_description}
            
            Current code:
            ```
            {content}
            ```
            
            Please provide the fixed code. Return only the complete fixed code, nothing else.
            """
            
            fixed_code = await self.llm.generate(prompt)
            
            file_info = self.project_manager.add_file_to_project(project_name, file_path, fixed_code)
            
            return {
                "status": "success",
                "message": f"Fixed bug in {file_path}: {bug_description}",
                "file": file_info,
                "project_name": project_name
            }
        except Exception as e:
            logger.error(f"Error fixing bug in {file_path}: {e}")
            return {"status": "error", "message": f"Error fixing bug: {str(e)}", "project_name": project_name}
    
    async def _handle_create_project(self, details: Dict[str, Any], project_name: str, project_dir: str) -> Dict[str, Any]:
        """Handle creating a project structure."""
        project_type = details.get("project_type", "python")
        
        try:
            if project_type == "python":
                self.project_manager.add_file_to_project(project_name, "README.md", f"# {project_name}\n\nA Python project.")
                self.project_manager.add_file_to_project(project_name, "requirements.txt", "# Dependencies\n")
                self.project_manager.add_file_to_project(project_name, "setup.py", f"""
from setuptools import setup, find_packages

setup(
    name="{project_name}",
    version="0.1.0",
    packages=find_packages(),
)
""")
                self.project_manager.add_file_to_project(project_name, f"{project_name.lower()}/__init__.py", "")
                self.project_manager.add_file_to_project(project_name, f"{project_name.lower()}/main.py", f"""
def main():
    print("Hello from {project_name}!")

if __name__ == "__main__":
    main()
""")
                self.project_manager.add_file_to_project(project_name, "tests/__init__.py", "")
                self.project_manager.add_file_to_project(project_name, "tests/test_main.py", f"""
import unittest
from {project_name.lower()}.main import main

class TestMain(unittest.TestCase):
    def test_main(self):
        self.assertTrue(True)

if __name__ == "__main__":
    unittest.main()
""")
            
            elif project_type == "web":
                self.project_manager.add_file_to_project(project_name, "README.md", f"# {project_name}\n\nA web project.")
                self.project_manager.add_file_to_project(project_name, "index.html", f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{project_name}</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <h1>Welcome to {project_name}</h1>
    <p>This is a web project created by the Autonomous AI Software Development Agent.</p>
    
    <script src="js/main.js"></script>
</body>
</html>
""")
                self.project_manager.add_file_to_project(project_name, "css/styles.css", """
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    line-height: 1.6;
}

h1 {
    color: #333;
}
""")
                self.project_manager.add_file_to_project(project_name, "js/main.js", """
// Main JavaScript file
console.log('Script loaded!');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');
});
""")
            
            else:
                return {"status": "error", "message": f"Unsupported project type: {project_type}", "project_name": project_name}
            
            return {
                "status": "success",
                "message": f"Created {project_type} project structure for {project_name}",
                "project_name": project_name
            }
        except Exception as e:
            logger.error(f"Error creating project structure: {e}")
            return {"status": "error", "message": f"Error creating project structure: {str(e)}", "project_name": project_name}

    def parse_plan_to_steps(self, plan_text: str, project_name: str) -> List[Dict[str, Any]]:
        """
        Parse the LLM plan output into a list of structured steps.
        Prefer extracting steps from a JSON code block at the end of the LLM response, if present.
        Recognizes commands, file/folder creation, VS Code/terminal open, etc.
        Now robust to markdown, bullets, and code blocks.
        """
        import json
        import re
        # Prefer extracting from JSON code block if present
        json_match = re.search(r"```json\s*([\s\S]*?)\s*```", plan_text, re.MULTILINE)
        if json_match:
            json_str = json_match.group(1).strip()
            try:
                parsed_json = json.loads(json_str)
                if isinstance(parsed_json, list):
                    return parsed_json
                # Handle alternative format: { "files": [ { "file": ..., "code": ... }, ... ] }
                if isinstance(parsed_json, dict) and "files" in parsed_json and isinstance(parsed_json["files"], list):
                    steps = []
                    for file_entry in parsed_json["files"]:
                        if "file" in file_entry and "code" in file_entry:
                            steps.append({
                                "type": "file",
                                "file_path": file_entry["file"],
                                "content": file_entry["code"]
                            })
                    if steps:
                        return steps
            except Exception as e:
                # Fallback to line-by-line parsing if JSON is invalid
                pass
        # Fallback: legacy line-by-line parsing
        steps = []
        code_block = False
        code_lines = []
        # First, extract all code blocks (for commands)
        for line in plan_text.splitlines():
            if line.strip().startswith("```"):
                code_block = not code_block
                continue
            if code_block:
                code_lines.append(line.strip())
        # Parse commands from code blocks
        for line in code_lines:
            if not line:
                continue
            if line.startswith("ng ") or line.startswith("npm ") or line.startswith("mkdir ") or line.startswith("cd "):
                steps.append({"type": "command", "command": line})
        # Now parse the rest of the plan
        for line in plan_text.splitlines():
            orig_line = line
            line = line.strip()
            if not line:
                continue
            # Numbered step (1. ... or 1) ...)
            if re.match(r"^\d+\.|^\d+\)", line):
                step_text = re.split(r"\.|\)", line, 1)[1].strip()
            # Markdown bullet or dash
            elif line.startswith("- ") or line.startswith("* "):
                step_text = line[2:].strip()
            # Markdown numbered list (1. ...)
            elif re.match(r"^\d+\. ", line):
                step_text = line.split(". ", 1)[1].strip()
            else:
                step_text = line
            # Extract commands from step_text
            # Command step
            if step_text.lower().startswith('run') or step_text.lower().startswith('execute'):
                match = re.search(r'run (.+)', step_text, re.IGNORECASE)
                if match:
                    cmd = match.group(1)
                    steps.append({"type": "command", "command": cmd})
            elif 'ng new' in step_text:
                match = re.search(r'ng new[^"\n]*', step_text)
                cmd = match.group(0) if match else f"ng new {project_name} --routing --style=css --skip-git"
                steps.append({"type": "command", "command": cmd})
            elif 'npm install' in step_text:
                steps.append({"type": "command", "command": "npm install"})
            elif 'ng generate component' in step_text:
                match = re.search(r'ng generate component ([\w/-]+)', step_text)
                comp = match.group(1) if match else 'component'
                steps.append({"type": "command", "command": f"ng generate component {comp}"})
            elif 'mkdir' in step_text or 'create folder' in step_text or 'create directory' in step_text:
                match = re.search(r'(?:mkdir|create folder|create directory) ([^\s]+)', step_text)
                folder = match.group(1) if match else 'src'
                steps.append({"type": "folder", "path": folder})
            elif 'create' in step_text and 'file' in step_text:
                match = re.search(r'create (?:a |an |the )?(.*?) file(?: named| called|:| |$)', step_text.lower())
                file_name = match.group(1).strip() if match else None
                if file_name:
                    steps.append({"type": "file", "file_path": file_name, "content": ""})
            elif 'readme' in step_text:
                steps.append({"type": "file", "file_path": "README.md", "content": f"# {project_name}\nThis is a demo Angular app."})
            elif 'open vs code' in step_text or 'open vscode' in step_text:
                steps.append({"type": "open_vscode"})
            elif 'open terminal' in step_text:
                steps.append({"type": "open_terminal"})
            # Fallback: extract common commands
            elif step_text.startswith("ng ") or step_text.startswith("npm ") or step_text.startswith("mkdir ") or step_text.startswith("cd "):
                steps.append({"type": "command", "command": step_text})
        return steps

    async def _execute_step_inner(self, step: Dict[str, Any], project_name: str, project_dir: str, sio) -> Dict[str, Any]:
        step_type = step.get('type')
        try:
            if step_type == 'command':
                from terminal_integration import TerminalIntegration
                terminal = TerminalIntegration()
                cmd = step['command']
                result = await terminal.run_command(cmd, cwd=project_dir, project_name=project_name)
                output = result.get("output", "")
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Command]: {cmd}\nOutput:\n{output[:1000]}",
                    "file_path": None
                })
                return {"status": "success", "message": f"Ran command: {cmd}", "output": output}
            elif step_type == 'file':
                file_path = step['file_path']
                content = step.get('content', '')
                if not content:
                    file_prompt = f"Generate the full content for a {file_path} as part of this project."
                    content = await self.llm.generate(file_prompt)
                file_info = self.project_manager.add_file_to_project(project_name, file_path, content)
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[File Created]: {file_path}\n```\n{content[:1000]}\n```",
                    "file_path": file_path
                })
                # Emit file update event
                if emit_agent_file_update:
                    await emit_agent_file_update(project_name, file_path, content)
                else:
                    await sio.emit("agent_file_update", {
                        "project_name": project_name,
                        "file_path": file_path,
                        "content": content
                    })
                return {"status": "success", "message": f"Created file {file_path}", "file": file_info}
            elif step_type == 'folder':
                folder_path = os.path.join(project_dir, step['path'])
                os.makedirs(folder_path, exist_ok=True)
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Folder Created]: {step['path']}",
                    "file_path": None
                })
                return {"status": "success", "message": f"Created folder {step['path']}"}
            elif step_type == 'open_vscode':
                result = await self._handle_open_vscode({}, project_name, project_dir)
                if result["status"] == "success":
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": result["message"],
                        "file_path": None
                    })
                else:
                    code_server_url = "http://127.0.0.1:8081/?folder=/home/<USER>"
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Warning]: Could not open VS Code. You can use code-server instead: {code_server_url}",
                        "file_path": None
                    })
                return result
            elif step_type == 'open_terminal':
                try:
                    if os.name == 'nt':
                        os.system(f'start powershell')
                    else:
                        os.system(f'gnome-terminal &')
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": "[Terminal Opened]",
                        "file_path": None
                    })
                    return {"status": "success", "message": "Opened terminal"}
                except Exception as e:
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Error]: Could not open terminal: {e}",
                        "file_path": None
                    })
                    return {"status": "error", "message": str(e)}
            else:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Info]: Step type '{step_type}' not recognized for automated execution.",
                    "file_path": None
                })
                return {"status": "skipped", "message": f"Step type '{step_type}' not recognized."}
        except Exception as e:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Error]: Exception in step execution: {e}",
                "file_path": None
            })
            return {"status": "error", "message": str(e)}

    async def execute_step(self, step: Dict[str, Any], project_name: str, project_dir: str, sio=None) -> Dict[str, Any]:
        """
        Execute a step.
        """
        try:
            # 30 minute timeout per step
            result = await asyncio.wait_for(self._execute_step_inner(step, project_name, project_dir, sio), timeout=1800)
            
            # Update project memory if available
            if project_name in self.project_memories:
                memory = self.project_memories[project_name]
                
                if step.get('type') == 'command':
                    memory.add_command_executed(
                        step.get('command'), 
                        output=result.get('output'),
                        status=result.get('status')
                    )
                elif step.get('type') == 'file':
                    memory.add_file_created(step.get('file_path'))
                
                # Save memory after each significant step
                memory.save_to_file(project_dir)
            
            return result
            
        except asyncio.TimeoutError:
            if sio:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": "[Error]: Step timed out after 30 minutes.",
                    "file_path": None
                })
            
            # Record error in project memory
            if project_name in self.project_memories:
                self.project_memories[project_name].add_error(
                    "Step timed out after 30 minutes",
                    context=str(step)
                )
            
            return {"status": "error", "message": "Step timed out."}

    async def plan_and_execute(self, message: str, project_name: str, sio) -> Dict[str, Any]:
        """
        Plan, reason, research, and execute a multi-step task with real automation.
        """
        logger.info(f"Planning and executing for project {project_name}: {message}")
        project_dir = self.project_manager.get_project_dir(project_name)
        if not project_dir:
            logger.error(f"Project {project_name} not found")
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"Project {project_name} not found."
            })
            return {"status": "error", "message": f"Project {project_name} not found", "project_name": project_name}

        # Initialize or load project memory
        memory = ProjectMemory.load_from_file(project_dir)
        if not memory:
            # Extract key requirements from the user message
            req_extraction_prompt = (
                f"Extract the key requirements from this user request: '{message}'\n\n"
                f"Return them as a JSON array of concise requirement statements, each focusing on one feature or aspect."
            )
            req_response = await self.llm.generate(req_extraction_prompt)
            
            try:
                # Extract JSON array from response
                req_match = re.search(r'\[.*\]', req_response, re.DOTALL)
                if req_match:
                    requirements = json.loads(req_match.group(0))
                else:
                    requirements = json.loads(req_response)
            except:
                # Fallback: create simple requirements list
                requirements = [message]
            
            # Initialize with extracted requirements
            memory = ProjectMemory(project_name, requirements)
            
            # Initial requirement status setting
            for req in requirements:
                memory.update_requirement_status(req, "not_started")
        
        # Store in agent's memory dict
        self.project_memories[project_name] = memory
        
        # Save project memory at the start
        memory_path = memory.save_to_file(project_dir)
        
        # Log memory initialization
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Memory]: Initialized project memory tracking at {memory_path}"
        })

        # Ensure Angular project exists and get the Angular root dir
        angular_project_dir = await self.ensure_angular_project(project_name, project_dir, sio)
        project_dir = angular_project_dir  # Use this for all file operations

        # Step 1: Generate plan and reasoning
        plan_prompt = (
            f"You are an expert AI software agent. Given the following user request, "
            f"generate a step-by-step plan (as a numbered list) for how you would accomplish it, "
            f"including what files/folders to create, what research to do, and what tests to run.\n"
            f"Then, explain your reasoning for the plan.\n\n"
            f"User request: {message}"
        )
        plan_response = await self.llm.generate(plan_prompt)
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Plan & Reasoning]:\n{plan_response}"
        })
        
        # Store plan in project memory metadata
        memory.metadata["plan"] = plan_response
        memory.save_to_file(project_dir)

        # Step 2: Parse plan into structured steps
        steps = self.parse_plan_to_steps(plan_response, project_name)
        
        # Execute the steps if available
        if steps:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Execution]: Executing {len(steps)} steps from plan..."
            })
            
            # Execute each step in the plan
            for step in steps:
                try:
                    # Update memory to mark this step as in progress
                    step_desc = step.get('description', str(step))
                    relevant_req = self._find_relevant_requirement(step_desc, memory.requirements)
                    step_result = await self.execute_step(step, project_name, project_dir, sio)
                    if step_result.get("status") != "success":
                        await sio.emit("agent_message", {
                            "project_name": project_name,
                            "message": f"[Warning]: Step execution result: {step_result.get('message', 'Unknown error')}"
                        })
                except Exception as e:
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Error]: Failed to execute step: {str(e)}"
                    })
                    logger.error(f"Error executing step: {e}")
        else:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": "[Warning]: No executable steps found in plan. Generating implementation files..."
            })
        
        # If we didn't find any steps or we need to generate additional files
        # Use fallback_generate_files to create necessary implementation
        file_steps = await self.fallback_generate_files(message, project_name, plan_response, sio, angular_project_dir)
        
        # Execute the generated file steps
        if file_steps:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Implementation]: Creating {len(file_steps)} implementation files..."
            })
            
            for step in file_steps:
                try:
                    await self.execute_step(step, project_name, project_dir, sio)
                except Exception as e:
                    await sio.emit("agent_message", {
                        "project_name": project_name, 
                        "message": f"[Error]: Failed to create file {step.get('file_path', 'unknown')}: {str(e)}"
                    })
                    logger.error(f"Error creating file: {e}")
        
        # VERIFICATION PHASE: Check if the implementation matches the requirements
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": "[Verification]: Analyzing implementation completeness..."
        })
        
        # Perform architecture analysis to understand the current implementation
        architecture_analysis = await self.analyze_project_architecture(project_name, project_dir, sio)
        
        # 1. Get a list of all generated files in the project
        files = self.project_manager.get_project_files(project_name)
        file_list = [f["path"] for f in files]
        
        # 2. Create a verification prompt to check for completeness
        verification_prompt = (
            f"You are an expert Angular developer tasked with verifying a project implementation.\n\n"
            f"User requirement: '{message}'\n\n"
            f"Current implementation files:\n{json.dumps(file_list, indent=2)}\n\n"
            f"Analyze if this implementation is COMPLETE for the requirements. Identify:\n"
            f"1. Any MISSING components, services, models, or configuration files\n"
            f"2. Any MISSING features or functionality based on the requirements\n"
            f"3. Any MISSING resources like images, data files, etc.\n"
            f"4. Any files that need MODIFICATION to meet the requirements\n\n"
            f"Return your analysis as a JSON object with these properties:\n"
            f"- is_complete: boolean - whether the implementation is complete\n"
            f"- missing_files: array of objects with path and description fields\n"
            f"- files_to_modify: array of objects with path and changes_needed fields\n"
            f"- missing_resources: array of objects with resource_type, path and description fields\n"
            f"- missing_features: array of strings describing missing features\n\n"
            f"If the implementation is complete, return {{'is_complete': true}} with empty arrays for the other fields."
        )
        
        verification_response = await self.llm.generate(verification_prompt)
        
        # Parse verification response
        try:
            verification_match = re.search(r'\{.*\}', verification_response, re.DOTALL)
            if verification_match:
                verification_json = json.loads(verification_match.group(0))
            else:
                verification_json = json.loads(verification_response)
                
            # Handle verification results
            if verification_json.get('is_complete', False):
                # Mark all requirements as completed
                for req in memory.requirements:
                    # Convert requirement to string if it's not already a string
                    req_key = str(req) if not isinstance(req, str) else req
                    current_status = memory.requirements_status.get(req_key, {}).get('status')
                    if current_status != "completed":
                        memory.update_requirement_status(req, "completed", "Verified as complete by agent")
                
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": "[Verification]: Implementation is complete! ✅"
                })
            else:
                # Mark requirements as in_progress
                for req in memory.requirements:
                    # Convert requirement to string if it's not already a string
                    req_key = str(req) if not isinstance(req, str) else req
                    current_status = memory.requirements_status.get(req_key, {}).get('status')
                    if current_status != "completed":
                        memory.update_requirement_status(req, "in_progress", "Some parts may still be missing")
                
                # If incomplete, inform the user what's missing
                missing_files = verification_json.get('missing_files', [])
                files_to_modify = verification_json.get('files_to_modify', [])
                missing_features = verification_json.get('missing_features', [])
                
                if missing_files or files_to_modify or missing_features:
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": "[Verification]: Some parts of the implementation may be incomplete."
                    })
                    
                    if missing_files:
                        file_list = "\n".join([f"- {file['path']}: {file['description']}" for file in missing_files[:5]])
                        await sio.emit("agent_message", {
                            "project_name": project_name,
                            "message": f"[Missing Files]:\n{file_list}"
                        })
                    
                    if files_to_modify:
                        mod_list = "\n".join([f"- {file['path']}: {file['changes_needed']}" for file in files_to_modify[:5]])
                        await sio.emit("agent_message", {
                            "project_name": project_name,
                            "message": f"[Files Needing Changes]:\n{mod_list}"
                        })
                    
                    if missing_features:
                        feature_list = "\n".join([f"- {feature}" for feature in missing_features[:5]])
                        await sio.emit("agent_message", {
                            "project_name": project_name,
                            "message": f"[Missing Features]:\n{feature_list}"
                        })
        except Exception as e:
            logger.error(f"Error parsing verification response: {e}")
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Verification Error]: Could not parse verification response: {str(e)}"
            })
        
        # Run npm install if it was deferred from project creation
        if hasattr(self, 'pending_npm_install') and self.pending_npm_install:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": "[Finalization]: Installing npm dependencies after all files are created..."
            })
            
            from terminal_integration import TerminalIntegration
            terminal = TerminalIntegration()
            
            # Get the Angular project directory
            ng_proj_dir = self.ng_proj_dir if hasattr(self, 'ng_proj_dir') else project_dir
            
            # Run npm install
            cmd = "npm install"
            result = await terminal.run_command(cmd, cwd=ng_proj_dir, project_name=project_name)
            output = result.get("output", "")
            
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[npm install output]:\n{output[:1000] if output else 'No output'}"
            })
            
            # Try a build to verify everything works, with advanced auto-fix loop
            max_build_attempts = 30  # Increased from 3 to 30 attempts
            build_attempts = 0
            build_successful = False
            
            while not build_successful and build_attempts < max_build_attempts:
                build_attempts += 1
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Build]: Running ng build (attempt {build_attempts}/{max_build_attempts})..."
                })
                
                cmd = "ng build"
                try:
                    result = await terminal.run_command(cmd, cwd=ng_proj_dir, project_name=project_name)
                    # Safely get output, defaulting to empty string if None
                    output = result.get("output", "") or ""
                    error = result.get("error", "") or ""
                    combined_output = output + "\n" + error
                    
                    if result.get("exit_code", 1) == 0:
                        build_successful = True
                        await sio.emit("agent_message", {
                            "project_name": project_name,
                            "message": f"[Build]: Project built successfully! ✅"
                        })
                    else:
                        # Safely slice output, handling potential encoding issues
                        safe_output = combined_output[:2000] if combined_output else "No output available"
                        
                        await sio.emit("agent_message", {
                            "project_name": project_name,
                            "message": f"[Build Warning]: Build failed with issues (attempt {build_attempts}/{max_build_attempts}):\n{safe_output[:500]}..."
                        })
                        
                        # Use universal error fixing system for more advanced fixes
                        error_context = {
                            "framework": "angular",
                            "command": cmd,
                            "build_attempt": build_attempts
                        }
                        
                        fixed = await self.fix_code_errors(ng_proj_dir, project_name, combined_output, sio, error_context)
                        
                        # If universal fixer couldn't fix it, fall back to Angular-specific fixes
                        if not fixed:
                            # Attempt to auto-fix errors - use Deepseek model specifically for fixes
                            await sio.emit("agent_message", {
                                "project_name": project_name,
                                "message": f"[Auto-Fix]: Analyzing build errors and applying framework-specific fixes..."
                            })
                            
                            # Rest of the original Angular-specific fixing code
                            # ... (keep the existing Angular-specific code)
                except Exception as e:
                    logger.error(f"Error during ng build: {str(e)}")
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Build Error]: Build process error: {str(e)}\nAttempting to recover and continue..."
                    })
                    
                    # Try to recover from catastrophic errors
                    await self._recover_from_build_error(ng_proj_dir, project_name, str(e), sio)
                    
                # After a certain number of attempts, add extra npm install to resolve potential dependency issues
                if build_attempts % 5 == 0 and not build_successful:
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Auto-Fix]: Running npm install again to resolve potential dependency issues after multiple fix attempts..."
                    })
                    
                    try:
                        # Run npm ci (clean install) instead of npm install for more reliable dependency resolution
                        cmd = "npm ci"
                        await terminal.run_command(cmd, cwd=ng_proj_dir, project_name=project_name)
                    except:
                        # Fallback to regular npm install if npm ci fails
                        cmd = "npm install"
                        await terminal.run_command(cmd, cwd=ng_proj_dir, project_name=project_name)
            
            if not build_successful:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Build Warning]: Could not resolve all build issues after {max_build_attempts} attempts. Project may need manual fixes to build correctly."
                })
            
            # Reset the flag
            self.pending_npm_install = False
        
        # Generate documentation files
        await self.export_project_memory_documentation(project_name, project_dir, sio)
        
        # Final success message
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": "[Completion]: Task execution complete! 🎉"
        })
        
        return {
            'status': 'completed',
            'results': {},
            'plan': plan_response,
            'memory': memory.to_json()
        }

    async def execute_step_with_cwd(self, step: Dict[str, Any], project_name: str, cwd: str, sio=None) -> Dict[str, Any]:
        """
        Like execute_step, but allows specifying the working directory for command steps.
        """
        try:
            # 30 minute timeout per step
            return await asyncio.wait_for(self._execute_step_inner_with_cwd(step, project_name, cwd, sio), timeout=1800)
        except asyncio.TimeoutError:
            if sio:
                await sio.emit("agent_message", {"project_name": project_name, "message": f"[Complex Feature]: Implementation plan created."})
            
            return []
            
        except Exception as e:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Complex Feature Error]: Failed to process feature breakdown: {str(e)}"
            })
            
            # Fallback to simpler implementation approach
            return []

    async def generate_tests_for_project(self, project_name: str, project_dir: str, sio) -> List[Dict]:
        """
        Generate comprehensive test files for the project components, services, and modules.
        Creates unit tests, integration tests, and end-to-end tests based on project structure.
        
        Args:
            project_name: Name of the project
            project_dir: Project directory
            sio: Socket.io instance for emitting messages
            
        Returns:
            List of test file implementation steps
        """
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": "[Testing]: Analyzing project to generate comprehensive tests..."
        })
        
        # Ensure we have code analysis available
        if project_name not in self.code_analyzers:
            await self.analyze_project_architecture(project_name, project_dir, sio)
            
        analyzer = self.code_analyzers.get(project_name)
        if not analyzer:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": "[Testing Error]: Could not analyze project structure for test generation."
            })
            return []
        
        # Get all components, services and modules
        components = [info for file, info in analyzer.component_graph.items() if info.get('type') == 'component']
        services = [info for file, info in analyzer.component_graph.items() if info.get('type') == 'service']
        
        test_steps = []
        
        # 1. Generate unit tests for services
        for service_info in services:
            service_file = service_info.get('file_path')
            if not service_file:
                continue
                
            service_name = os.path.basename(service_file).replace('.service.ts', '')
            test_file_path = service_file.replace('.service.ts', '.service.spec.ts')
            
            # Get current service content
            service_content = self.project_manager.get_file_content(project_name, service_file) or ""
            
            # Generate test content
            test_prompt = (
                f"Generate a comprehensive Angular unit test file for the following service:\n\n"
                f"```typescript\n{service_content}\n```\n\n"
                f"The test file should:\n"
                f"1. Create proper TestBed setup with all dependencies mocked\n"
                f"2. Test all public methods in the service\n"
                f"3. Include tests for success and error cases\n"
                f"4. Follow Angular testing best practices\n"
                f"5. Use jasmine spies for dependencies\n\n"
                f"Return only the complete test file code with no explanations or markdown."
            )
            
            test_code = await self.llm.generate(test_prompt)
            test_code = re.sub(r'^```[\w]*\n|```$', '', test_code, flags=re.MULTILINE).strip()
            
            test_steps.append({
                "type": "file",
                "file_path": test_file_path,
                "content": test_code,
                "description": f"Unit tests for {service_name} service"
            })
            
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Testing]: Generated unit tests for {service_name} service"
            })
        
        # 2. Generate unit tests for components
        for component_info in components:
            component_file = component_info.get('file_path')
            if not component_file:
                continue
                
            component_name = os.path.basename(component_file).replace('.component.ts', '')
            test_file_path = component_file.replace('.component.ts', '.component.spec.ts')
            
            # Get component content
            component_content = self.project_manager.get_file_content(project_name, component_file) or ""
            
            # Get template content
            template_file = None
            if component_info.get('template'):
                template_path = os.path.join(os.path.dirname(component_file), component_info.get('template'))
                template_file = self.project_manager.get_file_content(project_name, template_path) or ""
            
            # Generate test content
            test_prompt = (
                f"Generate a comprehensive Angular unit test file for the following component:\n\n"
                f"Component TS:\n```typescript\n{component_content}\n```\n\n"
            )
            
            if template_file:
                test_prompt += f"Component Template:\n```html\n{template_file}\n```\n\n"
                
            test_prompt += (
                f"The test file should:\n"
                f"1. Create proper TestBed setup with all dependencies mocked\n"
                f"2. Test component initialization\n"
                f"3. Test all public methods\n"
                f"4. Test component interactions (inputs/outputs)\n"
                f"5. Include basic template tests\n"
                f"6. Follow Angular testing best practices\n\n"
                f"Return only the complete test file code with no explanations or markdown."
            )
            
            test_code = await self.llm.generate(test_prompt)
            test_code = re.sub(r'^```[\w]*\n|```$', '', test_code, flags=re.MULTILINE).strip()
            
            test_steps.append({
                "type": "file",
                "file_path": test_file_path,
                "content": test_code,
                "description": f"Unit tests for {component_name} component"
            })
            
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Testing]: Generated unit tests for {component_name} component"
            })
        
        # 3. Generate E2E tests for key workflows
        e2e_test_file_path = "cypress/e2e/app.cy.ts"
        
        # Get memory for requirements if available
        requirements = []
        if project_name in self.project_memories:
            memory = self.project_memories[project_name]
            requirements = memory.requirements
        
        # Create E2E test prompt
        e2e_prompt = (
            f"Generate Cypress E2E tests for an Angular application based on these requirements:\n\n"
        )
        
        if requirements:
            e2e_prompt += "Requirements:\n" + "\n".join([f"- {req}" for req in requirements]) + "\n\n"
        
        e2e_prompt += (
            f"The test file should:\n"
            f"1. Test main user flows and scenarios\n"
            f"2. Include navigation tests\n"
            f"3. Test form submissions if applicable\n"
            f"4. Test error states\n"
            f"5. Follow Cypress best practices\n\n"
            f"Return only the complete Cypress test file code with no explanations or markdown."
        )
        
        e2e_test_code = await self.llm.generate(e2e_prompt)
        e2e_test_code = re.sub(r'^```[\w]*\n|```$', '', e2e_test_code, flags=re.MULTILINE).strip()
        
        # Add cypress config if it doesn't exist
        cypress_config_path = "cypress.config.ts"
        cypress_config_exists = any(f["path"] == cypress_config_path for f in self.project_manager.get_project_files(project_name))
        
        if not cypress_config_exists:
            # Generate cypress config
            cypress_config = """
import { defineConfig } from 'cypress'

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:4200',
    supportFile: false
  },
})
"""
            test_steps.append({
                "type": "file",
                "file_path": cypress_config_path,
                "content": cypress_config,
                "description": "Cypress configuration file"
            })
            
            # Create necessary directories
            os.makedirs(os.path.join(project_dir, "cypress/e2e"), exist_ok=True)
        
        test_steps.append({
            "type": "file",
            "file_path": e2e_test_file_path,
            "content": e2e_test_code,
            "description": "E2E tests for main application workflows"
        })
        
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Testing]: Generated E2E tests for main application workflows"
        })
        
        # 4. Generate package.json updates if needed to include testing commands
        package_json_path = "package.json"
        package_json_content = self.project_manager.get_file_content(project_name, package_json_path) or "{}"
        
        try:
            package_data = json.loads(package_json_content)
            
            # Add testing scripts if needed
            scripts = package_data.get("scripts", {})
            
            if "test:coverage" not in scripts:
                scripts["test:coverage"] = "ng test --code-coverage"
                
            if "e2e" not in scripts and not cypress_config_exists:
                scripts["e2e"] = "cypress open"
                scripts["e2e:run"] = "cypress run"
                
            # Add cypress dependency if not present
            devDependencies = package_data.get("devDependencies", {})
            if "cypress" not in devDependencies and not cypress_config_exists:
                devDependencies["cypress"] = "^12.0.0"
                
            # Update package.json
            package_data["scripts"] = scripts
            package_data["devDependencies"] = devDependencies
            
            test_steps.append({
                "type": "file",
                "file_path": package_json_path,
                "content": json.dumps(package_data, indent=2),
                "description": "Updated package.json with testing commands"
            })
            
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Testing]: Updated package.json with testing commands"
            })
        except Exception as e:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Testing Warning]: Could not update package.json: {str(e)}"
            })
        
        # Return all test steps
        return test_steps
    
    async def analyze_project_architecture(self, project_name: str, project_dir: str, sio) -> Dict[str, Any]:
        """
        Perform deep analysis of the project architecture to understand component relationships,
        dependencies, and structure. This helps with implementing complex features.
        
        Args:
            project_name: Name of the project to analyze
            project_dir: Project directory
            sio: Socket.io instance for emitting messages
            
        Returns:
            Dictionary with analysis results
        """
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": "[Architecture Analysis]: Analyzing project structure and dependencies..."
        })
        
        # Create or get code analyzer for this project
        if project_name not in self.code_analyzers:
            self.code_analyzers[project_name] = CodeAnalyzer(project_dir)
            
        analyzer = self.code_analyzers[project_name]
        
        # Perform analysis
        try:
            analysis = analyzer.analyze_project_structure()
            
            # Generate a summary of the architecture
            architecture_summary = analyzer.generate_architecture_summary()
            
            # Save architecture summary to a file in the project
            arch_file_path = os.path.join(project_dir, "architecture.md")
            with open(arch_file_path, "w") as f:
                f.write(architecture_summary)
                
            # Save to project memory if available
            if project_name in self.project_memories:
                memory = self.project_memories[project_name]
                memory.metadata["architecture_analysis"] = {
                    "component_count": len([i for i in analyzer.component_graph.values() if i.get('type') == 'component']),
                    "service_count": len([i for i in analyzer.component_graph.values() if i.get('type') == 'service']),
                    "module_count": len([i for i in analyzer.component_graph.values() if i.get('type') == 'module']),
                    "api_endpoints": len(analyzer.api_endpoints),
                    "timestamp": time.time()
                }
                memory.save_to_file(project_dir)
            
            # Emit summary to user
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Architecture Analysis]: Analysis complete. Architecture summary saved to {arch_file_path}"
            })
            
            # Emit shortened version of summary
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Architecture Summary]:\n```\n{architecture_summary[:1000]}...\n```"
            })
            
            return {
                "status": "success",
                "architecture_file": arch_file_path,
                "component_count": len([i for i in analyzer.component_graph.values() if i.get('type') == 'component']),
                "service_count": len([i for i in analyzer.component_graph.values() if i.get('type') == 'service']),
                "module_count": len([i for i in analyzer.component_graph.values() if i.get('type') == 'module']),
                "api_endpoints": len(analyzer.api_endpoints)
            }
        except Exception as e:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Architecture Analysis Error]: {str(e)}"
            })
            
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def generate_complex_feature_implementation(self, project_name: str, project_dir: str, 
                                                   feature_description: str, architecture_analysis: Dict, sio) -> List[Dict]:
        """
        Generate implementation for a complex feature based on architecture analysis.
        This breaks down complex features into smaller, manageable components.
        
        Args:
            project_name: Name of the project
            project_dir: Project directory
            feature_description: Description of the feature to implement
            architecture_analysis: Results from architecture analysis
            sio: Socket.io instance for emitting messages
            
        Returns:
            List of implementation steps
        """
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Complex Feature]: Planning implementation for: {feature_description}"
        })
        
        # Get analyzer if available
        analyzer = self.code_analyzers.get(project_name)
        if not analyzer:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": "[Complex Feature]: No architecture analysis available. Running analysis first..."
            })
            
            analysis_result = await self.analyze_project_architecture(project_name, project_dir, sio)
            analyzer = self.code_analyzers.get(project_name)
            
            if not analyzer or analysis_result.get("status") != "success":
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": "[Complex Feature Error]: Could not analyze project architecture."
                })
                return []
        
        # Generate a dependency breakdown for the feature
        breakdown_prompt = (
            f"Based on the following project architecture and feature request, break down the implementation into smaller, "
            f"manageable components and identify dependencies between them.\n\n"
            f"Project Architecture:\n"
            f"- Components: {architecture_analysis.get('component_count', 'unknown')}\n"
            f"- Services: {architecture_analysis.get('service_count', 'unknown')}\n"
            f"- Modules: {architecture_analysis.get('module_count', 'unknown')}\n"
            f"- API Endpoints: {architecture_analysis.get('api_endpoints', 'unknown')}\n\n"
            f"Feature Request: {feature_description}\n\n"
            f"Break this down into:\n"
            f"1. Data models/interfaces needed\n"
            f"2. Services needed (with methods)\n"
            f"3. Components needed (with responsibilities)\n"
            f"4. API endpoints to implement\n"
            f"5. Implementation order (dependency graph)\n\n"
            f"Return as a structured JSON object with these 5 sections."
        )
        
        breakdown_response = await self.llm.generate(breakdown_prompt)
        
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', breakdown_response, re.DOTALL)
            if json_match:
                breakdown = json.loads(json_match.group(0))
            else:
                breakdown = json.loads(breakdown_response)
                
            # Save to memory
            if project_name in self.project_memories:
                memory = self.project_memories[project_name]
                memory.metadata.setdefault("complex_features", {})[feature_description] = {
                    "breakdown": breakdown,
                    "timestamp": time.time()
                }
                memory.save_to_file(project_dir)
            
            # Convert breakdown to implementation steps
            [] = []
            
            # 1. First data models
            if "data_models" in breakdown:
                for model in breakdown["data_models"]:
                    model_name = model.get("name", "")
                    model_path = model.get("path", f"src/app/models/{model_name.lower()}.model.ts")
                    model_properties = model.get("properties", [])
                    
                    # Create prompt for generating model
                    model_prompt = (
                        f"Generate TypeScript interface or class for {model_name} with these properties:\n"
                        f"{json.dumps(model_properties, indent=2)}\n\n"
                        f"Make sure to include proper typing, optional fields, and any necessary imports.\n"
                        f"Return only the complete code with no explanations or markdown."
                    )
                    
                    model_code = await self.llm.generate(model_prompt)
                    model_code = re.sub(r'^```[\w]*\n|```$', '', model_code, flags=re.MULTILINE).strip()
                    
                    [].append({
                        "type": "file",
                        "file_path": model_path,
                        "content": model_code,
                        "description": f"Data model for {model_name}"
                    })
            
            # 2. Then services
            if "services" in breakdown:
                for service in breakdown["services"]:
                    service_name = service.get("name", "")
                    service_path = service.get("path", f"src/app/services/{service_name.lower()}.service.ts")
                    service_methods = service.get("methods", [])
                    dependencies = service.get("dependencies", [])
                    
                    # Create prompt for generating service
                    service_prompt = (
                        f"Generate Angular service {service_name} with these methods:\n"
                        f"{json.dumps(service_methods, indent=2)}\n\n"
                        f"Dependencies: {', '.join(dependencies)}\n\n"
                        f"Ensure proper Angular injectable decorator, imports, and HTTP client usage if needed.\n"
                        f"Return only the complete code with no explanations or markdown."
                    )
                    
                    service_code = await self.llm.generate(service_prompt)
                    service_code = re.sub(r'^```[\w]*\n|```$', '', service_code, flags=re.MULTILINE).strip()
                    
                    [].append({
                        "type": "file",
                        "file_path": service_path,
                        "content": service_code,
                        "description": f"Service for {service_name}"
                    })
            
            # 3. Then components
            if "components" in breakdown:
                for component in breakdown["components"]:
                    component_name = component.get("name", "")
                    base_path = f"src/app/components/{component_name.lower()}/{component_name.lower()}"
                    ts_path = f"{base_path}.component.ts"
                    html_path = f"{base_path}.component.html"
                    css_path = f"{base_path}.component.scss"
                    responsibilities = component.get("responsibilities", [])
                    dependencies = component.get("dependencies", [])
                    
                    # Create prompts for generating component files
                    ts_prompt = (
                        f"Generate Angular component TypeScript file for {component_name} with these responsibilities:\n"
                        f"{json.dumps(responsibilities, indent=2)}\n\n"
                        f"Dependencies: {', '.join(dependencies)}\n\n"
                        f"Ensure proper Angular component decorator, imports, and lifecycle hooks.\n"
                        f"Return only the complete code with no explanations or markdown."
                    )
                    
                    html_prompt = (
                        f"Generate HTML template for Angular component {component_name} with these responsibilities:\n"
                        f"{json.dumps(responsibilities, indent=2)}\n\n"
                        f"Include responsive layout, form elements if needed, and structural directives.\n"
                        f"Return only the complete code with no explanations or markdown."
                    )
                    
                    css_prompt = (
                        f"Generate SCSS styles for Angular component {component_name} that implement:\n"
                        f"{json.dumps(responsibilities, indent=2)}\n\n"
                        f"Use modern CSS practices, variables, and responsive design.\n"
                        f"Return only the complete code with no explanations or markdown."
                    )
                    
                    # Generate and clean code
                    ts_code = await self.llm.generate(ts_prompt)
                    ts_code = re.sub(r'^```[\w]*\n|```$', '', ts_code, flags=re.MULTILINE).strip()
                    
                    html_code = await self.llm.generate(html_prompt)
                    html_code = re.sub(r'^```[\w]*\n|```$', '', html_code, flags=re.MULTILINE).strip()
                    
                    css_code = await self.llm.generate(css_prompt)
                    css_code = re.sub(r'^```[\w]*\n|```$', '', css_code, flags=re.MULTILINE).strip()
                    
                    # Add implementation steps
                    [].append({
                        "type": "file",
                        "file_path": ts_path,
                        "content": ts_code,
                        "description": f"TypeScript for {component_name} component"
                    })
                    
                    [].append({
                        "type": "file",
                        "file_path": html_path,
                        "content": html_code,
                        "description": f"HTML template for {component_name} component"
                    })
                    
                    [].append({
                        "type": "file",
                        "file_path": css_path,
                        "content": css_code,
                        "description": f"SCSS styles for {component_name} component"
                    })
            
            # 4. Update routing if needed
            if "routing" in breakdown:
                routing_updates = breakdown.get("routing", [])
                if routing_updates:
                    # Find the routing module
                    routing_path = "src/app/app-routing.module.ts"
                    
                    # If analyzer found the routing file path, use that instead
                    if analyzer:
                        for file_path in analyzer.dependency_graph:
                            if "routing" in file_path.lower() and file_path.endswith(".ts"):
                                routing_path = file_path
                                break
                    
                    # Get current routing content
                    current_routing = self.project_manager.get_file_content(project_name, routing_path) or ""
                    
                    # Generate updated routing
                    routing_prompt = (
                        f"Update the Angular routing module with these new routes:\n"
                        f"{json.dumps(routing_updates, indent=2)}\n\n"
                        f"Current routing module content:\n```\n{current_routing}\n```\n\n"
                        f"Return the complete updated routing module with properly added routes and necessary imports.\n"
                        f"Return only the complete code with no explanations or markdown."
                    )
                    
                    updated_routing = await self.llm.generate(routing_prompt)
                    updated_routing = re.sub(r'^```[\w]*\n|```$', '', updated_routing, flags=re.MULTILINE).strip()
                    
                    [].append({
                        "type": "file",
                        "file_path": routing_path,
                        "content": updated_routing,
                        "description": "Updated routing module with new routes"
                    })
            
            # 5. Update module declarations if needed
            if "module_updates" in breakdown:
                module_updates = breakdown.get("module_updates", {})
                for module_path, updates in module_updates.items():
                    # Get current module content
                    current_module = self.project_manager.get_file_content(project_name, module_path) or ""
                    
                    # Generate updated module
                    module_prompt = (
                        f"Update the Angular module with these changes:\n"
                        f"{json.dumps(updates, indent=2)}\n\n"
                        f"Current module content:\n```\n{current_module}\n```\n\n"
                        f"Return the complete updated module with properly added declarations, imports, and providers.\n"
                        f"Return only the complete code with no explanations or markdown."
                    )
                    
                    updated_module = await self.llm.generate(module_prompt)
                    updated_module = re.sub(r'^```[\w]*\n|```$', '', updated_module, flags=re.MULTILINE).strip()
                    
                    [].append({
                        "type": "file",
                        "file_path": module_path,
                        "content": updated_module,
                        "description": f"Updated module with new declarations and imports"
                    })
            
            # Emit summary of planned implementation
            await sio.emit("agent_message", {"project_name": project_name, "message": f"[Complex Feature]: Implementation plan created."})
            
            return []
            
        except Exception as e:
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Complex Feature Error]: Failed to process feature breakdown: {str(e)}"
            })
            
            # Fallback to simpler implementation approach
            return []

    async def _analyze_build_errors(self, build_output: str) -> Dict[str, Any]:
        """
        Analyze Angular build errors and categorize them by type and severity.
        
        Args:
            build_output: The output from the ng build command
            
        Returns:
            Dictionary with categorized errors
        """
        error_types = {
            'syntax': [],
            'dependency': [],
            'type': [],
            'module': [],
            'other': []
        }
        
        # Common Angular error patterns
        syntax_patterns = [
            r'Unexpected token',
            r'Expected .+ but found',
            r'Cannot find the closing bracket',
            r'Unterminated string literal',
            r'Declaration or statement expected',
            r'Unexpected end of input',
            r'Unexpected character',
            r'Unexpected token \[.*\]. Expected',
            r'Unexpected character \[.*\]. Expected',
            r'Missing semicolon',
            r'Property .+ does not exist'
        ]
        
        dependency_patterns = [
            r"Cannot find module '([^']+)'",
            r"Cannot resolve module '([^']+)'",
            r"Module not found: Error: Can't resolve '([^']+)'",
            r'Cannot find package:',
            r'npm ERR!',
            r'Failed to resolve dependencies',
            r'Package .+ is not installed'
        ]
        
        type_patterns = [
            r'Type .+ is not assignable to type',
            r'Property .+ does not exist on type',
            r'Argument of type .+ is not assignable',
            r'Object is possibly \'null\'',
            r'Object is possibly \'undefined\'',
            r'Cannot read property .+ of (null|undefined)',
            r'Type .+ has no property .+ and no string index',
            r"Property '.*' is missing in type"
        ]
        
        module_patterns = [
            r'No directive found with exportAs',
            r'The pipe .+ could not be found',
            r'Component .+ is not part of any NgModule',
            r'is not a known element',
            r'No provider for',
            r'is not a known property of',
            r'Can\'t bind to',
            r"'.*' is not a known element"
        ]
        
        # Extract file paths with line numbers
        file_pattern = r'(?:^|\s)((?:[A-Za-z]:)?[^:]+\.[a-zA-Z]+)(?::(\d+)(?::(\d+))?)'
        file_errors = []
        
        # Process line by line
        lines = build_output.splitlines()
        for i, line in enumerate(lines):
            if any(re.search(pattern, line, re.IGNORECASE) for pattern in syntax_patterns):
                error_types['syntax'].append(line)
            elif any(re.search(pattern, line, re.IGNORECASE) for pattern in dependency_patterns):
                error_types['dependency'].append(line)
            elif any(re.search(pattern, line, re.IGNORECASE) for pattern in type_patterns):
                error_types['type'].append(line)
            elif any(re.search(pattern, line, re.IGNORECASE) for pattern in module_patterns):
                error_types['module'].append(line)
            elif 'error' in line.lower() or 'exception' in line.lower():
                error_types['other'].append(line)
            
            # Find files referenced in errors
            file_matches = re.findall(file_pattern, line)
            for match in file_matches:
                file_path = match[0]
                line_num = int(match[1]) if match[1] else None
                col_num = int(match[2]) if len(match) > 2 and match[2] else None
                
                # Get context (error message)
                error_context = line
                if i > 0:
                    error_context = lines[i-1] + "\n" + error_context
                if i < len(lines) - 1:
                    error_context += "\n" + lines[i+1]
                
                file_errors.append({
                    'file': file_path,
                    'line': line_num,
                    'column': col_num,
                    'context': error_context
                })
        
        return {
            'categorized': error_types,
            'file_errors': file_errors,
            'all_errors': [line for category in error_types.values() for line in category]
        }
    
    def _extract_files_from_build_output(self, build_output: str) -> List[str]:
        """
        Extract all file paths mentioned in build output.
        
        Args:
            build_output: The build output to analyze
            
        Returns:
            List of file paths
        """
        import re
        
        # Patterns to extract file paths
        patterns = [
            r'(?:^|\s)((?:[A-Za-z]:)?[^:]+\.[a-zA-Z]+)(?::\d+)?',  # file.ts:123
            r'(?:in|from|of|at) [\'"]?([^\'"\s:]+\.[a-zA-Z]+)[\'"]?',  # in/from "file.ts"
            r'Error in [\'"]?([^\'"\s:]+\.[a-zA-Z]+)[\'"]?'  # Error in file.ts
        ]
        
        matches = []
        for pattern in patterns:
            matches.extend(re.findall(pattern, build_output))
        
        # Filter out common false positives and normalize paths
        filtered = []
        for match in matches:
            # Skip obvious non-files
            if (not match.endswith(('.ts', '.js', '.html', '.css', '.json', '.scss', '.less')) or
                match in ['null', 'undefined'] or
                match.startswith(('http://', 'https://', 'ws://', 'wss://'))):
                continue
                
            filtered.append(match)
        
        # Return unique file paths
        return list(set(filtered))

    async def _fix_common_angular_errors(self, error_type: str, errors: List[str], project_dir: str, project_name: str, llm, sio) -> bool:
        """
        Apply fixes for common Angular error types based on patterns.
        
        Args:
            error_type: Type of error (module, dependency, type, syntax)
            errors: List of error messages
            project_dir: Project directory
            project_name: Project name
            llm: LLM instance to use for generating fixes
            sio: Socket.io instance for emitting messages
            
        Returns:
            True if fixes were applied, False otherwise
        """
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Auto-Fix]: Attempting to fix common {error_type} errors ({len(errors)} found)..."
        })
        
        if error_type == "dependency":
            # Check for missing packages and install them
            import re
            
            # Common patterns for missing packages
            missing_package_patterns = [
                r"Cannot find module '([^']+)'",
                r"Module '([^']+)' has no exported member",
                r"Module not found: Error: Can't resolve '([^']+)'"
            ]
            
            packages_to_install = set()
            for error in errors:
                for pattern in missing_package_patterns:
                    matches = re.findall(pattern, error)
                    for match in matches:
                        # Clean up package name to extract the actual npm package
                        if match.startswith('@'):
                            # Handle scoped packages
                            parts = match.split('/')
                            if len(parts) > 1:
                                package = f"{parts[0]}/{parts[1]}"
                            else:
                                package = match
                        else:
                            # Extract package name (first part before any /)
                            package = match.split('/')[0]
                        
                        # Skip Angular core packages and relative imports
                        if (package.startswith('.') or
                            package in ['src', 'app', 'assets', 'environments'] or
                            package.startswith('data:')):
                            continue
                            
                        packages_to_install.add(package)
            
            if packages_to_install:
                # Install missing packages
                packages_str = ' '.join(packages_to_install)
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Auto-Fix]: Installing missing packages: {packages_str}"
                })
                
                from terminal_integration import TerminalIntegration
                terminal = TerminalIntegration()
                
                try:
                    cmd = f"npm install --save {packages_str}"
                    await terminal.run_command(cmd, cwd=project_dir, project_name=project_name)
                    return True
                except Exception as e:
                    logger.error(f"Error installing packages: {e}")
            
            return False
            
        elif error_type == "module":
            # Check for module declaration issues
            # Look for app.module.ts and update it
            app_module_path = os.path.join(project_dir, 'src', 'app', 'app.module.ts')
            if os.path.exists(app_module_path):
                try:
                    with open(app_module_path, 'r', encoding='utf-8', errors='replace') as f:
                        module_content = f.read()
                    
                    # Use LLM to fix module declaration issues
                    prompt = f"""
                    You are fixing Angular module declaration issues. The application has the following errors:
                    
                    ```
                    {chr(10).join(errors[:10])}
                    ```
                    
                    Here is the current app.module.ts content:
                    
                    ```typescript
                    {module_content}
                    ```
                    
                    Fix any module-related issues including:
                    1. Missing declarations of components
                    2. Missing imports of modules
                    3. Missing providers
                    4. Circular dependencies
                    5. Incorrect module configuration
                    
                    Return ONLY the complete, fixed app.module.ts file content.
                    """
                    
                    fixed_content = await llm.generate(prompt)
                    
                    # Clean up output to get just the code
                    import re
                    code_match = re.search(r'```(?:typescript)?\s*(.*?)```', fixed_content, re.DOTALL)
                    if code_match:
                        fixed_content = code_match.group(1).strip()
                    
                    # Write fixed content back
                    with open(app_module_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_content)
                    
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Auto-Fix]: Updated app.module.ts to fix module declaration issues."
                    })
                    
                    return True
                except Exception as e:
                    logger.error(f"Error fixing module issues: {e}")
            
            return False
            
        elif error_type == "type":
            # Check for missing interfaces or incorrect types
            # This requires more in-depth analysis specific to the affected components
            affected_files = set()
            
            # Extract file paths from error messages
            import re
            file_pattern = r'(?:^|\s)((?:[A-Za-z]:)?[^:]+\.[a-zA-Z]+)(?::\d+)(?::\d+)?'
            
            for error in errors:
                matches = re.findall(file_pattern, error)
                affected_files.update(matches)
            
            if affected_files:
                fixed_files = []
                
                for file_path in affected_files:
                    # Handle absolute paths
                    if os.path.isabs(file_path):
                        try:
                            rel_path = os.path.relpath(file_path, project_dir)
                            file_path = rel_path
                        except:
                            continue
                    
                    abs_file_path = os.path.join(project_dir, file_path)
                    
                    if not os.path.exists(abs_file_path):
                        continue
                    
                    try:
                        with open(abs_file_path, 'r', encoding='utf-8', errors='replace') as f:
                            file_content = f.read()
                        
                        # Filter errors specific to this file
                        file_errors = [e for e in errors if file_path in e]
                        
                        # Use LLM to fix type issues
                        prompt = f"""
                        You are fixing TypeScript type issues in an Angular project. The file has the following type errors:
                        
                        ```
                        {chr(10).join(file_errors[:5])}
                        ```
                        
                        Here is the current file content:
                        
                        ```typescript
                        {file_content}
                        ```
                        
                        Fix any type-related issues including:
                        1. Missing type definitions
                        2. Incompatible types
                        3. Null/undefined handling
                        4. Type assertions where needed
                        5. Adding proper interfaces
                        
                        Return ONLY the complete, fixed file content.
                        """
                        
                        fixed_content = await llm.generate(prompt)
                        
                        # Clean up output to get just the code
                        import re
                        code_match = re.search(r'```(?:typescript)?\s*(.*?)```', fixed_content, re.DOTALL)
                        if code_match:
                            fixed_content = code_match.group(1).strip()
                        else:
                            fixed_content = fixed_content.strip()
                        
                        # Check if content has actually changed
                        if fixed_content != file_content:
                            # Write fixed content back
                            with open(abs_file_path, 'w', encoding='utf-8') as f:
                                f.write(fixed_content)
                            
                            fixed_files.append(file_path)
                    except Exception as e:
                        logger.error(f"Error fixing type issues in {file_path}: {e}")
                
                if fixed_files:
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Auto-Fix]: Fixed type issues in {len(fixed_files)} files: {', '.join(fixed_files[:3])}{' and more' if len(fixed_files) > 3 else ''}"
                    })
                    return True
            
            return False
            
        elif error_type == "syntax":
            # Fix syntax errors in affected files
            affected_files = set()
            
            # Extract file paths from error messages
            import re
            file_pattern = r'(?:^|\s)((?:[A-Za-z]:)?[^:]+\.[a-zA-Z]+)(?::\d+)(?::\d+)?'
            
            for error in errors:
                matches = re.findall(file_pattern, error)
                affected_files.update(matches)
            
            if affected_files:
                fixed_files = []
                
                for file_path in affected_files:
                    # Handle absolute paths
                    if os.path.isabs(file_path):
                        try:
                            rel_path = os.path.relpath(file_path, project_dir)
                            file_path = rel_path
                        except:
                            continue
                    
                    abs_file_path = os.path.join(project_dir, file_path)
                    
                    if not os.path.exists(abs_file_path):
                        continue
                    
                    try:
                        with open(abs_file_path, 'r', encoding='utf-8', errors='replace') as f:
                            file_content = f.read()
                        
                        # Filter errors specific to this file
                        file_errors = [e for e in errors if file_path in e]
                        
                        # Use LLM to fix syntax issues
                        prompt = f"""
                        You are fixing syntax errors in TypeScript/JavaScript code. The file has the following syntax errors:
                        
                        ```
                        {chr(10).join(file_errors[:5])}
                        ```
                        
                        Here is the current file content:
                        
                        ```typescript
                        {file_content}
                        ```
                        
                        Fix all syntax errors and ensure the code is valid TypeScript/JavaScript.
                        Return ONLY the complete, fixed file content without any additional comments or explanations.
                        """
                        
                        fixed_content = await llm.generate(prompt)
                        
                        # Clean up output to get just the code
                        import re
                        code_match = re.search(r'```(?:typescript|javascript)?\s*(.*?)```', fixed_content, re.DOTALL)
                        if code_match:
                            fixed_content = code_match.group(1).strip()
                        else:
                            fixed_content = fixed_content.strip()
                        
                        # Check if content has actually changed
                        if fixed_content != file_content:
                            # Write fixed content back
                            with open(abs_file_path, 'w', encoding='utf-8') as f:
                                f.write(fixed_content)
                            
                            fixed_files.append(file_path)
                    except Exception as e:
                        logger.error(f"Error fixing syntax issues in {file_path}: {e}")
                
                if fixed_files:
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Auto-Fix]: Fixed syntax issues in {len(fixed_files)} files: {', '.join(fixed_files[:3])}{' and more' if len(fixed_files) > 3 else ''}"
                    })
                    return True
            
            return False
        
        return False
    
    async def _fix_common_angular_project_issues(self, project_dir: str, project_name: str, sio) -> bool:
        """
        Fix common Angular project configuration issues.
        
        Args:
            project_dir: Project directory path
            project_name: Project name
            sio: Socket.io instance for emitting messages
            
        Returns:
            True if fixes were applied, False otherwise
        """
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Auto-Fix]: Checking and fixing common Angular project configuration issues..."
        })
        
        fixes_applied = False
        
        # Check angular.json
        angular_json_path = os.path.join(project_dir, 'angular.json')
        if os.path.exists(angular_json_path):
            try:
                import json
                with open(angular_json_path, 'r', encoding='utf-8', errors='replace') as f:
                    angular_config = json.load(f)
                
                # Fix project name if it doesn't match (common issue)
                if project_name not in angular_config.get('projects', {}):
                    # Try to find the actual project name
                    projects = list(angular_config.get('projects', {}).keys())
                    if projects:
                        actual_project_name = projects[0]
                        
                        # Update the project name
                        angular_config['projects'][project_name] = angular_config['projects'][actual_project_name]
                        del angular_config['projects'][actual_project_name]
                        
                        # Update default project if needed
                        if angular_config.get('defaultProject') == actual_project_name:
                            angular_config['defaultProject'] = project_name
                        
                        # Save updated config
                        with open(angular_json_path, 'w', encoding='utf-8') as f:
                            json.dump(angular_config, f, indent=2)
                        
                        await sio.emit("agent_message", {
                            "project_name": project_name,
                            "message": f"[Auto-Fix]: Updated project name in angular.json from '{actual_project_name}' to '{project_name}'"
                        })
                        
                        fixes_applied = True
            except Exception as e:
                logger.error(f"Error fixing angular.json: {e}")
        
        # Check package.json
        package_json_path = os.path.join(project_dir, 'package.json')
        if os.path.exists(package_json_path):
            try:
                import json
                with open(package_json_path, 'r', encoding='utf-8', errors='replace') as f:
                    package_json = json.load(f)
                
                # Check for common missing packages
                common_dependencies = {
                    "@angular/animations": "~13.2.0",
                    "@angular/common": "~13.2.0",
                    "@angular/compiler": "~13.2.0",
                    "@angular/core": "~13.2.0",
                    "@angular/forms": "~13.2.0",
                    "@angular/platform-browser": "~13.2.0",
                    "@angular/platform-browser-dynamic": "~13.2.0",
                    "@angular/router": "~13.2.0",
                    "rxjs": "~7.5.0",
                    "tslib": "^2.3.0",
                    "zone.js": "~0.11.4"
                }
                
                packages_added = []
                dependencies = package_json.get('dependencies', {})
                
                for pkg, version in common_dependencies.items():
                    if pkg not in dependencies:
                        dependencies[pkg] = version
                        packages_added.append(pkg)
                
                if packages_added:
                    package_json['dependencies'] = dependencies
                    
                    # Save updated package.json
                    with open(package_json_path, 'w', encoding='utf-8') as f:
                        json.dump(package_json, f, indent=2)
                    
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Auto-Fix]: Added missing dependencies to package.json: {', '.join(packages_added[:5])}{' and more' if len(packages_added) > 5 else ''}"
                    })
                    
                    fixes_applied = True
                    
                    # Run npm install to install the new dependencies
                    from terminal_integration import TerminalIntegration
                    terminal = TerminalIntegration()
                    cmd = "npm install"
                    await terminal.run_command(cmd, cwd=project_dir, project_name=project_name)
            except Exception as e:
                logger.error(f"Error fixing package.json: {e}")
        
        # Check tsconfig.json
        tsconfig_path = os.path.join(project_dir, 'tsconfig.json')
        if os.path.exists(tsconfig_path):
            try:
                import json
                with open(tsconfig_path, 'r', encoding='utf-8', errors='replace') as f:
                    tsconfig = json.load(f)
                
                # Fix common tsconfig issues
                compiler_options = tsconfig.get('compilerOptions', {})
                
                # Enable strict type checking if needed
                if not compiler_options.get('strict'):
                    compiler_options['strict'] = True
                    fixes_applied = True
                
                # Make sure we have basic compiler options
                tsconfig['compilerOptions'] = compiler_options
                
                # Save updated tsconfig
                with open(tsconfig_path, 'w', encoding='utf-8') as f:
                    json.dump(tsconfig, f, indent=2)
                
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Auto-Fix]: Updated tsconfig.json with optimized compiler options"
                })
            except Exception as e:
                logger.error(f"Error fixing tsconfig.json: {e}")
        
        return fixes_applied
    
    async def _recover_from_build_error(self, project_dir: str, project_name: str, error_message: str, sio) -> bool:
        """
        Attempt to recover from catastrophic build errors.
        
        Args:
            project_dir: Project directory path
            project_name: Project name
            error_message: The error message from the build
            sio: Socket.io instance for emitting messages
            
        Returns:
            True if recovery was successful, False otherwise
        """
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Auto-Fix]: Attempting to recover from build failure..."
        })
        
        # Try universal error fixing first for any type of error
        try:
            # Determine the framework based on project files
            framework = "unknown"
            if os.path.exists(os.path.join(project_dir, 'angular.json')):
                framework = "angular"
            elif os.path.exists(os.path.join(project_dir, 'package.json')):
                # Check if it's React or generic JS project
                with open(os.path.join(project_dir, 'package.json'), 'r', encoding='utf-8', errors='replace') as f:
                    package_json = f.read()
                    if '"react"' in package_json:
                        framework = "react"
                    elif '"vue"' in package_json:
                        framework = "vue"
                    else:
                        framework = "javascript"
            elif os.path.exists(os.path.join(project_dir, 'manage.py')):
                framework = "django"
            elif os.path.exists(os.path.join(project_dir, 'pom.xml')):
                framework = "spring"
            
            # Try universal error fixing
            error_context = {
                "framework": framework,
                "error_type": "build",
                "recovery_attempt": True
            }
            
            fixed = await self.fix_code_errors(project_dir, project_name, error_message, sio, error_context)
            if fixed:
                return True
        except Exception as e:
            logger.error(f"Error in universal error fixing: {e}")
        
        # Continue with specific error recovery approaches for common catastrophic errors
        # ... (keep the rest of the existing catastrophic error handling code)

    async def _attempt_critical_file_fixes(self, project_dir: str, project_name: str, error_output: str, sio) -> bool:
        """
        Attempt to fix critical configuration files based on build errors.
        
        Args:
            project_dir: Project directory path
            project_name: Project name
            error_output: Build error output
            sio: Socket.io instance for emitting messages
            
        Returns:
            True if fixes were applied, False otherwise
        """
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Auto-Fix]: Checking critical configuration files..."
        })
        
        # Check for specific error patterns and fix corresponding files
        critical_files = {
            'angular.json': os.path.join(project_dir, 'angular.json'),
            'package.json': os.path.join(project_dir, 'package.json'),
            'tsconfig.json': os.path.join(project_dir, 'tsconfig.json'),
            'tsconfig.app.json': os.path.join(project_dir, 'tsconfig.app.json'),
            'tsconfig.spec.json': os.path.join(project_dir, 'tsconfig.spec.json')
        }
        
        # LLM for creating or fixing config files
        llm = LLM.create("deepseek/deepseek-coder")
        
        # Check if we need to generate any missing critical files
        for file_name, file_path in critical_files.items():
            if not os.path.exists(file_path):
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Auto-Fix]: Critical file {file_name} is missing. Generating it..."
                })
                
                # Generate the missing file using LLM
                prompt = f"""
                Generate a standard {file_name} file for an Angular project named '{project_name}'.
                The file should contain all necessary configuration for a modern Angular application.
                Return ONLY the complete file content without any additional comments or explanations.
                """
                
                try:
                    file_content = await llm.generate(prompt)
                    
                    # Clean up output to get just the content
                    import re
                    content_match = re.search(r'```(?:json)?\s*(.*?)```', file_content, re.DOTALL)
                    if content_match:
                        file_content = content_match.group(1).strip()
                    
                    # Write the generated file
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(file_content)
                    
                    return True
                except Exception as e:
                    logger.error(f"Error generating {file_name}: {e}")
        
        # Check for errors with polyfills.ts
        if "Can't resolve './polyfills" in error_output or "polyfills" in error_output:
            polyfills_path = os.path.join(project_dir, 'src', 'polyfills.ts')
            if not os.path.exists(polyfills_path):
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Auto-Fix]: Generating missing polyfills.ts file..."
                })
                
                # Create standard polyfills.ts
                polyfills_content = """/**
 * This file includes polyfills needed by Angular and is loaded before the app.
 * You can add your own extra polyfills to this file.
 */

/***************************************************************************************************
 * BROWSER POLYFILLS
 */

/**
 * By default, zone.js will patch all possible macroTask and DomEvents
 * user can disable parts of macroTask/DomEvents patch by setting following flags
 */

// (window as any).__Zone_disable_requestAnimationFrame = true; // disable patch requestAnimationFrame
// (window as any).__Zone_disable_on_property = true; // disable patch onProperty such as onclick
// (window as any).__zone_symbol__BLACK_LISTED_EVENTS = ['scroll', 'mousemove']; // disable patch specified eventNames

/*
 * in IE/Edge developer tools, the addEventListener will also be wrapped by zone.js
 * with the following flag, it will bypass `zone.js` patch for IE/Edge
 */
// (window as any).__Zone_enable_cross_context_check = true;

/***************************************************************************************************
 * Zone JS is required by default for Angular itself.
 */
import 'zone.js';  // Included with Angular CLI.

/***************************************************************************************************
 * APPLICATION IMPORTS
 */
"""
                
                os.makedirs(os.path.dirname(polyfills_path), exist_ok=True)
                with open(polyfills_path, 'w', encoding='utf-8') as f:
                    f.write(polyfills_content)
                
                return True
        
        # Check environment files
        environments_dir = os.path.join(project_dir, 'src', 'environments')
        if not os.path.exists(environments_dir):
            os.makedirs(environments_dir, exist_ok=True)
            
            # Create environment.ts
            env_path = os.path.join(environments_dir, 'environment.ts')
            env_content = """
export const environment = {
  production: false
};
"""
            with open(env_path, 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            # Create environment.prod.ts
            env_prod_path = os.path.join(environments_dir, 'environment.prod.ts')
            env_prod_content = """
export const environment = {
  production: true
};
"""
            with open(env_prod_path, 'w', encoding='utf-8') as f:
                f.write(env_prod_content)
            
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Auto-Fix]: Created missing environment files"
            })
            
            return True
        
        return False

    async def fix_code_errors(self, project_dir: str, project_name: str, error_output: str, sio, error_context: Dict = None) -> bool:
        """
        Advanced universal error fixing system that can handle any type of code error.
        
        Args:
            project_dir: Project directory path
            project_name: Project name
            error_output: Error output text
            sio: Socket.io instance for emitting messages
            error_context: Optional additional context about the error (framework, command, etc.)
            
        Returns:
            True if fixes were applied, False otherwise
        """
        error_context = error_context or {}
        framework = error_context.get('framework', 'unknown')
        command = error_context.get('command', 'unknown')
        
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Auto-Fix]: Analyzing errors and applying intelligent fixes..."
        })
        
        # Step 1: Analyze error to determine its type and language/framework
        error_analysis = await self._analyze_generic_error(error_output, project_dir, framework)
        error_type = error_analysis.get('error_type', 'unknown')
        language = error_analysis.get('language', 'unknown')
        affected_files = error_analysis.get('affected_files', [])
        
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Auto-Fix]: Detected {error_type} error in {language} code. Affected files: {len(affected_files)}"
        })
        
        # Step 2: Handle based on the detected error type
        if not affected_files:
            # Try to find relevant files if none were detected in the error
            affected_files = await self._find_relevant_files_for_error(error_output, project_dir, language, framework)
            
            if not affected_files:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Auto-Fix]: Could not determine which files need fixing. Attempting project-wide fixes."
                })
                
                # Try project-wide fixes based on framework
                return await self._apply_project_wide_fixes(project_dir, project_name, error_output, framework, language, sio)
        
        # Step 3: Collect file contents to provide context to the LLM
        file_contents = {}
        for file_path in affected_files:
            abs_path = os.path.join(project_dir, file_path) if not os.path.isabs(file_path) else file_path
            
            if os.path.exists(abs_path):
                try:
                    with open(abs_path, 'r', encoding='utf-8', errors='replace') as f:
                        file_contents[file_path] = f.read()
                except Exception as e:
                    logger.error(f"Error reading file {file_path}: {e}")
        
        # Step 4: Use Deepseek to generate fixes
        deepseek_llm = LLM.create("deepseek/deepseek-coder")
        
        # Construct file context string
        file_context = ""
        for file_path, content in file_contents.items():
            file_context += f"\n--- {file_path} ---\n```\n{content}\n```\n"
        
        # Framework-specific prompting
        framework_context = ""
        if framework == "angular":
            framework_context = "This is an Angular project with TypeScript."
        elif framework == "react":
            framework_context = "This is a React project, likely using JSX/TSX."
        elif framework == "python":
            framework_context = "This is a Python project."
        elif framework == "java":
            framework_context = "This is a Java project."
        else:
            # Try to detect framework from file extensions and contents
            framework_context = f"This project appears to use {language} as its primary language."
        
        # Create prompt for Deepseek
        prompt = f"""
        You are an expert developer tasked with fixing errors in code. {framework_context}
        
        ERROR OUTPUT:
        ```
        {error_output[:2000]}  # Limit error output to prevent prompt overflow
        ```
        
        RELEVANT FILES:
        {file_context}
        
        TASK:
        Analyze the error output and fix the issues in the provided files. Identify the root causes, not just symptoms.
        
        INSTRUCTIONS:
        1. For each issue, identify the specific file that needs fixing
        2. Understand the context from the provided file contents
        3. Apply the appropriate fix for each error
        4. Return your solution as a JSON object with this structure:
        
        {{
            "analysis": "Detailed explanation of the root issues identified and your fixing approach",
            "fixes": [
                {{
                    "file": "path/to/file",
                    "error": "Specific error description",
                    "fix": "Complete corrected file content",
                    "explanation": "Why this fix resolves the issue"
                }}
            ],
            "framework_specific_actions": [
                {{
                    "action": "install_dependency",
                    "details": "package_name"
                }}
            ]
        }}
        
        Only include files that need to be fixed. Provide the FULL content for each file, not just the changed lines.
        If configuration changes or dependency installations are needed, include them in framework_specific_actions.
        """
        
        try:
            # Use Deepseek for deep code understanding
            fix_response = await deepseek_llm.generate(prompt)
            
            # Parse response
            import json
            import re
            
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', fix_response, re.DOTALL)
            if json_match:
                fixes_data = json.loads(json_match.group(0))
            else:
                fixes_data = json.loads(fix_response)
            
            # Apply fixes from response
            fixes = fixes_data.get("fixes", [])
            analysis = fixes_data.get("analysis", "No analysis provided")
            fw_actions = fixes_data.get("framework_specific_actions", [])
            
            if fixes:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Auto-Fix]: Found {len(fixes)} files to fix. Analysis: {analysis[:300]}..."
                })
                
                # Apply each fix
                for fix_info in fixes:
                    file_path = fix_info.get("file")
                    error_desc = fix_info.get("error", "Unknown error")
                    fix_content = fix_info.get("fix")
                    explanation = fix_info.get("explanation", "No explanation provided")
                    
                    if file_path and fix_content:
                        # Normalize file path
                        if os.path.isabs(file_path):
                            try:
                                rel_path = os.path.relpath(file_path, project_dir)
                                file_path = rel_path
                            except:
                                pass
                        
                        # Get absolute path for file operations
                        abs_file_path = os.path.join(project_dir, file_path)
                        
                        # Make sure directory exists
                        os.makedirs(os.path.dirname(abs_file_path), exist_ok=True)
                        
                        # Check if content has changed to avoid unnecessary writes
                        current_content = ""
                        try:
                            if os.path.exists(abs_file_path):
                                with open(abs_file_path, "r", encoding="utf-8", errors='replace') as f:
                                    current_content = f.read()
                        except:
                            pass
                        
                        if current_content != fix_content:
                            # Write the fixed content
                            try:
                                with open(abs_file_path, "w", encoding="utf-8") as f:
                                    f.write(fix_content)
                                
                                await sio.emit("agent_message", {
                                    "project_name": project_name,
                                    "message": f"[Auto-Fix]: Fixed file {file_path}:\n{error_desc}\n\nExplanation: {explanation[:250]}..."
                                })
                            except Exception as e:
                                await sio.emit("agent_message", {
                                    "project_name": project_name,
                                    "message": f"[Auto-Fix Error]: Failed to write to {file_path}: {str(e)}"
                                })
                        else:
                            await sio.emit("agent_message", {
                                "project_name": project_name,
                                "message": f"[Auto-Fix]: File {file_path} already has the correct content."
                            })
                
                # Apply any framework-specific actions
                if fw_actions:
                    await self._apply_framework_specific_actions(fw_actions, project_dir, project_name, sio)
                
                return True
            else:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Auto-Fix]: Could not generate specific fixes. Root cause analysis: {analysis[:500]}..."
                })
                return False
                
        except Exception as e:
            logger.error(f"Error in universal error fixing: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            await sio.emit("agent_message", {
                "project_name": project_name, 
                "message": f"[Auto-Fix Error]: Error during fix generation: {str(e)}"
            })
            
            return False

    async def _analyze_generic_error(self, error_output: str, project_dir: str, framework_hint: str = None) -> Dict[str, Any]:
        """
        Analyze any type of error output to determine its type, affected files, and language.
        
        Args:
            error_output: The error output text
            project_dir: Project directory path
            framework_hint: Optional hint about the framework in use
            
        Returns:
            Dictionary with error analysis information
        """
        # Common error patterns across languages
        error_patterns = {
            "syntax": [
                r"syntax error", r"unexpected", r"expected", r"token", r"unterminated", 
                r"indentation", r"missing ['\"]", r"illegal", r"invalid syntax"
            ],
            "runtime": [
                r"exception", r"error:", r"undefined", r"null pointer", r"segmentation fault",
                r"cannot read property", r"is not a function", r"is not defined",
                r"runtime error", r"is null", r"is undefined", r"NullPointerException",
                r"TypeError", r"ReferenceError"
            ],
            "type": [
                r"type error", r"incompatible types", r"cannot assign", r"expected type",
                r"no attribute", r"has no member", r"has no property", r"does not exist on type",
                r"type '.*' is not assignable to type", r"undefined property"
            ],
            "import": [
                r"module not found", r"cannot find module", r"import error", r"unable to resolve",
                r"No module named", r"ModuleNotFoundError", r"ImportError", r"require(.*) is not defined"
            ],
            "dependency": [
                r"depends on", r"dependency", r"package not found", r"version conflict",
                r"incompatible version", r"npm ERR!", r"pip ERR", r"Missing dependency"
            ],
            "build": [
                r"build failed", r"compilation failed", r"compiler error", r"cannot compile",
                r"build error", r"failed to compile", r"Error during compilation"
            ],
            "permission": [
                r"permission denied", r"EPERM", r"EACCES", r"access denied", 
                r"insufficient privileges", r"not authorized"
            ],
            "config": [
                r"configuration", r"config", r"settings", r"option", r"parameter", 
                r"missing .* in config", r"unknown option"
            ]
        }
        
        # Language detection patterns
        language_patterns = {
            "javascript": [r"\.js:", r"node.js", r"JavaScript", r"JS", r"ECMAScript", r"npm", r"node", r"V8"],
            "typescript": [r"\.ts:", r"TypeScript", r"TS", r"tsc", r"tsconfig", r"angular"],
            "python": [r"\.py:", r"python", r"Traceback", r"IndentationError", r"def ", r"class ", r"pip"],
            "java": [r"\.java:", r"Exception", r"public class", r"void", r"maven", r"gradle", r"JVM"],
            "c#": [r"\.cs:", r"C#", r"using System", r"namespace", r"dotnet", r"csc"],
            "php": [r"\.php:", r"PHP", r"Fatal error", r"Parse error", r"<?php"],
            "ruby": [r"\.rb:", r"Ruby", r"gem", r"rails", r"rake", r"bundler"],
            "go": [r"\.go:", r"Go", r"golang", r"package", r"func", r"goroutine"],
            "rust": [r"\.rs:", r"Rust", r"cargo", r"impl", r"struct", r"trait", r"crate"],
            "html": [r"\.html:", r"HTML", r"tag", r"element", r"selector"],
            "css": [r"\.css:", r"CSS", r"style", r"selector", r"SCSS", r"SASS"],
        }
        
        # Framework detection patterns
        framework_patterns = {
            "angular": [r"angular", r"ng ", r"Component", r"Module", r"Injectable", r"template"],
            "react": [r"react", r"jsx", r"tsx", r"component", r"props", r"state", r"hook"],
            "vue": [r"vue", r"component", r"template", r"script", r"v-", r"vuex"],
            "django": [r"django", r"urls.py", r"views.py", r"models.py", r"makemigrations", r"migrate"],
            "flask": [r"flask", r"route", r"app.run", r"request", r"Blueprint"],
            "spring": [r"spring", r"bean", r"autowired", r"repository", r"service", r"controller"],
            "express": [r"express", r"app.use", r"router", r"middleware", r"req", r"res"],
            "laravel": [r"laravel", r"artisan", r"migration", r"blade", r"eloquent"],
            "rails": [r"rails", r"activerecord", r"rake", r"migration", r"model", r"controller"],
        }
        
        # Initialize result
        result = {
            "error_type": "unknown",
            "language": "unknown",
            "framework": framework_hint or "unknown",
            "affected_files": [],
            "error_categories": {},
            "error_lines": []
        }
        
        # Extract error lines
        error_lines = [line for line in error_output.split('\n') if line.strip()]
        result["error_lines"] = error_lines
        
        # Detect error type
        error_counts = {error_type: 0 for error_type in error_patterns}
        for line in error_lines:
            line_lower = line.lower()
            for error_type, patterns in error_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, line_lower, re.IGNORECASE):
                        error_counts[error_type] += 1
                        if error_type not in result["error_categories"]:
                            result["error_categories"][error_type] = []
                        result["error_categories"][error_type].append(line)
                        break
        
        # Determine the most likely error type
        if error_counts:
            max_error_type = max(error_counts.items(), key=lambda x: x[1])
            if max_error_type[1] > 0:
                result["error_type"] = max_error_type[0]
        
        # Detect language
        language_counts = {lang: 0 for lang in language_patterns}
        for line in error_lines:
            for lang, patterns in language_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        language_counts[lang] += 1
                        break
        
        # Determine the most likely language
        if language_counts:
            max_lang = max(language_counts.items(), key=lambda x: x[1])
            if max_lang[1] > 0:
                result["language"] = max_lang[0]
        
        # Detect framework if not provided
        if result["framework"] == "unknown":
            framework_counts = {framework: 0 for framework in framework_patterns}
            for line in error_lines:
                for framework, patterns in framework_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            framework_counts[framework] += 1
                            break
            
            # Determine the most likely framework
            if framework_counts:
                max_framework = max(framework_counts.items(), key=lambda x: x[1])
                if max_framework[1] > 0:
                    result["framework"] = max_framework[0]
        
        # Extract affected files
        file_patterns = [
            # Common file:line:col patterns
            r'(?:^|\s)((?:[A-Za-z]:)?[^:]+\.[a-zA-Z]+)(?::(\d+)(?::(\d+))?)',
            # Error in file patterns
            r'(?:error|exception) in [\'"]?([^\'"\s:]+\.[a-zA-Z]+)[\'"]?',
            # From file patterns
            r'(?:from|at|in) [\'"]?([^\'"\s:]+\.[a-zA-Z]+)[\'"]?',
            # Cannot open file patterns
            r'cannot open [\'"]?([^\'"\s:]+\.[a-zA-Z]+)[\'"]?',
            # Various language-specific patterns
            r'File "([^"]+)", line \d+',  # Python
            r'at .*\(([^:]+\.[a-zA-Z]+):(\d+)(?::(\d+))?\)',  # JavaScript
            r'In file included from ([^:]+):(\d+)',  # C/C++
        ]
        
        all_file_matches = []
        for line in error_lines:
            for pattern in file_patterns:
                matches = re.findall(pattern, line)
                if matches:
                    # Handle different match formats
                    if isinstance(matches[0], tuple):
                        # If the match is a tuple, the first element is the file path
                        all_file_matches.extend([m[0] for m in matches])
                    else:
                        # Otherwise, the match itself is the file path
                        all_file_matches.extend(matches)
        
        # Filter and normalize paths
        filtered_files = []
        for file_path in all_file_matches:
            # Skip paths that are clearly not files
            if not file_path or file_path in ['null', 'undefined'] or file_path.startswith(('http://', 'https://')):
                continue
            
            # Normalize path
            if os.path.isabs(file_path):
                try:
                    # Try to make path relative to project directory
                    rel_path = os.path.relpath(file_path, project_dir)
                    file_path = rel_path
                except:
                    # If that fails, keep the original path
                    pass
            
            # Add to filtered list if it exists
            abs_path = os.path.join(project_dir, file_path) if not os.path.isabs(file_path) else file_path
            if os.path.exists(abs_path) and os.path.isfile(abs_path):
                filtered_files.append(file_path)
        
        # De-duplicate files
        result["affected_files"] = list(set(filtered_files))
        
        return result
    
    async def _find_relevant_files_for_error(self, error_output: str, project_dir: str, language: str, framework: str) -> List[str]:
        """
        Find files that might be relevant to the error when no specific files are mentioned in the error output.
        
        Args:
            error_output: Error output text
            project_dir: Project directory
            language: Detected language
            framework: Detected framework
            
        Returns:
            List of relevant file paths
        """
        # Extract potential keywords from error
        keywords = set()
        lines = error_output.split('\n')
        
        # Generic keywords extraction
        for line in lines:
            # Split by common separators and extract words
            words = re.findall(r'[a-zA-Z][a-zA-Z0-9_]+', line)
            for word in words:
                # Filter out common words and keep meaningful identifiers
                if (len(word) > 3 and 
                    word.lower() not in ['error', 'exception', 'warning', 'file', 'line', 'the', 'and', 'this', 'that', 'with'] and
                    not word.startswith(('err', 'warn'))):
                    keywords.add(word)
        
        # Look for class/function names
        class_pattern = r'class [\'"]?([a-zA-Z][a-zA-Z0-9_]+)[\'"]?'
        function_pattern = r'(?:function|def|method) [\'"]?([a-zA-Z][a-zA-Z0-9_]+)[\'"]?'
        
        for line in lines:
            # Extract class names
            class_matches = re.findall(class_pattern, line, re.IGNORECASE)
            keywords.update(class_matches)
            
            # Extract function names
            function_matches = re.findall(function_pattern, line, re.IGNORECASE)
            keywords.update(function_matches)
        
        # Define file extensions based on language
        extensions = self._get_extensions_for_language(language)
        
        # Find all potential source files
        all_source_files = []
        for root, _, files in os.walk(project_dir):
            # Skip common directories to ignore
            if any(ignored in root for ignored in ['node_modules', 'venv', '__pycache__', '.git', 'dist', 'build']):
                continue
                
            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    rel_path = os.path.relpath(os.path.join(root, file), project_dir)
                    all_source_files.append(rel_path)
        
        # If we have no keywords, return the main files for the framework
        if not keywords:
            return self._get_main_files_for_framework(all_source_files, framework, language)
        
        # Score files based on keyword matches
        scored_files = []
        for file_path in all_source_files:
            try:
                with open(os.path.join(project_dir, file_path), 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
                
                score = 0
                for keyword in keywords:
                    if keyword in content:
                        score += 1
                    # Boost score if keyword is in the file name
                    if keyword in os.path.basename(file_path):
                        score += 3
                
                if score > 0:
                    scored_files.append((file_path, score))
            except:
                continue
        
        # Sort by score (descending) and take top 5
        scored_files.sort(key=lambda x: x[1], reverse=True)
        top_files = [file_path for file_path, _ in scored_files[:5]]
        
        # If we found relevant files, return them
        if top_files:
            return top_files
        
        # Otherwise, return main files for the framework
        return self._get_main_files_for_framework(all_source_files, framework, language)
    
    def _get_extensions_for_language(self, language: str) -> List[str]:
        """
        Get file extensions for a given language.
        
        Args:
            language: Language name
            
        Returns:
            List of file extensions
        """
        # Define common extensions by language
        extensions_map = {
            "javascript": ['.js', '.jsx', '.cjs', '.mjs'],
            "typescript": ['.ts', '.tsx', '.d.ts'],
            "python": ['.py', '.pyw', '.pyx'],
            "java": ['.java', '.jar', '.class'],
            "c#": ['.cs', '.csx'],
            "php": ['.php', '.phtml', '.php5'],
            "ruby": ['.rb', '.rake', '.ru'],
            "go": ['.go'],
            "rust": ['.rs'],
            "html": ['.html', '.htm', '.xhtml'],
            "css": ['.css', '.scss', '.sass', '.less'],
            "unknown": ['.ts', '.js', '.py', '.java', '.php', '.cs', '.go', '.rb'],  # Default to common ones
        }
        
        return extensions_map.get(language.lower(), extensions_map["unknown"])
    
    def _get_main_files_for_framework(self, all_files: List[str], framework: str, language: str) -> List[str]:
        """
        Get main files for a framework that are likely to be relevant for errors.
        
        Args:
            all_files: List of all source files
            framework: Framework name
            language: Language name
            
        Returns:
            List of main file paths
        """
        # Common main files by framework
        framework_main_files = {
            "angular": [
                r'.*app.module.ts$', r'.*main.ts$', r'.*app-routing.module.ts$', 
                r'.*app.component.ts$', r'.*environment.ts$', r'.*package.json$'
            ],
            "react": [
                r'.*index\.[jt]sx?$', r'.*App\.[jt]sx?$', r'.*main\.[jt]sx?$',
                r'.*package.json$', r'.*tsconfig.json$', r'.*webpack.config\.[jt]s$'
            ],
            "vue": [
                r'.*main\.[jt]s$', r'.*App.vue$', r'.*index.html$',
                r'.*router\.[jt]s$', r'.*store\.[jt]s$', r'.*package.json$'
            ],
            "django": [
                r'.*urls\.py$', r'.*views\.py$', r'.*models\.py$',
                r'.*settings\.py$', r'.*admin\.py$', r'.*apps\.py$'
            ],
            "flask": [
                r'.*app\.py$', r'.*__init__\.py$', r'.*views\.py$',
                r'.*models\.py$', r'.*routes\.py$', r'.*config\.py$'
            ],
            "spring": [
                r'.*Application\.java$', r'.*Configuration\.java$',
                r'.*Controller\.java$', r'.*Service\.java$', r'.*Repository\.java$'
            ],
            "express": [
                r'.*app\.[jt]s$', r'.*server\.[jt]s$', r'.*index\.[jt]s$',
                r'.*routes\.[jt]s$', r'.*package.json$'
            ],
            "unknown": [
                r'.*index\.[jt]s(x)?$', r'.*main\.[jt]s(x)?$', r'.*app\.[jt]s(x)?$',
                r'.*server\.[jt]s$', r'.*package.json$', r'.*config\.[jt]s$'
            ]
        }
        
        # Get patterns for the framework or use defaults
        main_file_patterns = framework_main_files.get(framework.lower(), framework_main_files["unknown"])
        
        # Filter files matching the patterns
        main_files = []
        for pattern in main_file_patterns:
            for file_path in all_files:
                if re.match(pattern, file_path, re.IGNORECASE):
                    main_files.append(file_path)
        
        # Add language-specific common files if needed
        if not main_files:
            for file_path in all_files:
                file_name = os.path.basename(file_path).lower()
                if language == "python" and file_name in ["app.py", "main.py", "__init__.py"]:
                    main_files.append(file_path)
                elif (language in ["javascript", "typescript"]) and file_name in ["index.js", "index.ts", "app.js", "app.ts"]:
                    main_files.append(file_path)
                elif language == "java" and file_name.endswith("application.java"):
                    main_files.append(file_path)
        
        # Return top 5 main files, prioritizing config files
        config_files = [f for f in main_files if os.path.basename(f).lower() in ["package.json", "tsconfig.json", "settings.py", "config.py"]]
        other_files = [f for f in main_files if f not in config_files]
        
        return (config_files + other_files)[:5]
    
    async def _apply_project_wide_fixes(self, project_dir: str, project_name: str, error_output: str, framework: str, language: str, sio) -> bool:
        """
        Apply project-wide fixes when specific files can't be determined.
        
        Args:
            project_dir: Project directory
            project_name: Project name
            error_output: Error output text
            framework: Framework name
            language: Language name
            sio: Socket.io instance
            
        Returns:
            True if fixes were applied, False otherwise
        """
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Auto-Fix]: Applying project-wide fixes for {framework} ({language}) project..."
        })
        
        # Use Deepseek LLM to analyze and suggest fixes
        deepseek_llm = LLM.create("deepseek/deepseek-coder")
        
        # Find important config files
        config_files = self._get_config_files_for_project(project_dir, framework, language)
        
        # Read config file contents
        config_contents = {}
        for file_path in config_files:
            abs_path = os.path.join(project_dir, file_path)
            if os.path.exists(abs_path):
                try:
                    with open(abs_path, 'r', encoding='utf-8', errors='replace') as f:
                        config_contents[file_path] = f.read()
                except Exception as e:
                    logger.error(f"Error reading config file {file_path}: {e}")
        
        # Build config context
        config_context = ""
        for file_path, content in config_contents.items():
            config_context += f"\n--- {file_path} ---\n```\n{content}\n```\n"
        
        # Create prompt for Deepseek
        prompt = f"""
        You are an expert developer tasked with fixing project-wide errors in a {framework} project using {language}.
        
        ERROR OUTPUT:
        ```
        {error_output[:2000]}  # Limit error output to prevent prompt overflow
        ```
        
        PROJECT CONFIGURATION FILES:
        {config_context}
        
        TASK:
        Analyze the error output and suggest project-wide fixes. The errors cannot be traced to specific files, so we need general solutions.
        
        INSTRUCTIONS:
        1. Identify the likely root causes for the errors
        2. Check the configuration files for potential issues
        3. Suggest fixes for the configuration files
        4. Suggest commands that might help resolve the errors
        5. Return your solution as a JSON object with this structure:
        
        {{
            "analysis": "Detailed explanation of the root issues identified and your fixing approach",
            "config_fixes": [
                {{
                    "file": "path/to/config/file",
                    "fix": "Complete corrected file content",
                    "explanation": "Why this fix resolves the issue"
                }}
            ],
            "commands": [
                {{
                    "command": "Command to execute",
                    "explanation": "What this command does and why it helps"
                }}
            ],
            "missing_files": [
                {{
                    "file": "path/to/new/file",
                    "content": "Complete file content",
                    "explanation": "Why this file is needed"
                }}
            ]
        }}
        """
        
        try:
            # Generate fixes
            fix_response = await deepseek_llm.generate(prompt)
            
            # Parse response
            import json
            import re
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', fix_response, re.DOTALL)
            if json_match:
                fixes_data = json.loads(json_match.group(0))
            else:
                fixes_data = json.loads(fix_response)
            
            # Apply fixes
            config_fixes = fixes_data.get("config_fixes", [])
            commands = fixes_data.get("commands", [])
            missing_files = fixes_data.get("missing_files", [])
            analysis = fixes_data.get("analysis", "No analysis provided")
            
            # Report analysis
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Auto-Fix]: Project-wide analysis: {analysis[:500]}..."
            })
            
            fixes_applied = False
            
            # Apply config fixes
            for fix_info in config_fixes:
                file_path = fix_info.get("file")
                fix_content = fix_info.get("fix")
                explanation = fix_info.get("explanation", "No explanation provided")
                
                if file_path and fix_content:
                    # Normalize path
                    abs_file_path = os.path.join(project_dir, file_path)
                    os.makedirs(os.path.dirname(abs_file_path), exist_ok=True)
                    
                    # Write fixed content
                    with open(abs_file_path, "w", encoding="utf-8") as f:
                        f.write(fix_content)
                    
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Auto-Fix]: Updated configuration file {file_path}.\nReason: {explanation[:200]}..."
                    })
                    
                    fixes_applied = True
            
            # Create missing files
            for file_info in missing_files:
                file_path = file_info.get("file")
                file_content = file_info.get("content")
                explanation = file_info.get("explanation", "No explanation provided")
                
                if file_path and file_content:
                    # Normalize path
                    abs_file_path = os.path.join(project_dir, file_path)
                    os.makedirs(os.path.dirname(abs_file_path), exist_ok=True)
                    
                    # Write content
                    with open(abs_file_path, "w", encoding="utf-8") as f:
                        f.write(file_content)
                    
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Auto-Fix]: Created missing file {file_path}.\nReason: {explanation[:200]}..."
                    })
                    
                    fixes_applied = True
            
            # Run suggested commands
            if commands:
                from terminal_integration import TerminalIntegration
                terminal = TerminalIntegration()
                
                for cmd_info in commands:
                    cmd = cmd_info.get("command")
                    explanation = cmd_info.get("explanation", "No explanation provided")
                    
                    if cmd:
                        await sio.emit("agent_message", {
                            "project_name": project_name,
                            "message": f"[Auto-Fix]: Running command: {cmd}\nReason: {explanation[:200]}..."
                        })
                        
                        try:
                            result = await terminal.run_command(cmd, cwd=project_dir, project_name=project_name)
                            fixes_applied = True
                        except Exception as e:
                            await sio.emit("agent_message", {
                                "project_name": project_name,
                                "message": f"[Auto-Fix]: Command failed: {str(e)}"
                            })
            
            return fixes_applied
            
        except Exception as e:
            logger.error(f"Error in project-wide fixes: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Auto-Fix]: Error generating project-wide fixes: {str(e)}"
            })
            
            return False
    
    def _get_config_files_for_project(self, project_dir: str, framework: str, language: str) -> List[str]:
        """
        Get important configuration files for a project.
        
        Args:
            project_dir: Project directory
            framework: Framework name
            language: Language name
            
        Returns:
            List of config file paths
        """
        # Define common config files by framework/language
        config_files = {
            "angular": ["angular.json", "package.json", "tsconfig.json", "tsconfig.app.json"],
            "react": ["package.json", "tsconfig.json", "webpack.config.js", ".babelrc", "vite.config.js"],
            "vue": ["package.json", "vue.config.js", "vite.config.js", "nuxt.config.js"],
            "django": ["settings.py", "urls.py", "requirements.txt", "manage.py"],
            "flask": ["app.py", "config.py", "requirements.txt", "__init__.py"],
            "spring": ["pom.xml", "application.properties", "application.yml", "build.gradle"],
            "express": ["package.json", "app.js", "server.js", "tsconfig.json"],
            "javascript": ["package.json", "package-lock.json", ".npmrc", "webpack.config.js"],
            "typescript": ["tsconfig.json", "package.json", "tslint.json", "eslint.json"],
            "python": ["requirements.txt", "setup.py", "setup.cfg", "pyproject.toml", "Pipfile"],
            "java": ["pom.xml", "build.gradle", "application.properties", "application.yml"],
        }
        
        # Get config files for framework
        framework_config_files = config_files.get(framework.lower(), [])
        
        # Add language config files if different from framework
        language_config_files = []
        if language.lower() != framework.lower() and language.lower() in config_files:
            language_config_files = config_files.get(language.lower(), [])
        
        # Combine and search for files
        all_config_patterns = framework_config_files + language_config_files
        found_config_files = []
        
        # First look for exact matches
        for config_file in all_config_patterns:
            # Check if it's a direct file name or a pattern
            if '.' in config_file and not config_file.startswith('.'):
                config_path = os.path.join(project_dir, config_file)
                if os.path.exists(config_path):
                    found_config_files.append(config_file)
            else:
                # For patterns, search recursively but not too deep
                for root, _, files in os.walk(project_dir):
                    # Skip node_modules and other large directories
                    if any(d in root for d in ["node_modules", "venv", ".git", "dist", "build"]):
                        continue
                    
                    # Limit search depth to 3 levels
                    rel_path = os.path.relpath(root, project_dir)
                    if rel_path.count(os.path.sep) > 3:
                        continue
                    
                    for file in files:
                        if file == config_file:
                            rel_file_path = os.path.relpath(os.path.join(root, file), project_dir)
                            found_config_files.append(rel_file_path)
        
        # Add any obvious config files that might be present
        common_configs = ["package.json", "tsconfig.json", "requirements.txt", "pyproject.toml", "setup.py", 
                         "pom.xml", "build.gradle", "config.py", "settings.py"]
        
        for config in common_configs:
            config_path = os.path.join(project_dir, config)
            rel_path = config
            if os.path.exists(config_path) and rel_path not in found_config_files:
                found_config_files.append(rel_path)
        
        return found_config_files
    
    async def _apply_framework_specific_actions(self, actions: List[Dict], project_dir: str, project_name: str, sio) -> bool:
        """
        Apply framework-specific actions like installing dependencies.
        
        Args:
            actions: List of actions to perform
            project_dir: Project directory
            project_name: Project name
            sio: Socket.io instance
            
        Returns:
            True if actions were applied, False otherwise
        """
        from terminal_integration import TerminalIntegration
        terminal = TerminalIntegration()
        
        actions_applied = False
        
        for action in actions:
            action_type = action.get("action", "")
            details = action.get("details", "")
            
            if not action_type or not details:
                continue
            
            if action_type == "install_dependency":
                # Determine package manager
                package_manager = "npm"
                if os.path.exists(os.path.join(project_dir, "requirements.txt")) or os.path.exists(os.path.join(project_dir, "setup.py")):
                    package_manager = "pip"
                elif os.path.exists(os.path.join(project_dir, "pom.xml")):
                    package_manager = "mvn"
                elif os.path.exists(os.path.join(project_dir, "build.gradle")):
                    package_manager = "gradle"
                
                # Install dependency
                if package_manager == "npm":
                    cmd = f"npm install --save {details}"
                elif package_manager == "pip":
                    cmd = f"pip install {details}"
                elif package_manager == "mvn":
                    cmd = f"mvn dependency:get -Dartifact={details}"
                elif package_manager == "gradle":
                    cmd = f"gradle --refresh-dependencies"  # Gradle has no direct CLI for adding dependencies
                
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Auto-Fix]: Installing dependency {details} using {package_manager}..."
                })
                
                try:
                    result = await terminal.run_command(cmd, cwd=project_dir, project_name=project_name)
                    actions_applied = True
                except Exception as e:
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Auto-Fix]: Failed to install dependency: {str(e)}"
                    })
            
            elif action_type == "run_command":
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Auto-Fix]: Running command: {details}"
                })
                
                try:
                    result = await terminal.run_command(details, cwd=project_dir, project_name=project_name)
                    actions_applied = True
                except Exception as e:
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Auto-Fix]: Command failed: {str(e)}"
                    })
        
        return actions_applied

    async def ensure_angular_project(self, project_name: str, project_dir: str, sio) -> str:
        """
        Ensures that an Angular project exists in the given directory.
        If the project doesn't exist, creates it.
        
        Args:
            project_name: The name of the project
            project_dir: The project directory
            sio: The Socket.IO instance
            
        Returns:
            The path to the Angular project directory
        """
        from terminal_integration import TerminalIntegration
        terminal = TerminalIntegration()
        
        # Check if angular.json exists in the project directory
        angular_json_path = os.path.join(project_dir, 'angular.json')
        
        if not os.path.exists(angular_json_path):
            # Angular project doesn't exist, create it
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Setup]: Creating new Angular project for {project_name}..."
            })
            
            # Run Angular CLI to create a new project with routing and SCSS
            # Add --skip-install flag to defer npm install until after files are generated
            cmd = f"ng new {project_name} --routing --style=scss --skip-git --skip-install"
            
            try:
                result = await terminal.run_command(cmd, cwd=project_dir, project_name=project_name)
                
                # Check if project was created successfully
                if result.get("exit_code", 1) != 0:
                    error = result.get("error", "Unknown error")
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Error]: Failed to create Angular project: {error}"
                    })
                    raise Exception(f"Failed to create Angular project: {error}")
                
                # Fix potential nested directory issue (projects/projectname/projectname)
                ng_proj_dir = os.path.join(project_dir, project_name)
                
                # Check if we have a nested directory with the same name
                nested_dir = os.path.join(ng_proj_dir, project_name)
                if os.path.exists(nested_dir) and os.path.isdir(nested_dir):
                    # We have a nested directory situation, let's fix it
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Setup]: Fixing nested directories for {project_name}..."
                    })
                    
                    # Move all files from nested dir to parent dir
                    for item in os.listdir(nested_dir):
                        src = os.path.join(nested_dir, item)
                        dst = os.path.join(ng_proj_dir, item)
                        
                        if os.path.exists(dst):
                            if os.path.isdir(dst):
                                shutil.rmtree(dst)
                            else:
                                os.remove(dst)
                        
                        if os.path.isdir(src):
                            shutil.copytree(src, dst)
                        else:
                            shutil.copy2(src, dst)
                    
                    # Remove the now-empty nested directory
                    shutil.rmtree(nested_dir)
                    
                    await sio.emit("agent_message", {
                        "project_name": project_name,
                        "message": f"[Setup]: Fixed nested directories"
                    })
                
                # Set flag to run npm install at the end of plan_and_execute
                self.pending_npm_install = True
                self.ng_proj_dir = ng_proj_dir
                
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Setup]: Angular project created successfully at {ng_proj_dir}"
                })
                
                return ng_proj_dir
                
            except Exception as e:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Error]: Failed to create Angular project: {str(e)}"
                })
                raise Exception(f"Failed to create Angular project: {str(e)}")
        else:
            # Angular project already exists
            # Determine the actual project directory by checking angular.json
            try:
                with open(angular_json_path, 'r', encoding='utf-8') as f:
                    angular_json = json.load(f)
                
                # Check if the project directory is nested
                if "projects" in angular_json:
                    # Get the first project from angular.json
                    projects = list(angular_json.get("projects", {}).keys())
                    if projects:
                        actual_project_name = projects[0]
                        
                        # If project name doesn't match, update it
                        if actual_project_name != project_name:
                            await sio.emit("agent_message", {
                                "project_name": project_name,
                                "message": f"[Setup]: Updating Angular project name from '{actual_project_name}' to '{project_name}'"
                            })
                            
                            # Update angular.json to use the correct project name
                            angular_json["projects"][project_name] = angular_json["projects"][actual_project_name]
                            del angular_json["projects"][actual_project_name]
                            
                            # Update defaultProject if it exists
                            if "defaultProject" in angular_json and angular_json["defaultProject"] == actual_project_name:
                                angular_json["defaultProject"] = project_name
                            
                            # Save updated angular.json
                            with open(angular_json_path, 'w', encoding='utf-8') as f:
                                json.dump(angular_json, f, indent=2)
            except Exception as e:
                logger.error(f"Error reading angular.json: {e}")
            
            # Return the project directory
            ng_proj_dir = project_dir
            
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Setup]: Using existing Angular project at {ng_proj_dir}"
            })
            
            return ng_proj_dir

    async def fallback_generate_files(self, message, project_name, plan_response, sio, project_dir):
        """
        Fallback method to generate necessary implementation files when the standard process fails.
        
        Args:
            message: The original user request message
            project_name: The name of the project
            plan_response: The response from the planning phase
            sio: Socket.IO instance for sending messages
            project_dir: The project directory path
            
        Returns:
            List of file creation steps
        """
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": "[Fallback]: Generating necessary implementation files..."
        })
        
        # Get a list of existing files to avoid duplicates
        existing_files = self.project_manager.get_project_files(project_name)
        existing_paths = [f["path"] for f in existing_files]
        
        # Create a prompt for generating the necessary files
        analysis_prompt = (
            f"You are tasked with implementing an Angular project.\n\n"
            f"Project requirement: {message}\n\n"
            f"Existing files:\n{json.dumps(existing_paths, indent=2)}\n\n"
            f"Create all necessary files to complete this implementation. For each file:\n"
            f"1. Provide the relative file path (starting from the project root)\n"
            f"2. Provide the complete content of the file\n"
            f"3. Ensure all components, services, modules, etc. work together\n\n"
            f"Format each file as:\n"
            f"FILE: <relative_path>\n"
            f"```\n<file_content>\n```\n\n"
            f"Focus on creating these types of files:\n"
            f"- Component TypeScript files (.ts)\n"
            f"- Component HTML templates (.html)\n"
            f"- Component CSS/SCSS files (.scss)\n"
            f"- Services (.ts)\n"
            f"- Models/interfaces (.ts)\n"
            f"- Modules (.ts)\n"
            f"- Routing modules (.ts)\n\n"
            f"Make sure the implementation is complete and all necessary files are created."
        )
        
        try:
            # Generate implementation files
            files_response = await self.llm.generate(analysis_prompt)
            
            # Parse the files from the response
            file_matches = re.finditer(r'FILE: (.+?)[\r\n]+```[\r\n]+(.*?)[\r\n]+```', files_response, re.DOTALL)
            
            file_steps = []
            for match in file_matches:
                file_path = match.group(1).strip()
                file_content = match.group(2).strip()
                
                # Skip if file already exists
                if file_path in existing_paths:
                    continue
                
                file_steps.append({
                    "type": "create_file",
                    "file_path": file_path,
                    "content": file_content
                })
            
            if file_steps:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Fallback]: Generated {len(file_steps)} implementation files."
                })
            else:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": "[Fallback]: No additional files needed to be generated."
                })
            
            return file_steps
        except Exception as e:
            logger.error(f"Error in fallback file generation: {e}")
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"[Error]: Could not generate implementation files: {str(e)}"
            })
            return []

    def _find_relevant_requirement(self, step_content, requirements):
        """
        Find the most relevant requirement for a given step.
        
        Args:
            step_content: The content/description of the step
            requirements: List of requirements to match against
            
        Returns:
            The most relevant requirement or None if no match
        """
        if not requirements or not step_content:
            return None
        
        # Convert step content to lowercase for case-insensitive matching
        step_content_lower = step_content.lower()
        
        # First, try to find an exact match
        for req in requirements:
            req_str = str(req).lower()
            if req_str in step_content_lower or step_content_lower in req_str:
                return req
        
        # If no exact match, use token matching to find the most relevant requirement
        best_match = None
        best_score = 0
        
        for req in requirements:
            req_str = str(req).lower()
            
            # Split into tokens
            req_tokens = set(req_str.split())
            step_tokens = set(step_content_lower.split())
            
            # Calculate intersection and union for Jaccard similarity
            intersection = len(req_tokens.intersection(step_tokens))
            union = len(req_tokens.union(step_tokens))
            
            # Avoid division by zero
            if union > 0:
                score = intersection / union
                if score > best_score:
                    best_score = score
                    best_match = req
        
        # Return the best match if score is above threshold
        return best_match if best_score > 0.2 else None

    async def export_project_memory_documentation(self, project_name: str, project_dir: str, sio) -> str:
        """
        Exports the project memory to documentation files.
        
        Args:
            project_name: The name of the project
            project_dir: The project directory path
            sio: Socket.IO instance for sending messages
            
        Returns:
            Path to the documentation file
        """
        # Get project memory if it exists
        memory = self.project_memories.get(project_name)
        if not memory:
            # Try to load it from disk
            memory = ProjectMemory.load_from_file(project_dir)
            if not memory:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": f"[Documentation]: No project memory found to generate documentation."
                })
                return None
        
        # Generate documentation
        report = memory.generate_report()
        docs_dir = os.path.join(project_dir, "docs")
        os.makedirs(docs_dir, exist_ok=True)
        
        # Save documentation to file
        doc_path = os.path.join(docs_dir, "project_documentation.md")
        with open(doc_path, "w") as f:
            f.write(report)
        
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"[Documentation]: Project documentation generated at {doc_path}"
        })
        
        return doc_path
