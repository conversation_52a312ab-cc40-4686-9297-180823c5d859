"""
Project Manager for the Autonomous AI Software Development Agent.
"""
import os
import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
import shutil
import fnmatch

logger = logging.getLogger(__name__)

class ProjectManager:
    """
    Manages projects, files, and messages.
    """
    def __init__(self, base_dir: Optional[str] = None):
        """
        Initialize the ProjectManager with a base directory.
        
        Args:
            base_dir: The base directory for all projects.
        """
        if base_dir is None:
            if os.name == 'nt':  # Windows
                self.base_dir = os.path.expanduser("C:/SourceProjects/AutonomousAI/projects")
            elif os.name == 'posix':  # Linux/Mac
                self.base_dir = os.path.expanduser("~/SourceProjects/AutonomousAI/projects")
            else:
                self.base_dir = os.path.expanduser("~/projects")
        else:
            self.base_dir = base_dir
        
        os.makedirs(self.base_dir, exist_ok=True)
        logger.info(f"Initialized ProjectManager with base directory: {self.base_dir}")
        
        self.projects = {}
        self.messages = {}
        self._load_projects()
    
    def _load_projects(self):
        """Load existing projects from the base directory."""
        for project_dir in os.listdir(self.base_dir):
            project_path = os.path.join(self.base_dir, project_dir)
            if os.path.isdir(project_path):
                self.projects[project_dir] = {
                    "name": project_dir,
                    "path": project_path,
                    "files": self._get_files(project_path)
                }
                # Load messages from disk if available
                messages_path = os.path.join(project_path, "messages.json")
                if os.path.isfile(messages_path):
                    try:
                        with open(messages_path, "r", encoding="utf-8") as f:
                            self.messages[project_dir] = json.load(f)
                    except Exception as e:
                        logger.error(f"Error loading messages for {project_dir}: {e}")
                        self.messages[project_dir] = []
                else:
                    self.messages[project_dir] = []
    
    def _get_files(self, project_path: str) -> List[Dict[str, Any]]:
        """
        Get all files in a project directory.
        
        Args:
            project_path: The path to the project directory.
            
        Returns:
            A list of file information dictionaries.
        """
        files = []
        # Patterns to ignore (both directories and files)
        ignore_patterns = [
            'node_modules', '.git', 'dist', 'build', '.cache', 
            '__pycache__', '.pytest_cache', '.angular', 'coverage',
            '.next', '.nuxt', '.output', '.vscode', '.idea',
            '*.log', '*.lock', '*.min.js', '*.min.css',
            '.DS_Store', 'Thumbs.db'
        ]
        
        for root, dirs, filenames in os.walk(project_path):
            # Remove ignored directories from dirs to prevent walking them
            dirs[:] = [d for d in dirs if not any(
                fnmatch.fnmatch(d, pattern) for pattern in ignore_patterns
            )]
            
            # Skip files matching ignore patterns
            for filename in filenames:
                if any(fnmatch.fnmatch(filename, pattern) for pattern in ignore_patterns):
                    continue
                    
                file_path = os.path.join(root, filename)
                rel_path = os.path.relpath(file_path, project_path)
                files.append({
                    "name": filename,
                    "path": rel_path,
                    "full_path": file_path
                })
        return files
    
    def _normalize_paths(self, data):
        """
        Recursively normalize all 'path' and 'full_path' fields in dicts/lists to use forward slashes.
        """
        if isinstance(data, dict):
            new_data = {}
            for k, v in data.items():
                if k in ("path", "full_path") and isinstance(v, str):
                    new_data[k] = v.replace("\\", "/")
                elif isinstance(v, (dict, list)):
                    new_data[k] = self._normalize_paths(v)
                else:
                    new_data[k] = v
            return new_data
        elif isinstance(data, list):
            return [self._normalize_paths(item) for item in data]
        else:
            return data

    def get_projects(self) -> List[Dict[str, Any]]:
        """
        Get all projects.
        
        Returns:
            A list of project information dictionaries.
        """
        projects = list(self.projects.values())
        return self._normalize_paths(projects)
    
    def get_project(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get a project by name.
        
        Args:
            name: The name of the project.
            
        Returns:
            The project information dictionary or None if not found.
        """
        project = self.projects.get(name)
        return self._normalize_paths(project) if project else None
    
    def create_project(self, name: str) -> Dict[str, Any]:
        """
        Create a new project.
        
        Args:
            name: The name of the project.
            
        Returns:
            The project information dictionary.
        """
        project_path = os.path.join(self.base_dir, name)
        os.makedirs(project_path, exist_ok=True)
        
        project = {
            "name": name,
            "path": project_path,
            "files": []
        }
        
        self.projects[name] = project
        self.messages[name] = []
        
        return project
    
    def delete_project(self, name: str) -> bool:
        """
        Delete a project.
        
        Args:
            name: The name of the project.
            
        Returns:
            True if the project was deleted, False otherwise.
        """
        if name not in self.projects:
            return False
        
        project_path = self.projects[name]["path"]
        
        # Remove from in-memory structures
        del self.projects[name]
        if name in self.messages:
            del self.messages[name]
        
        # Delete the folder from the file system
        try:
            shutil.rmtree(project_path)
        except Exception as e:
            logger.error(f"Error deleting project directory for {name}: {e}")
            return False
        
        return True
    
    def get_project_files(self, name: str) -> List[Dict[str, Any]]:
        """
        Get all files in a project.
        
        Args:
            name: The name of the project.
            
        Returns:
            A list of file information dictionaries.
        """
        if name not in self.projects:
            return []
        
        project_path = self.projects[name]["path"]
        files = self._get_files(project_path)
        return self._normalize_paths(files)
    
    def get_file_content(self, project_name: str, file_path: str) -> Optional[str]:
        """
        Get the content of a file.
        
        Args:
            project_name: The name of the project.
            file_path: The path to the file relative to the project directory.
            
        Returns:
            The content of the file or None if not found.
        """
        if project_name not in self.projects:
            return None
        
        project_path = self.projects[project_name]["path"]
        full_path = os.path.join(project_path, file_path)
        
        if not os.path.isfile(full_path):
            return None
        
        try:
            with open(full_path, "r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading file {full_path}: {e}")
            return None
    
    def add_file_to_project(self, project_name: str, file_path: str, content: str) -> Optional[Dict[str, Any]]:
        """
        Add or update a file in a project.
        
        Args:
            project_name: The name of the project.
            file_path: The path to the file relative to the project directory.
            content: The content of the file.
            
        Returns:
            The file information dictionary or None if the project was not found.
        """
        if project_name not in self.projects:
            return None
        
        project_path = self.projects[project_name]["path"]
        full_path = os.path.join(project_path, file_path)
        
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        with open(full_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        file_info = {
            "name": os.path.basename(file_path),
            "path": file_path,
            "full_path": full_path
        }
        
        self.projects[project_name]["files"] = self._get_files(project_path)
        
        return self._normalize_paths(file_info)
    
    def delete_file(self, project_name: str, file_path: str) -> bool:
        """
        Delete a file from a project.
        
        Args:
            project_name: The name of the project.
            file_path: The path to the file relative to the project directory.
            
        Returns:
            True if the file was deleted, False otherwise.
        """
        if project_name not in self.projects:
            return False
        
        project_path = self.projects[project_name]["path"]
        full_path = os.path.join(project_path, file_path)
        
        if not os.path.isfile(full_path):
            return False
        
        try:
            os.remove(full_path)
            self.projects[project_name]["files"] = self._get_files(project_path)
            return True
        except Exception as e:
            logger.error(f"Error deleting file {full_path}: {e}")
            return False
    
    def get_project_messages(self, project_name: str) -> List[Dict[str, Any]]:
        """
        Get all messages for a project.
        """
        if project_name not in self.messages:
            # Try loading from disk if not in memory
            if project_name in self.projects:
                project_path = self.projects[project_name]["path"]
                messages_path = os.path.join(project_path, "messages.json")
                if os.path.isfile(messages_path):
                    try:
                        with open(messages_path, "r", encoding="utf-8") as f:
                            self.messages[project_name] = json.load(f)
                    except Exception as e:
                        logger.error(f"Error loading messages for {project_name}: {e}")
                        self.messages[project_name] = []
                else:
                    self.messages[project_name] = []
            else:
                return []
        return self.messages[project_name]
    
    def add_message_from_user(self, project_name: str, content: str) -> Dict[str, Any]:
        """
        Add a message from the user to a project.
        """
        if project_name not in self.messages:
            self.messages[project_name] = []
        message = {
            "content": content,
            "is_user": True,
            "timestamp": str(datetime.now())
        }
        self.messages[project_name].append(message)
        self._save_messages(project_name)
        return message
    
    def add_message_from_agent(self, project_name: str, content: str) -> Dict[str, Any]:
        """
        Add a message from the agent to a project.
        """
        if project_name not in self.messages:
            self.messages[project_name] = []
        message = {
            "content": content,
            "is_user": False,
            "timestamp": str(datetime.now())
        }
        self.messages[project_name].append(message)
        self._save_messages(project_name)
        return message
    
    def get_project_dir(self, project_name: str) -> Optional[str]:
        """
        Get the directory of a project.
        
        Args:
            project_name: The name of the project.
            
        Returns:
            The project directory or None if not found.
        """
        if project_name not in self.projects:
            return None
        
        return self.projects[project_name]["path"]
    
    def _save_messages(self, project_name: str):
        """Save messages for a project to disk."""
        if project_name in self.projects:
            project_path = self.projects[project_name]["path"]
            messages_path = os.path.join(project_path, "messages.json")
            try:
                with open(messages_path, "w", encoding="utf-8") as f:
                    json.dump(self.messages.get(project_name, []), f, ensure_ascii=False, indent=2)
            except Exception as e:
                logger.error(f"Error saving messages for {project_name}: {e}")
    
    def delete_all_projects(self) -> bool:
        """
        Delete all projects and their folders.
        Returns True if all were deleted, False otherwise.
        """
        all_deleted = True
        for name in list(self.projects.keys()):
            if not self.delete_project(name):
                all_deleted = False
        return all_deleted
    
    def update_project_messages(self, project_name: str, messages: List[Dict[str, Any]]) -> bool:
        """
        Update all messages for a project.
        
        Args:
            project_name: The name of the project.
            messages: The list of messages to save.
            
        Returns:
            True if successful, False otherwise.
        """
        if project_name not in self.projects:
            return False
        
        self.messages[project_name] = messages
        self._save_messages(project_name)
        return True
    
    def update_project_metadata(self, project_name: str, metadata: Dict[str, Any]) -> bool:
        """
        Update project metadata.
        
        Args:
            project_name: The name of the project.
            metadata: Metadata to update (key-value pairs).
            
        Returns:
            True if successful, False otherwise.
        """
        if project_name not in self.projects:
            return False
        
        # Update project metadata
        for key, value in metadata.items():
            self.projects[project_name][key] = value
        
        # Save to metadata.json
        project_path = self.projects[project_name]["path"]
        metadata_path = os.path.join(project_path, "metadata.json")
        
        try:
            # Create a clean version without paths for saving
            metadata_to_save = {k: v for k, v in self.projects[project_name].items() if k != "path" and k != "files"}
            
            # Add the new metadata
            for key, value in metadata.items():
                metadata_to_save[key] = value
                
            with open(metadata_path, "w", encoding="utf-8") as f:
                json.dump(metadata_to_save, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving metadata for {project_name}: {e}")
            return False

    def get_projects_dir(self) -> str:
        """
        Get the base directory for all projects.
        
        Returns:
            The base directory path
        """
        return self.base_dir
