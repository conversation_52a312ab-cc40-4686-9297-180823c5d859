# Autonomous AI Software Development Agent - Installation Guide

## System Requirements
- Windows 10/11 or Linux/macOS
- Python 3.8+ with pip
- Node.js 16+ with npm
- Visual Studio Code (Community Edition)
- 4GB RAM minimum, 8GB recommended
- 10GB free disk space

## Windows Installation
1. Extract the zip file to a location of your choice (e.g., C:\AutonomousAI)
2. Open Command Prompt as Administrator
3. Navigate to the extracted folder: `cd C:\AutonomousAI`
4. Run the setup script: `setup.bat`
5. Wait for the installation to complete (this may take several minutes)
6. Start the application: `start_application.bat`
7. Open your browser and navigate to: http://localhost:4200

## Linux/macOS Installation
1. Extract the zip file to a location of your choice (e.g., ~/AutonomousAI)
2. Open Terminal
3. Navigate to the extracted folder: `cd ~/AutonomousAI`
4. Make the scripts executable: `chmod +x setup.sh start_application.sh`
5. Run the setup script: `./setup.sh`
6. Wait for the installation to complete (this may take several minutes)
7. Start the application: `./start_application.sh`
8. Open your browser and navigate to: http://localhost:4200

## Configuration
1. After starting the application, go to the Configuration page
2. Enter your API keys for the following services:
   - OpenAI API Key
   - Google Search API Key and Search Engine ID
   - (Optional) Ollama API endpoint
   - (Optional) LM Studio API endpoint
3. Click "Save Configuration" and "Test Connection" to verify

## Using the Application
1. Create a new project from the Projects page
2. Enter a project name and description
3. Once the project is created, you can:
   - Chat with the AI Agent to request tasks
   - View and edit files in the integrated editor
   - Preview web applications in the browser preview
   - Test your code with the autonomous testing features

## Troubleshooting
- If the application fails to start, check the logs in the `logs` directory
- Ensure all API keys are correctly configured
- Verify that Python and Node.js are properly installed and in your PATH
- For VS Code integration issues, ensure VS Code is installed and accessible from the command line

## Support
For additional help, please refer to the documentation or contact support.
