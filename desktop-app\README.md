# Autonomous AI Software Development Agent

An advanced AI software development agent that autonomously creates complete projects based on high-level human instructions. This agent can generate all source code, configuration files, and documentation needed for fully functional applications.

## Features

- **Autonomous Project Creation**: Generate entire projects with all necessary files and directories
- **Real-time Feedback**: See changes instantly in the embedded browser preview
- **Integrated Development Environment**: Code editor and browser preview side by side
- **Multiple LLM Provider Support**: Connect to OpenAI, Ollama, or LM Studio
- **Local-only Operation**: All components run locally with no hidden cloud dependencies
- **Documentation Generation**: Create comprehensive documentation for your projects
- **Visual Studio Code Integration**: Seamlessly open and edit files in VS Code
- **SQLite Database**: Embedded database for storing project information
- **Offline Support**: Handles network blockages with graceful fallbacks
- **Google Search Integration**: Powerful search capabilities for autonomous operations

## System Requirements

- Windows 10/11 or macOS 10.15+ or Linux (Ubuntu 20.04+)
- Visual Studio Code (free version)
- 50GB free disk space for application and generated projects
- 8GB RAM minimum (16GB recommended)
- Internet connection for external API calls (optional, can work offline)

## Quick Start

### Windows

1. Extract the zip file to any location
2. Run `setup.bat` to install the application
3. Edit the `config.json` file in `C:\SourceProjects\AutonomousAI` to add your API keys
4. Run `start.bat` in `C:\SourceProjects\AutonomousAI` to launch the application

### macOS/Linux

1. Extract the zip file to any location
2. Run `setup.sh` to install the application
3. Edit the `config.json` file in `~/SourceProjects/AutonomousAI` to add your API keys
4. Run `start.sh` in `~/SourceProjects/AutonomousAI` to launch the application

## Configuration

The application uses a `config.json` file with the following settings:

```json
{
  "openai_api_key": "your_api_key_here",
  "use_google_search": true,
  "sqlite_path": "C:\\SourceProjects\\AutonomousAI\\data\\agent.db",
  "log_path": "C:\\SourceProjects\\AutonomousAI\\logs",
  "vscode_path": "C:\\Program Files\\Microsoft VS Code\\Code.exe"
}
```

- `openai_api_key`: Your OpenAI API key (optional)
- `use_google_search`: Whether to use Google Search API for web searches
- `sqlite_path`: Path to the SQLite database file
- `log_path`: Directory for log files
- `vscode_path`: Path to VS Code executable

## Usage

1. Launch the application using the start script
2. VS Code will open automatically with the project
3. The browser interface will be available at http://localhost:4200
4. Create a new project or open an existing one
5. Enter your requirements in the chat interface
6. The AI agent will generate the project files
7. View and edit files directly in VS Code or the embedded editor
8. See real-time updates in the browser preview

## Offline Mode

The application can work offline with the following limitations:
- External API calls (OpenAI, etc.) will not be available
- Internet-based searches will not work
- Local LLM models (via Ollama or LM Studio) can still be used if configured

## Troubleshooting

- **Application won't start**: Check that VS Code is installed and the path in config.json is correct
- **API errors**: Verify your API keys in config.json
- **Database errors**: Ensure the data directory exists and is writable
- **VS Code integration issues**: Make sure VS Code is installed and accessible from the command line

## License

MIT
