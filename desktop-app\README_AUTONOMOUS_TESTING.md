# Autonomous Testing for AI Software Development Agent

This document explains how to use the autonomous testing functionality of the AI Software Development Agent.

## Overview

The autonomous testing system automatically runs tests, detects errors, fixes them, and validates functionality in the browser. It integrates with Visual Studio Code to provide a seamless development and testing experience.

## Features

- **Automated Test Execution**: Automatically runs tests for your projects
- **Error Detection and Fixing**: Detects errors in your code and attempts to fix them automatically
- **Browser Validation**: Validates functionality in the browser after fixing errors
- **VS Code Integration**: Integrates with Visual Studio Code for a seamless development experience
- **Terminal Integration**: Runs commands in the terminal and captures output
- **Continuous Testing**: Continuously runs tests as you make changes to your code

## Installation

The autonomous testing functionality is included in the main application. To set it up:

1. Run the setup script:
   ```
   # On Windows
   .\scripts\setup.bat

   # On Linux/macOS
   ./scripts/setup.sh
   ```

2. Configure the application by editing the `config.json` file:
   ```json
   {
     "openai_api_key": "your-api-key",
     "use_google_search": true,
     "sqlite_path": "C:\\SourceProjects\\AutonomousAI\\data\\agent.db",
     "log_path": "C:\\SourceProjects\\AutonomousAI\\logs",
     "vscode_path": "C:\\Program Files\\Microsoft VS Code\\Code.exe"
   }
   ```

## Usage

### Running Tests

To run tests for a project:

```bash
# On Windows
python scripts\run_tests.py --project-dir C:\SourceProjects\AutonomousAI\my-project

# On Linux/macOS
python scripts/run_tests.py --project-dir ~/SourceProjects/AutonomousAI/my-project
```

### Opening a Project in VS Code

To open a project in VS Code:

```bash
# On Windows
python scripts\run_in_vscode.py --project-dir C:\SourceProjects\AutonomousAI\my-project

# On Linux/macOS
python scripts/run_in_vscode.py --project-dir ~/SourceProjects/AutonomousAI/my-project
```

### Running the Demo

To run the demonstration script:

```bash
# On Windows
python scripts\demo_autonomous_testing.py --project-dir C:\SourceProjects\AutonomousAI\demo

# On Linux/macOS
python scripts/demo_autonomous_testing.py --project-dir ~/SourceProjects/AutonomousAI/demo
```

## Integration with Main Application

The autonomous testing functionality is integrated with the main application. When you create a project using the main application, you can run tests for it using the autonomous testing system.

To run tests for a project created with the main application:

1. Open the project in the main application
2. Click on the "Run Tests" button
3. The autonomous testing system will run tests, fix errors, and validate functionality

## Configuration

The autonomous testing system can be configured by editing the `config.json` file. The following options are available:

```json
{
  "testing": {
    "test_command": "pytest",
    "test_args": ["-v"],
    "linting_command": "flake8",
    "linting_args": [],
    "max_retries": 3,
    "retry_delay": 2,
    "auto_fix": true,
    "test_timeout": 60,
    "browser_validation": true,
    "browser_validation_timeout": 10
  },
  "vscode": {
    "path": "code",
    "extensions": [
      "ms-python.python",
      "dbaeumer.vscode-eslint",
      "esbenp.prettier-vscode",
      "ms-vscode.vscode-typescript-tslint-plugin",
      "ritwickdey.liveserver"
    ],
    "settings": {
      "editor.formatOnSave": true,
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": true
      },
      "python.linting.enabled": true,
      "python.linting.pylintEnabled": true
    }
  }
}
```

## Directory Structure

The autonomous testing system is organized as follows:

```
desktop-app/
├── backend/
│   ├── autonomous_testing.py
│   ├── browser_integration.py
│   ├── integration.py
│   ├── terminal_integration.py
│   ├── test_runner.py
│   └── vscode_integration.py
├── scripts/
│   ├── demo_autonomous_testing.py
│   ├── run_in_vscode.py
│   └── run_tests.py
└── config.json
```

## How It Works

1. The `test_runner.py` module runs tests for a project and detects errors
2. If errors are found, it attempts to fix them automatically
3. After fixing errors, it runs tests again to verify the fixes
4. If the tests pass, it validates functionality in the browser
5. The `integration.py` module ties everything together
6. The `autonomous_testing.py` module provides high-level autonomous testing functionality

## Example

Here's an example of how to use the autonomous testing system in your own code:

```python
from backend.integration import Integration
from backend.autonomous_testing import AutonomousTesting

# Create integration components
integration = Integration()
autonomous_testing = AutonomousTesting(integration=integration)

# Run tests
test_results = autonomous_testing.run_autonomous_testing(
    project_dir="C:\\SourceProjects\\AutonomousAI\\my-project",
    test_dir="tests",
    browser_url="http://localhost:4200"
)

# Print test results
print(f"Test results: {test_results['success']}")
```

## Troubleshooting

If you encounter issues with the autonomous testing system:

1. Check the log files in the `logs` directory
2. Make sure VS Code is installed and accessible
3. Verify that the project directory exists and contains tests
4. Check that the browser is installed and accessible
5. Ensure that the required dependencies are installed

## Conclusion

The autonomous testing system provides a powerful way to automatically test your projects, fix errors, and validate functionality. It integrates with Visual Studio Code to provide a seamless development and testing experience.
