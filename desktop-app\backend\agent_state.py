"""
Agent State for the Autonomous AI Software Development Agent.
"""
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class AgentState:
    """
    Manages the state of agents across projects.
    """
    def __init__(self):
        """Initialize the AgentState."""
        print("[DEBUG] Initializing AgentState")
        self.active_agents = {}
        self.completed_agents = {}
        logger.info("Initialized AgentState")
        print("[DEBUG] AgentState initialized with empty active_agents and completed_agents")

    def is_agent_active(self, project_name: str) -> bool:
        """
        Check if an agent is active for a project.
        
        Args:
            project_name: The name of the project.
            
        Returns:
            True if an agent is active, False otherwise.
        """
        print(f"[DEBUG] Checking if agent is active for project: {project_name}")
        active = self.active_agents.get(project_name, False)
        print(f"[DEBUG] Agent active state for project {project_name}: {active}")
        return active

    def set_agent_active(self, project_name: str, active: bool):
        """
        Set the active state of an agent for a project.
        
        Args:
            project_name: The name of the project.
            active: Whether the agent is active.
        """
        print(f"[DEBUG] Setting agent active state for project {project_name} to {active}")
        self.active_agents[project_name] = active
        logger.info(f"Set agent active state for project {project_name}: {active}")
        print(f"[DEBUG] Updated active_agents: {self.active_agents}")

    def is_agent_completed(self, project_name: str) -> bool:
        """
        Check if an agent has completed for a project.
        
        Args:
            project_name: The name of the project.
            
        Returns:
            True if an agent has completed, False otherwise.
        """
        print(f"[DEBUG] Checking if agent has completed for project: {project_name}")
        completed = self.completed_agents.get(project_name, False)
        print(f"[DEBUG] Agent completed state for project {project_name}: {completed}")
        return completed

    def set_agent_completed(self, project_name: str, completed: bool):
        """
        Set the completed state of an agent for a project.
        
        Args:
            project_name: The name of the project.
            completed: Whether the agent has completed.
        """
        print(f"[DEBUG] Setting agent completed state for project {project_name} to {completed}")
        self.completed_agents[project_name] = completed
        logger.info(f"Set agent completed state for project {project_name}: {completed}")
        print(f"[DEBUG] Updated completed_agents: {self.completed_agents}")

    def reset_agent_state(self, project_name: str):
        """
        Reset the state of an agent for a project.
        
        Args:
            project_name: The name of the project.
        """
        print(f"[DEBUG] Resetting agent state for project: {project_name}")
        self.active_agents[project_name] = False
        self.completed_agents[project_name] = False
        logger.info(f"Reset agent state for project {project_name}")
        print(f"[DEBUG] Reset active_agents and completed_agents for project {project_name}")
