import os
import sys
import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
from socketio import AsyncServer
import socketio

from fastapi import APIRouter, HTTPException, Depends, Request, FastAPI
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from integration import Integration
from autonomous_testing import AutonomousTesting
from vscode_integration import VSCodeIntegration

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('api_endpoints.log')
    ]
)
logger = logging.getLogger('APIEndpoints')

testing_router = APIRouter(prefix="/api/testing", tags=["testing"])
chat_router = APIRouter(prefix="/api/chat", tags=["chat"])
integration = Integration()
autonomous_testing = AutonomousTesting(integration=integration)

app = FastAPI()
api_router = APIRouter()
api_router.include_router(testing_router)
api_router.include_router(chat_router)
app.include_router(api_router)

# Load configuration
config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.json")
config = {}
if os.path.exists(config_path):
    with open(config_path, "r") as f:
        config = json.load(f)

class TestRequest(BaseModel):
    """Test request model."""
    project_dir: str
    test_dir: Optional[str] = None
    test_file: Optional[str] = None
    test_name: Optional[str] = None
    browser_url: Optional[str] = None
    auto_fix: Optional[bool] = None
    auto_validate: Optional[bool] = None

class VSCodeRequest(BaseModel):
    """VS Code request model."""
    project_dir: str
    file_path: Optional[str] = None
    line: Optional[int] = None
    column: Optional[int] = None
    install_extensions: Optional[bool] = None
    setup_workspace: Optional[bool] = None

class DemoRequest(BaseModel):
    """Demo request model."""
    project_dir: str
class ChatRequest(BaseModel):
    """Chat request model."""
    messages: List[Dict[str, str]]
    model_id: str

@chat_router.post("")
async def chat(chat_request: ChatRequest) -> Dict[str, Any]:
    """
    Chat with AI models.
    """
    logger.info("[CHAT] Received chat request")
    print("[CHAT] Received chat request")  # Console log
    logger.debug(f"[CHAT] Payload received: model_id={chat_request.model_id}, messages={chat_request.messages}")
    print(f"[CHAT] Payload received: model_id={chat_request.model_id}, messages={chat_request.messages}")  # Console log

    try:
        provider, model = chat_request.model_id.split("/")
        logger.debug(f"[CHAT] Parsed model provider: {provider}, model: {model}")
        print(f"[CHAT] Parsed model provider: {provider}, model: {model}")  # Console log

        if provider == "ollama":
            logger.info("[CHAT] Routing to Ollama")
            print("[CHAT] Routing to Ollama")  # Console log
            response = integration.ollama.chat(
                messages=chat_request.messages,
                model=model
            )

        elif provider == "lm_studio":
            logger.info("[CHAT] Routing to LM Studio")
            print("[CHAT] Routing to LM Studio")  # Console log
            response = integration.lm_studio.chat(
                messages=chat_request.messages,
                model=model
            )

        elif provider == "openai":
            logger.info("[CHAT] Routing to OpenAI")
            print("[CHAT] Routing to OpenAI")  # Console log
            if not config.get("openai_api_key"):
                logger.error("[CHAT] Missing OpenAI API key in config")
                print("[CHAT] Missing OpenAI API key in config")  # Console log
                raise HTTPException(
                    status_code=400,
                    detail="OpenAI API key not configured"
                )
            try:
                response = integration.openai.chat(
                    messages=chat_request.messages,
                    model=model
                )
            except Exception as e:
                logger.exception("[CHAT] OpenAI integration failed")
                print(f"[CHAT] OpenAI integration failed: {str(e)}")  # Console log
                raise HTTPException(
                    status_code=500,
                    detail=f"Error in OpenAI chat: {str(e)}"
                )

        else:
            logger.error(f"[CHAT] Unsupported provider: {provider}")
            print(f"[CHAT] Unsupported provider: {provider}")  # Console log
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported model provider: {provider}"
            )

        logger.info("[CHAT] Response generated successfully")
        print("[CHAT] Response generated successfully")  # Console log

        return {
            "success": True,
            "message": response,
            "model": chat_request.model_id
        }

    except Exception as e:
        logger.exception("[CHAT] Unhandled exception in chat endpoint")
        print(f"[CHAT] Unhandled exception in chat endpoint: {str(e)}")  # Console log
        raise HTTPException(
            status_code=500,
            detail=f"Error processing chat request: {str(e)}"
        )



@testing_router.post("/run")
async def run_tests(test_request: TestRequest) -> Dict[str, Any]:
    """
    Run autonomous tests for a project.
    
    Args:
        test_request: Test request
        
    Returns:
        Dict containing test results
    """
    logger.info(f"Running tests for project: {test_request.project_dir}")
    
    if test_request.auto_fix is not None:
        autonomous_testing.config["autonomous_testing"]["auto_fix"] = test_request.auto_fix
    if test_request.auto_validate is not None:
        autonomous_testing.config["autonomous_testing"]["auto_validate"] = test_request.auto_validate
    
    try:
        test_results = autonomous_testing.run_autonomous_testing(
            project_dir=test_request.project_dir,
            test_dir=test_request.test_dir,
            browser_url=test_request.browser_url
        )
        
        return {
            "success": test_results["success"],
            "message": "Tests completed",
            "results": test_results
        }
    except Exception as e:
        logger.error(f"Error running tests: {e}")
        raise HTTPException(status_code=500, detail=f"Error running tests: {str(e)}")

@testing_router.post("/continuous")
async def start_continuous_testing(test_request: TestRequest) -> Dict[str, Any]:
    """
    Start continuous testing for a project.
    
    Args:
        test_request: Test request
        
    Returns:
        Dict containing status
    """
    logger.info(f"Starting continuous testing for project: {test_request.project_dir}")
    
    try:
        autonomous_testing.start_continuous_testing(
            project_dir=test_request.project_dir,
            test_dir=test_request.test_dir,
            browser_url=test_request.browser_url
        )
        
        return {
            "success": True,
            "message": "Continuous testing started"
        }
    except Exception as e:
        logger.error(f"Error starting continuous testing: {e}")
        raise HTTPException(status_code=500, detail=f"Error starting continuous testing: {str(e)}")

@testing_router.post("/vscode")
async def open_in_vscode(vscode_request: VSCodeRequest) -> Dict[str, Any]:
    """
    Open a project in VS Code.
    
    Args:
        vscode_request: VS Code request
        
    Returns:
        Dict containing status
    """
    logger.info(f"Opening project in VS Code: {vscode_request.project_dir}")
    
    try:
        if vscode_request.file_path:
            integration.vscode.open_file(
                os.path.join(vscode_request.project_dir, vscode_request.file_path),
                vscode_request.line,
                vscode_request.column
            )
        else:
            integration.vscode.open_folder(vscode_request.project_dir)
        
        if vscode_request.install_extensions:
            integration.vscode.install_recommended_extensions()
        
        if vscode_request.setup_workspace:
            integration.vscode.setup_project(vscode_request.project_dir)
        
        return {
            "success": True,
            "message": "Project opened in VS Code"
        }
    except Exception as e:
        logger.error(f"Error opening project in VS Code: {e}")
        raise HTTPException(status_code=500, detail=f"Error opening project in VS Code: {str(e)}")

@testing_router.post("/demo")
async def run_demo(demo_request: DemoRequest) -> Dict[str, Any]:
    """
    Run the autonomous testing demo.
    
    Args:
        demo_request: Demo request
        
    Returns:
        Dict containing status
    """
    logger.info(f"Running demo for project: {demo_request.project_dir}")
    
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from scripts.demo_autonomous_testing import create_sample_project, introduce_error
        
        create_sample_project(demo_request.project_dir)
        
        integration.vscode.open_folder(demo_request.project_dir)
        
        test_results = autonomous_testing.run_autonomous_testing(
            project_dir=demo_request.project_dir,
            test_dir="tests",
            browser_url=f"file://{os.path.abspath(os.path.join(demo_request.project_dir, 'index.html'))}"
        )
        
        introduce_error(demo_request.project_dir)
        
        test_results_with_error = autonomous_testing.run_autonomous_testing(
            project_dir=demo_request.project_dir,
            test_dir="tests",
            browser_url=f"file://{os.path.abspath(os.path.join(demo_request.project_dir, 'index.html'))}"
        )
        
        integration.browser.open_url(f"file://{os.path.abspath(os.path.join(demo_request.project_dir, 'index.html'))}")
        
        return {
            "success": True,
            "message": "Demo completed successfully",
            "initial_test_results": test_results,
            "test_results_with_error": test_results_with_error
        }
    except Exception as e:
        logger.error(f"Error running demo: {e}")
        raise HTTPException(status_code=500, detail=f"Error running demo: {str(e)}")

@testing_router.get("/config")
async def get_config() -> Dict[str, Any]:
    """
    Get autonomous testing configuration.
    
    Returns:
        Dict containing configuration
    """
    return {
        "testing": autonomous_testing.config["autonomous_testing"],
        "vscode": integration.config["vscode"] if "vscode" in integration.config else None
    }

@testing_router.put("/config")
async def update_config(request: Request) -> Dict[str, Any]:
    """
    Update autonomous testing configuration.
    
    Args:
        request: Request
        
    Returns:
        Dict containing status
    """
    try:
        body = await request.json()
        
        if "testing" in body:
            autonomous_testing.config["autonomous_testing"].update(body["testing"])
        
        if "vscode" in body and "vscode" in integration.config:
            integration.config["vscode"].update(body["vscode"])
        
        return {
            "success": True,
            "message": "Configuration updated successfully",
            "config": {
                "testing": autonomous_testing.config["autonomous_testing"],
                "vscode": integration.config["vscode"] if "vscode" in integration.config else None
            }
        }
    except Exception as e:
        logger.error(f"Error updating configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating configuration: {str(e)}")

@testing_router.post("/stop")
async def stop_testing(test_request: TestRequest) -> Dict[str, Any]:
    """
    Stop ongoing tests for a project.
    
    Args:
        test_request: Test request
        
    Returns:
        Dict containing status
    """
    logger.info(f"Stopping testing for project: {test_request.project_dir}")
    
    try:
        autonomous_testing.stop_testing(
            project_dir=test_request.project_dir
        )
        
        return {
            "success": True,
            "message": "Testing stopped"
        }
    except Exception as e:
        logger.error(f"Error stopping testing: {e}")
        raise HTTPException(status_code=500, detail=f"Error stopping testing: {str(e)}")
