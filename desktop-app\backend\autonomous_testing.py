"""
Autonomous Testing Module for AI Software Development Agent.

This module provides functionality for autonomously testing projects,
including running tests, fixing errors, and validating functionality.
"""

import os
import sys
import json
import logging
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union, Callable

from integration import Integration

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('autonomous_testing.log')
    ]
)
logger = logging.getLogger('AutonomousTesting')

class AutonomousTesting:
    """
    Handles autonomous testing of projects.
    """
    
    def __init__(self, config_path: Optional[str] = None, integration: Optional[Integration] = None):
        """
        Initialize the autonomous testing.
        
        Args:
            config_path: Path to the configuration file
            integration: Integration instance
        """
        self.config = self._load_config(config_path)
        self.integration = integration or Integration(config_path)
        
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "autonomous_testing": {
                "max_retries": 3,
                "retry_delay": 5,  # seconds
                "test_timeout": 60,  # seconds
                "validation_timeout": 10,  # seconds
                "auto_fix": True,
                "auto_validate": True,
                "continuous_testing": False,
                "continuous_testing_interval": 30,  # seconds
                "notify_on_success": True,
                "notify_on_failure": True,
                "open_failed_files_in_vscode": True
            }
        }
        
        if not config_path:
            parent_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = parent_dir.parent / "config.json"
        
        if os.path.exists(config_path):
            try:
                with open(config_path, "r") as f:
                    user_config = json.load(f)
                    if "autonomous_testing" in user_config:
                        default_config["autonomous_testing"].update(user_config["autonomous_testing"])
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def run_autonomous_testing(self, project_dir: str, test_dir: Optional[str] = None,
                              browser_url: Optional[str] = None,
                              callback: Optional[Callable[[str, Dict[str, Any]], None]] = None) -> Dict[str, Any]:
        """
        Run autonomous testing for a project.
        
        Args:
            project_dir: Path to the project directory
            test_dir: Path to the test directory (relative to project_dir)
            browser_url: URL to validate in browser
            callback: Callback function to receive test output and results
            
        Returns:
            Dict containing test results
        """
        logger.info(f"Running autonomous testing for project: {project_dir}")
        
        test_cycle_results = self.integration.test_runner.run_full_test_cycle(
            project_dir=project_dir,
            test_dir=test_dir,
            browser_url=browser_url,
            callback=callback
        )
        
        if test_cycle_results["success"]:
            logger.info("Tests passed")
            
            if self.config["autonomous_testing"]["notify_on_success"]:
                self._notify("Tests passed", "All tests passed successfully")
        else:
            logger.warning("Tests failed")
            
            retries = 0
            while not test_cycle_results["success"] and retries < self.config["autonomous_testing"]["max_retries"]:
                retries += 1
                logger.info(f"Retrying tests (attempt {retries}/{self.config['autonomous_testing']['max_retries']})")
                
                time.sleep(self.config["autonomous_testing"]["retry_delay"])
                
                test_cycle_results = self.integration.test_runner.run_full_test_cycle(
                    project_dir=project_dir,
                    test_dir=test_dir,
                    browser_url=browser_url,
                    callback=callback
                )
                
                if test_cycle_results["success"]:
                    logger.info("Tests passed")
                    
                    if self.config["autonomous_testing"]["notify_on_success"]:
                        self._notify("Tests passed", "All tests passed successfully")
                    
                    break
            
            if not test_cycle_results["success"] and self.config["autonomous_testing"]["notify_on_failure"]:
                self._notify("Tests failed", "Tests failed after multiple attempts")
                
                if self.config["autonomous_testing"]["open_failed_files_in_vscode"]:
                    self._open_failed_files_in_vscode(test_cycle_results)
        
        return test_cycle_results
    
    def _notify(self, title: str, message: str) -> None:
        """
        Show a notification.
        
        Args:
            title: Notification title
            message: Notification message
        """
        logger.info(f"Notification: {title} - {message}")
    
    def _open_failed_files_in_vscode(self, test_results: Dict[str, Any]) -> None:
        """
        Open failed files in VS Code.
        
        Args:
            test_results: Test results
        """
        if not self.integration.vscode:
            logger.error("VS Code integration is not available")
            return
        
        if "testing" in test_results and "errors" in test_results["testing"]:
            errors = test_results["testing"]["errors"]
            
            for error in errors:
                if "file" in error and "line" in error:
                    self.integration.vscode.open_file(error["file"], error["line"])
    
    def start_continuous_testing(self, project_dir: str, test_dir: Optional[str] = None,
                                browser_url: Optional[str] = None,
                                interval: Optional[int] = None,
                                callback: Optional[Callable[[str, Dict[str, Any]], None]] = None) -> None:
        """
        Start continuous testing for a project.
        
        Args:
            project_dir: Path to the project directory
            test_dir: Path to the test directory (relative to project_dir)
            browser_url: URL to validate in browser
            interval: Interval between test cycles in seconds
            callback: Callback function to receive test output and results
        """
        if interval is None:
            interval = self.config["autonomous_testing"]["continuous_testing_interval"]
        
        logger.info(f"Starting continuous testing for project: {project_dir} with interval {interval} seconds")
        
        self.integration.test_runner.run_continuous_testing(
            project_dir=project_dir,
            test_dir=test_dir,
            browser_url=browser_url,
            interval=interval,
            callback=callback
        )
    
    def run_tests_in_vscode(self, project_dir: str, test_dir: Optional[str] = None,
                           test_file: Optional[str] = None, test_name: Optional[str] = None) -> bool:
        """
        Run tests in VS Code.
        
        Args:
            project_dir: Path to the project directory
            test_dir: Path to the test directory (relative to project_dir)
            test_file: Path to a specific test file (relative to test_dir)
            test_name: Name of a specific test to run
            
        Returns:
            True if the tests were run successfully, False otherwise
        """
        logger.info(f"Running tests in VS Code for project: {project_dir}")
        
        if not self.integration.vscode:
            logger.error("VS Code integration is not available")
            return False
        
        return self.integration.test_runner.run_tests_in_vscode_terminal(
            project_dir=project_dir,
            test_dir=test_dir,
            test_file=test_file,
            test_name=test_name
        )
    
    def stop_testing(self, project_dir: str) -> None:
        """
        Stop ongoing tests for a project.
        
        Args:
            project_dir: Path to the project directory
        """
        logger.info(f"Stopping tests for project: {project_dir}")
        
        if "running" not in self.config["autonomous_testing"]:
            self.config["autonomous_testing"]["running"] = False
        else:
            self.config["autonomous_testing"]["running"] = False
        
        if hasattr(self.integration, "terminal") and hasattr(self.integration.terminal, "kill_all_processes"):
            self.integration.terminal.kill_all_processes()
        
        if self.config["autonomous_testing"].get("continuous_testing", False):
            self.config["autonomous_testing"]["continuous_testing"] = False
            logger.info("Continuous testing stopped")
    
    def autonomous_development_and_testing(self, project_name: str, requirements: str) -> Dict[str, Any]:
        """
        Autonomously develop and test a project based on requirements.
        
        Args:
            project_name: Name of the project
            requirements: Project requirements
            
        Returns:
            Dict containing development and testing results
        """
        logger.info(f"Autonomously developing and testing project: {project_name}")
        
        development_results = self.integration.autonomous_development(project_name, requirements)
        
        if not development_results["success"]:
            logger.error("Development failed")
            return development_results
        
        project_dir = os.path.join(self.integration.project_dir, project_name)
        
        testing_results = self.run_autonomous_testing(
            project_dir=project_dir,
            browser_url="http://localhost:4200"
        )
        
        results = {
            "development": development_results,
            "testing": testing_results,
            "success": development_results["success"] and testing_results["success"],
            "message": "Development and testing completed"
        }
        
        return results
