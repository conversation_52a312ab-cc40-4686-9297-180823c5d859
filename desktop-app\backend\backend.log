2025-04-19 16:52:28,260 - VSCodeIntegration - WARNING - Could not find VS Code executable, using default command 'code'
/home/<USER>/.pyenv/versions/3.12.8/lib/python3.12/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_id" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/home/<USER>/implementation/desktop-app/backend/main.py:61: MovedIn20Warning: The ``declarative_base()`` function is now available as sqlalchemy.orm.declarative_base(). (deprecated since: 2.0) (Background on SQLAlchemy 2.0 at: https://sqlalche.me/e/b8d9)
  Base = declarative_base()
[DEBUG] Initializing TerminalIntegration with config_path: None
[DEBUG] Loading configuration from: None
[DEBUG] Default config path set to: /home/<USER>/implementation/config.json
[DEBUG] Config file exists at: /home/<USER>/implementation/config.json
[DEBUG] Loaded user config: {'openai': {'api_key': '********************************************************************************************************************************************************************'}, 'google': {'api_key': 'AIzaSyCWE4vs1WwSmMDgl6oYjpfqEnkCGpJKPY0', 'search_engine_id': '753e6a85c0f964d03'}, 'gemini': {'api_key': 'AIzaSyCNWEUVh734_2ULDA9eM3f5BXrDQJvjjGs', 'rate_limit': 2}, 'ollama': {'base_url': 'http://localhost:11434'}, 'lm_studio': {'base_url': 'http://localhost:1234/v1'}, 'timeout': {'inference': 2400000}}
[DEBUG] Loaded config: {'terminal': {'max_output_lines': 1000, 'command_timeout': 60, 'shell': True, 'encoding': 'utf-8'}}
[DEBUG] TerminalIntegration initialized
[DEBUG] Initializing BrowserIntegration with config_path: None
[DEBUG] Loading configuration from: None
[DEBUG] Default config path set to: /home/<USER>/implementation/config.json
[DEBUG] Config file exists at: /home/<USER>/implementation/config.json
[DEBUG] Loaded user config: {'openai': {'api_key': '********************************************************************************************************************************************************************'}, 'google': {'api_key': 'AIzaSyCWE4vs1WwSmMDgl6oYjpfqEnkCGpJKPY0', 'search_engine_id': '753e6a85c0f964d03'}, 'gemini': {'api_key': 'AIzaSyCNWEUVh734_2ULDA9eM3f5BXrDQJvjjGs', 'rate_limit': 2}, 'ollama': {'base_url': 'http://localhost:11434'}, 'lm_studio': {'base_url': 'http://localhost:1234/v1'}, 'timeout': {'inference': 2400000}}
[DEBUG] Loaded config: {'browser': {'preferred_browser': None, 'auto_refresh': True, 'refresh_interval': 50, 'live_server_port': 5500, 'search_engine': 'google', 'search_engines': {'google': 'https://www.google.com/search?q=', 'bing': 'https://www.bing.com/search?q=', 'yahoo': 'https://search.yahoo.com/search?p='}, 'api_endpoints': {'bing': 'https://api.bing.microsoft.com/v7.0/search', 'google': 'https://www.googleapis.com/customsearch/v1', 'ollama': 'http://127.0.0.1:11434', 'lm_studio': 'http://localhost:1234/v1', 'openai': 'https://api.openai.com/v1'}}}
[DEBUG] BrowserIntegration initialized
[DEBUG] Initializing TestRunner with config_path: None
[DEBUG] Loading configuration from: None
[DEBUG] Default config path set to: /home/<USER>/implementation/config.json
[DEBUG] Config file exists at: /home/<USER>/implementation/config.json
[DEBUG] Loaded user config: {'openai': {'api_key': '********************************************************************************************************************************************************************'}, 'google': {'api_key': 'AIzaSyCWE4vs1WwSmMDgl6oYjpfqEnkCGpJKPY0', 'search_engine_id': '753e6a85c0f964d03'}, 'gemini': {'api_key': 'AIzaSyCNWEUVh734_2ULDA9eM3f5BXrDQJvjjGs', 'rate_limit': 2}, 'ollama': {'base_url': 'http://localhost:11434'}, 'lm_studio': {'base_url': 'http://localhost:1234/v1'}, 'timeout': {'inference': 2400000}}
[DEBUG] Loaded config: {'testing': {'test_command': 'pytest', 'test_args': ['-v'], 'linting_command': 'flake8', 'linting_args': [], 'max_retries': 3, 'retry_delay': 2, 'auto_fix': True, 'test_timeout': 60, 'browser_validation': True, 'browser_validation_timeout': 10, 'error_patterns': []}}
[DEBUG] Compiling error patterns from config
[DEBUG] TestRunner initialized
[DEBUG] OpenAIClient initialized with base_url: https://api.openai.com/v1
2025-04-19 16:52:28,294 - project_manager - INFO - Initialized ProjectManager with base directory: /home/<USER>/SourceProjects/AutonomousAI/projects
[DEBUG] Initializing AgentState
2025-04-19 16:52:28,295 - agent_state - INFO - Initialized AgentState
[DEBUG] AgentState initialized with empty active_agents and completed_agents
2025-04-19 16:52:28,331 - __main__ - ERROR - Failed to open VS Code: [Errno 2] No such file or directory: 'code'
INFO:     Will watch for changes in these directories: ['/home/<USER>/implementation/desktop-app/backend']
ERROR:    [Errno 98] Address already in use
