"""
Browser Integration Module for AI Software Development Agent.

This module provides functionality for integrating with the browser,
allowing the application to open URLs and interact with web pages.
"""

import os
import sys
import json
import subprocess
import logging
import time
import threading
import webbrowser
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union, Callable

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('browser_integration.log')
    ]
)
logger = logging.getLogger('BrowserIntegration')

# Hardcoded Google API credentials
GOOGLE_SEARCH = "AIzaSyCWE4vs1WwSmMDgl6oYjpfqEnkCGpJKPY0"
GOOGLE_SEARCH_ENGINE_ID = "753e6a85c0f964d03"

class BrowserIntegration:
    """
    Handles integration with the browser.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the browser integration.
        
        Args:
            config_path: Path to the configuration file
        """
        print(f"[DEBUG] Initializing BrowserIntegration with config_path: {config_path}")
        self.config = self._load_config(config_path)
        print(f"[DEBUG] Loaded config: {self.config}")
        self.live_server_process = None
        print(f"[DEBUG] BrowserIntegration initialized")

    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        print(f"[DEBUG] Loading configuration from: {config_path}")
        default_config = {
            "browser": {
                "preferred_browser": None,  # Use default browser
                "auto_refresh": True,
                "refresh_interval": 50,  # seconds
                "live_server_port": 5500,
                "search_engine": "searxng",  # default search engine
                "search_engines": {
                    "searxng": "http://localhost:8888/search?q=",
                    "google": "https://www.google.com/search?q=",
                    "bing": "https://www.bing.com/search?q=",
                    "yahoo": "https://search.yahoo.com/search?p="
                },
                "api_endpoints": {
                    "searxng": "http://localhost:8888/search",
                    "bing": "https://api.bing.microsoft.com/v7.0/search",
                    "google": "https://www.googleapis.com/customsearch/v1",
                    "ollama": "http://127.0.0.1:11434",
                    "lm_studio": "http://localhost:1234/v1",
                    "openai": "https://api.openai.com/v1"
                }
            }
        }
        
        if not config_path:
            parent_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = parent_dir.parent / "config.json"
            print(f"[DEBUG] Default config path set to: {config_path}")
        
        if os.path.exists(config_path):
            print(f"[DEBUG] Config file exists at: {config_path}")
            try:
                with open(config_path, "r") as f:
                    user_config = json.load(f)
                    print(f"[DEBUG] Loaded user config: {user_config}")
                    if "browser" in user_config:
                        default_config["browser"].update(user_config["browser"])
                        print(f"[DEBUG] Updated default config with user config: {default_config}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
                print(f"[ERROR] Error loading config from {config_path}: {e}")
        
        return default_config

    def open_url(self, url: str, new_window: bool = False) -> bool:
        """
        Open a URL in the browser.
        
        Args:
            url: URL to open
            new_window: Whether to open in a new window
            
        Returns:
            True if the URL was opened successfully, False otherwise
        """
        print(f"[DEBUG] Attempting to open URL: {url}, new_window: {new_window}")
        try:
            browser = self.config["browser"]["preferred_browser"]
            print(f"[DEBUG] Preferred browser: {browser}")
            
            if browser:
                if browser in webbrowser._browsers:
                    browser_controller = webbrowser.get(browser)
                    print(f"[DEBUG] Using browser controller for: {browser}")
                else:
                    logger.warning(f"Browser {browser} not found, using default browser")
                    print(f"[WARNING] Browser {browser} not found, using default browser")
                    browser_controller = webbrowser
            else:
                browser_controller = webbrowser
            
            browser_controller.open(url, new=2 if new_window else 1)
            logger.info(f"Opened URL {url} in browser")
            print(f"[DEBUG] Successfully opened URL: {url}")
            return True
        except Exception as e:
            logger.error(f"Error opening URL {url} in browser: {e}")
            print(f"[ERROR] Error opening URL {url} in browser: {e}")
            return False

    def start_live_server(self, directory: str, port: Optional[int] = None) -> bool:
        """
        Start a live server for the specified directory.
        
        Args:
            directory: Directory to serve
            port: Port to use (if None, use configured port)
            
        Returns:
            True if the server was started successfully, False otherwise
        """
        print(f"[DEBUG] Starting live server for directory: {directory}, port: {port}")
        try:
            self.stop_live_server()
            print(f"[DEBUG] Stopped any existing live server")
            
            if port is None:
                port = self.config["browser"]["live_server_port"]
                print(f"[DEBUG] Using configured port: {port}")
            
            if not os.path.exists(directory):
                logger.error(f"Directory {directory} does not exist")
                print(f"[ERROR] Directory {directory} does not exist")
                return False
            
            cmd = [sys.executable, "-m", "http.server", str(port)]
            print(f"[DEBUG] Live server command: {cmd}")
            
            self.live_server_process = subprocess.Popen(
                cmd,
                cwd=directory,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            print(f"[DEBUG] Live server process started")
            
            time.sleep(1)
            
            if self.live_server_process.poll() is not None:
                error_message = self.live_server_process.stderr.read()
                logger.error(f"Error starting live server: {error_message}")
                print(f"[ERROR] Error starting live server: {error_message}")
                return False
            
            logger.info(f"Started live server for directory {directory} on port {port}")
            print(f"[DEBUG] Live server started successfully for directory: {directory}, port: {port}")
            
            if self.config["browser"]["auto_refresh"]:
                print(f"[DEBUG] Auto-refresh is enabled, starting auto-refresh thread")
                self._start_auto_refresh_thread(f"http://localhost:{port}")
            
            return True
        except Exception as e:
            logger.error(f"Error starting live server: {e}")
            print(f"[ERROR] Error starting live server: {e}")
            return False

    def stop_live_server(self) -> bool:
        """
        Stop the live server.
        
        Returns:
            True if the server was stopped successfully, False otherwise
        """
        print(f"[DEBUG] Attempting to stop live server")
        if self.live_server_process is None:
            print(f"[DEBUG] No live server process to stop")
            return True
        
        try:
            self.live_server_process.terminate()
            print(f"[DEBUG] Terminated live server process")
            
            self.live_server_process.wait(timeout=5)
            print(f"[DEBUG] Live server process terminated successfully")
            
            self.live_server_process = None
            logger.info("Stopped live server")
            print(f"[DEBUG] Live server stopped successfully")
            return True
        except Exception as e:
            logger.error(f"Error stopping live server: {e}")
            print(f"[ERROR] Error stopping live server: {e}")
            return False
    
    def _start_auto_refresh_thread(self, url: str) -> None:
        """
        Start a thread that periodically refreshes the browser.
        
        Args:
            url: URL to refresh
        """
        def refresh_browser():
            while self.live_server_process is not None and self.live_server_process.poll() is None:
                time.sleep(self.config["browser"]["refresh_interval"])
                
                try:
                    script = f"""
                    var xhr = new XMLHttpRequest();
                    xhr.open('GET', '{url}', true);
                    xhr.onreadystatechange = function() {{
                        if (xhr.readyState === 4) {{
                            location.reload();
                        }}
                    }};
                    xhr.send();
                    """
                    
                    logger.info(f"Would refresh browser at {url}")
                except Exception as e:
                    logger.error(f"Error refreshing browser: {e}")
        
        thread = threading.Thread(target=refresh_browser)
        thread.daemon = True
        thread.start()
    
    def get_live_server_url(self, port: Optional[int] = None) -> str:
        """
        Get the URL of the live server.
        
        Args:
            port: Port to use (if None, use configured port)
            
        Returns:
            URL of the live server
        """
        if port is None:
            port = self.config["browser"]["live_server_port"]
        
        return f"http://localhost:{port}"
    
    def is_live_server_running(self) -> bool:
        """
        Check if the live server is running.
        
        Returns:
            True if the live server is running, False otherwise
        """
        return self.live_server_process is not None and self.live_server_process.poll() is None
    
    def create_html_preview(self, html_content: str, preview_dir: str, filename: str = "preview.html") -> str:
        """
        Create an HTML preview file.
        
        Args:
            html_content: HTML content to preview
            preview_dir: Directory to save the preview file
            filename: Name of the preview file
            
        Returns:
            Path to the preview file
        """
        try:
            os.makedirs(preview_dir, exist_ok=True)
            
            preview_path = os.path.join(preview_dir, filename)
            with open(preview_path, "w") as f:
                f.write(html_content)
            
            logger.info(f"Created HTML preview file at {preview_path}")
            return preview_path
        except Exception as e:
            logger.error(f"Error creating HTML preview file: {e}")
            return ""
    
    def preview_html(self, html_content: str, preview_dir: str, filename: str = "preview.html") -> bool:
        """
        Preview HTML content in the browser.
        
        Args:
            html_content: HTML content to preview
            preview_dir: Directory to save the preview file
            filename: Name of the preview file
            
        Returns:
            True if the preview was created and opened successfully, False otherwise
        """
        preview_path = self.create_html_preview(html_content, preview_dir, filename)
        
        if not preview_path:
            return False
        
        return self.open_url(f"file://{preview_path}")
    
    def preview_file(self, file_path: str) -> bool:
        """
        Preview a file in the browser.
        
        Args:
            file_path: Path to the file to preview
            
        Returns:
            True if the file was opened successfully, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"File {file_path} does not exist")
                return False
            
            return self.open_url(f"file://{os.path.abspath(file_path)}")
        except Exception as e:
            logger.error(f"Error previewing file {file_path}: {e}")
            return False
            
    def search(self, query: str, search_engine: Optional[str] = None) -> bool:
        """
        Search using the configured search engine.
        
        Args:
            query: Search query
            search_engine: Optional search engine to use (overrides config)
            
        Returns:
            True if the search was successful, False otherwise
        """
        try:
            engine = search_engine or self.config["browser"]["search_engine"]
            engine_url = self.config["browser"]["search_engines"].get(engine)
            
            if not engine_url:
                logger.error(f"Search engine {engine} not found in configuration")
                # Fall back to default search engine
                engine = "google"
                engine_url = self.config["browser"]["search_engines"].get(engine)
                if not engine_url:
                    return False
            
            formatted_query = query.replace(' ', '+')
            search_url = f"{engine_url}{formatted_query}"
            
            logger.info(f"Searching for '{query}' using {engine}")
            return self.open_url(search_url)
        except Exception as e:
            logger.error(f"Error searching for '{query}': {e}")
            return False
            
    async def api_search(self, query: str, api_engine: str = None, api_key: str = None, project_name: str = None) -> Dict[str, Any]:
        """
        Search using API endpoint for more structured results.
        
        Args:
            query: Search query
            api_engine: Engine to use (google, bing, searxng)
            api_key: API key to use
            project_name: Optional project context
            
        Returns:
            Search results with structured data
        """
        import aiohttp
        import html
        import re
        
        logger.info(f"Performing API search for: {query}")
        print(f"[DEBUG] API search for: {query} using engine: {api_engine}")
        
        api_engine = api_engine or self.config.get("browser", {}).get("search_engine", "searxng")
        
        # If no specific engine requested, try all available ones in sequence until one works
        if api_engine == "auto":
            engines_to_try = ["searxng", "google", "bing"]
        else:
            engines_to_try = [api_engine]
            
        # Extract technical terms from query to enhance search
        technical_terms = []
        if project_name:
            tech_context = self._detect_tech_terms(project_name)
            tech_terms = tech_context.split()
            # Only use the most relevant terms to avoid diluting the query
            technical_terms = tech_terms[:3] if tech_terms else []
            
        # Enhanced query with technical context if applicable
        enhanced_query = query
        if technical_terms and not any(term.lower() in query.lower() for term in technical_terms):
            enhanced_query = f"{query} {' '.join(technical_terms)}"
            logger.info(f"Enhanced search query with technical context: {enhanced_query}")
            
        results = {"query": query, "enhanced_query": enhanced_query, "results": []}
        
        for engine in engines_to_try:
            try:
                if engine == "searxng":
                    local_searxng_url = self.config.get("browser", {}).get("api_endpoints", {}).get("searxng", "http://localhost:8888/search")
                    async with aiohttp.ClientSession() as session:
                        params = {
                            "q": enhanced_query,
                            "format": "json",
                            "engines": "general,it,github",  # Prioritize technical sources
                            "language": "en-US",
                            "time_range": "month"  # Prefer recent results
                        }
                        async with session.get(local_searxng_url, params=params) as response:
                            if response.status == 200:
                                data = await response.json()
                                
                                if data.get("results"):
                                    for item in data["results"][:15]:  # Limit to 15 most relevant results
                                        result_item = {
                                            "title": item.get("title", ""),
                                            "url": item.get("url", ""),
                                            "snippet": item.get("content", ""),
                                            "engine": engine,
                                            "score": item.get("score", 0)
                                        }
                                        # Clean up HTML in snippets
                                        if result_item["snippet"]:
                                            result_item["snippet"] = html.unescape(re.sub(r'<[^>]+>', '', result_item["snippet"]))
                                        results["results"].append(result_item)
                                    
                                    # If we got results, attempt to extract the most relevant information
                                    if results["results"]:
                                        code_snippets = []
                                        for result in results["results"]:
                                            # Look for code blocks in the snippet
                                            code_blocks = re.findall(r'```(.+?)```', result["snippet"], re.DOTALL)
                                            if code_blocks:
                                                code_snippets.extend(code_blocks)
                                                
                                        results["code_snippets"] = code_snippets[:5]  # Limit to 5 most relevant code snippets
                                        
                                        # If we have results and don't need to try other engines, break
                                        if len(results["results"]) > 5:
                                            break
                
                elif engine == "google":
                    google_api_key = api_key or self.config.get("google", {}).get("api_key")
                    search_engine_id = self.config.get("google", {}).get("search_engine_id")
                    
                    if not google_api_key or not search_engine_id:
                        logger.warning("Google search API key or search engine ID not found in config")
                        continue
                        
                    google_api_url = self.config.get("browser", {}).get("api_endpoints", {}).get("google", "https://www.googleapis.com/customsearch/v1")
                    
                    async with aiohttp.ClientSession() as session:
                        params = {
                            "key": google_api_key,
                            "cx": search_engine_id,
                            "q": enhanced_query,
                            "num": 10
                        }
                        async with session.get(google_api_url, params=params) as response:
                            if response.status == 200:
                                data = await response.json()
                                
                                if data.get("items"):
                                    for item in data["items"]:
                                        result_item = {
                                            "title": item.get("title", ""),
                                            "url": item.get("link", ""),
                                            "snippet": item.get("snippet", ""),
                                            "engine": engine
                                        }
                                        results["results"].append(result_item)
                                        
                                    # If we got results, break out of the loop
                                    if len(results["results"]) > 5:
                                        break
                
                elif engine == "bing":
                    bing_api_key = api_key or self.config.get("bing", {}).get("api_key")
                    
                    if not bing_api_key:
                        logger.warning("Bing search API key not found in config")
                        continue
                        
                    bing_api_url = self.config.get("browser", {}).get("api_endpoints", {}).get("bing", "https://api.bing.microsoft.com/v7.0/search")
                    
                    async with aiohttp.ClientSession() as session:
                        headers = {"Ocp-Apim-Subscription-Key": bing_api_key}
                        params = {
                            "q": enhanced_query,
                            "count": 10,
                            "responseFilter": "Webpages"
                        }
                        async with session.get(bing_api_url, headers=headers, params=params) as response:
                            if response.status == 200:
                                data = await response.json()
                                
                                if data.get("webPages", {}).get("value"):
                                    for item in data["webPages"]["value"]:
                                        result_item = {
                                            "title": item.get("name", ""),
                                            "url": item.get("url", ""),
                                            "snippet": item.get("snippet", ""),
                                            "engine": engine
                                        }
                                        results["results"].append(result_item)
                                        
                                    # If we got results, break out of the loop
                                    if len(results["results"]) > 5:
                                        break
                    
            except Exception as e:
                logger.error(f"Error searching with {engine}: {str(e)}")
                print(f"[ERROR] {engine} search failed: {str(e)}")
                continue
        
        # If we didn't get any results from any engine
        if not results["results"]:
            results["error"] = "No results found from any search engine"
            
        return results
        
    def _detect_tech_terms(self, project_name: str) -> str:
        """
        Detect technology terms from a project to enhance search queries.
        
        Args:
            project_name: Name of the project
            
        Returns:
            String of relevant technology terms
        """
        # Try to build tech context based on file extensions and known patterns
        tech_terms = []
        
        try:
            # Look for common framework files in project folder
            project_folder = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "projects", project_name)
            
            if os.path.exists(os.path.join(project_folder, "package.json")):
                try:
                    with open(os.path.join(project_folder, "package.json"), "r") as f:
                        package_data = json.load(f)
                        dependencies = {
                            **package_data.get("dependencies", {}),
                            **package_data.get("devDependencies", {})
                        }
                        
                        # Look for known frameworks and add as tech terms
                        if "@angular/core" in dependencies:
                            tech_terms.append("angular")
                        if "react" in dependencies:
                            tech_terms.append("react")
                        if "vue" in dependencies:
                            tech_terms.append("vue")
                        if "express" in dependencies:
                            tech_terms.append("nodejs express")
                        if "next" in dependencies:
                            tech_terms.append("nextjs")
                except:
                    pass
                    
            if os.path.exists(os.path.join(project_folder, "requirements.txt")):
                tech_terms.append("python")
                
                try:
                    with open(os.path.join(project_folder, "requirements.txt"), "r") as f:
                        reqs = f.read().lower()
                        if "django" in reqs:
                            tech_terms.append("django")
                        if "flask" in reqs:
                            tech_terms.append("flask")
                        if "fastapi" in reqs:
                            tech_terms.append("fastapi")
                except:
                    pass
        except:
            pass
            
        # Join tech terms as a string
        if tech_terms:
            return " ".join(tech_terms)
        return "" 