"""
Configuration loading helper functions
"""
import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

def get_config_path() -> Path:
    """
    Get the path to the config.json file.
    
    Returns:
        Path object pointing to the config.json file
    """
    parent_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    return parent_dir.parent / "config.json"

def load_config() -> Dict[str, Any]:
    """
    Load the configuration from config.json.
    
    Returns:
        Dictionary containing the configuration
    """
    config_path = get_config_path()
    config = {}
    
    if os.path.exists(config_path):
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
                logger.info(f"Loaded configuration from {config_path}")
        except Exception as e:
            logger.error(f"Error loading configuration from {config_path}: {e}")
    else:
        logger.warning(f"Configuration file not found at {config_path}")
    
    return config

def get_api_key(service: str, env_var: Optional[str] = None) -> Optional[str]:
    """
    Get the API key for a service from config.json or environment variables.
    
    Args:
        service: The service name (e.g., 'openai', 'deepseek')
        env_var: Optional environment variable name to check as fallback
        
    Returns:
        API key if found, None otherwise
    """
    # First try to get from config file
    config = load_config()
    api_key = None
    
    if service in config and "api_key" in config[service]:
        api_key = config[service]["api_key"]
        if api_key:
            logger.info(f"Loaded {service.capitalize()} API key from config.json")
            return api_key
    
    # Fallback to environment variable if provided
    if env_var:
        api_key = os.environ.get(env_var)
        if api_key:
            logger.info(f"Using {service.capitalize()} API key from environment variable {env_var}")
            return api_key
    
    logger.warning(f"No API key found for {service}")
    return None

def get_service_config(service: str) -> Dict[str, Any]:
    """
    Get the configuration for a specific service.
    
    Args:
        service: The service name (e.g., 'openai', 'deepseek', 'browser')
        
    Returns:
        Dictionary containing the service configuration, or empty dict if not found
    """
    config = load_config()
    if service in config:
        return config[service]
    return {}

def save_config(config: Dict[str, Any]) -> bool:
    """
    Save configuration to config.json.
    
    Args:
        config: Dictionary containing the configuration to save
        
    Returns:
        True if successful, False otherwise
    """
    config_path = get_config_path()
    try:
        with open(config_path, "w") as f:
            json.dump(config, f, indent=4)
        logger.info(f"Saved configuration to {config_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving configuration to {config_path}: {e}")
        return False 