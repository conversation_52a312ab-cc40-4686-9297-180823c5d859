"""
Configure Application Script

This script configures the Autonomous AI Software Development Agent.
"""
import os
import sys
import json
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("ConfigureApp")

def configure_app():
    """
    Configure the application.
    
    Returns:
        True if the configuration was successful, False otherwise
    """
    logger.info("Configuring application...")
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(os.path.dirname(script_dir))
    
    config_file = os.path.join(project_dir, "config.json")
    
    try:
        if not os.path.exists(config_file):
            default_config = {
                "openai": {
                    "api_key": "********************************************************************************************************************************************************************"
                },
                "ollama": {
                    "base_url": "http://localhost:11434"
                },
                "lm_studio": {
                    "base_url": "http://localhost:1234/v1"
                }
            }
            
            with open(config_file, "w") as f:
                json.dump(default_config, f, indent=2)
            
            logger.info(f"Created default config.json at: {config_file}")
        else:
            logger.info(f"Config file already exists at: {config_file}")
        
        frontend_env_file = os.path.join(project_dir, "frontend", "src", "environments", "environment.ts")
        frontend_env_prod_file = os.path.join(project_dir, "frontend", "src", "environments", "environment.prod.ts")
        
        os.makedirs(os.path.dirname(frontend_env_file), exist_ok=True)
        
        with open(frontend_env_file, "w") as f:
            f.write('''export const environment = {
  production: false,
  apiUrl: 'http://localhost:8000/api',
  wsUrl: 'ws://localhost:8000/ws'
};
''')
        
        with open(frontend_env_prod_file, "w") as f:
            f.write('''export const environment = {
  production: true,
  apiUrl: 'http://localhost:8000/api',
  wsUrl: 'ws://localhost:8000/ws'
};
''')
        
        logger.info("Frontend environment files configured.")
        
        return True
    
    except Exception as e:
        logger.error(f"Error configuring application: {e}")
        return False

def main():
    """Main function to configure the application."""
    success = configure_app()
    
    if success:
        logger.info("Application configuration completed successfully.")
        sys.exit(0)
    else:
        logger.error("Application configuration failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
