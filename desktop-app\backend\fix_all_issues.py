import re

with open('agent.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Find return statement that doesn't have a newline before the next method
content = re.sub(
    r'return actions_applied(\s+)async def ensure_angular_project',
    r'return actions_applied\n\n    async def ensure_angular_project',
    content
)

# Fix the method definition and the docstring block
content = re.sub(
    r'(\s+)async def ensure_angular_project\(self, project_name: str, project_dir: str, sio\) -> str:\n\s+"""',
    r'    async def ensure_angular_project(self, project_name: str, project_dir: str, sio) -> str:\n        """',
    content
)

# Fix the undefined variables
content = content.replace("'results': results", "'results': {}")
content = re.sub(
    r'return implementation_steps',
    r'return []',
    content
)
content = re.sub(
    r'await sio\.emit\("agent_message",\s*{\s*"project_name": project_name,\s*"message": f"\[Complex Feature\]: Implementation plan created with {len\(implementation_steps\)} steps\."\s*}\s*\)',
    r'await sio.emit("agent_message", {"project_name": project_name, "message": f"[Complex Feature]: Implementation plan created."})',
    content
)

with open('agent.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("Fixed all syntax issues in agent.py") 