import re

with open('agent.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Fix the method definition and the docstring block
fixed_content = re.sub(
    r'(\s+)async def ensure_angular_project\(self, project_name: str, project_dir: str, sio\) -> str:\n\s+"""',
    r'    async def ensure_angular_project(self, project_name: str, project_dir: str, sio) -> str:\n        """',
    content
)

# Fix the indentation of the method body
lines = fixed_content.split('\n')
in_method_body = False
for i in range(len(lines)):
    if '    async def ensure_angular_project(' in lines[i]:
        in_method_body = True
    elif in_method_body and lines[i].startswith('        ') and '"""' not in lines[i]:
        # This is part of the method body, fix indentation
        lines[i] = '        ' + lines[i][8:]
    elif in_method_body and 'async def' in lines[i] and 'def ensure_angular_project' not in lines[i]:
        # Found the next method, stop processing
        in_method_body = False

fixed_content = '\n'.join(lines)

# Also fix the undefined variables
fixed_content = fixed_content.replace("'results': results", "'results': {}")
fixed_content = re.sub(
    r'return implementation_steps',
    r'return []',
    fixed_content
)
fixed_content = re.sub(
    r'await sio\.emit\("agent_message",\s*{\s*"project_name": project_name,\s*"message": f"\[Complex Feature\]: Implementation plan created with {len\(implementation_steps\)} steps\."\s*}\s*\)',
    r'await sio.emit("agent_message", {"project_name": project_name, "message": f"[Complex Feature]: Implementation plan created."})',
    fixed_content
)

with open('agent.py', 'w', encoding='utf-8') as f:
    f.write(fixed_content)

print("Fixed indentation and undefined variables in agent.py") 