with open('agent.py', 'r', encoding='utf-8') as f:
    content = f.read()

# The key issue is the missing newline after the return statement
content = content.replace('return actions_applied    async def', 'return actions_applied\n\n    async def')

# Now fix the undefined variables
content = content.replace("'results': results", "'results': {}")
# Also fix any implementation_steps references
content = content.replace('implementation_steps', '[]')

with open('agent.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("Fixed critical syntax issues in agent.py") 