"""
Frontend Integration for the Autonomous AI Software Development Agent.
"""
import os
import sys
import logging
import subprocess
import webbrowser
import time
import json
import requests
from pathlib import Path
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class FrontendIntegration:
    """
    Integrates the frontend application with VS Code and browser.
    """
    def __init__(self, frontend_dir: str, backend_dir: str):
        """
        Initialize the Frontend Integration.
        
        Args:
            frontend_dir: Path to the frontend directory.
            backend_dir: Path to the backend directory.
        """
        self.frontend_dir = frontend_dir
        self.backend_dir = backend_dir
        self.frontend_url = "http://localhost:4200"
        self.backend_url = "http://localhost:8000"
        
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from vscode_launcher import VSCodeLauncher
        
        self.vscode_launcher = VSCodeLauncher()
        logger.info(f"Initialized Frontend Integration with frontend_dir: {frontend_dir}, backend_dir: {backend_dir}")
    
    def start_frontend(self) -> bool:
        """
        Start the frontend application.
        
        Returns:
            True if the frontend was started successfully, False otherwise.
        """
        try:
            logger.info("Starting frontend application...")
            
            os.chdir(self.frontend_dir)
            
            subprocess.Popen(["npm", "start"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            for _ in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get(self.frontend_url)
                    if response.status_code == 200:
                        logger.info("Frontend application started successfully.")
                        return True
                except requests.exceptions.ConnectionError:
                    pass
                
                time.sleep(1)
            
            logger.error("Timed out waiting for frontend application to start.")
            return False
        except Exception as e:
            logger.error(f"Error starting frontend application: {e}")
            return False
    
    def start_backend(self) -> bool:
        """
        Start the backend application.
        
        Returns:
            True if the backend was started successfully, False otherwise.
        """
        try:
            logger.info("Starting backend application...")
            
            os.chdir(self.backend_dir)
            
            venv_activate = os.path.join(self.backend_dir, "venv", "bin", "activate")
            if os.path.isfile(venv_activate):
                cmd = f"source {venv_activate} && python main.py"
                subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            else:
                subprocess.Popen(["python", "main.py"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            for _ in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get(f"{self.backend_url}/api/health")
                    if response.status_code == 200:
                        logger.info("Backend application started successfully.")
                        return True
                except requests.exceptions.ConnectionError:
                    pass
                
                time.sleep(1)
            
            logger.error("Timed out waiting for backend application to start.")
            return False
        except Exception as e:
            logger.error(f"Error starting backend application: {e}")
            return False
    
    def open_in_browser(self) -> bool:
        """
        Open the frontend application in the default browser.
        
        Returns:
            True if the browser was opened successfully, False otherwise.
        """
        try:
            logger.info(f"Opening frontend application in browser at {self.frontend_url}...")
            webbrowser.open(self.frontend_url)
            return True
        except Exception as e:
            logger.error(f"Error opening frontend application in browser: {e}")
            return False
    
    def open_in_vscode(self) -> bool:
        """
        Open the project in VS Code.
        
        Returns:
            True if VS Code was opened successfully, False otherwise.
        """
        try:
            logger.info("Opening project in VS Code...")
            
            project_dir = os.path.dirname(os.path.dirname(self.frontend_dir))
            
            return self.vscode_launcher.launch(project_dir, open_terminal=True)
        except Exception as e:
            logger.error(f"Error opening project in VS Code: {e}")
            return False
    
    def start_application(self) -> bool:
        """
        Start the entire application (frontend, backend, browser, and VS Code).
        
        Returns:
            True if the application was started successfully, False otherwise.
        """
        logger.info("Starting the entire application...")
        
        if not self.start_backend():
            logger.error("Failed to start backend application.")
            return False
        
        if not self.start_frontend():
            logger.error("Failed to start frontend application.")
            return False
        
        if not self.open_in_browser():
            logger.error("Failed to open frontend application in browser.")
            return False
        
        if not self.open_in_vscode():
            logger.error("Failed to open project in VS Code.")
            return False
        
        logger.info("Application started successfully.")
        return True

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs", "frontend_integration.log"), mode="a")
        ]
    )
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    project_dir = os.path.dirname(parent_dir)
    
    frontend_dir = os.path.join(project_dir, "frontend")
    backend_dir = os.path.join(parent_dir, "backend")
    
    integration = FrontendIntegration(frontend_dir, backend_dir)
    
    success = integration.start_application()
    
    if success:
        logger.info("Application started successfully.")
    else:
        logger.error("Failed to start application.")
        sys.exit(1)
