"""
Integration Module for AI Software Development Agent.

This module integrates VS Code, terminal, browser, and test runner components
to provide a seamless autonomous development and testing experience.
"""

import os
import sys
import json
import logging
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union, Callable

from vscode_integration import VSCodeIntegration
from terminal_integration import TerminalIntegration
from browser_integration import BrowserIntegration
from test_runner import TestRunner

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('integration.log')
    ]
)
logger = logging.getLogger('Integration')

class Integration:
    """
    Integrates VS Code, terminal, browser, and test runner components.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the integration.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.vscode = VSCodeIntegration(config_path)
        self.terminal = TerminalIntegration(config_path)
        self.browser = BrowserIntegration(config_path)
        self.test_runner = TestRunner(
            config_path=config_path,
            vscode_integration=self.vscode,
            terminal_integration=self.terminal,
            browser_integration=self.browser
        )
        self.project_dir = self.config["project"]["dir"]
        
        # Initialize model clients
        from ollama_client import OllamaClient
        from lm_studio_client import LMStudioClient
        from openai_client import OpenAIClient
        
        self.ollama = OllamaClient(
            base_url=self.config.get("ollama_base_url", "http://localhost:11434")
        )
        self.lm_studio = LMStudioClient(
            base_url=self.config.get("lm_studio_base_url", "http://localhost:1234")
        )
        self.openai = OpenAIClient(
            api_key=self.config.get("openai_api_key", ""),
            base_url=self.config.get("openai_base_url", "https://api.openai.com/v1")
        )
        
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "project": {
                "dir": "C:\\SourceProjects\\AutonomousAI" if sys.platform == "win32" else os.path.expanduser("~/SourceProjects/AutonomousAI"),
                "backend_dir": "backend",
                "frontend_dir": "frontend",
                "test_dir": "tests",
                "docs_dir": "docs"
            },
            "integration": {
                "auto_start": True,
                "auto_test": True,
                "auto_fix": True,
                "auto_validate": True,
                "continuous_testing": False,
                "continuous_testing_interval": 30,  # seconds
                "open_vscode_on_start": True,
                "open_browser_on_start": True
            }
        }
        
        if not config_path:
            parent_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = parent_dir.parent / "config.json"
        
        if os.path.exists(config_path):
            try:
                with open(config_path, "r") as f:
                    user_config = json.load(f)
                    if "project" in user_config:
                        default_config["project"].update(user_config["project"])
                    if "integration" in user_config:
                        default_config["integration"].update(user_config["integration"])
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def start(self) -> bool:
        """
        Start the integration.
        
        Returns:
            True if the integration was started successfully, False otherwise
        """
        logger.info("Starting integration")
        
        os.makedirs(self.project_dir, exist_ok=True)
        
        backend_dir = os.path.join(self.project_dir, self.config["project"]["backend_dir"])
        frontend_dir = os.path.join(self.project_dir, self.config["project"]["frontend_dir"])
        test_dir = os.path.join(self.project_dir, self.config["project"]["test_dir"])
        docs_dir = os.path.join(self.project_dir, self.config["project"]["docs_dir"])
        
        os.makedirs(backend_dir, exist_ok=True)
        os.makedirs(frontend_dir, exist_ok=True)
        os.makedirs(test_dir, exist_ok=True)
        os.makedirs(docs_dir, exist_ok=True)
        
        if self.config["integration"]["open_vscode_on_start"]:
            logger.info("Opening VS Code")
            self.vscode.open_folder(self.project_dir)
            
            self.vscode.setup_project(self.project_dir)
        
        if self.config["integration"]["continuous_testing"]:
            logger.info("Starting continuous testing")
            self.test_runner.run_continuous_testing(
                project_dir=self.project_dir,
                test_dir=self.config["project"]["test_dir"],
                interval=self.config["integration"]["continuous_testing_interval"],
                callback=self._test_callback
            )
        
        logger.info("Integration started successfully")
        return True
    
    def _test_callback(self, message: str, results: Dict[str, Any]) -> None:
        """
        Callback function for test runner.
        
        Args:
            message: Message from test runner
            results: Test results
        """
        logger.info(f"Test runner: {message}")
        
        if "stage" in results:
            stage = results["stage"]
            if stage == "testing" and "results" in results:
                test_results = results["results"]
                if test_results["success"]:
                    logger.info("Tests passed")
                else:
                    logger.warning("Tests failed")
                    
                    if "errors" in test_results and len(test_results["errors"]) > 0:
                        for error in test_results["errors"]:
                            if "file" in error and "line" in error:
                                self.vscode.open_file(error["file"], error["line"])
    
    def run_tests(self, test_dir: Optional[str] = None, test_file: Optional[str] = None,
                 test_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Run tests for the project.
        
        Args:
            test_dir: Path to the test directory (relative to project_dir)
            test_file: Path to a specific test file (relative to test_dir)
            test_name: Name of a specific test to run
            
        Returns:
            Dict containing test results
        """
        logger.info("Running tests")
        
        test_results = self.test_runner.run_tests(
            project_dir=self.project_dir,
            test_dir=test_dir or self.config["project"]["test_dir"],
            test_file=test_file,
            test_name=test_name
        )
        
        if not test_results["success"] and self.config["integration"]["auto_fix"]:
            logger.info("Fixing errors")
            
            fixing_results = self.test_runner.fix_errors(
                project_dir=self.project_dir,
                errors=test_results["errors"]
            )
            
            if fixing_results["success"]:
                logger.info("Running tests again")
                
                test_results = self.test_runner.run_tests(
                    project_dir=self.project_dir,
                    test_dir=test_dir or self.config["project"]["test_dir"],
                    test_file=test_file,
                    test_name=test_name
                )
        
        if test_results["success"] and self.config["integration"]["auto_validate"]:
            logger.info("Validating in browser")
            
            browser_url = "http://localhost:4200"
            
            validation_results = self.test_runner.validate_in_browser(browser_url)
            
            test_results["validation"] = validation_results
        
        return test_results
    
    def run_full_test_cycle(self) -> Dict[str, Any]:
        """
        Run a full test cycle: linting, testing, fixing, retesting, and browser validation.
        
        Returns:
            Dict containing test cycle results
        """
        logger.info("Running full test cycle")
        
        test_cycle_results = self.test_runner.run_full_test_cycle(
            project_dir=self.project_dir,
            test_dir=self.config["project"]["test_dir"],
            browser_url="http://localhost:4200",
            callback=self._test_callback
        )
        
        return test_cycle_results
    
    def open_project_in_vscode(self) -> bool:
        """
        Open the project in VS Code.
        
        Returns:
            True if the project was opened successfully, False otherwise
        """
        logger.info("Opening project in VS Code")
        
        return self.vscode.open_folder(self.project_dir)
    
    def run_command_in_terminal(self, command: str, cwd: Optional[str] = None) -> str:
        """
        Run a command in the terminal.
        
        Args:
            command: Command to run
            cwd: Working directory
            
        Returns:
            ID of the process
        """
        logger.info(f"Running command in terminal: {command}")
        
        if cwd is None:
            cwd = self.project_dir
        
        return self.terminal.run_command(command, cwd)
    
    def open_url_in_browser(self, url: str) -> bool:
        """
        Open a URL in the browser.
        
        Args:
            url: URL to open
            
        Returns:
            True if the URL was opened successfully, False otherwise
        """
        logger.info(f"Opening URL in browser: {url}")
        
        return self.browser.open_url(url)
    
    def start_servers(self) -> Tuple[str, str]:
        """
        Start backend and frontend servers.
        
        Returns:
            Tuple of (backend_process_id, frontend_process_id)
        """
        logger.info("Starting servers")
        
        backend_dir = os.path.join(self.project_dir, self.config["project"]["backend_dir"])
        backend_cmd = "python main.py"
        backend_process_id = self.terminal.run_command(backend_cmd, backend_dir)
        
        frontend_dir = os.path.join(self.project_dir, self.config["project"]["frontend_dir"])
        frontend_cmd = "npm start"
        frontend_process_id = self.terminal.run_command(frontend_cmd, frontend_dir)
        
        time.sleep(5)
        
        self.browser.open_url("http://localhost:4200")
        
        return backend_process_id, frontend_process_id
    
    def stop_servers(self, backend_process_id: str, frontend_process_id: str) -> Tuple[bool, bool]:
        """
        Stop backend and frontend servers.
        
        Args:
            backend_process_id: ID of the backend process
            frontend_process_id: ID of the frontend process
            
        Returns:
            Tuple of (backend_stopped, frontend_stopped)
        """
        logger.info("Stopping servers")
        
        backend_stopped = self.terminal.terminate_process(backend_process_id)
        
        frontend_stopped = self.terminal.terminate_process(frontend_process_id)
        
        return backend_stopped, frontend_stopped
    
    def create_project(self, project_name: str, project_description: str) -> Dict[str, Any]:
        """
        Create a new project.
        
        Args:
            project_name: Name of the project
            project_description: Description of the project
            
        Returns:
            Dict containing project information
        """
        logger.info(f"Creating project: {project_name}")
        
        project_dir = os.path.join(self.project_dir, project_name)
        os.makedirs(project_dir, exist_ok=True)
        
        backend_dir = os.path.join(project_dir, self.config["project"]["backend_dir"])
        frontend_dir = os.path.join(project_dir, self.config["project"]["frontend_dir"])
        test_dir = os.path.join(project_dir, self.config["project"]["test_dir"])
        docs_dir = os.path.join(project_dir, self.config["project"]["docs_dir"])
        
        os.makedirs(backend_dir, exist_ok=True)
        os.makedirs(frontend_dir, exist_ok=True)
        os.makedirs(test_dir, exist_ok=True)
        os.makedirs(docs_dir, exist_ok=True)
        
        project_config = {
            "name": project_name,
            "description": project_description,
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        config_path = os.path.join(project_dir, "project.json")
        with open(config_path, "w") as f:
            json.dump(project_config, f, indent=2)
        
        self.vscode.open_folder(project_dir)
        
        self.vscode.setup_project(project_dir)
        
        return project_config
    
    def autonomous_development(self, project_name: str, requirements: str) -> Dict[str, Any]:
        """
        Autonomously develop a project based on requirements.
        
        Args:
            project_name: Name of the project
            requirements: Project requirements
            
        Returns:
            Dict containing development results
        """
        logger.info(f"Autonomously developing project: {project_name}")
        
        project_config = self.create_project(project_name, requirements)
        
        
        return {
            "project": project_config,
            "success": True,
            "message": "Project created successfully"
        }
