"""
Base class for LLM providers.
"""
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any

class BaseLLM(ABC):
    """Base class for LLM providers."""
    
    @abstractmethod
    async def generate(self, prompt: str, context: Optional[str] = None, **kwargs) -> str:
        """Generate text from a prompt."""
        pass
    
    @abstractmethod
    async def test_connection(self) -> Dict[str, Any]:
        """Test the connection to the LLM provider."""
        pass
