"""
Base LLM class and factory for creating model-specific clients.
"""
import logging
from typing import Optional, Dict, Any

from .openai_client import OpenA<PERSON>lient
from .ollama_client import OllamaClient
from .lm_studio_client import LMStudioClient

logger = logging.getLogger(__name__)

class LLM:
    """Base class for LLM clients."""
    
    @staticmethod
    def create(model_id: str) -> 'LLM':
        """
        Factory method to create appropriate LLM client.
        
        Args:
            model_id: String identifier like 'openai/gpt-4', 'ollama/llama3'
            
        Returns:
            LLM client instance
        """
        provider, model = model_id.split('/')
        
        if provider == 'openai':
            return OpenAIClient(model)
        elif provider == 'ollama':
            return OllamaClient(model)
        elif provider == 'lm-studio':
            return LMStudioClient(model)
        else:
            raise ValueError(f"Unknown model provider: {provider}")
    
    async def generate(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Generate text from the model.
        
        Args:
            prompt: Input text prompt
            context: Optional context string
            
        Returns:
            Generated text response
        """
        raise NotImplementedError("Subclasses must implement generate()")
