"""
LM Studio API client implementation.
"""
import os
import json
import logging
from typing import Optional
import aiohttp

logger = logging.getLogger(__name__)

class LMStudioClient:
    """Client for LM Studio API."""
    
    def __init__(self, model: str):
        """Initialize LM Studio client."""
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config.json")
        
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
                if "lm_studio" in config and "base_url" in config["lm_studio"]:
                    self.base_url = config["lm_studio"]["base_url"].rstrip('/')
        
        if not hasattr(self, 'base_url'):
            self.base_url = "http://localhost:1234/v1"
            
        self.model = model
        logger.info(f"Initialized LM Studio client with model: {model} at {self.base_url}")
    
    async def generate(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Generate text using LM Studio API.
        
        Args:
            prompt: Input text prompt
            context: Optional context string
            
        Returns:
            Generated text response
        """
        try:
            messages = []
            if context:
                messages.append({"role": "system", "content": context})
            messages.append({"role": "user", "content": prompt})
            
            logger.info(f"Sending request to LM Studio API with model: {self.model}")
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    json={
                        "model": self.model,
                        "messages": messages
                    }
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                    
        except Exception as e:
            logger.error(f"Error in LM Studio generate: {e}")
            raise
