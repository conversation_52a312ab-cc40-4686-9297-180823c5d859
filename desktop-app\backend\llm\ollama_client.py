"""
Ollama API client implementation.
"""
import os
import json
import logging
import aiohttp
from typing import Optional

logger = logging.getLogger(__name__)

class OllamaClient:
    """Client for Ollama API."""
    
    def __init__(self, model: str):
        """Initialize Ollama client."""
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config.json")
        
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
                if "ollama" in config and "base_url" in config["ollama"]:
                    self.base_url = config["ollama"]["base_url"].rstrip('/')
        
        if not hasattr(self, 'base_url'):
            self.base_url = "http://localhost:11434"
            
        self.model = model
        logger.info(f"Initialized Ollama client with model: {model} at {self.base_url}")
    
    async def generate(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Generate text using Ollama API.
        
        Args:
            prompt: Input text prompt
            context: Optional context string
            
        Returns:
            Generated text response
        """
        try:
            data = {"model": self.model, "prompt": prompt}
            if context:
                data["context"] = context
                
            logger.info(f"Sending request to Ollama API with model: {self.model}")
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/api/generate", json=data) as response:
                    response.raise_for_status()
                    result = await response.json()
                    return result["response"]
                    
        except Exception as e:
            logger.error(f"Error in Ollama generate: {e}")
            raise
