"""
OpenAI API client implementation.
"""
import os
import logging
from typing import Optional, List, Dict, Any

import aiohttp

from ..config_loader import get_api_key, get_service_config

logger = logging.getLogger(__name__)

class OpenAIClient:
    """Client for OpenAI API."""
    
    def __init__(self, model: str):
        """Initialize OpenAI client."""
        # Get API key from config or environment
        self.api_key = get_api_key('openai', 'OPENAI_API_KEY')
        
        # Get OpenAI configuration
        openai_config = get_service_config('openai')
        self.base_url = openai_config.get('base_url', "https://api.openai.com/v1")
        
        if not self.api_key:
            error_msg = "OpenAI API key not found in config.json or environment. Please add it to the config file."
            logger.error(error_msg)
            raise ValueError(error_msg)
            
        self.model = model
        logger.info(f"Initialized OpenAI client with model: {model}")
    
    async def generate(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Generate text using OpenAI API.
        
        Args:
            prompt: Input text prompt
            context: Optional context string
            
        Returns:
            Generated text response
        """
        try:
            messages = []
            if context:
                messages.append({"role": "system", "content": context})
            messages.append({"role": "user", "content": prompt})
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model,
                "messages": messages
            }
            
            logger.info(f"Sending request to OpenAI API with model: {self.model}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)  # 2 minute timeout
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
            
        except aiohttp.ClientResponseError as e:
            if e.status == 401:
                logger.error("Authentication error: Invalid OpenAI API key")
                return "Error: Invalid OpenAI API key. Please check your config.json file."
            elif e.status == 429:
                logger.error("Rate limit exceeded with OpenAI API")
                return "Error: OpenAI API rate limit exceeded. Please try again later."
            else:
                logger.error(f"HTTP error in OpenAI generate: {e.status} - {e.message}")
                return f"Error calling OpenAI API: {e.status} - {e.message}"
        except Exception as e:
            logger.error(f"Error in OpenAI generate: {e}")
            return f"Error: {str(e)}"
