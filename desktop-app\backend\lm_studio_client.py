import requests
import logging
import time
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class LMStudioClient:
    """Client for interacting with LM Studio API."""
    
    def __init__(self, base_url: str = "http://localhost:1234"):
        """Initialize LM Studio client."""
        self.base_url = base_url.rstrip('/')
        
    async def chat(self, messages: List[Dict[str, str]], model: str, project_name: Optional[str] = None) -> str:
        """
        Send chat messages to LM Studio model.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Name of the model to use
            project_name: Name of the project for logging (optional)
            
        Returns:
            Model response text
        """
        try:
            # Try to import socket_instance for emitting events
            try:
                from socket_instance import emit_llm_call
            except ImportError:
                emit_llm_call = None
                logger.warning("Could not import socket_instance, will not emit events")
            
            # Start timing for performance logging
            start_time = time.time()
            
            # Prepare the request
            payload = {
                "messages": messages,
                "model": model,
                "stream": False
            }
            
            # Log the request
            logger.info(f"Sending request to LM Studio API with model {model}")
            
            # Make the API call
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            
            # Calculate the time taken
            elapsed_time = time.time() - start_time
            
            # Process the response
            response_data = response.json()
            result = response_data["choices"][0]["message"]["content"]
            
            # Log the result
            logger.info(f"Received response from LM Studio API (took {elapsed_time:.2f}s)")
            
            # Emit event if socket_instance is available and project_name is provided
            if emit_llm_call and project_name:
                # Extract the prompt content from messages
                prompt = "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
                
                await emit_llm_call(
                    project_name=project_name,
                    model_type="lm_studio",
                    model_id=model,
                    prompt=prompt,
                    response=result,
                    timing=elapsed_time
                )
                
            return result
            
        except Exception as e:
            logger.error(f"Error in LM Studio chat: {e}")
            
            # Emit error event if socket_instance is available
            if emit_llm_call and project_name:
                # Extract the prompt content from messages
                prompt = "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
                
                await emit_llm_call(
                    project_name=project_name,
                    model_type="lm_studio",
                    model_id=model,
                    prompt=prompt,
                    error=str(e),
                    timing=time.time() - start_time if 'start_time' in locals() else 0
                )
                
            raise
