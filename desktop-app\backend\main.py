"""
Main FastAPI application for the Autonomous AI Software Development Agent.
Integrated with SQLite database, VS Code, and autonomous testing functionality.
"""
import os
import json
import logging
import asyncio
import subprocess
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, Depends, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import socketio
from pydantic import BaseModel
import sqlite3
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime

from api_endpoints import testing_router, chat_router

config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.json")
if os.path.exists(config_path):
    with open(config_path, "r") as f:
        config = json.load(f)
else:
    config = {
        "use_google_search": True,
        "sqlite_path": os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "agent.db"),
        "log_path": os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs"),
        "vscode_path": "code"
    }
    # Save the default config
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    with open(config_path, "w") as f:
        json.dump(config, f, indent=4)

log_dir = config.get("log_path", "logs")
os.makedirs(log_dir, exist_ok=True)

current_date = datetime.now().strftime('%Y-%m-%d')
date_log_dir = os.path.join(log_dir, current_date)
os.makedirs(date_log_dir, exist_ok=True)

log_file = os.path.join(date_log_dir, f"agent_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

sqlite_path = config.get("sqlite_path")
os.makedirs(os.path.dirname(sqlite_path), exist_ok=True)

Base = declarative_base()
engine = create_engine(f"sqlite:///{sqlite_path}")
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

class Project(Base):
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Message(Base):
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, index=True)
    content = Column(Text)
    is_user = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

Base.metadata.create_all(bind=engine)

# Create a lifespan context manager to handle graceful startup and shutdown
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: Set up background tasks, state, or connections
    try:
        logger.info("Application starting up...")
        yield
    finally:
        # Shutdown: Clean up resources
        logger.info("Application shutting down...")
        # Cancel any pending tasks
        for task in active_tasks:
            if not task.done():
                task.cancel()
                try:
                    await asyncio.shield(task)
                except asyncio.CancelledError:
                    pass
        logger.info("All tasks have been cleaned up.")

# Use the lifespan context manager with FastAPI
app = FastAPI(title="Autonomous Agent API", lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:4200"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(testing_router)
app.include_router(chat_router)

sio = socketio.AsyncServer(async_mode="asgi", cors_allowed_origins=["http://localhost:4200"])
socket_app = socketio.ASGIApp(sio, app)

# Import and set socket instance for emitting events
import socket_instance
socket_instance.set_socket_instance(sio)

import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "src"))

from agent import Agent
from project_manager import ProjectManager
from agent_state import AgentState

project_manager = ProjectManager()
agent_state = AgentState()

class ProjectRequest(BaseModel):
    name: str
    description: Optional[str] = None

class MessageRequest(BaseModel):
    project_name: str
    message: str
    model_id: str = "deepseek/deepseek-chat"
    streaming_enabled: bool = True
    local_llm_model_id: Optional[str] = None

class ConfigRequest(BaseModel):
    use_google_search: Optional[bool] = None
    vscode_path: Optional[str] = None

# Add a list to track active tasks
active_tasks = []

@sio.event
async def connect(sid, environ):
    """Handle client connection."""
    logger.info(f"Client connected: {sid}")
    await sio.emit("connection_response", {"status": "connected"})

@sio.event
async def disconnect(sid):
    """Handle client disconnection."""
    logger.info(f"Client disconnected: {sid}")

@sio.event
async def user_message(sid, data):
    """Handle user messages and start the agent process."""
    try:
        message = data.get("message")
        project_name = data.get("project_name")
        model_id = data.get("model_id", "openai/gpt-4")
        
        logger.info(f"Received message from user: {message}")
        
        project_manager.add_message_from_user(project_name, message)
        
        if agent_state.is_agent_active(project_name):
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": "I'm already working on a task for this project. Please wait until I'm finished."
            })
            return
        
        agent = Agent(model_id=model_id)
        
        # Create the task and track it
        task = asyncio.create_task(execute_agent(agent, message, project_name, sio))
        active_tasks.append(task)
        
        # Set up a callback to remove the task from active_tasks when it's done
        def task_done_callback(t):
            if t in active_tasks:
                active_tasks.remove(t)
        
        task.add_done_callback(task_done_callback)
        
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": "I'm analyzing your request..."
        })
    
    except Exception as e:
        logger.error(f"Error processing user message: {e}")
        await sio.emit("agent_message", {
            "project_name": project_name,
            "message": f"An error occurred: {str(e)}"
        })

async def execute_agent(agent, message, project_name, sio):
    """Execute the agent in a background task."""
    try:
        agent_state.set_agent_active(project_name, True)
        
        try:
            result = await agent.plan_and_execute(message, project_name, sio)

            # Emit the full agent response to the frontend
            if result and "message" in result:
                await sio.emit("agent_message", {
                    "project_name": project_name,
                    "message": result["message"]
                })
            
            agent_state.set_agent_completed(project_name, True)
            
            logger.info(f"Agent execution completed for project {project_name}")
            
            await sio.emit("agent_complete", {"project_name": project_name})
        except asyncio.CancelledError:
            logger.info(f"Agent execution for project {project_name} was cancelled")
            raise
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error executing agent: {e}\n{error_details}")
            await sio.emit("agent_message", {
                "project_name": project_name,
                "message": f"An error occurred during execution: {str(e)}"
            })
    finally:
        # Always ensure we reset the agent state
        agent_state.set_agent_active(project_name, False)

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Autonomous Agent API is running"}

@app.get("/models")
def get_models():
    """Get available AI models"""
    try:
        # Read available models from config
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "config.json")
        config = {}
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
        
        models = [
            # OpenAI models
            {"id": "openai/gpt-4-turbo", "name": "GPT-4 Turbo"},
            {"id": "openai/gpt-4o", "name": "GPT-4o"},
            {"id": "openai/gpt-4o-mini", "name": "GPT-4o Mini"},
            {"id": "openai/gpt-4", "name": "GPT-4"},
            {"id": "openai/gpt-3.5-turbo", "name": "GPT-3.5 Turbo"},
            
            # DeepSeek models - based on official documentation
            {"id": "deepseek/deepseek-chat", "name": "DeepSeek-V3 (64K context)"},
            {"id": "deepseek/deepseek-coder", "name": "DeepSeek Coder"},
            {"id": "deepseek/deepseek-reasoner", "name": "DeepSeek-R1 (Reasoning)"},
            
            # Ollama models (if available)
            {"id": "ollama/llama3", "name": "Llama 3 (Local)"},
            {"id": "ollama/mistral", "name": "Mistral (Local)"},
            {"id": "ollama/mixtral", "name": "Mixtral (Local)"},
            {"id": "ollama/codellama", "name": "Code Llama (Local)"},
            
            # LM Studio
            {"id": "lm-studio/default", "name": "LM Studio Default (Local)"}
        ]
        
        # Add any additional models from config
        if "deepseek" in config and "models" in config["deepseek"]:
            # Add any custom DeepSeek models from config that aren't already in the list
            for model_name in config["deepseek"]["models"]:
                model_id = f"deepseek/{model_name}"
                if not any(m["id"] == model_id for m in models):
                    # Generate appropriate display name based on model
                    if model_name == "deepseek-chat":
                        display_name = "DeepSeek-V3 (64K context)"
                    elif model_name == "deepseek-reasoner":
                        display_name = "DeepSeek-R1 (Reasoning)"
                    else:
                        display_name = f"DeepSeek {model_name.replace('-', ' ').title()}"
                    
                    models.append({
                        "id": model_id,
                        "name": display_name
                    })
        
        return {"models": models}
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        return {"models": []}

@app.get("/projects")
async def get_projects():
    """Get list of projects."""
    projects = project_manager.get_projects()
    return {"projects": projects}

@app.post("/projects")
async def create_project(project_request: ProjectRequest):
    """Create a new project."""
    project = project_manager.create_project(project_request.name)
    
    db = SessionLocal()
    try:
        db_project = Project(
            name=project_request.name,
            description=project_request.description
        )
        db.add(db_project)
        db.commit()
    except Exception as e:
        logger.error(f"Error storing project in database: {e}")
    finally:
        db.close()
    
    return {"message": f"Project {project_request.name} created successfully", "project": project}

@app.get("/projects/{project_name}")
async def get_project(project_name: str):
    """Get project details."""
    project = project_manager.get_project(project_name)
    if not project:
        raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
    return {"project": project}

@app.delete("/projects/{project_name}")
async def delete_project(project_name: str):
    """Delete a project."""
    success = project_manager.delete_project(project_name)
    if not success:
        raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
    
    db = SessionLocal()
    try:
        db_project = db.query(Project).filter(Project.name == project_name).first()
        if db_project:
            db.delete(db_project)
            db.commit()
    except Exception as e:
        logger.error(f"Error deleting project from database: {e}")
    finally:
        db.close()
    
    return {"message": f"Project {project_name} deleted successfully"}

@app.get("/projects/{project_name}/files")
async def get_project_files(project_name: str):
    """Get project files."""
    files = project_manager.get_project_files(project_name)
    return {"files": files}

@app.get("/projects/{project_name}/files/{file_path:path}")
async def get_file_content(project_name: str, file_path: str):
    """Get file content."""
    content = project_manager.get_file_content(project_name, file_path)
    if content is None:
        raise HTTPException(status_code=404, detail=f"File {file_path} not found in project {project_name}")
    return {"content": content}

@app.get("/projects/{project_name}/preview/{file_path:path}")
async def preview_file(project_name: str, file_path: str):
    """Preview HTML file content."""
    from fastapi.responses import HTMLResponse
    
    content = project_manager.get_file_content(project_name, file_path)
    if content is None:
        raise HTTPException(status_code=404, detail=f"File {file_path} not found in project {project_name}")
    
    return HTMLResponse(content=content)

@app.put("/projects/{project_name}/files/{file_path:path}")
async def update_file_content(project_name: str, file_path: str, request: Request):
    """Update file content."""
    body = await request.json()
    content = body.get("content")
    if content is None:
        raise HTTPException(status_code=400, detail="Content is required")
    
    file = project_manager.add_file_to_project(project_name, file_path, content)
    return {"message": f"File {file_path} updated successfully", "file": file}

@app.delete("/projects/{project_name}/files/{file_path:path}")
async def delete_file(project_name: str, file_path: str):
    """Delete a file."""
    success = project_manager.delete_file(project_name, file_path)
    if not success:
        raise HTTPException(status_code=404, detail=f"File {file_path} not found in project {project_name}")
    return {"message": f"File {file_path} deleted successfully"}

@app.get("/projects/{project_name}/messages")
async def get_project_messages(project_name: str):
    """Get project messages."""
    messages = project_manager.get_project_messages(project_name)
    return {"messages": messages}

@app.post("/projects/{project_name}/messages")
async def add_message(project_name: str, message_request: MessageRequest):
    """Add a message to a project."""
    message = project_manager.add_message_from_user(project_name, message_request.message)
    
    db = SessionLocal()
    try:
        db_project = db.query(Project).filter(Project.name == project_name).first()
        if db_project:
            db_message = Message(
                project_id=db_project.id,
                content=message_request.message,
                is_user=True
            )
            db.add(db_message)
            db.commit()
    except Exception as e:
        logger.error(f"Error storing message in database: {e}")
    finally:
        db.close()
    
    agent = Agent(model_id=message_request.model_id)
    task = asyncio.create_task(execute_agent(agent, message_request.message, project_name, sio))
    active_tasks.append(task)
    
    # Set up a callback to remove the task from active_tasks when it's done
    def task_done_callback(t):
        if t in active_tasks:
            active_tasks.remove(t)
    
    task.add_done_callback(task_done_callback)
    
    return {"message": "Message added and agent started", "message_data": message}

@app.post("/projects/{project_name}/messages/save")
async def save_project_messages(project_name: str, request: Request):
    """Save project messages and chat expanded state."""
    body = await request.json()
    messages = body.get("messages", [])
    chat_expanded = body.get("chatExpanded", False)
    
    if not isinstance(messages, list):
        raise HTTPException(status_code=400, detail="Messages must be a list")
    
    try:
        # Get the project
        project = project_manager.get_project(project_name)
        if not project:
            raise HTTPException(status_code=404, detail=f"Project {project_name} not found")
        
        # Update the project data with the messages
        project_manager.update_project_messages(project_name, messages)
        
        # Store chat expanded state if the project manager supports it
        if hasattr(project_manager, 'update_project_metadata'):
            project_manager.update_project_metadata(project_name, {"chat_expanded": chat_expanded})
        
        return {"message": f"Saved {len(messages)} messages for project {project_name}"}
    except Exception as e:
        logger.error(f"Error saving project messages: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to save messages: {str(e)}")

@app.get("/config")
async def get_config():
    """Get current configuration."""
    return {
        "use_google_search": config.get("use_google_search", True),
        "vscode_path": config.get("vscode_path", "code")
    }

@app.put("/config")
async def update_config(config_request: ConfigRequest):
    """Update configuration."""
    global config
    
    if config_request.use_google_search is not None:
        config["use_google_search"] = config_request.use_google_search
    
    if config_request.vscode_path:
        config["vscode_path"] = config_request.vscode_path
    
    with open(config_path, "w") as f:
        json.dump(config, f, indent=2)
    
    return {"message": "Configuration updated successfully"}

@app.post("/vscode/open")
async def open_vscode(request: Request):
    """Open VS Code with the project."""
    body = await request.json()
    project_name = body.get("project_name")
    file_path = body.get("file_path")
    
    vscode_path = config.get("vscode_path", "code")
    project_dir = project_manager.get_project_dir(project_name)
    
    if file_path:
        full_path = os.path.join(project_dir, file_path)
        subprocess.Popen([vscode_path, full_path])
    else:
        subprocess.Popen([vscode_path, project_dir])
    
    return {"message": "VS Code opened successfully"}

@app.post("/models/test")
async def test_model_connection(request: Request):
    """Test model connection."""
    body = await request.json()
    model_id = body.get("model_id")
    
    if not model_id:
        raise HTTPException(status_code=400, detail="Model ID is required")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "src"))
        from llm.llm import LLM
        llm = LLM.create(model_id)
        response = await llm.generate("Hello, this is a test message.", "test")
        return {"success": True, "response": response}
    except Exception as e:
        logger.error(f"Error testing model connection: {e}")
        return {"success": False, "error": str(e)}

@sio.event
async def enhance_css(sid, data):
    """Enhance all CSS files in the project and emit a JSON block with file steps."""
    try:
        project_name = data.get("project_name")
        enhancement_instruction = data.get("enhancement_instruction", "make the CSS visually rich, modern, and detailed using advanced CSS (Flexbox, Grid, gradients, shadows, animations, etc)")
        model_id = data.get("model_id", "openai/gpt-4o-mini")
        agent = Agent(model_id=model_id)
        
        # Create the task and track it
        task = asyncio.create_task(agent.enhance_css_and_output_json(project_name, enhancement_instruction, sio))
        active_tasks.append(task)
        
        # Set up a callback to remove the task from active_tasks when it's done
        def task_done_callback(t):
            if t in active_tasks:
                active_tasks.remove(t)
        
        task.add_done_callback(task_done_callback)
        
    except asyncio.CancelledError:
        logger.info(f"CSS enhancement for project {data.get('project_name')} was cancelled")
        raise
    except Exception as e:
        logger.error(f"Error in enhance_css: {e}")
        await sio.emit("agent_message", {
            "project_name": data.get("project_name"),
            "message": f"[Enhance CSS Error]: {str(e)}"
        })

@app.delete("/projects")
async def delete_all_projects():
    """Delete all projects."""
    success = project_manager.delete_all_projects()
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete all projects")
    # Optionally, clear all projects from the database as well
    db = SessionLocal()
    try:
        db.query(Project).delete()
        db.commit()
    except Exception as e:
        logger.error(f"Error deleting all projects from database: {e}")
    finally:
        db.close()
    return {"message": "All projects deleted successfully"}

# Function to get project-specific config path
def get_project_config_path(project_name):
    """Get project-specific config file path"""
    projects_dir = project_manager.get_projects_dir()
    project_dir = os.path.join(projects_dir, project_name)
    return os.path.join(project_dir, "project_config.json")

# Function to get or create project-specific config
def get_project_config(project_name):
    """Get or create project-specific configuration"""
    project_config_path = get_project_config_path(project_name)
    if os.path.exists(project_config_path):
        try:
            with open(project_config_path, "r") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading project config: {e}")
    
    # Create default project config based on global config
    project_config = dict(config)  # Copy global config
    project_config["project_name"] = project_name
    
    try:
        with open(project_config_path, "w") as f:
            json.dump(project_config, f, indent=4)
    except Exception as e:
        logger.error(f"Error saving project config: {e}")
    
    return project_config

if __name__ == "__main__":
    import uvicorn
    
    project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    vscode_path = config.get("vscode_path", "code")
    try:
        subprocess.Popen([vscode_path, project_dir])
        logger.info(f"Opened VS Code with project directory: {project_dir}")
    except Exception as e:
        logger.error(f"Failed to open VS Code: {e}")
    
    uvicorn.run("main:socket_app", host="0.0.0.0", port=5000, reload=True)
