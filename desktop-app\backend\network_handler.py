"""
Network handler module for handling network blockages and prioritizing free services.
"""
import os
import json
import logging
import requests
import time
from typing import Dict, Any, Optional, List, Tuple
import sqlite3
from pathlib import Path
import urllib.parse

logger = logging.getLogger(__name__)

class NetworkHandler:
    """
    Handles network requests with fallback mechanisms for network blockages.
    Prioritizes Google search for autonomous operations.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the network handler.
        
        Args:
            config_path: Path to the configuration file.
        """
        self.config = self._load_config(config_path)
        self.cache_dir = self._get_cache_dir()
        self.db_path = os.path.join(self.cache_dir, "network_cache.db")
        self._init_cache_db()
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file.
            
        Returns:
            Dict containing configuration.
        """
        default_config = {
            "use_google_search": True,  # Use Google Search API
            "cache_ttl": 86400,  # 24 hours in seconds
            "max_retries": 3,
            "retry_delay": 2,
            "timeout": 10,
            "offline_mode": False
        }
        
        if not config_path:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_path = os.path.join(parent_dir, "config.json")
        
        if os.path.exists(config_path):
            try:
                with open(config_path, "r") as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def _get_cache_dir(self) -> str:
        """
        Get the cache directory path.
        
        Returns:
            Path to the cache directory.
        """
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        cache_dir = os.path.join(parent_dir, "data", "cache")
        os.makedirs(cache_dir, exist_ok=True)
        return cache_dir
    
    def _init_cache_db(self) -> None:
        """
        Initialize the cache database.
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS request_cache (
            url TEXT PRIMARY KEY,
            method TEXT,
            headers TEXT,
            data TEXT,
            response TEXT,
            status_code INTEGER,
            timestamp INTEGER
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS search_cache (
            query TEXT PRIMARY KEY,
            results TEXT,
            timestamp INTEGER
        )
        """)
        
        conn.commit()
        conn.close()
    
    def _is_cache_valid(self, timestamp: int) -> bool:
        """
        Check if a cached item is still valid based on TTL.
        
        Args:
            timestamp: Timestamp when the item was cached.
            
        Returns:
            True if the cache is still valid, False otherwise.
        """
        current_time = int(time.time())
        return (current_time - timestamp) < self.config["cache_ttl"]
    
    def _get_cached_request(self, url: str, method: str) -> Optional[Dict[str, Any]]:
        """
        Get a cached request if available and valid.
        
        Args:
            url: URL of the request.
            method: HTTP method.
            
        Returns:
            Cached response or None if not available.
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT response, status_code, timestamp FROM request_cache WHERE url = ? AND method = ?",
            (url, method)
        )
        result = cursor.fetchone()
        conn.close()
        
        if result and self._is_cache_valid(result[2]):
            return {
                "response": json.loads(result[0]),
                "status_code": result[1]
            }
        
        return None
    
    def _cache_request(self, url: str, method: str, headers: Dict[str, str], 
                      data: Optional[str], response: Dict[str, Any], status_code: int) -> None:
        """
        Cache a request and its response.
        
        Args:
            url: URL of the request.
            method: HTTP method.
            headers: Request headers.
            data: Request data.
            response: Response data.
            status_code: HTTP status code.
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        current_time = int(time.time())
        
        cursor.execute(
            """
            INSERT OR REPLACE INTO request_cache 
            (url, method, headers, data, response, status_code, timestamp) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
            (
                url, 
                method, 
                json.dumps(headers), 
                json.dumps(data) if data else None,
                json.dumps(response),
                status_code,
                current_time
            )
        )
        
        conn.commit()
        conn.close()
    
    async def request(self, url: str, method: str = "GET", headers: Optional[Dict[str, str]] = None, 
                    data: Optional[Dict[str, Any]] = None, use_cache: bool = True,
                    ignore_offline: bool = False) -> Tuple[Dict[str, Any], int]:
        """
        Make a network request with caching and retry logic.
        
        Args:
            url: URL to request.
            method: HTTP method.
            headers: Request headers.
            data: Request data.
            use_cache: Whether to use cached responses.
            ignore_offline: Whether to ignore offline mode setting.
            
        Returns:
            Tuple of (response_data, status_code)
        """
        if not headers:
            headers = {}
        
        if self.config["offline_mode"] and not ignore_offline:
            logger.info(f"Operating in offline mode, using cache for {url}")
            cached = self._get_cached_request(url, method)
            if cached:
                return cached["response"], cached["status_code"]
            else:
                logger.warning(f"No cache available for {url} in offline mode")
                return {"error": "No network connection and no cache available"}, 503
        
        if use_cache:
            cached = self._get_cached_request(url, method)
            if cached:
                logger.info(f"Using cached response for {url}")
                return cached["response"], cached["status_code"]
        
        retries = 0
        max_retries = self.config["max_retries"]
        
        while retries <= max_retries:
            try:
                if method.upper() == "GET":
                    response = requests.get(
                        url, 
                        headers=headers, 
                        timeout=self.config["timeout"]
                    )
                elif method.upper() == "POST":
                    response = requests.post(
                        url, 
                        headers=headers, 
                        json=data, 
                        timeout=self.config["timeout"]
                    )
                elif method.upper() == "PUT":
                    response = requests.put(
                        url, 
                        headers=headers, 
                        json=data, 
                        timeout=self.config["timeout"]
                    )
                elif method.upper() == "DELETE":
                    response = requests.delete(
                        url, 
                        headers=headers, 
                        timeout=self.config["timeout"]
                    )
                else:
                    logger.error(f"Unsupported HTTP method: {method}")
                    return {"error": f"Unsupported HTTP method: {method}"}, 400
                
                try:
                    response_data = response.json()
                except ValueError:
                    response_data = {"text": response.text}
                
                if response.status_code < 400:
                    self._cache_request(
                        url, method, headers, json.dumps(data) if data else None,
                        response_data, response.status_code
                    )
                
                return response_data, response.status_code
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request failed (attempt {retries+1}/{max_retries+1}): {e}")
                retries += 1
                
                if retries <= max_retries:
                    time.sleep(self.config["retry_delay"])
                else:
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    cursor.execute(
                        "SELECT response, status_code FROM request_cache WHERE url = ? AND method = ?",
                        (url, method)
                    )
                    result = cursor.fetchone()
                    conn.close()
                    
                    if result:
                        logger.info(f"Using expired cache for {url} after failed requests")
                        return json.loads(result[0]), result[1]
                    
                    logger.error(f"Request failed after {max_retries+1} attempts: {e}")
                    return {"error": f"Network error: {str(e)}"}, 503
    
    async def search(self, query: str, use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        Perform a search using Google Search API or fallback to cached results.
        
        Args:
            query: Search query.
            use_cache: Whether to use cached results.
            
        Returns:
            List of search results.
        """
        if use_cache:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT results, timestamp FROM search_cache WHERE query = ?",
                (query,)
            )
            result = cursor.fetchone()
            conn.close()
            
            if result and self._is_cache_valid(result[1]):
                logger.info(f"Using cached search results for '{query}'")
                return json.loads(result[0])
        
        if self.config["offline_mode"]:
            logger.info(f"Operating in offline mode, using cache for search: '{query}'")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT results FROM search_cache WHERE query = ?",
                (query,)
            )
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return json.loads(result[0])
            else:
                logger.warning(f"No cache available for search: '{query}' in offline mode")
                return []
        
        if self.config["use_google_search"]:
            try:
                parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_path = os.path.join(parent_dir, "config.json")
                google_api_key = None
                google_search_engine_id = None
                
                if os.path.exists(config_path):
                    try:
                        with open(config_path, "r") as f:
                            config = json.load(f)
                            if "google" in config:
                                google_api_key = config["google"].get("api_key")
                                google_search_engine_id = config["google"].get("search_engine_id")
                    except Exception as e:
                        logger.error(f"Error loading Google config from {config_path}: {e}")
                
                if not google_api_key or not google_search_engine_id:
                    logger.error("Google Search API key or search engine ID not configured")
                    return []
                
                encoded_query = urllib.parse.quote_plus(query)
                url = f"https://www.googleapis.com/customsearch/v1?key={google_api_key}&cx={google_search_engine_id}&q={encoded_query}"
                
                logger.info(f"Performing Google search for query: '{query}'")
                response_data, status_code = await self.request(
                    url, method="GET", use_cache=use_cache
                )
                
                if status_code == 200:
                    results = []
                    
                    if "items" in response_data:
                        for item in response_data["items"]:
                            results.append({
                                "title": item.get("title", ""),
                                "snippet": item.get("snippet", ""),
                                "url": item.get("link", "")
                            })
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    current_time = int(time.time())
                    
                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO search_cache 
                        (query, results, timestamp) 
                        VALUES (?, ?, ?)
                        """,
                        (
                            query,
                            json.dumps(results),
                            current_time
                        )
                    )
                    
                    conn.commit()
                    conn.close()
                    
                    logger.info(f"Google search returned {len(results)} results")
                    return results
                
                logger.warning(f"Google search failed with status {status_code}")
                
            except Exception as e:
                logger.error(f"Error during Google search: {e}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT results FROM search_cache WHERE query = ?",
            (query,)
        )
        result = cursor.fetchone()
        conn.close()
        
        if result:
            logger.info(f"Using expired cache for search: '{query}'")
            return json.loads(result[0])
        
        return []
    
    def set_offline_mode(self, offline: bool) -> None:
        """
        Set the offline mode.
        
        Args:
            offline: Whether to enable offline mode.
        """
        self.config["offline_mode"] = offline
        
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(parent_dir, "config.json")
        
        if os.path.exists(config_path):
            try:
                with open(config_path, "r") as f:
                    config = json.load(f)
                
                config["offline_mode"] = offline
                
                with open(config_path, "w") as f:
                    json.dump(config, f, indent=2)
            except Exception as e:
                logger.error(f"Error updating config file: {e}")
    
    def clear_cache(self, older_than: Optional[int] = None) -> int:
        """
        Clear the cache.
        
        Args:
            older_than: Clear entries older than this many seconds.
            
        Returns:
            Number of entries cleared.
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if older_than is not None:
            current_time = int(time.time())
            threshold = current_time - older_than
            
            cursor.execute(
                "DELETE FROM request_cache WHERE timestamp < ?",
                (threshold,)
            )
            
            cursor.execute(
                "DELETE FROM search_cache WHERE timestamp < ?",
                (threshold,)
            )
        else:
            cursor.execute("DELETE FROM request_cache")
            cursor.execute("DELETE FROM search_cache")
        
        count = cursor.rowcount
        conn.commit()
        conn.close()
        
        return count
