import requests
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class OllamaClient:
    """Client for interacting with Ollama API."""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        """Initialize Ollama client."""
        self.base_url = base_url.rstrip('/')
        
    def chat(self, messages: List[Dict[str, str]], model: str) -> str:
        """
        Send chat messages to Ollama model.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Name of the model to use
            
        Returns:
            Model response text
        """
        try:
            prompt = "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": model,
                    "prompt": prompt,
                    "stream": False
                }
            )
            response.raise_for_status()
            
            return response.json()["response"]
            
        except Exception as e:
            logger.error(f"Error in Ollama chat: {e}")
            raise
