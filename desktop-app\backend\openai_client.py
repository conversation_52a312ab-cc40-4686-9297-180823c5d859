import requests
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class OpenAIClient:
    """Client for interacting with OpenAI API."""

    def __init__(self, api_key: str = "********************************************************************************************************************************************************************", base_url: str = "https://api.openai.com/v1"):
        """Initialize OpenAI client."""
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        print(f"[DEBUG] OpenAIClient initialized with base_url: {self.base_url}")

    def chat(self, messages: List[Dict[str, str]], model: str) -> str:
        """
        Send chat messages to OpenAI model.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Name of the model to use

        Returns:
            Model response text
        """
        print(f"[DEBUG] Preparing to send chat request to OpenAI API")
        print(f"[DEBUG] Messages: {messages}")
        print(f"[DEBUG] Model: {model}")

        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            print(f"[DEBUG] Headers prepared (not logging API key for security)")

            url = f"{self.base_url}/chat/completions"
            print(f"[DEBUG] URL: {url}")

            payload = {
                "model": model,
                "messages": messages
            }
            print(f"[DEBUG] Payload: {payload}")

            response = requests.post(url, headers=headers, json=payload)
            print(f"[DEBUG] Response status code: {response.status_code}")

            response.raise_for_status()
            print(f"[DEBUG] Response JSON: {response.json()}")

            result = response.json()["choices"][0]["message"]["content"]
            print(f"[DEBUG] Extracted result: {result}")

            return result

        except requests.exceptions.RequestException as req_err:
            print(f"[ERROR] RequestException occurred: {req_err}")
            logger.error(f"RequestException in OpenAI chat: {req_err}")
            raise

        except KeyError as key_err:
            print(f"[ERROR] KeyError occurred while parsing response: {key_err}")
            logger.error(f"KeyError in OpenAI chat response: {key_err}")
            raise

        except Exception as e:
            print(f"[ERROR] General exception occurred: {e}")
            logger.error(f"Error in OpenAI chat: {e}")
            raise
