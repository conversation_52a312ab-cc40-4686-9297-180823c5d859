"""
Setup SQLite Database Script

This script sets up the SQLite database for the Autonomous AI Software Development Agent.
"""
import os
import sys
import sqlite3
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("SetupDatabase")

def setup_database():
    """
    Set up the SQLite database.
    
    Returns:
        True if the setup was successful, False otherwise
    """
    logger.info("Setting up SQLite database...")
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(os.path.dirname(script_dir))
    data_dir = os.path.join(project_dir, "data")
    
    os.makedirs(data_dir, exist_ok=True)
    
    db_file = os.path.join(data_dir, "autonomous_agent.db")
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS projects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_id INTEGER NOT NULL,
            path TEXT NOT NULL,
            content TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
            UNIQUE (project_id, path)
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS configurations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cache (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT NOT NULL UNIQUE,
            value TEXT,
            expires_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        
        logger.info(f"SQLite database set up successfully at: {db_file}")
        return True
    
    except Exception as e:
        logger.error(f"Error setting up SQLite database: {e}")
        return False
    
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Main function to set up the SQLite database."""
    success = setup_database()
    
    if success:
        logger.info("Database setup completed successfully.")
        sys.exit(0)
    else:
        logger.error("Database setup failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
