"""
DeepSeek API client implementation.
"""
import os
import logging
import asyncio
import time
from typing import Optional, List, Dict, Any
import sys

try:
    from openai import AsyncOpenAI, OpenAIError  # Use OpenAI's SDK for DeepSeek API
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False
    import aiohttp  # Fallback to aiohttp if OpenAI SDK is not available

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config_loader import get_api_key, get_service_config

logger = logging.getLogger(__name__)

class CircuitBreaker:
    """Circuit breaker pattern to prevent repeated failures when API is down."""
    
    def __init__(self, failure_threshold: int = 5, reset_timeout: int = 300):
        """Initialize circuit breaker."""
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.failure_count = 0
        self.is_open = False
        self.last_failure_time = 0
        self.rate_limit_count = 0  # Track rate limit errors
    
    def record_failure(self, is_rate_limit=False):
        """Record a failure and potentially open the circuit."""
        self.failure_count += 1
        if is_rate_limit:
            self.rate_limit_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            if not self.is_open:
                logger.warning(f"DeepSeek circuit breaker opened after {self.failure_count} consecutive failures")
            self.is_open = True
    
    def record_success(self):
        """Record a successful operation and reset failure count."""
        self.failure_count = 0
        if self.rate_limit_count > 0:
            self.rate_limit_count -= 1
        if self.is_open:
            logger.info("DeepSeek circuit breaker closed after successful operation")
            self.is_open = False
    
    async def check(self):
        """Check if circuit is open and whether to allow the operation."""
        if not self.is_open:
            return True
        
        # If circuit is open but reset timeout has elapsed, allow a trial request
        if time.time() - self.last_failure_time >= self.reset_timeout:
            logger.info(f"DeepSeek circuit breaker allowing trial request after {self.reset_timeout}s timeout")
            return False  # Circuit still technically open, but allowing trial
        
        # Circuit is open and timeout hasn't elapsed
        raise Exception(f"DeepSeek circuit breaker is open. API unavailable. Try again in {int(self.reset_timeout - (time.time() - self.last_failure_time))}s")

class DeepSeekClient:
    """Client for DeepSeek API."""
    
    def __init__(self, model: str, max_retries: int = 5, retry_delay: float = 1.0):
        """Initialize DeepSeek client."""
        # Get configuration from config_loader
        self.api_key = get_api_key('deepseek', 'DEEPSEEK_API_KEY')
        
        # Get DeepSeek configuration
        deepseek_config = get_service_config('deepseek')
        self.base_url = deepseek_config.get('base_url', "https://api.deepseek.com/v1")
        
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.circuit_breaker = CircuitBreaker()
        
        # Verify API key exists
        if not self.api_key:
            error_msg = "DeepSeek API key not found in config.json or environment variables. Please add it to the config file."
            logger.error(error_msg)
            raise ValueError(error_msg)
            
        self.model = model
        
        # Initialize OpenAI client if available
        if HAS_OPENAI:
            self.client = AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            logger.info("Using OpenAI SDK for DeepSeek API")
        else:
            logger.warning("OpenAI SDK not found. Using direct HTTP requests which may be less reliable.")
            self.client = None
            
        logger.info(f"Initialized DeepSeek client with model: {model}")
    
    async def generate(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Generate text using DeepSeek API with retry logic for network errors and rate limits.
        
        Args:
            prompt: Input text prompt
            context: Optional context string
            
        Returns:
            Generated text response
        """
        # Check circuit breaker before attempting request
        try:
            await self.circuit_breaker.check()
        except Exception as e:
            logger.error(f"DeepSeek circuit breaker prevented request: {e}")
            raise
            
        messages = []
        if context:
            messages.append({"role": "system", "content": context})
        messages.append({"role": "user", "content": prompt})
        
        logger.info(f"Sending request to DeepSeek API with model: {self.model}")
        
        # Use OpenAI SDK if available (preferred method)
        if HAS_OPENAI and self.client:
            return await self._generate_with_openai_sdk(messages)
        else:
            return await self._generate_with_aiohttp(messages)
            
    async def _generate_with_openai_sdk(self, messages: List[Dict[str, str]]) -> str:
        """Generate text using OpenAI SDK."""
        retries = 0
        while retries < self.max_retries:
            try:
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    stream=False
                )
                self.circuit_breaker.record_success()
                return response.choices[0].message.content
                
            except OpenAIError as e:
                retries += 1
                
                # Check if it's a rate limit error
                is_rate_limit = "rate_limit" in str(e).lower() or "429" in str(e)
                self.circuit_breaker.record_failure(is_rate_limit=is_rate_limit)
                
                if retries >= self.max_retries:
                    logger.error(f"Failed after {self.max_retries} retries. Error: {e}")
                    raise
                
                # More aggressive backoff for rate limits
                wait_time = self.retry_delay * (4 ** (retries - 1)) if is_rate_limit else self.retry_delay * (2 ** (retries - 1))
                if is_rate_limit:
                    wait_time = max(wait_time, 60)  # At least 60 seconds for rate limits
                    logger.warning(f"Rate limit error. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                else:
                    logger.warning(f"API error: {e}. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                self.circuit_breaker.record_failure()
                logger.error(f"Unrecoverable error in DeepSeek generate: {e}")
                raise
                
        # This line should never be reached due to the retry logic, but added as a safeguard
        raise Exception(f"Unexpected error: Maximum retries ({self.max_retries}) exceeded")
        
    async def _generate_with_aiohttp(self, messages: List[Dict[str, str]]) -> str:
        """Generate text using direct HTTP requests (fallback method)."""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": messages
        }
        
        # Implement retry logic
        retries = 0
        while retries < self.max_retries:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.base_url}/chat/completions",
                        headers=headers,
                        json=payload,
                        timeout=aiohttp.ClientTimeout(total=120)
                    ) as response:
                        # Handle rate limiting (429) specifically
                        if response.status == 429:
                            retries += 1
                            # Try to get retry-after header, default to exponential backoff
                            retry_after = int(response.headers.get('retry-after', 60))
                            logger.warning(f"Rate limit exceeded. Waiting for {retry_after} seconds before retry (attempt {retries}/{self.max_retries})")
                            self.circuit_breaker.record_failure(is_rate_limit=True)
                            
                            if retries < self.max_retries:
                                await asyncio.sleep(retry_after)
                                continue
                            else:
                                response.raise_for_status()
                        
                        # For other errors, raise as usual
                        response.raise_for_status()
                        
                        result = await response.json()
                        self.circuit_breaker.record_success()
                        return result["choices"][0]["message"]["content"]
                        
            except aiohttp.ClientResponseError as e:
                # Handle HTTP errors including 429 if raise_for_status was called
                retries += 1
                is_rate_limit = e.status == 429
                self.circuit_breaker.record_failure(is_rate_limit=is_rate_limit)
                
                if retries >= self.max_retries:
                    logger.error(f"Failed after {self.max_retries} retries. Error: {e}")
                    raise
                
                # More aggressive backoff for server errors and rate limits
                wait_time = self.retry_delay * (4 ** (retries - 1))
                if e.status == 429:  # Rate limit
                    wait_time = max(wait_time, 60)  # At least 60 seconds for rate limits
                    logger.warning(f"Rate limit error. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                elif 500 <= e.status < 600:  # Server error
                    logger.warning(f"Server error {e.status}. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                else:
                    logger.warning(f"HTTP error {e.status}. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                
                await asyncio.sleep(wait_time)
                
            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                retries += 1
                self.circuit_breaker.record_failure()
                
                if retries >= self.max_retries:
                    logger.error(f"Failed after {self.max_retries} retries. Error: {e}")
                    raise
                
                # Exponential backoff
                wait_time = self.retry_delay * (2 ** (retries - 1))
                logger.warning(f"Network error: {e}. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                self.circuit_breaker.record_failure()
                logger.error(f"Unrecoverable error in DeepSeek generate: {e}")
                raise
                
        # This line should never be reached due to the retry logic, but added as a safeguard
        raise Exception(f"Unexpected error: Maximum retries ({self.max_retries}) exceeded") 