"""
Base LLM class and factory for creating model-specific clients.
"""
import logging
import json
import os
from typing import Optional, Dict, Any, Tuple, List, Union

from .openai_client import OpenAIClient
from .ollama_client import OllamaClient
from .lm_studio_client import LMStudioClient
from .deepseek_client import DeepSeekClient

logger = logging.getLogger(__name__)

class LLM:
    """Base class for LLM clients."""
    
    # Model capability mapping - used for intelligent fallback
    MODEL_CAPABILITIES = {
        # DeepSeek models
        "deepseek/deepseek-chat": {"token_limit": 32000, "capabilities": ["general", "chat", "reasoning"]},
        "deepseek/deepseek-coder": {"token_limit": 32000, "capabilities": ["code", "programming", "technical"]},
        "deepseek/deepseek-reasoner": {"token_limit": 32000, "capabilities": ["complex_reasoning", "step_by_step", "math"]},
        
        # OpenAI models
        "openai/gpt-4o": {"token_limit": 128000, "capabilities": ["general", "chat", "reasoning", "code"]},
        "openai/gpt-4o-mini": {"token_limit": 128000, "capabilities": ["general", "chat", "code"]},
        "openai/gpt-4": {"token_limit": 8192, "capabilities": ["general", "chat", "reasoning", "code"]},
        
        # Local models
        "ollama/mistral": {"token_limit": 16000, "capabilities": ["general", "chat"]},
        "ollama/llama3": {"token_limit": 16000, "capabilities": ["general", "chat"]},
        "lm-studio/nemo-instruct-2407": {"token_limit": 32000, "capabilities": ["general", "chat", "reasoning"]},
    }
    
    # DeepSeek models to use for fallback based on task type
    DEEPSEEK_FALLBACKS = {
        "code": "deepseek/deepseek-coder",
        "programming": "deepseek/deepseek-coder",
        "technical": "deepseek/deepseek-coder",
        "reasoning": "deepseek/deepseek-reasoner",
        "math": "deepseek/deepseek-reasoner",
        "complex_reasoning": "deepseek/deepseek-reasoner",
        "general": "deepseek/deepseek-chat",
        "chat": "deepseek/deepseek-chat",
        "default": "deepseek/deepseek-chat"
    }
    
    # Task detection keywords - used to detect task type from prompt
    TASK_DETECTION_KEYWORDS = {
        "code": ["code", "function", "class", "programming", "algorithm", "implement", "debug", "bugfix", 
                "syntax", "javascript", "python", "typescript", "html", "css", "react", "angular", "component"],
        "reasoning": ["reasoning", "logic", "analyze", "solution", "step by step", "explain", "inference", 
                    "deduce", "conclusion", "problem solving", "think through"],
        "math": ["math", "calculation", "equation", "formula", "compute", "arithmetic", "algebra", 
                "calculus", "statistical", "numerical", "computation"],
        "general": ["general", "chat", "write", "draft", "compose", "summarize", "paraphrase", 
                    "describe", "explain", "overview"]
    }
    
    @staticmethod
    def _detect_task_type(prompt: str) -> str:
        """
        Detect the type of task based on the prompt content.
        
        Args:
            prompt: The user prompt
            
        Returns:
            Task type (code, reasoning, math, general)
        """
        prompt_lower = prompt.lower()
        
        # Check for code task first (highest priority)
        if any(keyword in prompt_lower for keyword in LLM.TASK_DETECTION_KEYWORDS["code"]):
            return "code"
            
        # Check for reasoning task
        if any(keyword in prompt_lower for keyword in LLM.TASK_DETECTION_KEYWORDS["reasoning"]):
            # Check if it's specifically math reasoning
            if any(keyword in prompt_lower for keyword in LLM.TASK_DETECTION_KEYWORDS["math"]):
                return "math"
            return "reasoning"
            
        # Default to general
        return "general"
    
    @staticmethod
    def _get_available_models() -> Dict[str, List[str]]:
        """Get list of available models from config"""
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, "r") as f:
                    config = json.load(f)
                    models = {}
                    
                    # Get DeepSeek models
                    if "deepseek" in config and "models" in config["deepseek"]:
                        models["deepseek"] = [f"deepseek/{model}" for model in config["deepseek"]["models"]]
                    else:
                        models["deepseek"] = ["deepseek/deepseek-chat", "deepseek/deepseek-coder", "deepseek/deepseek-reasoner"]
                    
                    # Get OpenAI models
                    if "openai" in config and "models" in config["openai"]:
                        models["openai"] = [f"openai/{model}" for model in config["openai"]["models"]]
                    else:
                        models["openai"] = []
                    
                    # Get Ollama models (could be dynamically queried but using defaults for now)
                    models["ollama"] = ["ollama/mistral", "ollama/llama3"]
                    
                    # Get LM Studio models
                    models["lm-studio"] = ["lm-studio/nemo-instruct-2407"]
                    
                    return models
            except Exception as e:
                logger.error(f"Error loading models from config: {e}")
        
        # Default models if config is not available
        return {
            "deepseek": ["deepseek/deepseek-chat", "deepseek/deepseek-coder", "deepseek/deepseek-reasoner"],
            "openai": ["openai/gpt-4o-mini"],
            "ollama": ["ollama/mistral", "ollama/llama3"],
            "lm-studio": ["lm-studio/nemo-instruct-2407"]
        }
    
    @classmethod
    def _find_fallback_model(cls, primary_model: str, task_type: str = "general", token_limit_exceeded: bool = False) -> str:
        """
        Find an appropriate fallback model based on the primary model and failure reason
        
        Args:
            primary_model: The model that failed
            task_type: Type of task (code, reasoning, general)
            token_limit_exceeded: Whether the failure was due to token limit
            
        Returns:
            Appropriate fallback model ID
        """
        # For token limit issues, prefer DeepSeek models as they handle large inputs well
        if token_limit_exceeded:
            return cls.DEEPSEEK_FALLBACKS.get(task_type, cls.DEEPSEEK_FALLBACKS["default"])
            
        # Get available models
        available_models = cls._get_available_models()
        all_models = [model for provider_models in available_models.values() for model in provider_models]
        
        # Prioritize DeepSeek models for fallback since they're more reliable
        deepseek_models = available_models.get("deepseek", [])
        
        # Find the best fallback model for the given task type
        if task_type in cls.DEEPSEEK_FALLBACKS and cls.DEEPSEEK_FALLBACKS[task_type] in deepseek_models:
            # If we have a specific DeepSeek model for this task type and it's available, use it
            best_fallback = cls.DEEPSEEK_FALLBACKS[task_type]
            
            # If we're already using this model, try a different DeepSeek model
            if best_fallback == primary_model:
                for model_id in deepseek_models:
                    if model_id != primary_model:
                        return model_id
        
        # If no specific DeepSeek model is available or we're already using it,
        # try other DeepSeek models first
        if deepseek_models:
            for model_id in deepseek_models:
                if model_id != primary_model:
                    return model_id
                    
        # If no DeepSeek models are available, try Ollama
        ollama_models = available_models.get("ollama", [])
        if ollama_models:
            return ollama_models[0]
            
        # If no Ollama models are available, try LM Studio
        lm_studio_models = available_models.get("lm-studio", [])
        if lm_studio_models:
            return lm_studio_models[0]
            
        # Last resort: try OpenAI models
        openai_models = available_models.get("openai", [])
        if openai_models:
            for model_id in openai_models:
                if model_id != primary_model:
                    return model_id
        
        # If nothing else is available, return the default DeepSeek model
        # (even if not available, as this is a last resort)
        return "deepseek/deepseek-chat"
    
    @staticmethod
    def create(model_id: str) -> 'LLM':
        """
        Factory method to create appropriate LLM client.
        
        Args:
            model_id: String identifier like 'openai/gpt-4', 'deepseek/deepseek-chat'
            
        Returns:
            LLM client instance
        """
        provider, model = model_id.split('/')
        
        # Create the client for the requested provider
        if provider == 'openai':
            return OpenAIClient(model)
        elif provider == 'deepseek':
            return DeepSeekClient(model)
        elif provider == 'lm-studio':
            try:
                return LMStudioClient(model)
            except Exception as e:
                logger.error(f"Error initializing LM Studio client: {e}")
                raise
        elif provider == 'ollama':
            try:
                # Check if Ollama is available
                import os
                import httpx
                base_url = os.environ.get("OLLAMA_BASE_URL", "http://localhost:11434")
                try:
                    # Quick check if Ollama is available
                    with httpx.Client(timeout=2.0) as client:
                        response = client.get(f"{base_url}/api/tags")
                        if response.status_code != 200:
                            raise Exception(f"Ollama API returned status {response.status_code}")
                    # If we get here, Ollama is available
                    return OllamaClient(model)
                except Exception as conn_error:
                    logger.error(f"Ollama server not available: {conn_error}")
                    raise
            except Exception as e:
                logger.error(f"Error initializing Ollama client: {e}")
                raise
        else:
            logger.error(f"Unknown model provider: {provider}")
            raise ValueError(f"Unknown model provider: {provider}")
    
    async def generate(self, prompt: str, context: Optional[str] = None, project_name: Optional[str] = None) -> str:
        """
        Generate text from the model.
        
        Args:
            prompt: Input text prompt
            context: Optional context string
            project_name: Optional project name for tracking
            
        Returns:
            Generated text response
        """
        raise NotImplementedError("Subclasses must implement generate()")
    
    @classmethod
    async def generate_with_fallback(cls, model_id: str, prompt: str, context: Optional[str] = None, 
                                    task_type: Optional[str] = None, max_attempts: int = 3, 
                                    project_name: Optional[str] = None) -> Dict[str, Union[str, dict]]:
        """
        Generate text with automatic fallback to other models if the primary model fails.
        
        Args:
            model_id: Primary model ID to use
            prompt: Input text prompt
            context: Optional context string
            task_type: Type of task (code, reasoning, general). If None, detected from prompt.
            max_attempts: Maximum number of fallback attempts
            project_name: Optional project name for tracking
            
        Returns:
            Dictionary containing generated text response and metadata about the generation
        """
        attempts = 0
        current_model_id = model_id
        token_limit_exceeded = False
        error_history = []
        
        # Detect task type if not provided
        if task_type is None:
            task_type = cls._detect_task_type(prompt)
            logger.info(f"Detected task type: {task_type}")
        
        while attempts < max_attempts:
            try:
                model = cls.create(current_model_id)
                response = await model.generate(prompt, context, project_name)
                
                # Return successful response with metadata
                return {
                    "response": response,
                    "metadata": {
                        "model_used": current_model_id,
                        "fallback_attempts": attempts,
                        "task_type": task_type,
                        "error_history": error_history
                    }
                }
                
            except Exception as e:
                attempts += 1
                error_str = str(e).lower()
                
                # Track error
                error_history.append({
                    "model": current_model_id,
                    "error": str(e)
                })
                
                # Check if error is related to token limit
                if "token" in error_str and any(word in error_str for word in ["limit", "exceed", "maximum", "too long", "context length"]):
                    token_limit_exceeded = True
                    logger.warning(f"Token limit exceeded with model {current_model_id}. Finding a suitable fallback model.")
                else:
                    logger.warning(f"Error using model {current_model_id}: {e}. Attempting fallback.")
                
                if attempts < max_attempts:
                    # Find a suitable fallback model
                    previous_model = current_model_id
                    current_model_id = cls._find_fallback_model(current_model_id, task_type, token_limit_exceeded)
                    logger.info(f"Falling back from {previous_model} to model: {current_model_id} (attempt {attempts+1}/{max_attempts})")
                else:
                    logger.error(f"All fallback attempts failed. Last error: {e}")
                    raise Exception(f"All model fallback attempts failed. Last error: {e}")
        
        # This line should never be reached
        raise Exception("Unexpected error in model fallback logic")

    @classmethod
    async def intelligently_select_model(cls, prompt: str, available_models: Optional[List[str]] = None) -> str:
        """
        Intelligently select the best model for a given prompt based on content analysis.
        
        Args:
            prompt: The user prompt
            available_models: Optional list of available models to choose from
            
        Returns:
            Model ID of the best model for the task
        """
        if available_models is None:
            # Get all available models from config
            model_dict = cls._get_available_models()
            available_models = [model for models in model_dict.values() for model in models]
        
        # Detect task type
        task_type = cls._detect_task_type(prompt)
        
        # Get token count estimate (rough approximation)
        token_count_estimate = len(prompt.split()) * 1.3  # Rough estimate: words * 1.3
        
        # Filter models by token limit
        suitable_models = []
        for model_id in available_models:
            if model_id in cls.MODEL_CAPABILITIES:
                token_limit = cls.MODEL_CAPABILITIES[model_id]["token_limit"]
                # Include models with sufficient token limit (with 20% buffer)
                if token_limit >= token_count_estimate * 1.2:
                    suitable_models.append((model_id, cls.MODEL_CAPABILITIES[model_id]))
        
        # If no suitable models, use any available model
        if not suitable_models and available_models:
            return available_models[0]
        
        # First priority: match task type with model capabilities
        for model_id, capabilities in suitable_models:
            model_capabilities = capabilities.get("capabilities", [])
            
            # Direct match with task type
            if task_type in model_capabilities:
                # Prioritize DeepSeek for specific tasks
                if model_id.startswith("deepseek/"):
                    return model_id
        
        # Second priority: match any model with matching capability
        for model_id, capabilities in suitable_models:
            model_capabilities = capabilities.get("capabilities", [])
            
            # Direct match with task type 
            if task_type in model_capabilities:
                return model_id
        
        # Third priority: use DeepSeek fallback models appropriate for the task
        if task_type in cls.DEEPSEEK_FALLBACKS:
            fallback = cls.DEEPSEEK_FALLBACKS[task_type]
            if fallback in available_models:
                return fallback
        
        # Default: use the first available model
        if available_models:
            return available_models[0]
        
        # Absolute fallback
        return "deepseek/deepseek-chat"
