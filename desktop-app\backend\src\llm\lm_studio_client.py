"""
LM Studio API client implementation.
"""
import os
import sys
import logging
from typing import Optional
import aiohttp

# Use the config_loader from backend folder
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config_loader import get_service_config

logger = logging.getLogger(__name__)

class LMStudioClient:
    """Client for LM Studio API."""
    
    def __init__(self, model: str):
        """Initialize LM Studio client."""
        # Get LM Studio configuration
        lm_studio_config = get_service_config('lm_studio')
        self.base_url = lm_studio_config.get('base_url', "http://localhost:1234/v1")
            
        self.model = model
        logger.info(f"Initialized LM Studio client with model: {model} at {self.base_url}")
    
    async def generate(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Generate text using LM Studio API.
        
        Args:
            prompt: Input text prompt
            context: Optional context string
            
        Returns:
            Generated text response
        """
        try:
            messages = []
            if context:
                messages.append({"role": "system", "content": context})
            messages.append({"role": "user", "content": prompt})
            
            logger.info(f"Sending request to LM Studio API with model: {self.model}")
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    json={
                        "model": self.model,
                        "messages": messages
                    }
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                    
        except Exception as e:
            logger.error(f"Error in LM Studio generate: {e}")
            raise
