"""
Ollama API client implementation.
"""
import os
import sys
import logging
import aiohttp
from typing import Optional

# Use the config_loader from backend folder
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config_loader import get_service_config

logger = logging.getLogger(__name__)

class OllamaClient:
    """Client for Ollama API."""
    
    def __init__(self, model: str):
        """Initialize Ollama client."""
        # Get Ollama configuration
        ollama_config = get_service_config('ollama')
        self.base_url = ollama_config.get('base_url', "http://localhost:11434")
            
        self.model = model
        logger.info(f"Initialized Ollama client with model: {model} at {self.base_url}")
    
    async def generate(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Generate text using Ollama API.
        
        Args:
            prompt: Input text prompt
            context: Optional context string
            
        Returns:
            Generated text response
        """
        try:
            data = {"model": self.model, "prompt": prompt}
            if context:
                data["context"] = context
                
            logger.info(f"Sending request to Ollama API with model: {self.model}")
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/api/generate", json=data) as response:
                    response.raise_for_status()
                    result = await response.json()
                    return result["response"]
                    
        except Exception as e:
            logger.error(f"Error in Ollama generate: {e}")
            raise
