"""
OpenAI API client implementation.
"""
import os
import json
import logging
import asyncio
import time
from typing import Optional, List, Dict, Any

import aiohttp  # Ensure this is installed via pip

logger = logging.getLogger(__name__)

class CircuitBreaker:
    """Circuit breaker pattern to prevent repeated failures when API is down."""
    
    def __init__(self, failure_threshold: int = 5, reset_timeout: int = 300):
        """Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of consecutive failures before opening circuit
            reset_timeout: Time in seconds before attempting to close circuit again
        """
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.failure_count = 0
        self.is_open = False
        self.last_failure_time = 0
        self.rate_limit_count = 0  # Specific counter for rate limit errors
    
    def record_failure(self, is_rate_limit=False):
        """Record a failure and potentially open the circuit."""
        self.failure_count += 1
        if is_rate_limit:
            self.rate_limit_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            if not self.is_open:
                logger.warning(f"Circuit breaker opened after {self.failure_count} consecutive failures")
            self.is_open = True
    
    def record_success(self):
        """Record a successful operation and reset failure count."""
        self.failure_count = 0
        # Keep rate limit count for longer-term tracking
        if self.rate_limit_count > 0:
            self.rate_limit_count -= 1
        if self.is_open:
            logger.info("Circuit breaker closed after successful operation")
            self.is_open = False
    
    async def check(self):
        """Check if circuit is open and whether to allow the operation.
        
        Returns:
            bool: True if circuit is closed, False if open but ready for trial request
            
        Raises:
            Exception: If circuit is open and reset timeout hasn't elapsed
        """
        if not self.is_open:
            return True
        
        # If circuit is open but reset timeout has elapsed, allow a trial request
        if time.time() - self.last_failure_time >= self.reset_timeout:
            logger.info(f"Circuit breaker allowing trial request after {self.reset_timeout}s timeout")
            return False  # Circuit still technically open, but allowing trial
        
        # Circuit is open and timeout hasn't elapsed
        raise Exception(f"Circuit breaker is open. API unavailable. Try again in {int(self.reset_timeout - (time.time() - self.last_failure_time))}s")
    
    @property
    def has_rate_limits(self):
        """Track if we've encountered rate limits."""
        return self.rate_limit_count >= 3  # If we've hit rate limits 3+ times

class OpenAIClient:
    """Client for OpenAI API."""
    
    def __init__(self, model: str, max_retries: int = 5, retry_delay: float = 1.0):
        """Initialize OpenAI client."""
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config.json")
        self.api_key = None
        self.base_url = "https://api.openai.com/v1"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.circuit_breaker = CircuitBreaker()
        
        # Attempt to load API key from config file
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
                if "openai" in config and "api_key" in config["openai"]:
                    self.api_key = config["openai"]["api_key"]
        
        # Fallback to hardcoded API key if not found
        if not self.api_key:
            self.api_key = "********************************************************************************************************************************************************************"  # Replace with your actual API key
            logger.warning("Using hardcoded API key as fallback.")
        
        if not self.api_key:
            raise ValueError("OpenAI API key not found in config.json or hardcoded fallback.")
            
        self.model = model
        logger.info(f"Initialized OpenAI client with model: {model}")
    
    async def generate(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Generate text using OpenAI API with retry logic for network errors and rate limits.
        
        Args:
            prompt: Input text prompt
            context: Optional context string
            
        Returns:
            Generated text response
        """
        # Check circuit breaker before attempting request
        try:
            await self.circuit_breaker.check()
            
            # Log rate limit warning but continue trying
            if self.circuit_breaker.has_rate_limits:
                logger.warning("OpenAI has been rate limited multiple times. Consider manually selecting a different provider.")
                
        except Exception as e:
            logger.error(f"Circuit breaker prevented request: {e}")
            # Raise error with rate limit info for proper handling by fallback mechanism
            if self.circuit_breaker.has_rate_limits:
                rate_limit_error = aiohttp.ClientResponseError(
                    status=429,
                    message=f"Rate limit detected by circuit breaker: {str(e)}",
                    headers={},
                    request_info=None,
                    history=()
                )
                raise rate_limit_error
            else:
                raise

        messages = []
        if context:
            messages.append({"role": "system", "content": context})
        messages.append({"role": "user", "content": prompt})
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": messages
        }
        
        logger.info(f"Sending request to OpenAI API with model: {self.model}")
        
        # Implement retry logic
        retries = 0
        while retries < self.max_retries:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.base_url}/chat/completions",
                        headers=headers,
                        json=payload,
                        timeout=aiohttp.ClientTimeout(total=120)
                    ) as response:
                        # Handle rate limiting (429) specifically
                        if response.status == 429:
                            retries += 1
                            # Try to get retry-after header, default to exponential backoff
                            retry_after = int(response.headers.get('retry-after', 60))
                            logger.warning(f"Rate limit exceeded. Waiting for {retry_after} seconds before retry (attempt {retries}/{self.max_retries})")
                            
                            # Record as rate limit failure specifically
                            self.circuit_breaker.record_failure(is_rate_limit=True)
                            
                            if retries < self.max_retries:
                                await asyncio.sleep(retry_after)
                                continue
                            else:
                                # If we've exhausted retries, raise with proper 429 error for fallback mechanism
                                error_msg = await response.text()
                                logger.error(f"Rate limit retries exhausted. Response: {error_msg}")
                                raise aiohttp.ClientResponseError(
                                    status=429,
                                    message=f"Too Many Requests: {error_msg}",
                                    headers=response.headers,
                                    request_info=response.request_info,
                                    history=response.history
                                )
                        
                        # For other errors, raise as usual
                        response.raise_for_status()
                        
                        result = await response.json()
                        self.circuit_breaker.record_success()  # Record successful API call
                        return result["choices"][0]["message"]["content"]
                        
            except aiohttp.ClientResponseError as e:
                # Handle HTTP errors including 429 if raise_for_status was called
                retries += 1
                is_rate_limit = e.status == 429
                self.circuit_breaker.record_failure(is_rate_limit=is_rate_limit)
                
                if retries >= self.max_retries:
                    logger.error(f"Failed after {self.max_retries} retries. Error: {e}")
                    raise
                
                # More aggressive backoff for server errors and rate limits
                wait_time = self.retry_delay * (4 ** (retries - 1))
                if is_rate_limit:  # Rate limit
                    wait_time = max(wait_time, 60)  # At least 60 seconds for rate limits
                    logger.warning(f"Rate limit error. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                elif 500 <= e.status < 600:  # Server error
                    logger.warning(f"Server error {e.status}. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                else:
                    logger.warning(f"HTTP error {e.status}. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                
                await asyncio.sleep(wait_time)
                
            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                retries += 1
                self.circuit_breaker.record_failure()
                
                if retries >= self.max_retries:
                    logger.error(f"Failed after {self.max_retries} retries. Error: {e}")
                    raise
                
                # Exponential backoff
                wait_time = self.retry_delay * (2 ** (retries - 1))
                logger.warning(f"Network error: {e}. Retrying in {wait_time:.2f} seconds (attempt {retries}/{self.max_retries})")
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                self.circuit_breaker.record_failure()
                logger.error(f"Unrecoverable error in OpenAI generate: {e}")
                raise
                
        # This line should never be reached due to the retry logic, but added as a safeguard
        raise Exception(f"Unexpected error: Maximum retries ({self.max_retries}) exceeded")
    
    async def is_rate_limited(self) -> bool:
        """
        Check if the client is currently rate limited.
        
        Returns:
            bool: True if we've hit rate limits multiple times
        """
        return self.circuit_breaker.has_rate_limits
