"""
Terminal Integration Module for AI Software Development Agent.

This module provides functionality for integrating with the terminal,
allowing the application to run commands and display output in the UI.
"""

import os
import sys
import json
import subprocess
import logging
import time
import threading
import queue
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union, Callable

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('terminal_integration.log')
    ]
)
logger = logging.getLogger('TerminalIntegration')

class TerminalIntegration:
    """
    Handles integration with the terminal.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the terminal integration.
        
        Args:
            config_path: Path to the configuration file
        """
        print(f"[DEBUG] Initializing TerminalIntegration with config_path: {config_path}")
        self.config = self._load_config(config_path)
        print(f"[DEBUG] Loaded config: {self.config}")
        self.processes = {}
        self.output_queues = {}
        self.output_callbacks = {}
        print(f"[DEBUG] TerminalIntegration initialized")

    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        print(f"[DEBUG] Loading configuration from: {config_path}")
        default_config = {
            "terminal": {
                "max_output_lines": 1000,
                "command_timeout": 60,  # seconds
                "shell": True,
                "encoding": "utf-8"
            }
        }
        
        if not config_path:
            parent_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = parent_dir.parent / "config.json"
            print(f"[DEBUG] Default config path set to: {config_path}")
        
        if os.path.exists(config_path):
            print(f"[DEBUG] Config file exists at: {config_path}")
            try:
                with open(config_path, "r") as f:
                    user_config = json.load(f)
                    print(f"[DEBUG] Loaded user config: {user_config}")
                    if "terminal" in user_config:
                        default_config["terminal"].update(user_config["terminal"])
                        print(f"[DEBUG] Updated default config with user config: {default_config}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
                print(f"[ERROR] Error loading config from {config_path}: {e}")
        
        return default_config

    async def run_command(self, command: str, cwd: Optional[str] = None, 
                      timeout: int = 600, 
                      capture_output: bool = True,
                      project_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Run a command in the terminal asynchronously.
        
        Args:
            command: The command to run
            cwd: Working directory. If not provided, uses current directory.
            timeout: Timeout in seconds
            capture_output: Whether to capture and return the output
            project_name: Optional project name for project-specific config
            
        Returns:
            Dictionary containing the command output, exit code, and any errors
        """
        # Try to get project-specific config if available
        if project_name:
            try:
                # Check if main has the project config function available
                import main
                if hasattr(main, 'get_project_config'):
                    project_config = main.get_project_config(project_name)
                    # Update specific config values for this command execution
                    if 'environment_variables' in project_config:
                        self.config['environment_variables'] = project_config.get('environment_variables')
            except (ImportError, AttributeError) as e:
                logger.warning(f"Could not load project config for {project_name}: {e}")
        
        # Set up environment variables from config
        env = os.environ.copy()
        
        # Add configured environment variables
        if 'environment_variables' in self.config:
            for key, value in self.config['environment_variables'].items():
                env[key] = str(value)
                
        # Add a specific env var to indicate we're running from the agent
        env['AGENT_EXECUTION'] = 'true'
        
        # Set encoding to handle Unicode characters
        env['PYTHONIOENCODING'] = 'utf-8'
        
        try:
            # Try to import socket_instance for emitting events
            try:
                from socket_instance import emit_terminal_command
            except ImportError:
                emit_terminal_command = None
                logger.warning("Could not import socket_instance, will not emit events")
            
            # Log the command
            logger.info(f"Running command: {command}")
            
            # If project_name is provided, emit command start event
            if emit_terminal_command and project_name:
                await emit_terminal_command(
                    project_name=project_name,
                    command=command
                )
            
            # Execute the command
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                cwd=cwd,
                timeout=1800,  # 30-minute timeout
                env=env,  # Use our environment with encoding settings
                errors='replace'  # Handle Unicode decode errors by replacing problematic chars
            )
            
            # Process the output safely
            try:
                output = result.stdout if result.stdout else ""
            except UnicodeDecodeError:
                # Fallback for encoding issues
                output = "Output contained characters that couldn't be decoded"
                
            try:
                error = result.stderr if result.stderr else ""
            except UnicodeDecodeError:
                # Fallback for encoding issues
                error = "Error output contained characters that couldn't be decoded"
                
            exit_code = result.returncode
            
            # Log the result
            logger.info(f"Command execution completed with exit code {exit_code}")
            
            # If project_name is provided, emit command result event
            if emit_terminal_command and project_name:
                await emit_terminal_command(
                    project_name=project_name,
                    command=command,
                    output=output,
                    error=error if exit_code != 0 else None
                )
                
            return {
                "output": output,
                "error": error if exit_code != 0 else None,
                "exit_code": exit_code
            }
            
        except subprocess.TimeoutExpired:
            error_message = f"Command timed out after 1800 seconds: {command}"
            logger.error(error_message)
            
            # If project_name is provided, emit command error event
            if emit_terminal_command and project_name:
                await emit_terminal_command(
                    project_name=project_name,
                    command=command,
                    error=error_message
                )
                
            return {
                "output": "",
                "error": error_message,
                "exit_code": -1
            }
            
        except Exception as e:
            error_message = f"Error running command: {e}"
            logger.error(error_message)
            
            # If project_name is provided, emit command error event
            if emit_terminal_command and project_name:
                await emit_terminal_command(
                    project_name=project_name,
                    command=command,
                    error=error_message
                )
                
            return {
                "output": "",
                "error": error_message,
                "exit_code": -1
            }

    def run_command_sync(self, command: str, cwd: Optional[str] = None, env: Optional[Dict[str, str]] = None,
                        timeout: Optional[float] = None) -> Tuple[int, str]:
        """
        Run a command synchronously.
        
        Args:
            command: Command to run
            cwd: Working directory
            env: Environment variables
            timeout: Timeout in seconds
            
        Returns:
            Tuple of (return code, output)
        """
        try:
            full_env = os.environ.copy()
            if env:
                full_env.update(env)
            
            result = subprocess.run(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                shell=self.config["terminal"]["shell"],
                cwd=cwd,
                env=full_env,
                text=True,
                encoding=self.config["terminal"]["encoding"],
                timeout=timeout
            )
            
            return result.returncode, result.stdout
        except subprocess.TimeoutExpired:
            return -1, "Command timed out"
        except Exception as e:
            logger.error(f"Error running command: {e}")
            return -1, f"Error running command: {e}"
    
    def run_in_terminal(self, command: str, cwd: Optional[str] = None, env: Optional[Dict[str, str]] = None) -> bool:
        """
        Run a command in a new terminal window.
        
        Args:
            command: Command to run
            cwd: Working directory
            env: Environment variables
            
        Returns:
            True if the command was started, False otherwise
        """
        try:
            full_env = os.environ.copy()
            if env:
                full_env.update(env)
            
            if sys.platform == "win32":
                cmd = ["start", "cmd", "/k", command]
                shell = True
            elif sys.platform == "darwin":
                cmd = ["osascript", "-e", f'tell app "Terminal" to do script "{command}"']
                shell = True
            else:
                terminals = ["gnome-terminal", "xterm", "konsole", "terminator"]
                terminal = None
                
                for t in terminals:
                    if subprocess.run(["which", t], stdout=subprocess.PIPE, stderr=subprocess.PIPE).returncode == 0:
                        terminal = t
                        break
                
                if terminal is None:
                    logger.error("No terminal found")
                    return False
                
                if terminal == "gnome-terminal":
                    cmd = [terminal, "--", "bash", "-c", f"{command}; exec bash"]
                else:
                    cmd = [terminal, "-e", f"bash -c '{command}; exec bash'"]
                
                shell = False
            
            subprocess.Popen(
                cmd,
                cwd=cwd,
                env=full_env,
                shell=shell
            )
            
            return True
        except Exception as e:
            logger.error(f"Error running command in terminal: {e}")
            return False
