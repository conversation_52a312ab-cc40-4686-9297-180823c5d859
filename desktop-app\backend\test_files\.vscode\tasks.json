{"version": "2.0.0", "tasks": [{"label": "Start Backend Server", "type": "shell", "command": "cd ${workspaceFolder}/backend && python main.py", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Start Frontend Server", "type": "shell", "command": "cd ${workspaceFolder}/frontend && npm start", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run Tests", "type": "shell", "command": "cd ${workspaceFolder}/backend && python -m pytest", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Start All", "dependsOn": ["Start Backend Server", "Start Frontend Server"], "problemMatcher": []}]}