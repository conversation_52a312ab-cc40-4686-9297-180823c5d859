{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal"}, {"name": "Python: Backend Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/backend/main.py", "console": "integratedTerminal"}, {"name": "JavaScript: Frontend Server", "type": "chrome", "request": "launch", "url": "http://localhost:4200", "webRoot": "${workspaceFolder}/frontend"}]}