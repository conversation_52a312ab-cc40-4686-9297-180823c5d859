{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"], "args": [], "icon": "terminal-cmd"}, "Project Terminal": {"path": "${env:windir}\\System32\\cmd.exe", "args": ["/K", "cd", "/d", "\\home\\ubuntu\\implementation\\desktop-app\\backend\\test_files\\test_project"], "icon": "terminal-cmd"}}, "terminal.integrated.profiles.linux": {"bash": {"path": "bash", "icon": "terminal-bash"}, "Project Terminal": {"path": "bash", "args": ["-c", "cd /home/<USER>/implementation/desktop-app/backend/test_files/test_project && bash"], "icon": "terminal-bash"}}, "terminal.integrated.profiles.osx": {"bash": {"path": "bash", "icon": "terminal-bash"}, "Project Terminal": {"path": "bash", "args": ["-c", "cd /home/<USER>/implementation/desktop-app/backend/test_files/test_project && bash"], "icon": "terminal-bash"}}, "terminal.integrated.defaultProfile.windows": "Project Terminal", "terminal.integrated.defaultProfile.linux": "Project Terminal", "terminal.integrated.defaultProfile.osx": "Project Terminal"}