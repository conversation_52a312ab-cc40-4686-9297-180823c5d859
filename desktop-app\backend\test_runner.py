"""
Test Runner Module for AI Software Development Agent.

This module provides functionality for automatically running tests,
detecting and fixing errors, and validating functionality in the browser.
"""

import os
import sys
import json
import subprocess
import logging
import time
import threading
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union, Callable

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('test_runner.log')
    ]
)
logger = logging.getLogger('TestRunner')

class TestRunner:
    """
    Handles automated testing, error detection, and fixing.
    """
    
    def __init__(self, config_path: Optional[str] = None, 
                 vscode_integration=None, 
                 terminal_integration=None, 
                 browser_integration=None):
        """
        Initialize the test runner.
        
        Args:
            config_path: Path to the configuration file
            vscode_integration: VSCodeIntegration instance
            terminal_integration: TerminalIntegration instance
            browser_integration: BrowserIntegration instance
        """
        print(f"[DEBUG] Initializing TestRunner with config_path: {config_path}")
        self.config = self._load_config(config_path)
        print(f"[DEBUG] Loaded config: {self.config}")
        self.vscode_integration = vscode_integration
        self.terminal_integration = terminal_integration
        self.browser_integration = browser_integration
        self.test_results = {}
        self.error_patterns = self._compile_error_patterns()
        print(f"[DEBUG] TestRunner initialized")

    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        print(f"[DEBUG] Loading configuration from: {config_path}")
        default_config = {
            "testing": {
                "test_command": "pytest",
                "test_args": ["-v"],
                "linting_command": "flake8",
                "linting_args": [],
                "max_retries": 3,
                "retry_delay": 2,  # seconds
                "auto_fix": True,
                "test_timeout": 60,  # seconds
                "browser_validation": True,
                "browser_validation_timeout": 10,  # seconds,
                "error_patterns": []
            }
        }
        
        if not config_path:
            parent_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = parent_dir.parent / "config.json"
            print(f"[DEBUG] Default config path set to: {config_path}")
        
        if os.path.exists(config_path):
            print(f"[DEBUG] Config file exists at: {config_path}")
            try:
                with open(config_path, "r") as f:
                    user_config = json.load(f)
                    print(f"[DEBUG] Loaded user config: {user_config}")
                    if "testing" in user_config:
                        default_config["testing"].update(user_config["testing"])
                        print(f"[DEBUG] Updated default config with user config: {default_config}")
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
                print(f"[ERROR] Error loading config from {config_path}: {e}")
        
        return default_config

    def _compile_error_patterns(self) -> List[Dict[str, Any]]:
        """
        Compile error patterns from configuration.
        
        Returns:
            List of compiled error patterns
        """
        print(f"[DEBUG] Compiling error patterns from config")
        compiled_patterns = []
        
        for pattern_config in self.config["testing"]["error_patterns"]:
            try:
                compiled_pattern = re.compile(pattern_config["pattern"])
                compiled_patterns.append({
                    "pattern": compiled_pattern,
                    "fix_command": pattern_config["fix_command"],
                    "description": pattern_config["description"]
                })
                print(f"[DEBUG] Compiled pattern: {pattern_config['pattern']}")
            except Exception as e:
                logger.error(f"Error compiling error pattern {pattern_config['pattern']}: {e}")
                print(f"[ERROR] Error compiling error pattern {pattern_config['pattern']}: {e}")
        
        return compiled_patterns

    def run_tests(self, project_dir: str, test_dir: Optional[str] = None, 
                 test_file: Optional[str] = None, test_name: Optional[str] = None,
                 callback: Optional[Callable[[str], None]] = None) -> Dict[str, Any]:
        """
        Run tests for the project.
        
        Args:
            project_dir: Path to the project directory
            test_dir: Path to the test directory (relative to project_dir)
            test_file: Path to a specific test file (relative to test_dir)
            test_name: Name of a specific test to run
            callback: Callback function to receive test output
            
        Returns:
            Dict containing test results
        """
        print(f"[DEBUG] Running tests in project_dir: {project_dir}")
        print(f"[DEBUG] Test directory: {test_dir}, Test file: {test_file}, Test name: {test_name}")
        cmd = [self.config["testing"]["test_command"]]
        cmd.extend(self.config["testing"]["test_args"])
        
        if test_dir:
            test_path = os.path.join(project_dir, test_dir)
            cmd.append(test_path)
        elif test_file:
            if test_dir:
                test_path = os.path.join(project_dir, test_dir, test_file)
            else:
                test_path = os.path.join(project_dir, test_file)
            cmd.append(test_path)
            if test_name:
                cmd.append(f"-k {test_name}")
        
        print(f"[DEBUG] Test command: {' '.join(cmd)}")
        logger.info(f"Running tests with command: {' '.join(cmd)}")
        
        if self.terminal_integration:
            print(f"[DEBUG] Using terminal integration to run tests")
            process_id = self.terminal_integration.run_command(
                command=' '.join(cmd),
                cwd=project_dir,
                callback=callback
            )
            print(f"[DEBUG] Process ID: {process_id}")
            
            output = []
            while self.terminal_integration.is_running(process_id):
                new_output = self.terminal_integration.get_output(process_id)
                output.extend(new_output)
                print(f"[DEBUG] New output from process: {new_output}")
                time.sleep(0.1)
            
            final_output = self.terminal_integration.get_output(process_id)
            output.extend(final_output)
            print(f"[DEBUG] Final output from process: {final_output}")
            
            test_results = self._parse_test_results('\n'.join(output))
            print(f"[DEBUG] Parsed test results: {test_results}")
            
            self.test_results = test_results
            
            return test_results
        else:
            try:
                print(f"[DEBUG] Running tests using subprocess")
                result = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    cwd=project_dir,
                    timeout=self.config["testing"]["test_timeout"]
                )
                print(f"[DEBUG] Subprocess completed with output: {result.stdout}")
                
                test_results = self._parse_test_results(result.stdout)
                print(f"[DEBUG] Parsed test results: {test_results}")
                
                self.test_results = test_results
                
                return test_results
            except subprocess.TimeoutExpired:
                logger.error("Tests timed out")
                print(f"[ERROR] Tests timed out")
                return {
                    "success": False,
                    "passed": 0,
                    "failed": 0,
                    "errors": ["Tests timed out"],
                    "output": "Tests timed out"
                }
            except Exception as e:
                logger.error(f"Error running tests: {e}")
                print(f"[ERROR] Error running tests: {e}")
                return {
                    "success": False,
                    "passed": 0,
                    "failed": 0,
                    "errors": [str(e)],
                    "output": str(e)
                }

    def _parse_test_results(self, output: str) -> Dict[str, Any]:
        """
        Parse test results from output.
        
        Args:
            output: Test output
            
        Returns:
            Dict containing test results
        """
        result = {
            "success": False,
            "passed": 0,
            "failed": 0,
            "errors": [],
            "output": output
        }
        
        if "FAILED" not in output and "ERROR" not in output:
            result["success"] = True
        
        passed_match = re.search(r"(\d+) passed", output)
        if passed_match:
            result["passed"] = int(passed_match.group(1))
        
        failed_match = re.search(r"(\d+) failed", output)
        if failed_match:
            result["failed"] = int(failed_match.group(1))
        
        errors = []
        for error_pattern in self.error_patterns:
            for match in error_pattern["pattern"].finditer(output):
                error_text = match.group(0)
                errors.append({
                    "text": error_text,
                    "description": error_pattern["description"],
                    "fix_command": error_pattern["fix_command"],
                    "match": match
                })
        
        result["errors"] = errors
        
        return result
    
    def run_linting(self, project_dir: str, file_path: Optional[str] = None,
                   callback: Optional[Callable[[str], None]] = None) -> Dict[str, Any]:
        """
        Run linting for the project.
        
        Args:
            project_dir: Path to the project directory
            file_path: Path to a specific file to lint (relative to project_dir)
            callback: Callback function to receive linting output
            
        Returns:
            Dict containing linting results
        """
        cmd = [self.config["testing"]["linting_command"]]
        cmd.extend(self.config["testing"]["linting_args"])
        
        if file_path:
            cmd.append(os.path.join(project_dir, file_path))
        
        logger.info(f"Running linting with command: {' '.join(cmd)}")
        
        if self.terminal_integration:
            process_id = self.terminal_integration.run_command(
                command=' '.join(cmd),
                cwd=project_dir,
                callback=callback
            )
            
            output = []
            while self.terminal_integration.is_running(process_id):
                new_output = self.terminal_integration.get_output(process_id)
                output.extend(new_output)
                time.sleep(0.1)
            
            final_output = self.terminal_integration.get_output(process_id)
            output.extend(final_output)
            
            linting_results = self._parse_linting_results('\n'.join(output))
            
            return linting_results
        else:
            try:
                result = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    cwd=project_dir,
                    timeout=self.config["testing"]["test_timeout"]
                )
                
                linting_results = self._parse_linting_results(result.stdout)
                
                return linting_results
            except subprocess.TimeoutExpired:
                logger.error("Linting timed out")
                return {
                    "success": False,
                    "errors": ["Linting timed out"],
                    "output": "Linting timed out"
                }
            except Exception as e:
                logger.error(f"Error running linting: {e}")
                return {
                    "success": False,
                    "errors": [str(e)],
                    "output": str(e)
                }
    
    def _parse_linting_results(self, output: str) -> Dict[str, Any]:
        """
        Parse linting results from output.
        
        Args:
            output: Linting output
            
        Returns:
            Dict containing linting results
        """
        result = {
            "success": len(output.strip()) == 0,  # Empty output means no errors
            "errors": [],
            "output": output
        }
        
        errors = []
        for line in output.splitlines():
            if ":" in line and len(line.split(":")) >= 3:
                errors.append(line.strip())
        
        result["errors"] = errors
        
        return result
    
    def fix_errors(self, project_dir: str, errors: List[Dict[str, Any]],
                  callback: Optional[Callable[[str], None]] = None) -> Dict[str, Any]:
        """
        Fix errors automatically.
        
        Args:
            project_dir: Path to the project directory
            errors: List of errors to fix
            callback: Callback function to receive fix output
            
        Returns:
            Dict containing fix results
        """
        if not self.config["testing"]["auto_fix"]:
            logger.info("Auto-fix is disabled")
            return {
                "success": False,
                "fixed": 0,
                "not_fixed": len(errors),
                "output": "Auto-fix is disabled"
            }
        
        fixed = 0
        not_fixed = 0
        output = []
        
        for error in errors:
            if "fix_command" in error and error["fix_command"]:
                fix_command = error["fix_command"]
                if "match" in error:
                    for i, group in enumerate(error["match"].groups()):
                        fix_command = fix_command.replace(f"{{{i+1}}}", group)
                
                logger.info(f"Fixing error with command: {fix_command}")
                output.append(f"Fixing error: {error['text']}")
                output.append(f"Running command: {fix_command}")
                
                if self.terminal_integration:
                    process_id = self.terminal_integration.run_command(
                        command=fix_command,
                        cwd=project_dir,
                        callback=callback
                    )
                    
                    while self.terminal_integration.is_running(process_id):
                        new_output = self.terminal_integration.get_output(process_id)
                        output.extend(new_output)
                        time.sleep(0.1)
                    
                    final_output = self.terminal_integration.get_output(process_id)
                    output.extend(final_output)
                    
                    fixed += 1
                else:
                    try:
                        result = subprocess.run(
                            fix_command,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.STDOUT,
                            shell=True,
                            text=True,
                            cwd=project_dir
                        )
                        
                        output.append(result.stdout)
                        
                        if result.returncode == 0:
                            fixed += 1
                        else:
                            not_fixed += 1
                    except Exception as e:
                        logger.error(f"Error fixing error: {e}")
                        output.append(f"Error fixing error: {e}")
                        not_fixed += 1
            else:
                output.append(f"No automatic fix available for error: {error['text']}")
                not_fixed += 1
        
        return {
            "success": fixed > 0 and not_fixed == 0,
            "fixed": fixed,
            "not_fixed": not_fixed,
            "output": '\n'.join(output)
        }
    
    def validate_in_browser(self, url: str, validation_checks: Optional[List[Dict[str, Any]]] = None,
                           timeout: Optional[float] = None) -> Dict[str, Any]:
        """
        Validate functionality in the browser.
        
        Args:
            url: URL to validate
            validation_checks: List of validation checks to perform
            timeout: Timeout in seconds
            
        Returns:
            Dict containing validation results
        """
        if not self.browser_integration:
            logger.error("Browser integration is not available")
            return {
                "success": False,
                "errors": ["Browser integration is not available"],
                "output": "Browser integration is not available"
            }
        
        if not self.config["testing"]["browser_validation"]:
            logger.info("Browser validation is disabled")
            return {
                "success": False,
                "errors": ["Browser validation is disabled"],
                "output": "Browser validation is disabled"
            }
        
        logger.info(f"Validating URL in browser: {url}")
        
        if not self.browser_integration.open_url(url):
            logger.error(f"Error opening URL in browser: {url}")
            return {
                "success": False,
                "errors": [f"Error opening URL in browser: {url}"],
                "output": f"Error opening URL in browser: {url}"
            }
        
        if timeout is None:
            timeout = self.config["testing"]["browser_validation_timeout"]
        
        time.sleep(timeout)
        
        
        return {
            "success": True,
            "errors": [],
            "output": f"Validated URL in browser: {url}"
        }
    
    def run_full_test_cycle(self, project_dir: str, test_dir: Optional[str] = None,
                           browser_url: Optional[str] = None,
                           callback: Optional[Callable[[str, Dict[str, Any]], None]] = None) -> Dict[str, Any]:
        """
        Run a full test cycle: linting, testing, fixing, retesting, and browser validation.
        
        Args:
            project_dir: Path to the project directory
            test_dir: Path to the test directory (relative to project_dir)
            browser_url: URL to validate in browser
            callback: Callback function to receive test cycle output and results
            
        Returns:
            Dict containing test cycle results
        """
        results = {
            "linting": None,
            "testing": None,
            "fixing": None,
            "retesting": None,
            "browser_validation": None,
            "success": False
        }
        
        logger.info("Running linting")
        if callback:
            callback("Running linting", {"stage": "linting"})
        
        linting_results = self.run_linting(project_dir)
        results["linting"] = linting_results
        
        if callback:
            callback("Linting completed", {"stage": "linting", "results": linting_results})
        
        if not linting_results["success"] and len(linting_results["errors"]) > 0:
            logger.info("Fixing linting errors")
            if callback:
                callback("Fixing linting errors", {"stage": "fixing", "type": "linting"})
            
            fixing_results = self.fix_errors(project_dir, linting_results["errors"])
            results["fixing"] = fixing_results
            
            if callback:
                callback("Fixing completed", {"stage": "fixing", "type": "linting", "results": fixing_results})
            
            logger.info("Running linting again")
            if callback:
                callback("Running linting again", {"stage": "linting"})
            
            linting_results = self.run_linting(project_dir)
            results["linting"] = linting_results
            
            if callback:
                callback("Linting completed", {"stage": "linting", "results": linting_results})
        
        logger.info("Running tests")
        if callback:
            callback("Running tests", {"stage": "testing"})
        
        testing_results = self.run_tests(project_dir, test_dir)
        results["testing"] = testing_results
        
        if callback:
            callback("Testing completed", {"stage": "testing", "results": testing_results})
        
        if not testing_results["success"] and len(testing_results["errors"]) > 0:
            logger.info("Fixing test errors")
            if callback:
                callback("Fixing test errors", {"stage": "fixing", "type": "testing"})
            
            fixing_results = self.fix_errors(project_dir, testing_results["errors"])
            if "fixing" in results:
                results["fixing"]["test_errors"] = fixing_results
            else:
                results["fixing"] = fixing_results
            
            if callback:
                callback("Fixing completed", {"stage": "fixing", "type": "testing", "results": fixing_results})
            
            logger.info("Running tests again")
            if callback:
                callback("Running tests again", {"stage": "retesting"})
            
            retesting_results = self.run_tests(project_dir, test_dir)
            results["retesting"] = retesting_results
            
            if callback:
                callback("Retesting completed", {"stage": "retesting", "results": retesting_results})
        
        if browser_url and (results["testing"]["success"] or (results["retesting"] and results["retesting"]["success"])):
            logger.info(f"Validating in browser: {browser_url}")
            if callback:
                callback(f"Validating in browser: {browser_url}", {"stage": "browser_validation"})
            
            browser_validation_results = self.validate_in_browser(browser_url)
            results["browser_validation"] = browser_validation_results
            
            if callback:
                callback("Browser validation completed", {"stage": "browser_validation", "results": browser_validation_results})
        
        results["success"] = (
            results["linting"]["success"] and
            (results["testing"]["success"] or (results["retesting"] and results["retesting"]["success"])) and
            (not browser_url or not results["browser_validation"] or results["browser_validation"]["success"])
        )
        
        return results
    
    def run_continuous_testing(self, project_dir: str, test_dir: Optional[str] = None,
                              browser_url: Optional[str] = None, interval: int = 5,
                              callback: Optional[Callable[[str, Dict[str, Any]], None]] = None) -> None:
        """
        Run continuous testing in a separate thread.
        
        Args:
            project_dir: Path to the project directory
            test_dir: Path to the test directory (relative to project_dir)
            browser_url: URL to validate in browser
            interval: Interval between test cycles in seconds
            callback: Callback function to receive test cycle output and results
        """
        def run_tests():
            while True:
                try:
                    self.run_full_test_cycle(project_dir, test_dir, browser_url, callback)
                    
                    time.sleep(interval)
                except Exception as e:
                    logger.error(f"Error in continuous testing: {e}")
                    time.sleep(interval)
        
        thread = threading.Thread(target=run_tests)
        thread.daemon = True
        thread.start()
        
        logger.info(f"Started continuous testing with interval {interval} seconds")
    
    def open_test_file_in_vscode(self, project_dir: str, test_file: str) -> bool:
        """
        Open a test file in VS Code.
        
        Args:
            project_dir: Path to the project directory
            test_file: Path to the test file (relative to project_dir)
            
        Returns:
            True if the file was opened successfully, False otherwise
        """
        if not self.vscode_integration:
            logger.error("VS Code integration is not available")
            return False
        
        file_path = os.path.join(project_dir, test_file)
        return self.vscode_integration.open_file(file_path)
    
    def run_tests_in_vscode_terminal(self, project_dir: str, test_dir: Optional[str] = None,
                                    test_file: Optional[str] = None, test_name: Optional[str] = None) -> bool:
        """
        Run tests in VS Code terminal.
        
        Args:
            project_dir: Path to the project directory
            test_dir: Path to the test directory (relative to project_dir)
            test_file: Path to a specific test file (relative to test_dir)
            test_name: Name of a specific test to run
            
        Returns:
            True if the tests were run successfully, False otherwise
        """
        if not self.vscode_integration:
            logger.error("VS Code integration is not available")
            return False
        
        cmd = [self.config["testing"]["test_command"]]
        cmd.extend(self.config["testing"]["test_args"])
        
        if test_dir:
            test_path = os.path.join(project_dir, test_dir)
            cmd.append(test_path)
        elif test_file:
            if test_dir:
                test_path = os.path.join(project_dir, test_dir, test_file)
            else:
                test_path = os.path.join(project_dir, test_file)
            cmd.append(test_path)
            if test_name:
                cmd.append(f"-k {test_name}")
        
        if not self.vscode_integration.open_folder(project_dir):
            logger.error(f"Error opening folder in VS Code: {project_dir}")
            return False
        
        logger.info(f"Would run tests in VS Code terminal with command: {' '.join(cmd)}")
        
        return True
