"""
Simple test script for VS Code integration.
"""
import os
import sys
from vscode_integration import VSCodeIntegration

def main():
    """Test VS Code integration directly."""
    vscode = VSCodeIntegration()
    
    test_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_files")
    os.makedirs(test_dir, exist_ok=True)
    
    test_file = os.path.join(test_dir, "test_vscode.py")
    with open(test_file, "w") as f:
        f.write("print('Hello from VS Code test')\n\n# Line 3\n# Line 4\n# Line 5\n")
    
    print(f"Created test file: {test_file}")
    
    print("\nTesting open_file...")
    success = vscode.open_file(test_file)
    print(f"Open file result: {success}")
    
    print("\nTesting open_file with line number...")
    success = vscode.open_file(test_file, line=3)
    print(f"Open file with line result: {success}")
    
    print("\nTesting open_folder...")
    success = vscode.open_folder(test_dir)
    print(f"Open folder result: {success}")
    
    print("\nTesting create_workspace_settings...")
    success = vscode.create_workspace_settings(test_dir)
    print(f"Create workspace settings result: {success}")
    
    print("\nTesting create_tasks_file...")
    success = vscode.create_tasks_file(test_dir)
    print(f"Create tasks file result: {success}")
    
    print("\nTesting setup_project...")
    success = vscode.setup_project(test_dir)
    print(f"Setup project result: {success}")
    
    print("\nVS Code integration test completed.")

if __name__ == "__main__":
    main()
