"""
Test the agent's ability to create and manage projects through VS Code.
"""
import os
import unittest
from unittest.mock import patch, MagicMock
import pytest
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from agent import Agent
from project_manager import ProjectManager
from vscode_integration import VSCodeIntegration

class TestAgentProjectVSCode(unittest.TestCase):
    """Test agent's project management capabilities with VS Code integration."""
    
    @patch('llm.llm.LLM.create')
    def setUp(self, mock_llm_create):
        self.mock_llm_instance = MagicMock()
        mock_llm_create.return_value = self.mock_llm_instance
        
        self.project_manager = ProjectManager()
        self.agent = Agent(model_id="openai/gpt-4o-mini", project_manager=self.project_manager)
        self.test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_files")
        os.makedirs(self.test_dir, exist_ok=True)
        
        self.project_name = "test_project"
        self.project_dir = os.path.join(self.test_dir, self.project_name)
        os.makedirs(self.project_dir, exist_ok=True)
    
    @pytest.mark.asyncio
    async def test_create_project_and_open_vscode(self):
        """Test creating a project and opening it in VS Code."""
        with patch.object(self.agent.vscode, 'open_folder', return_value=True) as mock_open_folder, \
             patch.object(self.agent.vscode, 'create_workspace_settings', return_value=True) as mock_create_settings, \
             patch.object(self.agent.vscode, 'create_tasks_file', return_value=True) as mock_create_tasks, \
             patch.object(self.agent.vscode, 'create_terminal_profile', return_value=True) as mock_create_profile:
            
            create_details = {
                "project_type": "python",
                "name": self.project_name
            }
            
            result = await self.agent._handle_create_project(create_details, self.project_name, self.project_dir)
            self.assertEqual(result["status"], "success")
            
            open_details = {}
            result = await self.agent._handle_open_vscode(open_details, self.project_name, self.project_dir)
            self.assertEqual(result["status"], "success")
            
            mock_open_folder.assert_called_once_with(self.project_dir)
            mock_create_settings.assert_called_once_with(self.project_dir)
            mock_create_tasks.assert_called_once_with(self.project_dir)
            mock_create_profile.assert_called_once_with(self.project_dir)
    
    @pytest.mark.asyncio
    async def test_create_and_edit_file_in_vscode(self):
        """Test creating and editing a file through VS Code."""
        test_file = "main.py"
        file_path = os.path.join(self.project_dir, test_file)
        
        with patch.object(self.agent.vscode, 'open_file', return_value=True) as mock_open_file:
            create_details = {
                "file_path": test_file,
                "content": "print('Hello, World!')"
            }
            
            result = await self.agent._handle_create_file(create_details, self.project_name, self.project_dir)
            self.assertEqual(result["status"], "success")
            
            open_details = {
                "file_path": test_file,
                "line": 1,
                "column": 1
            }
            
            result = await self.agent._handle_open_vscode(open_details, self.project_name, self.project_dir)
            self.assertEqual(result["status"], "success")
            
            mock_open_file.assert_called_once_with(file_path, line=1, column=1, wait=True)
    
    @pytest.mark.asyncio
    async def test_project_task_execution(self):
        """Test executing a project task that requires VS Code."""
        with patch.object(self.agent.vscode, 'open_file', return_value=True) as mock_open_file, \
             patch.object(self.mock_llm_instance, 'generate') as mock_generate:
            
            mock_generate.return_value = '{"task_type": "open_vscode", "details": {"file_path": "main.py", "line": 5}}'
            
            result = await self.agent.execute("Open main.py in VS Code and go to line 5", self.project_name)
            self.assertEqual(result["status"], "success")
            
            mock_open_file.assert_called_once_with(
                os.path.join(self.project_dir, "main.py"),
                line=5,
                column=None,
                wait=True
            )

if __name__ == "__main__":
    unittest.main()
