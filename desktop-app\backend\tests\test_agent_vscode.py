import os
import unittest
import pytest
from unittest.mock import patch, MagicMock
from agent import Agent
from project_manager import ProjectManager

class TestAgentVSCode(unittest.TestCase):
    @patch('llm.llm.LLM.create')
    def setUp(self, mock_llm_create):
        self.mock_llm_instance = MagicMock()
        mock_llm_create.return_value = self.mock_llm_instance
        
        self.project_manager = ProjectManager()
        self.agent = Agent(model_id="openai/gpt-4", project_manager=self.project_manager)
        self.test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_files")
        os.makedirs(self.test_dir, exist_ok=True)
        
    @pytest.mark.asyncio
    async def test_handle_open_vscode_file(self):
        test_file = "test.py"
        details = {
            "file_path": test_file,
            "line": 10,
            "column": 5
        }
        
        with patch("vscode_integration.VSCodeIntegration.open_file") as mock_open_file:
            mock_open_file.return_value = True
            
            result = await self.agent._handle_open_vscode(details, "test_project", self.test_dir)
            self.assertEqual(result["status"], "success")
            mock_open_file.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_handle_open_vscode_folder(self):
        details = {}
        
        with patch("vscode_integration.VSCodeIntegration.open_folder") as mock_open_folder, \
             patch("vscode_integration.VSCodeIntegration.create_workspace_settings") as mock_create_settings, \
             patch("vscode_integration.VSCodeIntegration.create_tasks_file") as mock_create_tasks, \
             patch("vscode_integration.VSCodeIntegration.create_terminal_profile") as mock_create_profile, \
             patch("vscode_integration.VSCodeIntegration.install_recommended_extensions") as mock_install_extensions:
            
            mock_open_folder.return_value = True
            mock_create_settings.return_value = True
            mock_create_tasks.return_value = True
            mock_create_profile.return_value = True
            
            result = await self.agent._handle_open_vscode(details, "test_project", self.test_dir)
            self.assertEqual(result["status"], "success")
            
            mock_open_folder.assert_called_once()
            mock_create_settings.assert_called_once()
            mock_create_tasks.assert_called_once()
            mock_create_profile.assert_called_once()
            mock_install_extensions.assert_called_once()

if __name__ == "__main__":
    unittest.main()
