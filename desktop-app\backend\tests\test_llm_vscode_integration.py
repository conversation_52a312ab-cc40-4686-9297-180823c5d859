"""
Test LLM integration with VS Code.
"""
import os
import unittest
from unittest.mock import patch, MagicMock
import pytest
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from agent import Agent
from project_manager import ProjectManager
from vscode_integration import VSCodeIntegration
from llm.llm import LLM

class TestLLMVSCodeIntegration(unittest.TestCase):
    """Test LLM integration with VS Code."""
    
    @patch('llm.llm.LLM.create')
    def setUp(self, mock_llm_create):
        self.mock_llm_instance = MagicMock()
        mock_llm_create.return_value = self.mock_llm_instance
        
        self.project_manager = ProjectManager()
        self.agent = Agent(model_id="openai/gpt-4o-mini", project_manager=self.project_manager)
        self.test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_files")
        os.makedirs(self.test_dir, exist_ok=True)
        
        self.project_name = "test_llm_vscode"
        self.project_dir = os.path.join(self.test_dir, self.project_name)
        os.makedirs(self.project_dir, exist_ok=True)
    
    @pytest.mark.asyncio
    async def test_llm_parse_vscode_task(self):
        """Test LLM parsing a VS Code task."""
        self.mock_llm_instance.generate.return_value = '{"task_type": "open_vscode", "details": {"file_path": "main.py", "line": 5}}'
        
        with patch.object(self.agent.vscode, 'open_file', return_value=True) as mock_open_file:
            result = await self.agent.execute("Open main.py in VS Code and go to line 5", self.project_name)
            self.assertEqual(result["status"], "success")
            
            mock_open_file.assert_called_once_with(
                os.path.join(self.project_dir, "main.py"),
                line=5,
                column=None,
                wait=True
            )
    
    @pytest.mark.asyncio
    async def test_llm_generate_code_and_open_vscode(self):
        """Test LLM generating code and opening it in VS Code."""
        self.mock_llm_instance.generate.side_effect = [
            '{"task_type": "create_file", "details": {"file_path": "hello.py", "content": "print(\'Hello, World!\')"}}',
            '{"task_type": "open_vscode", "details": {"file_path": "hello.py"}}'
        ]
        
        with patch.object(self.agent.vscode, 'open_file', return_value=True) as mock_open_file:
            result1 = await self.agent.execute("Create a Python file that prints Hello, World!", self.project_name)
            self.assertEqual(result1["status"], "success")
            
            result2 = await self.agent.execute("Open the Python file in VS Code", self.project_name)
            self.assertEqual(result2["status"], "success")
            
            mock_open_file.assert_called_once_with(
                os.path.join(self.project_dir, "hello.py"),
                line=None,
                column=None,
                wait=True
            )
            
            file_path = os.path.join(self.project_dir, "hello.py")
            self.assertTrue(os.path.exists(file_path))
            with open(file_path, "r") as f:
                content = f.read()
                self.assertEqual(content, "print('Hello, World!')")

if __name__ == "__main__":
    unittest.main()
