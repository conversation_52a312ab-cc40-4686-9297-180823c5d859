import os
import unittest
from unittest.mock import patch, MagicMock
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from vscode_integration import VSCodeIntegration

class TestVSCodeDirect(unittest.TestCase):
    """Test VS Code integration directly without agent dependencies."""
    
    def setUp(self):
        self.vscode = VSCodeIntegration()
        self.test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_files")
        os.makedirs(self.test_dir, exist_ok=True)
        
        self.test_file = os.path.join(self.test_dir, "test_vscode.py")
        with open(self.test_file, "w") as f:
            f.write("print('Hello from VS Code test')")
    
    def test_open_file_with_line(self):
        """Test opening a file with line number."""
        with patch("subprocess.Popen") as mock_popen:
            mock_process = MagicMock()
            mock_process.returncode = 0
            mock_popen.return_value = mock_process
            
            success = self.vscode.open_file(self.test_file, line=10)
            self.assertTrue(success)
            
            args, _ = mock_popen.call_args
            cmd = args[0]
            self.assertIn(f"--goto={self.test_file}:10", cmd)
    
    def test_open_folder_with_workspace_setup(self):
        """Test opening a folder and setting up workspace."""
        with patch("subprocess.Popen") as mock_popen:
            mock_process = MagicMock()
            mock_process.returncode = 0
            mock_popen.return_value = mock_process
            
            success = self.vscode.open_folder(self.test_dir)
            self.assertTrue(success)
            
            success = self.vscode.create_workspace_settings(self.test_dir)
            self.assertTrue(success)
            
            vscode_dir = os.path.join(self.test_dir, ".vscode")
            self.assertTrue(os.path.exists(vscode_dir))
            
            settings_file = os.path.join(vscode_dir, "settings.json")
            self.assertTrue(os.path.exists(settings_file))

if __name__ == "__main__":
    unittest.main()
