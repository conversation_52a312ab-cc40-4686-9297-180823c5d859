import os
import unittest
from unittest.mock import patch, MagicMock
from vscode_integration import VSCodeIntegration

class TestVSCodeIntegration(unittest.TestCase):
    def setUp(self):
        self.vscode = VSCodeIntegration()
        self.test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_files")
        os.makedirs(self.test_dir, exist_ok=True)
        
    def test_open_file(self):
        test_file = os.path.join(self.test_dir, "test.py")
        with open(test_file, "w") as f:
            f.write("print('test')")
            
        with patch("subprocess.Popen") as mock_popen:
            mock_process = MagicMock()
            mock_process.returncode = 0
            mock_popen.return_value = mock_process
            
            success = self.vscode.open_file(test_file)
            self.assertTrue(success)
            mock_popen.assert_called_once()
            
            success = self.vscode.open_file(test_file, line=10)
            self.assertTrue(success)
            
            success = self.vscode.open_file(test_file, line=10, column=5)
            self.assertTrue(success)
            
    def test_open_folder(self):
        with patch("subprocess.Popen") as mock_popen:
            mock_process = MagicMock()
            mock_process.returncode = 0
            mock_popen.return_value = mock_process
            
            success = self.vscode.open_folder(self.test_dir)
            self.assertTrue(success)
            mock_popen.assert_called_once()
            
    def test_create_workspace_settings(self):
        success = self.vscode.create_workspace_settings(self.test_dir)
        self.assertTrue(success)
        
        vscode_dir = os.path.join(self.test_dir, ".vscode")
        self.assertTrue(os.path.exists(vscode_dir))
        self.assertTrue(os.path.exists(os.path.join(vscode_dir, "settings.json")))
        self.assertTrue(os.path.exists(os.path.join(vscode_dir, "extensions.json")))
        self.assertTrue(os.path.exists(os.path.join(vscode_dir, "launch.json")))
        
    def test_create_tasks_file(self):
        success = self.vscode.create_tasks_file(self.test_dir)
        self.assertTrue(success)
        
        tasks_file = os.path.join(self.test_dir, ".vscode", "tasks.json")
        self.assertTrue(os.path.exists(tasks_file))
        
    def test_create_terminal_profile(self):
        success = self.vscode.create_terminal_profile(self.test_dir)
        self.assertTrue(success)
        
        settings_file = os.path.join(self.test_dir, ".vscode", "settings.json")
        self.assertTrue(os.path.exists(settings_file))

if __name__ == "__main__":
    unittest.main()
