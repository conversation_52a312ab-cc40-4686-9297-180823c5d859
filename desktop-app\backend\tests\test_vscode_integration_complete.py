"""
Complete integration test for VS Code integration with the agent.
"""
import os
import unittest
from unittest.mock import patch, MagicMock
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from vscode_integration import VSCodeIntegration

class TestVSCodeIntegrationComplete(unittest.TestCase):
    """Test VS Code integration with complete project setup."""
    
    def setUp(self):
        self.vscode = VSCodeIntegration()
        self.test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_files")
        os.makedirs(self.test_dir, exist_ok=True)
        
        self.project_dir = os.path.join(self.test_dir, "test_project")
        os.makedirs(self.project_dir, exist_ok=True)
        
        self.python_file = os.path.join(self.project_dir, "main.py")
        with open(self.python_file, "w") as f:
            f.write("print('Hello from VS Code test')\n\ndef test_function():\n    return 'Test function'\n")
        
        self.html_file = os.path.join(self.project_dir, "index.html")
        with open(self.html_file, "w") as f:
            f.write("<html>\n<head>\n    <title>Test Page</title>\n</head>\n<body>\n    <h1>Test Page</h1>\n</body>\n</html>")
    
    def test_setup_project(self):
        """Test complete project setup with VS Code."""
        with patch("subprocess.Popen") as mock_popen:
            mock_process = MagicMock()
            mock_process.returncode = 0
            mock_popen.return_value = mock_process
            
            success = self.vscode.setup_project(self.project_dir)
            self.assertTrue(success)
            
            vscode_dir = os.path.join(self.project_dir, ".vscode")
            self.assertTrue(os.path.exists(vscode_dir))
            
            self.assertTrue(os.path.exists(os.path.join(vscode_dir, "settings.json")))
            self.assertTrue(os.path.exists(os.path.join(vscode_dir, "tasks.json")))
            self.assertTrue(os.path.exists(os.path.join(vscode_dir, "launch.json")))
            self.assertTrue(os.path.exists(os.path.join(vscode_dir, "extensions.json")))
    
    def test_open_specific_file_with_line(self):
        """Test opening a specific file with line number."""
        with patch("subprocess.Popen") as mock_popen:
            mock_process = MagicMock()
            mock_process.returncode = 0
            mock_popen.return_value = mock_process
            
            success = self.vscode.open_file(self.python_file, line=3, column=5, wait=True)
            self.assertTrue(success)
            
            args, _ = mock_popen.call_args
            cmd = args[0]
            self.assertIn(f"--goto={self.python_file}:3:5", " ".join(cmd))
            self.assertIn("--wait", cmd)

if __name__ == "__main__":
    unittest.main()
