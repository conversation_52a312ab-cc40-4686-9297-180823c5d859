"""
Test VS Code integration with proper mocking.
"""
import os
import unittest
from unittest.mock import patch, MagicMock
import sys
import pytest
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from vscode_integration import VSCodeIntegration

class TestVSCodeMock(unittest.TestCase):
    """Test VS Code integration with mocked subprocess."""
    
    def setUp(self):
        self.popen_patcher = patch('subprocess.Popen')
        self.mock_popen = self.popen_patcher.start()
        
        self.mock_process = MagicMock()
        self.mock_process.returncode = 0
        self.mock_process.communicate.return_value = (b"", b"")
        self.mock_popen.return_value = self.mock_process
        
        self.vscode = VSCodeIntegration()
        
        self.test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_files")
        os.makedirs(self.test_dir, exist_ok=True)
        
        self.test_file = os.path.join(self.test_dir, "test_vscode_mock.py")
        with open(self.test_file, "w") as f:
            f.write("print('Hello from VS Code test')")
    
    def tearDown(self):
        self.popen_patcher.stop()
    
    def test_open_file(self):
        """Test opening a file in VS Code."""
        success = self.vscode.open_file(self.test_file)
        self.assertTrue(success)
        
        self.mock_popen.assert_called_once()
        args, _ = self.mock_popen.call_args
        self.assertEqual(args[0][0], self.vscode.vscode_path)
        self.assertEqual(args[0][1], self.test_file)
    
    def test_open_file_with_line(self):
        """Test opening a file with line number."""
        success = self.vscode.open_file(self.test_file, line=10)
        self.assertTrue(success)
        
        args, _ = self.mock_popen.call_args
        self.assertIn(f"--goto={self.test_file}:10", args[0])
    
    def test_open_file_with_line_and_column(self):
        """Test opening a file with line and column numbers."""
        success = self.vscode.open_file(self.test_file, line=10, column=5)
        self.assertTrue(success)
        
        args, _ = self.mock_popen.call_args
        self.assertIn(f"--goto={self.test_file}:10:5", args[0])
    
    def test_open_folder(self):
        """Test opening a folder in VS Code."""
        success = self.vscode.open_folder(self.test_dir)
        self.assertTrue(success)
        
        args, _ = self.mock_popen.call_args
        self.assertEqual(args[0][0], self.vscode.vscode_path)
        self.assertEqual(args[0][1], self.test_dir)
    
    def test_setup_project(self):
        """Test setting up a complete project."""
        with patch.object(self.vscode, 'open_folder', return_value=True) as mock_open_folder, \
             patch.object(self.vscode, 'create_workspace_settings', return_value=True) as mock_create_settings, \
             patch.object(self.vscode, 'create_tasks_file', return_value=True) as mock_create_tasks, \
             patch.object(self.vscode, 'create_terminal_profile', return_value=True) as mock_create_profile, \
             patch.object(self.vscode, 'install_recommended_extensions', return_value=True) as mock_install_extensions:
            
            success = self.vscode.setup_project(self.test_dir)
            self.assertTrue(success)
            
            mock_open_folder.assert_called_once_with(self.test_dir)
            mock_create_settings.assert_called_once_with(self.test_dir)
            mock_create_tasks.assert_called_once_with(self.test_dir)
            mock_create_profile.assert_called_once_with(self.test_dir)
            mock_install_extensions.assert_called_once()

if __name__ == "__main__":
    unittest.main()
