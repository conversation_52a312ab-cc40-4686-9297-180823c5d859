"""
Test VS Code integration in simulation mode without requiring VS Code to be installed.
"""
import os
import unittest
from unittest.mock import patch, MagicMock
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from vscode_integration import VSCodeIntegration

class TestVSCodeSimulation(unittest.TestCase):
    """Test VS Code integration in simulation mode."""
    
    def setUp(self):
        self.popen_patcher = patch('subprocess.Popen')
        self.mock_popen = self.popen_patcher.start()
        
        self.mock_process = MagicMock()
        self.mock_process.returncode = 0
        self.mock_process.communicate.return_value = (b"", b"")
        self.mock_popen.return_value = self.mock_process
        
        self.vscode = VSCodeIntegration()
        
        self.test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "test_files")
        os.makedirs(self.test_dir, exist_ok=True)
        
        self.test_file = os.path.join(self.test_dir, "test_simulation.py")
        with open(self.test_file, "w") as f:
            f.write("print('Hello from VS Code simulation test')")
    
    def tearDown(self):
        self.popen_patcher.stop()
    
    def test_simulation_open_file(self):
        """Test opening a file in simulation mode."""
        success = self.vscode.open_file(self.test_file)
        self.assertTrue(success)
        
        self.mock_popen.assert_called_once()
        args, _ = self.mock_popen.call_args
        self.assertEqual(args[0][0], self.vscode.vscode_path)
        self.assertEqual(args[0][1], self.test_file)
    
    def test_simulation_open_file_with_line(self):
        """Test opening a file with line number in simulation mode."""
        success = self.vscode.open_file(self.test_file, line=5)
        self.assertTrue(success)
        
        args, _ = self.mock_popen.call_args
        self.assertIn(f"--goto={self.test_file}:5", args[0])
    
    def test_simulation_open_folder(self):
        """Test opening a folder in simulation mode."""
        success = self.vscode.open_folder(self.test_dir)
        self.assertTrue(success)
        
        args, _ = self.mock_popen.call_args
        self.assertEqual(args[0][0], self.vscode.vscode_path)
        self.assertEqual(args[0][1], self.test_dir)
    
    def test_simulation_create_workspace_settings(self):
        """Test creating workspace settings in simulation mode."""
        success = self.vscode.create_workspace_settings(self.test_dir)
        self.assertTrue(success)
        
        vscode_dir = os.path.join(self.test_dir, ".vscode")
        self.assertTrue(os.path.exists(vscode_dir))
        self.assertTrue(os.path.exists(os.path.join(vscode_dir, "settings.json")))
        self.assertTrue(os.path.exists(os.path.join(vscode_dir, "extensions.json")))
    
    def test_simulation_create_tasks_file(self):
        """Test creating tasks file in simulation mode."""
        success = self.vscode.create_tasks_file(self.test_dir)
        self.assertTrue(success)
        
        tasks_file = os.path.join(self.test_dir, ".vscode", "tasks.json")
        self.assertTrue(os.path.exists(tasks_file))
    
    def test_simulation_setup_project(self):
        """Test setting up a project in simulation mode."""
        success = self.vscode.setup_project(self.test_dir)
        self.assertTrue(success)
        
        vscode_dir = os.path.join(self.test_dir, ".vscode")
        self.assertTrue(os.path.exists(vscode_dir))
        self.assertTrue(os.path.exists(os.path.join(vscode_dir, "settings.json")))
        self.assertTrue(os.path.exists(os.path.join(vscode_dir, "tasks.json")))
        self.assertTrue(os.path.exists(os.path.join(vscode_dir, "launch.json")))
        self.assertTrue(os.path.exists(os.path.join(vscode_dir, "extensions.json")))

if __name__ == "__main__":
    unittest.main()
