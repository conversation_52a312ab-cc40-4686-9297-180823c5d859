"""
Update API endpoints configuration to match frontend expectations.
"""
import os
import re

def update_api_endpoints():
    """Update API endpoints to remove /api prefix from router."""
    api_endpoints_path = os.path.join(os.path.dirname(__file__), 'api_endpoints.py')
    
    with open(api_endpoints_path, 'r') as f:
        content = f.read()
    
    content = content.replace('prefix="/testing"', 'prefix="/api/testing"')
    content = content.replace('prefix="/chat"', 'prefix="/api/chat"')
    
    content = re.sub(
        r'api_router = APIRouter\(prefix="/api"\)',
        'api_router = APIRouter()',
        content
    )
    
    with open(api_endpoints_path, 'w') as f:
        f.write(content)
    
    print('Updated api_endpoints.py to match frontend API paths')

if __name__ == "__main__":
    update_api_endpoints()
