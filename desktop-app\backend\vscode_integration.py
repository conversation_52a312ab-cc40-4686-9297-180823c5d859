"""
Visual Studio Code Integration Module for AI Software Development Agent.

This module provides functionality for integrating with Visual Studio Code,
allowing the application to open files in VS Code and control the editor.
"""

import os
import sys
import json
import subprocess
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('vscode_integration.log')
    ]
)
logger = logging.getLogger('VSCodeIntegration')

class VSCodeIntegration:
    """
    Handles integration with Visual Studio Code.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the VS Code integration.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.vscode_path = self._get_vscode_path()
        
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dict containing configuration
        """
        default_config = {
            "vscode": {
                "path": "code",  # Default command for VS Code
                "extensions": [
                    "ms-python.python",
                    "dbaeumer.vscode-eslint",
                    "esbenp.prettier-vscode",
                    "ms-vscode.vscode-typescript-tslint-plugin",
                    "ritwickdey.liveserver"
                ],
                "settings": {
                    "editor.formatOnSave": True,
                    "editor.codeActionsOnSave": {
                        "source.fixAll.eslint": True
                    },
                    "python.linting.enabled": True,
                    "python.linting.pylintEnabled": True
                }
            }
        }
        
        if not config_path:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_path = os.path.join(os.path.dirname(parent_dir), "config.json")
        
        if os.path.exists(config_path):
            try:
                with open(config_path, "r") as f:
                    user_config = json.load(f)
                    if "vscode" in user_config:
                        default_config["vscode"].update(user_config["vscode"])
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
        
        return default_config
    
    def _get_vscode_path(self) -> str:
        """
        Get the path to the VS Code executable.
        
        Returns:
            Path to VS Code executable
        """
        vscode_path = self.config["vscode"]["path"]
        
        if os.path.exists(vscode_path):
            return vscode_path
        
        common_paths = [
            r"C:\Program Files\Microsoft VS Code\Code.exe",
            r"C:\Program Files (x86)\Microsoft VS Code\Code.exe",
            "/Applications/Visual Studio Code.app/Contents/Resources/app/bin/code",
            "/usr/bin/code",
            "/usr/local/bin/code",
            "/snap/bin/code"
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                logger.info(f"Found VS Code at {path}")
                return path
        
        logger.warning("Could not find VS Code executable, using default command 'code'")
        return "code"
    
    def is_vscode_installed(self) -> bool:
        """
        Check if VS Code is installed.
        
        Returns:
            True if VS Code is installed, False otherwise
        """
        try:
            result = subprocess.run(
                [self.vscode_path, "--version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False
            )
            
            return result.returncode == 0
        except Exception as e:
            logger.error(f"Error checking if VS Code is installed: {e}")
            return False
    
    def open_file(self, file_path: str, line: Optional[int] = None, column: Optional[int] = None, wait: bool = False) -> bool:
        """
        Open a file in VS Code.
        
        Args:
            file_path: Path to the file to open
            line: Line number to navigate to
            column: Column number to navigate to
            wait: Whether to wait for VS Code to open the file
            
        Returns:
            True if the file was opened successfully, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                parent_dir = os.path.dirname(file_path)
                if not os.path.exists(parent_dir):
                    os.makedirs(parent_dir, exist_ok=True)
                with open(file_path, 'w') as f:
                    f.write("")
                logger.info(f"Created empty file {file_path}")
            
            cmd = [self.vscode_path, file_path]
            
            if line is not None:
                goto_param = f"--goto={file_path}:{line}"
                if column is not None:
                    goto_param += f":{column}"
                cmd.append(goto_param)
            
            if wait:
                cmd.append("--wait")
            
            logger.info(f"Running command: {' '.join(cmd)}")
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            if wait:
                stdout, stderr = process.communicate()
                if process.returncode != 0:
                    stderr_text = stderr.decode() if stderr else "Unknown error"
                    logger.error(f"Error opening file {file_path} in VS Code: {stderr_text}")
                    return False
            
            logger.info(f"Opened file {file_path} in VS Code")
            return True
        except Exception as e:
            logger.error(f"Error opening file {file_path} in VS Code: {e}")
            return False
    
    def open_folder(self, folder_path: str) -> bool:
        """
        Open a folder in VS Code.
        
        Args:
            folder_path: Path to the folder to open
            
        Returns:
            True if the folder was opened successfully, False otherwise
        """
        try:
            cmd = [self.vscode_path, folder_path]
            
            subprocess.Popen(cmd)
            
            logger.info(f"Opened folder {folder_path} in VS Code")
            return True
        except Exception as e:
            logger.error(f"Error opening folder {folder_path} in VS Code: {e}")
            return False
    
    def install_extension(self, extension_id: str) -> bool:
        """
        Install a VS Code extension.
        
        Args:
            extension_id: ID of the extension to install
            
        Returns:
            True if the extension was installed successfully, False otherwise
        """
        try:
            cmd = [self.vscode_path, "--install-extension", extension_id]
            
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False
            )
            
            if result.returncode == 0:
                logger.info(f"Installed extension {extension_id}")
                return True
            else:
                logger.error(f"Error installing extension {extension_id}: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Error installing extension {extension_id}: {e}")
            return False
    
    def install_recommended_extensions(self) -> Dict[str, bool]:
        """
        Install recommended extensions for the project.
        
        Returns:
            Dict mapping extension IDs to installation status
        """
        results = {}
        
        for extension_id in self.config["vscode"]["extensions"]:
            results[extension_id] = self.install_extension(extension_id)
        
        return results
    
    def create_workspace_settings(self, project_dir: str) -> bool:
        """
        Create VS Code workspace settings for the project.
        
        Args:
            project_dir: Path to the project directory
            
        Returns:
            True if the settings were created successfully, False otherwise
        """
        try:
            vscode_dir = os.path.join(project_dir, ".vscode")
            os.makedirs(vscode_dir, exist_ok=True)
            
            settings_path = os.path.join(vscode_dir, "settings.json")
            with open(settings_path, "w") as f:
                json.dump(self.config["vscode"]["settings"], f, indent=2)
            
            extensions_path = os.path.join(vscode_dir, "extensions.json")
            with open(extensions_path, "w") as f:
                json.dump({
                    "recommendations": self.config["vscode"]["extensions"]
                }, f, indent=2)
            
            launch_path = os.path.join(vscode_dir, "launch.json")
            with open(launch_path, "w") as f:
                json.dump({
                    "version": "0.2.0",
                    "configurations": [
                        {
                            "name": "Python: Current File",
                            "type": "python",
                            "request": "launch",
                            "program": "${file}",
                            "console": "integratedTerminal"
                        },
                        {
                            "name": "Python: Backend Server",
                            "type": "python",
                            "request": "launch",
                            "program": "${workspaceFolder}/backend/main.py",
                            "console": "integratedTerminal"
                        },
                        {
                            "name": "JavaScript: Frontend Server",
                            "type": "chrome",
                            "request": "launch",
                            "url": "http://localhost:4200",
                            "webRoot": "${workspaceFolder}/frontend"
                        }
                    ]
                }, f, indent=2)
            
            logger.info(f"Created VS Code workspace settings in {vscode_dir}")
            return True
        except Exception as e:
            logger.error(f"Error creating VS Code workspace settings: {e}")
            return False
    
    def create_tasks_file(self, project_dir: str) -> bool:
        """
        Create VS Code tasks.json file for the project.
        
        Args:
            project_dir: Path to the project directory
            
        Returns:
            True if the tasks file was created successfully, False otherwise
        """
        try:
            vscode_dir = os.path.join(project_dir, ".vscode")
            os.makedirs(vscode_dir, exist_ok=True)
            
            tasks_path = os.path.join(vscode_dir, "tasks.json")
            with open(tasks_path, "w") as f:
                json.dump({
                    "version": "2.0.0",
                    "tasks": [
                        {
                            "label": "Start Backend Server",
                            "type": "shell",
                            "command": "cd ${workspaceFolder}/backend && python main.py",
                            "presentation": {
                                "reveal": "always",
                                "panel": "new"
                            },
                            "problemMatcher": []
                        },
                        {
                            "label": "Start Frontend Server",
                            "type": "shell",
                            "command": "cd ${workspaceFolder}/frontend && npm start",
                            "presentation": {
                                "reveal": "always",
                                "panel": "new"
                            },
                            "problemMatcher": []
                        },
                        {
                            "label": "Run Tests",
                            "type": "shell",
                            "command": "cd ${workspaceFolder}/backend && python -m pytest",
                            "presentation": {
                                "reveal": "always",
                                "panel": "new"
                            },
                            "problemMatcher": []
                        },
                        {
                            "label": "Start All",
                            "dependsOn": ["Start Backend Server", "Start Frontend Server"],
                            "problemMatcher": []
                        }
                    ]
                }, f, indent=2)
            
            logger.info(f"Created VS Code tasks file in {vscode_dir}")
            return True
        except Exception as e:
            logger.error(f"Error creating VS Code tasks file: {e}")
            return False
    
    def create_terminal_profile(self, project_dir: str) -> bool:
        """
        Create VS Code terminal profile for the project.
        
        Args:
            project_dir: Path to the project directory
            
        Returns:
            True if the terminal profile was created successfully, False otherwise
        """
        try:
            vscode_dir = os.path.join(project_dir, ".vscode")
            os.makedirs(vscode_dir, exist_ok=True)
            
            settings_path = os.path.join(vscode_dir, "settings.json")
            
            settings = {}
            if os.path.exists(settings_path):
                with open(settings_path, "r") as f:
                    settings = json.load(f)
            
            settings["terminal.integrated.profiles.windows"] = {
                "PowerShell": {
                    "source": "PowerShell",
                    "icon": "terminal-powershell"
                },
                "Command Prompt": {
                    "path": [
                        "${env:windir}\\Sysnative\\cmd.exe",
                        "${env:windir}\\System32\\cmd.exe"
                    ],
                    "args": [],
                    "icon": "terminal-cmd"
                },
                "Project Terminal": {
                    "path": "${env:windir}\\System32\\cmd.exe",
                    "args": ["/K", "cd", "/d", project_dir.replace("/", "\\")],
                    "icon": "terminal-cmd"
                }
            }
            
            settings["terminal.integrated.profiles.linux"] = {
                "bash": {
                    "path": "bash",
                    "icon": "terminal-bash"
                },
                "Project Terminal": {
                    "path": "bash",
                    "args": ["-c", f"cd {project_dir} && bash"],
                    "icon": "terminal-bash"
                }
            }
            
            settings["terminal.integrated.profiles.osx"] = {
                "bash": {
                    "path": "bash",
                    "icon": "terminal-bash"
                },
                "Project Terminal": {
                    "path": "bash",
                    "args": ["-c", f"cd {project_dir} && bash"],
                    "icon": "terminal-bash"
                }
            }
            
            settings["terminal.integrated.defaultProfile.windows"] = "Project Terminal"
            settings["terminal.integrated.defaultProfile.linux"] = "Project Terminal"
            settings["terminal.integrated.defaultProfile.osx"] = "Project Terminal"
            
            with open(settings_path, "w") as f:
                json.dump(settings, f, indent=2)
            
            logger.info(f"Created VS Code terminal profile in {vscode_dir}")
            return True
        except Exception as e:
            logger.error(f"Error creating VS Code terminal profile: {e}")
            return False
    
    def setup_project(self, project_dir: str) -> bool:
        """
        Set up VS Code for the project.
        
        Args:
            project_dir: Path to the project directory
            
        Returns:
            True if the setup was successful, False otherwise
        """
        if not self.create_workspace_settings(project_dir):
            return False
        
        if not self.create_tasks_file(project_dir):
            return False
        
        if not self.create_terminal_profile(project_dir):
            return False
        
        self.install_recommended_extensions()
        
        return self.open_folder(project_dir)
