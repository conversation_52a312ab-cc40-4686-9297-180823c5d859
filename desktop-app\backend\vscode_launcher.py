"""
VS Code Launcher for the Autonomous AI Software Development Agent.
"""
import os
import sys
import subprocess
import logging
import platform
from pathlib import Path

logger = logging.getLogger(__name__)

class VSCodeLauncher:
    """
    Launches VS Code with integrated terminal for the Autonomous AI Software Development Agent.
    """
    def __init__(self):
        """Initialize the VS Code Launcher."""
        self.vscode_path = self._find_vscode_path()
        logger.info(f"Initialized VS Code Launcher with path: {self.vscode_path}")
    
    def _find_vscode_path(self) -> str:
        """
        Find the path to the VS Code executable.
        
        Returns:
            The path to the VS Code executable.
        """
        system = platform.system()
        
        if system == "Windows":
            paths = [
                os.path.join(os.environ.get("LOCALAPPDATA", ""), "Programs", "Microsoft VS Code", "Code.exe"),
                os.path.join(os.environ.get("ProgramFiles", ""), "Microsoft VS Code", "Code.exe"),
                os.path.join(os.environ.get("ProgramFiles(x86)", ""), "Microsoft VS Code", "Code.exe")
            ]
            
            for path in paths:
                if os.path.isfile(path):
                    return path
        
        elif system == "Darwin":  # macOS
            paths = [
                "/Applications/Visual Studio Code.app/Contents/Resources/app/bin/code",
                os.path.expanduser("~/Applications/Visual Studio Code.app/Contents/Resources/app/bin/code")
            ]
            
            for path in paths:
                if os.path.isfile(path):
                    return path
        
        elif system == "Linux":
            try:
                result = subprocess.run(["which", "code"], capture_output=True, text=True)
                if result.returncode == 0:
                    return result.stdout.strip()
            except Exception as e:
                logger.warning(f"Error finding VS Code in PATH: {e}")
            
            paths = [
                "/usr/bin/code",
                "/usr/local/bin/code",
                os.path.expanduser("~/.local/bin/code")
            ]
            
            for path in paths:
                if os.path.isfile(path):
                    return path
        
        logger.warning("VS Code executable not found. Please install VS Code or set the path manually.")
        return ""
    
    def launch(self, project_path: str, open_terminal: bool = True) -> bool:
        """
        Launch VS Code with the specified project.
        
        Args:
            project_path: The path to the project to open.
            open_terminal: Whether to open the integrated terminal.
            
        Returns:
            True if VS Code was launched successfully, False otherwise.
        """
        if not self.vscode_path:
            logger.error("VS Code executable not found. Cannot launch VS Code.")
            return False
        
        if not os.path.isdir(project_path):
            logger.error(f"Project path {project_path} does not exist or is not a directory.")
            return False
        
        try:
            cmd = [self.vscode_path, project_path]
            
            if open_terminal:
                cmd.append("--new-window")
                cmd.append("--goto")
                cmd.append("terminal:create")
            
            logger.info(f"Launching VS Code with command: {cmd}")
            subprocess.Popen(cmd)
            
            return True
        except Exception as e:
            logger.error(f"Error launching VS Code: {e}")
            return False
    
    def is_installed(self) -> bool:
        """
        Check if VS Code is installed.
        
        Returns:
            True if VS Code is installed, False otherwise.
        """
        return bool(self.vscode_path)
    
    def get_vscode_path(self) -> str:
        """
        Get the path to the VS Code executable.
        
        Returns:
            The path to the VS Code executable.
        """
        return self.vscode_path
    
    def set_vscode_path(self, path: str) -> bool:
        """
        Set the path to the VS Code executable.
        
        Args:
            path: The path to the VS Code executable.
            
        Returns:
            True if the path was set successfully, False otherwise.
        """
        if not os.path.isfile(path):
            logger.error(f"VS Code executable not found at {path}.")
            return False
        
        self.vscode_path = path
        logger.info(f"Set VS Code path to {path}.")
        return True

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    launcher = VSCodeLauncher()
    
    if launcher.is_installed():
        logger.info(f"VS Code is installed at {launcher.get_vscode_path()}")
    else:
        logger.error("VS Code is not installed.")
        sys.exit(1)
    
    if len(sys.argv) > 1:
        project_path = sys.argv[1]
    else:
        project_path = os.getcwd()
    
    success = launcher.launch(project_path)
    
    if success:
        logger.info(f"VS Code launched successfully with project {project_path}")
    else:
        logger.error(f"Failed to launch VS Code with project {project_path}")
        sys.exit(1)
