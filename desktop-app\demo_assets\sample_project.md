# Sample Project: Web Application with User Authentication

## Project Description

Create a simple web application with user authentication. The application should allow users to register, log in, and access protected resources.

## Features

- User registration
- User login
- Password reset
- Protected resources
- User profile

## Technologies

- Frontend: Angular
- Backend: FastAPI
- Database: SQLite
