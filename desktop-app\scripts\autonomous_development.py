"""
Autonomous Development Script for the Autonomous AI Software Development Agent.

This script demonstrates the autonomous development process:
1. Launches VS Code with the project
2. Runs tests
3. Fixes errors
4. Retests
5. Validates functionality in the browser
"""
import os
import sys
import time
import logging
import argparse
import subprocess
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))
from backend.vscode_launcher import VSCodeLauncher
from backend.test_runner import TestRunner

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs", "autonomous_dev.log"), mode="a")
    ]
)
logger = logging.getLogger(__name__)

class AutonomousDevelopment:
    """
    Autonomous Development class that demonstrates the autonomous development process.
    """
    def __init__(self, project_path, browser_url="http://localhost:4200"):
        """
        Initialize the Autonomous Development process.
        
        Args:
            project_path: Path to the project to work on.
            browser_url: URL to the browser preview.
        """
        self.project_path = project_path
        self.browser_url = browser_url
        self.vscode_launcher = VSCodeLauncher()
        self.test_runner = TestRunner(project_path)
        
        logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
        os.makedirs(logs_dir, exist_ok=True)
        
        logger.info(f"Initialized Autonomous Development for project: {project_path}")
    
    def launch_vscode(self):
        """
        Launch VS Code with the project.
        
        Returns:
            True if VS Code was launched successfully, False otherwise.
        """
        logger.info("Launching VS Code...")
        return self.vscode_launcher.launch(self.project_path, open_terminal=True)
    
    def run_tests(self):
        """
        Run tests for the project.
        
        Returns:
            True if all tests passed, False otherwise.
        """
        logger.info("Running tests...")
        return self.test_runner.run_tests()
    
    def fix_errors(self, error_log):
        """
        Fix errors in the project.
        
        Args:
            error_log: Log of errors to fix.
            
        Returns:
            True if errors were fixed, False otherwise.
        """
        logger.info("Fixing errors...")
        logger.info(f"Errors to fix: {error_log}")
        return True
    
    def validate_in_browser(self):
        """
        Validate functionality in the browser.
        
        Returns:
            True if validation passed, False otherwise.
        """
        logger.info(f"Validating in browser at {self.browser_url}...")
        try:
            if sys.platform == "win32":
                os.startfile(self.browser_url)
            elif sys.platform == "darwin":
                subprocess.run(["open", self.browser_url], check=True)
            else:
                subprocess.run(["xdg-open", self.browser_url], check=True)
            return True
        except Exception as e:
            logger.error(f"Error opening browser: {e}")
            return False
    
    def run_autonomous_development(self):
        """
        Run the autonomous development process.
        
        Returns:
            True if the process completed successfully, False otherwise.
        """
        logger.info("Starting autonomous development process...")
        
        if not self.launch_vscode():
            logger.error("Failed to launch VS Code.")
            return False
        
        test_results = self.run_tests()
        if test_results.get("success", False):
            logger.info("All tests passed.")
        else:
            if not self.fix_errors(test_results.get("error_log", "")):
                logger.error("Failed to fix errors.")
                return False
            
            retest_results = self.run_tests()
            if not retest_results.get("success", False):
                logger.error("Tests still failing after fixes.")
                return False
        
        if not self.validate_in_browser():
            logger.error("Failed to validate in browser.")
            return False
        
        logger.info("Autonomous development process completed successfully.")
        return True

def main():
    """Main function to run the autonomous development process."""
    parser = argparse.ArgumentParser(description="Run the autonomous development process.")
    parser.add_argument("--project-path", type=str, help="Path to the project to work on.")
    parser.add_argument("--browser-url", type=str, default="http://localhost:4200", help="URL to the browser preview.")
    args = parser.parse_args()
    
    if args.project_path:
        project_path = args.project_path
    else:
        project_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
    
    if not os.path.isdir(project_path):
        logger.error(f"Project path {project_path} does not exist or is not a directory.")
        sys.exit(1)
    
    autonomous_dev = AutonomousDevelopment(project_path, args.browser_url)
    
    success = autonomous_dev.run_autonomous_development()
    
    if success:
        logger.info("Autonomous development process completed successfully.")
    else:
        logger.error("Autonomous development process failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
