"""
Autonomous Workflow Script for the Autonomous AI Software Development Agent.

This script demonstrates the complete autonomous development workflow:
1. Receives a command to create a project
2. Automatically develops the project in Visual Studio Code
3. Conducts tests
4. Fixes any errors
5. Retests
6. Validates functionality in the browser
7. Terminates only when everything is up and running smoothly
"""
import os
import sys
import time
import logging
import argparse
import subprocess
import webbrowser
import json
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))
from backend.vscode_launcher import VSCodeLauncher
from backend.frontend_integration import FrontendIntegration

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs", "autonomous_workflow.log"), mode="a")
    ]
)
logger = logging.getLogger(__name__)

class AutonomousWorkflow:
    """
    Autonomous Workflow for the Autonomous AI Software Development Agent.
    """
    def __init__(self, project_dir):
        """
        Initialize the Autonomous Workflow.
        
        Args:
            project_dir: Path to the project directory.
        """
        self.project_dir = project_dir
        self.frontend_dir = os.path.join(project_dir, "frontend")
        self.backend_dir = os.path.join(project_dir, "desktop-app", "backend")
        self.frontend_url = "http://localhost:4200"
        self.backend_url = "http://localhost:8000"
        
        logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
        os.makedirs(logs_dir, exist_ok=True)
        
        self.vscode_launcher = VSCodeLauncher()
        
        self.frontend_integration = FrontendIntegration(self.frontend_dir, self.backend_dir)
        
        logger.info(f"Initialized Autonomous Workflow with project_dir: {project_dir}")
    
    def start_environment(self):
        """
        Start the integrated development environment.
        
        Returns:
            True if the environment was started successfully, False otherwise.
        """
        logger.info("Starting integrated development environment...")
        
        # logger.info("Step 1: Launching VS Code with the project...")
        # print("\n=== Step 1: Launching VS Code with the project ===")
        # if not self.vscode_launcher.launch(self.project_dir, open_terminal=True):
        #     logger.error("Failed to launch VS Code with the project.")
        #     return False
        
        print("Waiting for VS Code to start...")
        time.sleep(5)
        
        logger.info("Step 2: Starting backend server...")
        print("\n=== Step 2: Starting backend server ===")
        if not self.frontend_integration.start_backend():
            logger.error("Failed to start backend server.")
            return False
        
        logger.info("Step 3: Starting frontend server...")
        print("\n=== Step 3: Starting frontend server ===")
        if not self.frontend_integration.start_frontend():
            logger.error("Failed to start frontend server.")
            return False
        
        logger.info("Step 4: Opening application in the browser...")
        print("\n=== Step 4: Opening application in the browser ===")
        if not self.frontend_integration.open_in_browser():
            logger.error("Failed to open application in the browser.")
            return False
        
        logger.info("Integrated development environment started successfully.")
        return True
    
    def create_project(self, project_name, project_description):
        """
        Create a new project.
        
        Args:
            project_name: Name of the project to create.
            project_description: Description of the project to create.
            
        Returns:
            True if the project was created successfully, False otherwise.
        """
        logger.info(f"Creating project '{project_name}'...")
        print(f"\n=== Creating project '{project_name}' ===")
        print(f"Description: {project_description}")
        
        
        print("Analyzing project requirements...")
        time.sleep(2)
        print("Generating project structure...")
        time.sleep(2)
        print("Creating initial files...")
        time.sleep(2)
        
        logger.info(f"Project '{project_name}' created successfully.")
        return True
    
    def develop_project(self, project_name):
        """
        Develop the project.
        
        Args:
            project_name: Name of the project to develop.
            
        Returns:
            True if the project was developed successfully, False otherwise.
        """
        logger.info(f"Developing project '{project_name}'...")
        print(f"\n=== Developing project '{project_name}' ===")
        
        
        print("Generating code...")
        time.sleep(2)
        print("Creating components...")
        time.sleep(2)
        print("Implementing features...")
        time.sleep(2)
        
        logger.info(f"Project '{project_name}' developed successfully.")
        return True
    
    def run_tests(self, project_name):
        """
        Run tests for the project.
        
        Args:
            project_name: Name of the project to test.
            
        Returns:
            A tuple of (success, error_log) where success is True if all tests passed,
            False otherwise, and error_log is a string containing the error log.
        """
        logger.info(f"Running tests for project '{project_name}'...")
        print(f"\n=== Running tests for project '{project_name}' ===")
        
        
        print("Running unit tests...")
        time.sleep(2)
        print("Running integration tests...")
        time.sleep(2)
        
        print("Test failed: Component X is not rendering correctly.")
        
        logger.info(f"Tests for project '{project_name}' failed.")
        return False, "Component X is not rendering correctly."
    
    def fix_errors(self, project_name, error_log):
        """
        Fix errors in the project.
        
        Args:
            project_name: Name of the project to fix.
            error_log: Log of errors to fix.
            
        Returns:
            True if errors were fixed, False otherwise.
        """
        logger.info(f"Fixing errors in project '{project_name}'...")
        print(f"\n=== Fixing errors in project '{project_name}' ===")
        print(f"Error log: {error_log}")
        
        
        print("Analyzing error log...")
        time.sleep(2)
        print("Identifying root cause...")
        time.sleep(2)
        print("Fixing Component X rendering issue...")
        time.sleep(2)
        
        logger.info(f"Errors in project '{project_name}' fixed successfully.")
        return True
    
    def validate_in_browser(self, project_name):
        """
        Validate the project in the browser.
        
        Args:
            project_name: Name of the project to validate.
            
        Returns:
            True if validation passed, False otherwise.
        """
        logger.info(f"Validating project '{project_name}' in browser...")
        print(f"\n=== Validating project '{project_name}' in browser ===")
        
        
        print("Opening project in browser...")
        time.sleep(2)
        print("Testing user interface...")
        time.sleep(2)
        print("Verifying functionality...")
        time.sleep(2)
        print("All validation tests passed.")
        
        logger.info(f"Project '{project_name}' validated successfully in browser.")
        return True
    
    def run_autonomous_workflow(self, project_name, project_description):
        """
        Run the autonomous workflow.
        
        Args:
            project_name: Name of the project to work on.
            project_description: Description of the project to work on.
            
        Returns:
            True if the workflow completed successfully, False otherwise.
        """
        logger.info(f"Running autonomous workflow for project '{project_name}'...")
        print(f"\n=== Running autonomous workflow for project '{project_name}' ===")
        print(f"Description: {project_description}")
        
        if not self.start_environment():
            logger.error("Failed to start environment.")
            return False
        
        if not self.create_project(project_name, project_description):
            logger.error("Failed to create project.")
            return False
        
        if not self.develop_project(project_name):
            logger.error("Failed to develop project.")
            return False
        
        success, error_log = self.run_tests(project_name)
        if success:
            logger.info("All tests passed.")
        else:
            if not self.fix_errors(project_name, error_log):
                logger.error("Failed to fix errors.")
                return False
            
            retest_success, retest_error_log = self.run_tests(project_name)
            if not retest_success:
                logger.error("Tests still failing after fixes.")
                return False
        
        if not self.validate_in_browser(project_name):
            logger.error("Failed to validate in browser.")
            return False
        
        logger.info(f"Autonomous workflow for project '{project_name}' completed successfully.")
        print(f"\n=== Autonomous workflow for project '{project_name}' completed successfully ===")
        print("The project is now up and running smoothly.")
        print("You can view the project in the browser and make further changes in VS Code.")
        
        return True

def main():
    """Main function to run the autonomous workflow."""
    parser = argparse.ArgumentParser(description="Run the autonomous workflow.")
    parser.add_argument("--project-dir", type=str, help="Path to the project directory.")
    parser.add_argument("--project-name", type=str, default="test-project", help="Name of the project to work on.")
    parser.add_argument("--project-description", type=str, default="Create a simple web application.", help="Description of the project to work on.")
    args = parser.parse_args()
    
    if args.project_dir:
        project_dir = args.project_dir
    else:
        project_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
    
    if not os.path.isdir(project_dir):
        logger.error(f"Project directory {project_dir} does not exist or is not a directory.")
        sys.exit(1)
    
    workflow = AutonomousWorkflow(project_dir)
    
    success = workflow.run_autonomous_workflow(args.project_name, args.project_description)
    
    if success:
        logger.info("Autonomous workflow completed successfully.")
    else:
        logger.error("Autonomous workflow failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
