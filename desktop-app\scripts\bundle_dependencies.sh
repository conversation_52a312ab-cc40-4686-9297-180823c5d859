echo "Bundling dependencies for Autonomous AI Agent..."

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BUNDLE_DIR="$SCRIPT_DIR/../resources/bundled"
PYTHON_BUNDLE="$BUNDLE_DIR/python"
NODE_BUNDLE="$BUNDLE_DIR/node"
SQLITE_BUNDLE="$BUNDLE_DIR/sqlite"

mkdir -p "$PYTHON_BUNDLE"
mkdir -p "$NODE_BUNDLE"
mkdir -p "$SQLITE_BUNDLE"

echo "Bundling Python packages..."
cd "$SCRIPT_DIR/../backend"
python3 -m pip download -r requirements.txt -d "$PYTHON_BUNDLE"

echo "Bundling Node.js packages..."
cd "$SCRIPT_DIR/../frontend"
npm pack $(npm list --prod --parseable | sed 's/.*node_modules\///' | grep -v '^$') --pack-destination="$NODE_BUNDLE"

echo "Bundling SQLite..."
wget https://www.sqlite.org/2023/sqlite-autoconf-3430000.tar.gz -O "$SQLITE_BUNDLE/sqlite.tar.gz"
mkdir -p "$SQLITE_BUNDLE/src"
tar -xzf "$SQLITE_BUNDLE/sqlite.tar.gz" -C "$SQLITE_BUNDLE/src" --strip-components=1
rm "$SQLITE_BUNDLE/sqlite.tar.gz"

echo "Creating empty SQLite database..."
mkdir -p "$SCRIPT_DIR/../resources/data"
sqlite3 "$SCRIPT_DIR/../resources/data/agent.db" <<EOF
CREATE TABLE IF NOT EXISTS projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER,
    content TEXT,
    is_user BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id)
);

CREATE TABLE IF NOT EXISTS files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER,
    path TEXT,
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id)
);

CREATE TABLE IF NOT EXISTS config (
    key TEXT PRIMARY KEY,
    value TEXT
);

INSERT OR IGNORE INTO config (key, value) VALUES ('openai_api_key', '');
INSERT OR IGNORE INTO config (key, value) VALUES ('use_google_search', 'true');
INSERT OR IGNORE INTO config (key, value) VALUES ('vscode_path', 'code');
EOF

echo "Updating setup scripts to use bundled dependencies..."

cat > "$SCRIPT_DIR/setup.bat" << 'EOL'
@echo off
echo Starting Autonomous AI Agent Setup...

REM Create project directory structure
if not exist "C:\SourceProjects\AutonomousAI" (
    echo Creating project directory...
    mkdir "C:\SourceProjects\AutonomousAI"
    mkdir "C:\SourceProjects\AutonomousAI\backend"
    mkdir "C:\SourceProjects\AutonomousAI\frontend"
    mkdir "C:\SourceProjects\AutonomousAI\resources"
    mkdir "C:\SourceProjects\AutonomousAI\data"
    mkdir "C:\SourceProjects\AutonomousAI\logs"
)

REM Copy application files
echo Copying application files...
xcopy /E /I /Y "%~dp0..\backend" "C:\SourceProjects\AutonomousAI\backend"
xcopy /E /I /Y "%~dp0..\frontend" "C:\SourceProjects\AutonomousAI\frontend"
xcopy /E /I /Y "%~dp0..\resources" "C:\SourceProjects\AutonomousAI\resources"

REM Copy bundled SQLite database
echo Copying SQLite database...
xcopy /Y "%~dp0..\resources\data\agent.db" "C:\SourceProjects\AutonomousAI\data\"

REM Set up Python virtual environment using bundled packages
echo Setting up Python environment...
cd "C:\SourceProjects\AutonomousAI\backend"
if not exist "venv" (
    python -m venv venv
)
call venv\Scripts\activate.bat
pip install --no-index --find-links="C:\SourceProjects\AutonomousAI\resources\bundled\python" -r requirements.txt

REM Install Node.js dependencies from bundled packages
echo Setting up Node.js environment...
cd "C:\SourceProjects\AutonomousAI\frontend"
npm install --offline --no-registry --global-style --cache="C:\SourceProjects\AutonomousAI\resources\bundled\node"

REM Create configuration file
echo Creating configuration file...
echo {> "C:\SourceProjects\AutonomousAI\config.json"
echo   "openai_api_key": "",>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "use_google_search": true,>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "sqlite_path": "C:\\SourceProjects\\AutonomousAI\\data\\agent.db",>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "log_path": "C:\\SourceProjects\\AutonomousAI\\logs",>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "vscode_path": "C:\\Program Files\\Microsoft VS Code\\Code.exe">> "C:\SourceProjects\AutonomousAI\config.json"
echo }>> "C:\SourceProjects\AutonomousAI\config.json"

REM Create start script
echo Creating start script...
echo @echo off> "C:\SourceProjects\AutonomousAI\start.bat"
echo echo Starting Autonomous AI Agent...>> "C:\SourceProjects\AutonomousAI\start.bat"
echo start "" "C:\Program Files\Microsoft VS Code\Code.exe" "C:\SourceProjects\AutonomousAI">> "C:\SourceProjects\AutonomousAI\start.bat"
echo cd "C:\SourceProjects\AutonomousAI\backend">> "C:\SourceProjects\AutonomousAI\start.bat"
echo call venv\Scripts\activate.bat>> "C:\SourceProjects\AutonomousAI\start.bat"
echo start /B python main.py>> "C:\SourceProjects\AutonomousAI\start.bat"
echo cd "C:\SourceProjects\AutonomousAI\frontend">> "C:\SourceProjects\AutonomousAI\start.bat"
echo start /B npm start>> "C:\SourceProjects\AutonomousAI\start.bat"
echo echo Autonomous AI Agent started successfully!>> "C:\SourceProjects\AutonomousAI\start.bat"

echo Setup completed successfully!
echo Please edit the config.json file to add your API keys.
echo Run start.bat to launch the application.
pause
EOL

cat > "$SCRIPT_DIR/setup.sh" << 'EOL'
echo "Starting Autonomous AI Agent Setup..."

PROJECT_DIR="$HOME/SourceProjects/AutonomousAI"
if [ ! -d "$PROJECT_DIR" ]; then
    echo "Creating project directory..."
    mkdir -p "$PROJECT_DIR"
    mkdir -p "$PROJECT_DIR/backend"
    mkdir -p "$PROJECT_DIR/frontend"
    mkdir -p "$PROJECT_DIR/resources"
    mkdir -p "$PROJECT_DIR/data"
    mkdir -p "$PROJECT_DIR/logs"
fi

echo "Copying application files..."
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cp -r "$SCRIPT_DIR/../backend/"* "$PROJECT_DIR/backend/"
cp -r "$SCRIPT_DIR/../frontend/"* "$PROJECT_DIR/frontend/"
cp -r "$SCRIPT_DIR/../resources/"* "$PROJECT_DIR/resources/"

echo "Copying SQLite database..."
cp "$SCRIPT_DIR/../resources/data/agent.db" "$PROJECT_DIR/data/"

echo "Setting up Python environment..."
cd "$PROJECT_DIR/backend"
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi
source venv/bin/activate
pip install --no-index --find-links="$PROJECT_DIR/resources/bundled/python" -r requirements.txt

echo "Setting up Node.js environment..."
cd "$PROJECT_DIR/frontend"
npm install --offline --no-registry --global-style --cache="$PROJECT_DIR/resources/bundled/node"

echo "Creating configuration file..."
cat > "$PROJECT_DIR/config.json" << EOF
{
  "openai_api_key": "",
  "use_google_search": true,
  "sqlite_path": "$PROJECT_DIR/data/agent.db",
  "log_path": "$PROJECT_DIR/logs",
  "vscode_path": "/usr/bin/code"
}
EOF

echo "Creating start script..."
cat > "$PROJECT_DIR/start.sh" << EOF
echo "Starting Autonomous AI Agent..."
code "$PROJECT_DIR" &
cd "$PROJECT_DIR/backend"
source venv/bin/activate
python main.py &
cd "$PROJECT_DIR/frontend"
npm start &
echo "Autonomous AI Agent started successfully!"
EOF
chmod +x "$PROJECT_DIR/start.sh"

echo "Setup completed successfully!"
echo "Please edit the config.json file to add your API keys."
echo "Run start.sh to launch the application."
EOL
chmod +x "$SCRIPT_DIR/setup.sh"

echo "Dependencies bundled successfully!"
