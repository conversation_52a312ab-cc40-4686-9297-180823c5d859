"""
Demonstration script for autonomous testing functionality.

This script demonstrates how the autonomous testing system works by:
1. Creating a sample project with test files
2. Running autonomous tests on the project
3. Introducing errors and showing how they are automatically fixed
4. Validating the fixed code in the browser
"""

import os
import sys
import json
import logging
import time
import argparse
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.integration import Integration
from backend.autonomous_testing import AutonomousTesting
from backend.vscode_integration import VSCodeIntegration
from backend.terminal_integration import TerminalIntegration
from backend.browser_integration import BrowserIntegration

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('demo_autonomous_testing.log')
    ]
)
logger = logging.getLogger('DemoAutonomousTesting')

def create_sample_project(project_dir: str) -> None:
    """
    Create a sample project with test files.
    
    Args:
        project_dir: Path to the project directory
    """
    logger.info(f"Creating sample project in {project_dir}")
    
    os.makedirs(project_dir, exist_ok=True)
    
    src_dir = os.path.join(project_dir, "src")
    tests_dir = os.path.join(project_dir, "tests")
    
    os.makedirs(src_dir, exist_ok=True)
    os.makedirs(tests_dir, exist_ok=True)
    
    with open(os.path.join(src_dir, "__init__.py"), "w") as f:
        f.write("")
    
    with open(os.path.join(src_dir, "utils.py"), "w") as f:
        f.write("""
def validate_email(email):
    \"\"\"Validate an email address.\"\"\"
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def format_phone_number(phone):
    \"\"\"Format a phone number to (XXX) XXX-XXXX format.\"\"\"
    import re
    digits = re.sub(r'\D', '', phone)
    if len(digits) != 10:
        return None
    return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"

def truncate_string(text, max_length=50):
    \"\"\"Truncate a string to a maximum length and add ellipsis if needed.\"\"\"
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def slugify(text):
    \"\"\"Convert a string to a URL-friendly slug.\"\"\"
    import re
    # Convert to lowercase
    text = text.lower()
    # Replace spaces with hyphens
    text = re.sub(r'\s+', '-', text)
    # Remove non-alphanumeric characters
    text = re.sub(r'[^a-z0-9-]', '', text)
    # Remove duplicate hyphens
    text = re.sub(r'-+', '-', text)
    return text
""")
    
    with open(os.path.join(tests_dir, "__init__.py"), "w") as f:
        f.write("")
    
    with open(os.path.join(tests_dir, "test_utils.py"), "w") as f:
        f.write("""
import pytest
from src.utils import validate_email, format_phone_number, truncate_string, slugify

def test_validate_email():
    assert validate_email("<EMAIL>") == True
    assert validate_email("<EMAIL>") == True
    assert validate_email("<EMAIL>") == True
    assert validate_email("invalid-email") == False
    assert validate_email("missing@domain") == False

def test_format_phone_number():
    assert format_phone_number("1234567890") == "(*************"
    assert format_phone_number("************") == "(*************"
    assert format_phone_number("(*************") == "(*************"
    assert format_phone_number("12345") == None
    assert format_phone_number("123456789012") == None

def test_truncate_string():
    assert truncate_string("This is a short string") == "This is a short string"
    assert truncate_string("This is a long string that should be truncated", 20) == "This is a long str..."
    assert truncate_string("", 10) == ""

def test_slugify():
    assert slugify("Hello World") == "hello-world"
    assert slugify("This is a test!") == "this-is-a-test"
    assert slugify("Multiple   spaces") == "multiple-spaces"
    assert slugify("special-@#characters") == "special-characters"
""")
    
    with open(os.path.join(project_dir, "index.html"), "w") as f:
        f.write("""
<!DOCTYPE html>
<html>
<head>
    <title>Utils Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input[type="text"], 
        input[type="email"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-left: 5px solid #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Utility Functions Demo</h1>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" placeholder="Enter an email to validate">
            <button onclick="validateEmail()">Validate</button>
        </div>
        
        <div class="form-group">
            <label for="phone">Phone Number:</label>
            <input type="text" id="phone" placeholder="Enter a phone number to format">
            <button onclick="formatPhone()">Format</button>
        </div>
        
        <div class="form-group">
            <label for="text">Text:</label>
            <input type="text" id="text" placeholder="Enter text to truncate or slugify">
            <button onclick="truncateText()">Truncate</button>
            <button onclick="slugifyText()">Slugify</button>
        </div>
        
        <div class="result" id="result">
            Results will appear here.
        </div>
    </div>
    
    <script>
        function validateEmail() {
            const email = document.getElementById('email').value;
            const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            const result = pattern.test(email);
            document.getElementById('result').innerText = 
                `Email validation result: ${result ? 'Valid email' : 'Invalid email'}`;
        }
        
        function formatPhone() {
            const phone = document.getElementById('phone').value;
            const digits = phone.replace(/\D/g, '');
            let result = 'Invalid phone number (must be 10 digits)';
            
            if (digits.length === 10) {
                result = `Formatted phone: (${digits.slice(0,3)}) ${digits.slice(3,6)}-${digits.slice(6)}`;
            }
            
            document.getElementById('result').innerText = result;
        }
        
        function truncateText() {
            const text = document.getElementById('text').value;
            const maxLength = 20;
            let result = text;
            
            if (text.length > maxLength) {
                result = text.slice(0, maxLength - 3) + '...';
            }
            
            document.getElementById('result').innerText = `Truncated text: ${result}`;
        }
        
        function slugifyText() {
            const text = document.getElementById('text').value;
            let slug = text.toLowerCase()
                .replace(/\s+/g, '-')        // Replace spaces with -
                .replace(/[^\w\-]+/g, '')    // Remove non-word chars
                .replace(/\-\-+/g, '-')      // Replace multiple - with single -
                .replace(/^-+/, '')          // Trim - from start
                .replace(/-+$/, '');         // Trim - from end
            
            document.getElementById('result').innerText = `Slugified text: ${slug}`;
        }
    </script>
</body>
</html>
""")
    
    with open(os.path.join(project_dir, "pytest.ini"), "w") as f:
        f.write("""
[pytest]
testpaths = tests
python_files = test_*.py
python_functions = test_*
""")
    
    with open(os.path.join(project_dir, "requirements.txt"), "w") as f:
        f.write("""
pytest==7.3.1
""")
    
    logger.info("Sample project created successfully")

def introduce_error(project_dir: str) -> None:
    """
    Introduce an error in the sample project.
    
    Args:
        project_dir: Path to the project directory
    """
    logger.info("Introducing error in the sample project")
    
    utils_path = os.path.join(project_dir, "src", "utils.py")
    with open(utils_path, "w") as f:
        f.write("""
def validate_email(email):
    \"\"\"Validate an email address.\"\"\"
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def format_phone_number(phone):
    \"\"\"Format a phone number to (XXX) XXX-XXXX format.\"\"\"
    import re
    digits = re.sub(r'\D', '', phone)
    # Error: not checking length properly
    return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"

def truncate_string(text, max_length=50):
    \"\"\"Truncate a string to a maximum length and add ellipsis if needed.\"\"\"
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def slugify(text):
    \"\"\"Convert a string to a URL-friendly slug.\"\"\"
    import re
    # Convert to lowercase
    text = text.lower()
    # Replace spaces with hyphens
    text = re.sub(r'\s+', '-', text)
    # Remove non-alphanumeric characters
    text = re.sub(r'[^a-z0-9-]', '', text)
    # Remove duplicate hyphens
    text = re.sub(r'-+', '-', text)
    return text
""")
    
    logger.info("Error introduced in utils.py")

def main() -> int:
    """
    Main entry point.
    
    Returns:
        Exit code
    """
    parser = argparse.ArgumentParser(description="Demonstrate autonomous testing functionality")
    parser.add_argument("--project-dir", default=os.path.expanduser("~/SourceProjects/AutonomousAI/demo"), help="Path to the project directory")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    integration = Integration()
    autonomous_testing = AutonomousTesting(integration=integration)
    
    create_sample_project(args.project_dir)
    
    logger.info("Opening project in VS Code")
    integration.vscode.open_folder(args.project_dir)
    
    logger.info("Running tests")
    test_results = autonomous_testing.run_autonomous_testing(
        project_dir=args.project_dir,
        test_dir="tests",
        browser_url=f"file://{os.path.abspath(os.path.join(args.project_dir, 'index.html'))}"
    )
    
    logger.info(f"Test results: {test_results['success']}")
    
    logger.info("Introducing error")
    introduce_error(args.project_dir)
    
    logger.info("Running tests with error")
    test_results = autonomous_testing.run_autonomous_testing(
        project_dir=args.project_dir,
        test_dir="tests",
        browser_url=f"file://{os.path.abspath(os.path.join(args.project_dir, 'index.html'))}"
    )
    
    logger.info(f"Test results after fixing: {test_results['success']}")
    
    logger.info("Opening browser to view the utility functions demo")
    integration.browser.open_url(f"file://{os.path.abspath(os.path.join(args.project_dir, 'index.html'))}")
    
    logger.info("Autonomous testing demonstration completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
