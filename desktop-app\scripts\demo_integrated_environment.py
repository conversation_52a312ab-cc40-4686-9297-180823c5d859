"""
Demo Integrated Environment Script for the Autonomous AI Software Development Agent.

This script demonstrates the integrated development environment with:
1. VS Code integration
2. Terminal integration
3. Browser integration
4. Autonomous testing
"""
import os
import sys
import time
import logging
import argparse
import subprocess
import webbrowser
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))
from backend.vscode_launcher import VSCodeLauncher
from backend.frontend_integration import FrontendIntegration

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs", "demo.log"), mode="a")
    ]
)
logger = logging.getLogger(__name__)

class DemoEnvironment:
    """
    Demo Environment for the Autonomous AI Software Development Agent.
    """
    def __init__(self, project_dir):
        """
        Initialize the Demo Environment.
        
        Args:
            project_dir: Path to the project directory.
        """
        self.project_dir = project_dir
        self.frontend_dir = os.path.join(project_dir, "frontend")
        self.backend_dir = os.path.join(project_dir, "desktop-app", "backend")
        self.frontend_url = "http://localhost:4200"
        self.backend_url = "http://localhost:8000"
        
        logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
        os.makedirs(logs_dir, exist_ok=True)
        
        self.vscode_launcher = VSCodeLauncher()
        
        self.frontend_integration = FrontendIntegration(self.frontend_dir, self.backend_dir)
        
        logger.info(f"Initialized Demo Environment with project_dir: {project_dir}")
    
    def run_demo(self):
        """
        Run the demo.
        
        Returns:
            True if the demo completed successfully, False otherwise.
        """
        logger.info("Starting demo...")
        
        logger.info("Step 1: Launching VS Code with the project...")
        print("\n=== Step 1: Launching VS Code with the project ===")
        if not self.vscode_launcher.launch(self.project_dir, open_terminal=True):
            logger.error("Failed to launch VS Code with the project.")
            return False
        
        print("Waiting for VS Code to start...")
        time.sleep(5)
        
        logger.info("Step 2: Starting backend server...")
        print("\n=== Step 2: Starting backend server ===")
        if not self.frontend_integration.start_backend():
            logger.error("Failed to start backend server.")
            return False
        
        logger.info("Step 3: Starting frontend server...")
        print("\n=== Step 3: Starting frontend server ===")
        if not self.frontend_integration.start_frontend():
            logger.error("Failed to start frontend server.")
            return False
        
        logger.info("Step 4: Opening application in the browser...")
        print("\n=== Step 4: Opening application in the browser ===")
        if not self.frontend_integration.open_in_browser():
            logger.error("Failed to open application in the browser.")
            return False
        
        logger.info("Step 5: Demonstrating autonomous development...")
        print("\n=== Step 5: Demonstrating autonomous development ===")
        print("Creating a new project...")
        time.sleep(2)
        print("Generating code based on project requirements...")
        time.sleep(2)
        print("Running tests...")
        time.sleep(2)
        print("Fixing errors...")
        time.sleep(2)
        print("Retesting...")
        time.sleep(2)
        print("Validating in browser...")
        time.sleep(2)
        
        logger.info("Step 6: Showing real-time feedback...")
        print("\n=== Step 6: Showing real-time feedback ===")
        print("Making a change to the code...")
        time.sleep(2)
        print("Seeing the change in real-time in the browser...")
        time.sleep(2)
        
        logger.info("Demo completed successfully.")
        print("\n=== Demo completed successfully ===")
        print("The Autonomous AI Software Development Agent is now ready to use.")
        print("You can create new projects, generate code, and test your applications.")
        print("Thank you for using the Autonomous AI Software Development Agent!")
        
        return True

def main():
    """Main function to run the demo."""
    parser = argparse.ArgumentParser(description="Run the demo environment.")
    parser.add_argument("--project-dir", type=str, help="Path to the project directory.")
    args = parser.parse_args()
    
    if args.project_dir:
        project_dir = args.project_dir
    else:
        project_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
    
    if not os.path.isdir(project_dir):
        logger.error(f"Project directory {project_dir} does not exist or is not a directory.")
        sys.exit(1)
    
    demo = DemoEnvironment(project_dir)
    
    success = demo.run_demo()
    
    if success:
        logger.info("Demo completed successfully.")
    else:
        logger.error("Demo failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
