# Demo Video Instructions

## Requirements

- Screen recording software (e.g., OBS Studio, Camtasia, or built-in screen recorder)
- Microphone for narration
- The Autonomous AI Software Development Agent installed and configured

## Setup

1. Make sure the agent is installed and configured by running the setup script:
   ```bash
   # For Linux/macOS
   ./desktop-app/scripts/setup_integrated_environment.sh

   # For Windows
   .\desktop-app\scripts\setup_integrated_environment.bat
   ```

2. Set up your screen recording software to capture your entire screen or the relevant windows.

## Recording the Demo

1. Start your screen recording software.

2. Follow the demo script (`demo_script.md`) to demonstrate the features of the agent.

3. Narrate the demo as you go, explaining what you're doing and what the agent is doing.

4. Keep the demo concise and focused on the key features:
   - Installation and setup
   - VS Code integration
   - Autonomous development workflow
   - Real-time feedback
   - Autonomous testing

5. End the recording when you've completed the demo.

## Editing the Video

1. Trim the beginning and end of the video to remove any unnecessary content.

2. Add captions or annotations to highlight key points.

3. Add a title screen with the name of the agent.

4. Add a conclusion screen with contact information or next steps.

## Publishing the Video

1. Export the video in a common format (e.g., MP4).

2. Upload the video to a video sharing platform or include it with the project documentation.

3. Share the video link with users who want to learn about the agent.
