# Autonomous AI Software Development Agent - Demo Video Script

## Introduction (0:00 - 0:30)
- Welcome to the Autonomous AI Software Development Agent demonstration
- Brief overview of what the agent does: creates complete projects with AI assistance
- Mention that the agent works locally with free/community tools
- Explain that it integrates with external model providers via API keys

## Installation Process (0:30 - 2:00)
- Show the installation directory: C:\SourceProjects\AutonomousAI
- Run the setup script: `setup.bat` (Windows) or `setup.sh` (macOS/Linux)
- Show the installation process:
  - Installing Python dependencies
  - Installing Node.js dependencies
  - Setting up SQLite database
  - Configuring the application

## Configuration (2:00 - 3:00)
- Open the application
- Navigate to the Configuration page
- Show how to configure API keys for different model providers:
  - OpenAI
  - Ollama
  - LM Studio
- Explain that only API keys are needed, no other external services

## Creating a New Project (3:00 - 4:30)
- Navigate to the Projects page
- Click "Start a New Project"
- Enter project details:
  - Name: "Demo Web Application"
  - Description: "A simple web application with user authentication and data visualization"
  - Select technologies: Angular, Node.js, Express
- Click "Create Project"

## VS Code Integration (4:30 - 6:00)
- Show VS Code automatically opening with the project
- Point out the integrated terminal within VS Code
- Demonstrate how the terminal is used to run commands
- Show the project structure in VS Code
- Explain how the agent uses VS Code for development

## Autonomous Development Process (6:00 - 9:00)
- Show the agent planning the project structure
- Demonstrate code generation for components
- Show the agent writing tests
- Highlight how the agent identifies and fixes errors automatically
- Show the terminal output during the build process
- Demonstrate how everything is handled autonomously without user interaction

## Real-Time Feedback (9:00 - 10:30)
- Show the browser preview updating in real-time as code changes
- Make a small change to a component
- Demonstrate how the change is immediately reflected in the browser
- Show tests running automatically when code changes
- Highlight the seamless integration between coding and testing

## Complete Project Demonstration (10:30 - 12:00)
- Show the completed project running in the browser
- Demonstrate the main features of the generated application
- Show the documentation generated by the agent
- Highlight the quality of the code and documentation

## Conclusion (12:00 - 13:00)
- Recap the key features of the Autonomous AI Software Development Agent
- Emphasize that it works locally with free/community tools
- Mention that it only requires API keys for external model providers
- Thank the viewer for watching

## Technical Setup Notes for Recording

### Environment Setup
- Use a clean Windows 10/11 machine with at least 8GB RAM
- Install OBS Studio for screen recording
- Set display resolution to 1920x1080
- Install Visual Studio Code (latest version)
- Install Node.js 14+ and npm 6+
- Install Python 3.8+
- Create the directory: C:\SourceProjects\AutonomousAI

### Recording Settings
- Resolution: 1920x1080
- Frame rate: 30fps
- Audio: System audio + microphone
- Format: MP4 (H.264)

### Demo Flow Checklist
1. Show desktop with installation directory
2. Run setup script
3. Configure API keys
4. Create new project
5. Show VS Code integration
6. Demonstrate autonomous development
7. Show real-time feedback
8. Demonstrate completed project
9. Conclude with summary

### Command Reference
```batch
:: Windows Setup
cd C:\SourceProjects\AutonomousAI
setup.bat

:: Start Application
cd C:\SourceProjects\AutonomousAI
start_application.bat
```

```bash
# macOS/Linux Setup
cd ~/SourceProjects/AutonomousAI
./setup.sh

# Start Application
cd ~/SourceProjects/AutonomousAI
./start_application.sh
```
