"""
Demonstration script for VS Code integration with the autonomous agent.
"""
import os
import sys
import time
import logging
import argparse
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from backend.vscode_integration import VSCodeIntegration
from backend.project_manager import ProjectManager
from backend.agent import Agent

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs', 'vscode_demo.log'))
    ]
)
logger = logging.getLogger(__name__)

def create_demo_project(project_manager, project_name):
    """Create a demo project with sample files."""
    logger.info(f"Creating demo project: {project_name}")
    
    project_dir = project_manager.get_project_dir(project_name)
    if not project_dir:
        project_manager.create_project(project_name)
        project_dir = project_manager.get_project_dir(project_name)
    
    python_file = "main.py"
    python_content = """#!/usr/bin/env python3
\"\"\"
Sample Python application created by the Autonomous AI Agent.
\"\"\"

def greet(name):
    \"\"\"Greet the user.\"\"\"
    return f"Hello, {name}!"

def calculate_sum(a, b):
    \"\"\"Calculate the sum of two numbers.\"\"\"
    return a + b

def main():
    \"\"\"Main function.\"\"\"
    name = input("Enter your name: ")
    print(greet(name))
    
    a = float(input("Enter first number: "))
    b = float(input("Enter second number: "))
    print(f"Sum: {calculate_sum(a, b)}")

if __name__ == "__main__":
    main()
"""
    project_manager.add_file_to_project(project_name, python_file, python_content)
    
    html_file = "index.html"
    html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autonomous AI Agent Demo</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>Autonomous AI Agent</h1>
        <p>This page was created by the Autonomous AI Agent</p>
    </header>
    
    <main>
        <section>
            <h2>Features</h2>
            <ul>
                <li>Autonomous project creation</li>
                <li>VS Code integration</li>
                <li>Web search capabilities</li>
                <li>Testing and debugging</li>
            </ul>
        </section>
    </main>
    
    <footer>
        <p>&copy; 2025 Autonomous AI Agent</p>
    </footer>
    
    <script src="script.js"></script>
</body>
</html>
"""
    project_manager.add_file_to_project(project_name, html_file, html_content)
    
    css_file = "styles.css"
    css_content = """body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    color: #333;
}

header {
    background-color: #4CAF50;
    color: white;
    text-align: center;
    padding: 1rem;
}

main {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
}

footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 1rem;
    position: fixed;
    bottom: 0;
    width: 100%;
}
"""
    project_manager.add_file_to_project(project_name, css_file, css_content)
    
    js_file = "script.js"
    js_content = """// Sample JavaScript file created by the Autonomous AI Agent

document.addEventListener('DOMContentLoaded', function() {
    console.log('Document loaded');
    
    // Add event listener to header
    const header = document.querySelector('header');
    header.addEventListener('click', function() {
        alert('Welcome to the Autonomous AI Agent demo!');
    });
});
"""
    project_manager.add_file_to_project(project_name, js_file, js_content)
    
    logger.info(f"Demo project {project_name} created successfully")
    return project_dir

def demonstrate_vscode_integration(project_name):
    """Demonstrate VS Code integration with the autonomous agent."""
    logger.info("Starting VS Code integration demonstration")
    
    project_manager = ProjectManager()
    project_dir = create_demo_project(project_manager, project_name)
    
    vscode = VSCodeIntegration()
    
    if not vscode.is_vscode_installed():
        logger.error("VS Code is not installed or not found in PATH")
        return False
    
    logger.info(f"Opening project folder in VS Code: {project_dir}")
    success = vscode.open_folder(project_dir)
    if not success:
        logger.error("Failed to open project folder in VS Code")
        return False
    
    logger.info("Creating workspace settings")
    vscode.create_workspace_settings(project_dir)
    
    logger.info("Creating tasks file")
    vscode.create_tasks_file(project_dir)
    
    logger.info("Creating terminal profile")
    vscode.create_terminal_profile(project_dir)
    
    logger.info("Waiting for VS Code to initialize...")
    time.sleep(2)
    
    logger.info("Opening main.py file")
    main_py_path = os.path.join(project_dir, "main.py")
    success = vscode.open_file(main_py_path, line=10, column=5)
    if not success:
        logger.error("Failed to open main.py file in VS Code")
        return False
    
    time.sleep(1)
    
    logger.info("Opening index.html file")
    index_html_path = os.path.join(project_dir, "index.html")
    success = vscode.open_file(index_html_path)
    if not success:
        logger.error("Failed to open index.html file in VS Code")
        return False
    
    logger.info("VS Code integration demonstration completed successfully")
    return True

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Demonstrate VS Code integration with the autonomous agent")
    parser.add_argument("--project", default="vscode_demo", help="Project name for the demonstration")
    args = parser.parse_args()
    
    logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
    os.makedirs(logs_dir, exist_ok=True)
    
    success = demonstrate_vscode_integration(args.project)
    
    if success:
        print("\n✅ VS Code integration demonstration completed successfully")
        print("The demonstration has:")
        print("  - Created a sample project with Python, HTML, CSS, and JavaScript files")
        print("  - Opened the project folder in VS Code")
        print("  - Created workspace settings, tasks, and terminal profile")
        print("  - Opened specific files with line and column positioning")
        print("\nYou can now interact with the project in VS Code")
    else:
        print("\n❌ VS Code integration demonstration failed")
        print("Please check the logs for more information")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
