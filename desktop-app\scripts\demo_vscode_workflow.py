"""
Demonstrate VS Code workflow integration with the autonomous agent.
"""
import os
import sys
import time
import logging
import argparse
from pathlib import Path

parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)
from backend.vscode_integration import VSCodeIntegration

def main():
    """Main function."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    vscode = VSCodeIntegration()
    
    test_dir = os.path.join(parent_dir, "test_files", "vscode_workflow_test")
    os.makedirs(test_dir, exist_ok=True)
    
    python_file = os.path.join(test_dir, "main.py")
    with open(python_file, 'w') as f:
        f.write('print("Hello from VS Code workflow test!")\n')
    
    logger.info(f"Opening project in VS Code: {test_dir}")
    if vscode.open_folder(test_dir):
        logger.info("Successfully opened project folder")
    else:
        logger.error("Failed to open project folder")
        return 1
    
    if vscode.create_workspace_settings(test_dir):
        logger.info("Successfully created workspace settings")
    else:
        logger.error("Failed to create workspace settings")
        return 1
    
    if vscode.open_file(python_file, line=1, column=1):
        logger.info("Successfully opened Python file")
    else:
        logger.error("Failed to open Python file")
        return 1
    
    logger.info("VS Code workflow demonstration completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
