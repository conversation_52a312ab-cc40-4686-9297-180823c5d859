"""
Launch VS Code with integrated terminal for the Autonomous AI Software Development Agent.
"""
import os
import sys
import subprocess
import logging
import argparse
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))
from backend.vscode_launcher import VSCodeLauncher

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs", "vscode_launch.log"), mode="a")
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Main function to launch VS Code with integrated terminal."""
    parser = argparse.ArgumentParser(description="Launch VS Code with integrated terminal.")
    parser.add_argument("--project-path", type=str, help="Path to the project to open in VS Code.")
    parser.add_argument("--no-terminal", action="store_true", help="Do not open the integrated terminal.")
    args = parser.parse_args()
    
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
    os.makedirs(logs_dir, exist_ok=True)
    
    launcher = VSCodeLauncher()
    
    if not launcher.is_installed():
        logger.error("VS Code is not installed. Please install VS Code and try again.")
        sys.exit(1)
    
    if args.project_path:
        project_path = args.project_path
    else:
        project_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
    
    if not os.path.isdir(project_path):
        logger.error(f"Project path {project_path} does not exist or is not a directory.")
        sys.exit(1)
    
    success = launcher.launch(project_path, open_terminal=not args.no_terminal)
    
    if success:
        logger.info(f"VS Code launched successfully with project {project_path}")
    else:
        logger.error(f"Failed to launch VS Code with project {project_path}")
        sys.exit(1)

if __name__ == "__main__":
    main()
