"""
Prepare Demo Video Script for the Autonomous AI Software Development Agent.

This script prepares a demo video that demonstrates:
1. Installation and setup process
2. VS Code integration
3. Autonomous development workflow
4. Real-time feedback
5. Autonomous testing
"""
import os
import sys
import time
import logging
import argparse
import subprocess
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs", "demo_video.log"), mode="a")
    ]
)
logger = logging.getLogger(__name__)

class DemoVideoPreparation:
    """
    Prepares a demo video for the Autonomous AI Software Development Agent.
    """
    def __init__(self, project_dir):
        """
        Initialize the Demo Video Preparation.
        
        Args:
            project_dir: Path to the project directory.
        """
        self.project_dir = project_dir
        self.desktop_app_dir = os.path.join(project_dir, "desktop-app")
        self.scripts_dir = os.path.join(self.desktop_app_dir, "scripts")
        
        logs_dir = os.path.join(self.desktop_app_dir, "logs")
        os.makedirs(logs_dir, exist_ok=True)
        
        logger.info(f"Initialized Demo Video Preparation with project_dir: {project_dir}")
    
    def prepare_demo_script(self):
        """
        Prepare a demo script for the video.
        
        Returns:
            Path to the demo script.
        """
        logger.info("Preparing demo script...")
        
        demo_script_path = os.path.join(self.scripts_dir, "demo_script.md")
        
        with open(demo_script_path, "w") as f:
            f.write("# Autonomous AI Software Development Agent Demo Script\n\n")
            
            f.write("## Introduction\n\n")
            f.write("Hello! Today I'll be demonstrating the Autonomous AI Software Development Agent, a powerful tool that can autonomously create complete projects with AI assistance.\n\n")
            
            f.write("## Installation and Setup\n\n")
            f.write("1. First, let's install the agent by running the setup script:\n")
            f.write("   ```bash\n")
            f.write("   # For Linux/macOS\n")
            f.write("   ./desktop-app/scripts/setup_integrated_environment.sh\n\n")
            f.write("   # For Windows\n")
            f.write("   .\\desktop-app\\scripts\\setup_integrated_environment.bat\n")
            f.write("   ```\n\n")
            
            f.write("2. This script will install all the necessary dependencies and configure the environment.\n\n")
            
            f.write("## Starting the Integrated Environment\n\n")
            f.write("1. Now, let's start the integrated environment:\n")
            f.write("   ```bash\n")
            f.write("   python desktop-app/scripts/run_integrated_environment.py\n")
            f.write("   ```\n\n")
            
            f.write("2. This will launch VS Code with the project, start the backend and frontend servers, and open the application in the browser.\n\n")
            
            f.write("## Creating a New Project\n\n")
            f.write("1. Let's create a new project by clicking the 'Start a New Project' button.\n\n")
            f.write("2. We'll enter a project name and description, then click 'Create'.\n\n")
            
            f.write("## Autonomous Development Workflow\n\n")
            f.write("1. Now, let's see the autonomous development workflow in action:\n")
            f.write("   ```bash\n")
            f.write("   python desktop-app/scripts/autonomous_workflow.py --project-name \"my-web-app\" --project-description \"Create a simple web application with user authentication\"\n")
            f.write("   ```\n\n")
            
            f.write("2. This will:\n")
            f.write("   - Start the integrated environment\n")
            f.write("   - Create a new project\n")
            f.write("   - Develop the project\n")
            f.write("   - Run tests\n")
            f.write("   - Fix errors\n")
            f.write("   - Retest\n")
            f.write("   - Validate in the browser\n\n")
            
            f.write("## Real-Time Feedback\n\n")
            f.write("1. Let's make a change to the code in VS Code and see the real-time feedback in the browser.\n\n")
            f.write("2. Notice how the changes are immediately reflected in the browser preview.\n\n")
            
            f.write("## Autonomous Testing\n\n")
            f.write("1. Now, let's run the autonomous testing process:\n")
            f.write("   ```bash\n")
            f.write("   python desktop-app/scripts/demo_autonomous_testing.py\n")
            f.write("   ```\n\n")
            
            f.write("2. This will automatically test the application, fix any errors, and validate the functionality.\n\n")
            
            f.write("## Conclusion\n\n")
            f.write("That's it! The Autonomous AI Software Development Agent can help you create complete projects with AI assistance, including source code, configuration files, and documentation.\n\n")
            f.write("Thank you for watching this demo!\n")
        
        logger.info(f"Demo script prepared at: {demo_script_path}")
        return demo_script_path
    
    def prepare_demo_video_instructions(self):
        """
        Prepare instructions for creating the demo video.
        
        Returns:
            Path to the demo video instructions.
        """
        logger.info("Preparing demo video instructions...")
        
        instructions_path = os.path.join(self.scripts_dir, "demo_video_instructions.md")
        
        with open(instructions_path, "w") as f:
            f.write("# Demo Video Instructions\n\n")
            
            f.write("## Requirements\n\n")
            f.write("- Screen recording software (e.g., OBS Studio, Camtasia, or built-in screen recorder)\n")
            f.write("- Microphone for narration\n")
            f.write("- The Autonomous AI Software Development Agent installed and configured\n\n")
            
            f.write("## Setup\n\n")
            f.write("1. Make sure the agent is installed and configured by running the setup script:\n")
            f.write("   ```bash\n")
            f.write("   # For Linux/macOS\n")
            f.write("   ./desktop-app/scripts/setup_integrated_environment.sh\n\n")
            f.write("   # For Windows\n")
            f.write("   .\\desktop-app\\scripts\\setup_integrated_environment.bat\n")
            f.write("   ```\n\n")
            
            f.write("2. Set up your screen recording software to capture your entire screen or the relevant windows.\n\n")
            
            f.write("## Recording the Demo\n\n")
            f.write("1. Start your screen recording software.\n\n")
            
            f.write("2. Follow the demo script (`demo_script.md`) to demonstrate the features of the agent.\n\n")
            
            f.write("3. Narrate the demo as you go, explaining what you're doing and what the agent is doing.\n\n")
            
            f.write("4. Keep the demo concise and focused on the key features:\n")
            f.write("   - Installation and setup\n")
            f.write("   - VS Code integration\n")
            f.write("   - Autonomous development workflow\n")
            f.write("   - Real-time feedback\n")
            f.write("   - Autonomous testing\n\n")
            
            f.write("5. End the recording when you've completed the demo.\n\n")
            
            f.write("## Editing the Video\n\n")
            f.write("1. Trim the beginning and end of the video to remove any unnecessary content.\n\n")
            
            f.write("2. Add captions or annotations to highlight key points.\n\n")
            
            f.write("3. Add a title screen with the name of the agent.\n\n")
            
            f.write("4. Add a conclusion screen with contact information or next steps.\n\n")
            
            f.write("## Publishing the Video\n\n")
            f.write("1. Export the video in a common format (e.g., MP4).\n\n")
            
            f.write("2. Upload the video to a video sharing platform or include it with the project documentation.\n\n")
            
            f.write("3. Share the video link with users who want to learn about the agent.\n")
        
        logger.info(f"Demo video instructions prepared at: {instructions_path}")
        return instructions_path
    
    def prepare_demo_assets(self):
        """
        Prepare assets for the demo video.
        
        Returns:
            Dictionary of paths to demo assets.
        """
        logger.info("Preparing demo assets...")
        
        assets_dir = os.path.join(self.desktop_app_dir, "demo_assets")
        os.makedirs(assets_dir, exist_ok=True)
        
        title_image_path = os.path.join(assets_dir, "title.svg")
        with open(title_image_path, "w") as f:
            f.write('<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">\n')
            f.write('  <rect width="1280" height="720" fill="#2196f3"/>\n')
            f.write('  <text x="640" y="300" font-family="Arial" font-size="60" text-anchor="middle" fill="white">Autonomous AI Software Development Agent</text>\n')
            f.write('  <text x="640" y="400" font-family="Arial" font-size="30" text-anchor="middle" fill="white">Demo Video</text>\n')
            f.write('</svg>\n')
        
        conclusion_image_path = os.path.join(assets_dir, "conclusion.svg")
        with open(conclusion_image_path, "w") as f:
            f.write('<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">\n')
            f.write('  <rect width="1280" height="720" fill="#2196f3"/>\n')
            f.write('  <text x="640" y="300" font-family="Arial" font-size="60" text-anchor="middle" fill="white">Thank You!</text>\n')
            f.write('  <text x="640" y="400" font-family="Arial" font-size="30" text-anchor="middle" fill="white">Autonomous AI Software Development Agent</text>\n')
            f.write('</svg>\n')
        
        sample_project_path = os.path.join(assets_dir, "sample_project.md")
        with open(sample_project_path, "w") as f:
            f.write("# Sample Project: Web Application with User Authentication\n\n")
            f.write("## Project Description\n\n")
            f.write("Create a simple web application with user authentication. The application should allow users to register, log in, and access protected resources.\n\n")
            f.write("## Features\n\n")
            f.write("- User registration\n")
            f.write("- User login\n")
            f.write("- Password reset\n")
            f.write("- Protected resources\n")
            f.write("- User profile\n\n")
            f.write("## Technologies\n\n")
            f.write("- Frontend: Angular\n")
            f.write("- Backend: FastAPI\n")
            f.write("- Database: SQLite\n")
        
        assets = {
            "title_image": title_image_path,
            "conclusion_image": conclusion_image_path,
            "sample_project": sample_project_path
        }
        
        logger.info(f"Demo assets prepared in: {assets_dir}")
        return assets
    
    def run(self):
        """
        Run the demo video preparation.
        
        Returns:
            Dictionary of paths to demo video preparation files.
        """
        logger.info("Running demo video preparation...")
        
        demo_script_path = self.prepare_demo_script()
        
        instructions_path = self.prepare_demo_video_instructions()
        
        assets = self.prepare_demo_assets()
        
        result = {
            "demo_script": demo_script_path,
            "instructions": instructions_path,
            "assets": assets
        }
        
        logger.info("Demo video preparation completed successfully.")
        return result

def main():
    """Main function to run the demo video preparation."""
    parser = argparse.ArgumentParser(description="Prepare a demo video for the Autonomous AI Software Development Agent.")
    parser.add_argument("--project-dir", type=str, help="Path to the project directory.")
    args = parser.parse_args()
    
    if args.project_dir:
        project_dir = args.project_dir
    else:
        project_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
    
    if not os.path.isdir(project_dir):
        logger.error(f"Project directory {project_dir} does not exist or is not a directory.")
        sys.exit(1)
    
    preparation = DemoVideoPreparation(project_dir)
    
    result = preparation.run()
    
    print("\n=== Demo Video Preparation Completed Successfully ===")
    print(f"Demo script: {result['demo_script']}")
    print(f"Instructions: {result['instructions']}")
    print("Assets:")
    for name, path in result['assets'].items():
        print(f"  - {name}: {path}")

if __name__ == "__main__":
    main()
