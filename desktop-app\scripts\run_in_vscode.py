"""
<PERSON><PERSON><PERSON> to run the AI Software Development Agent in Visual Studio Code.

This script initializes the VS Code integration and opens a project in VS Code,
setting up the workspace with the necessary settings and extensions.
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union, Callable

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.vscode_integration import VSCodeIntegration

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('run_in_vscode.log')
    ]
)
logger = logging.getLogger('RunInVSCode')

def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Run the AI Software Development Agent in Visual Studio Code")
    
    parser.add_argument("--config", help="Path to configuration file")
    parser.add_argument("--project-dir", help="Path to project directory")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    parser.add_argument("--file", help="Path to file to open")
    parser.add_argument("--line", type=int, help="Line number to navigate to")
    parser.add_argument("--column", type=int, help="Column number to navigate to")
    parser.add_argument("--install-extensions", action="store_true", help="Install recommended extensions")
    parser.add_argument("--setup-workspace", action="store_true", help="Set up workspace settings")
    
    return parser.parse_args()

def main() -> int:
    """
    Main entry point.
    
    Returns:
        Exit code
    """
    args = parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    vscode = VSCodeIntegration(args.config)
    
    if not vscode.is_vscode_installed():
        logger.error("Visual Studio Code is not installed")
        return 1
    
    if args.project_dir:
        project_dir = args.project_dir
    else:
        parent_dir = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        config_path = parent_dir.parent / "config.json"
        
        if os.path.exists(config_path):
            try:
                with open(config_path, "r") as f:
                    config = json.load(f)
                    project_dir = config["project"]["dir"]
            except Exception as e:
                logger.error(f"Error loading config from {config_path}: {e}")
                project_dir = os.path.expanduser("~/SourceProjects/AutonomousAI")
        else:
            project_dir = os.path.expanduser("~/SourceProjects/AutonomousAI")
    
    os.makedirs(project_dir, exist_ok=True)
    
    if args.install_extensions:
        logger.info("Installing recommended extensions")
        vscode.install_recommended_extensions()
    
    if args.setup_workspace:
        logger.info("Setting up workspace")
        vscode.setup_project(project_dir)
    
    if args.file:
        logger.info(f"Opening file: {args.file}")
        vscode.open_file(args.file, args.line, args.column)
    else:
        logger.info(f"Opening project: {project_dir}")
        vscode.open_folder(project_dir)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
