"""
Run Integrated Environment Script for the Autonomous AI Software Development Agent.

This script starts the entire integrated development environment:
1. Launches VS Code with the project
2. Starts the backend server
3. Starts the frontend server
4. Opens the application in the browser
"""
import os
import sys
import time
import logging
import argparse
import subprocess
import webbrowser
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))
from backend.vscode_launcher import VSCodeLauncher
from backend.frontend_integration import FrontendIntegration

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs", "integrated_environment.log"), mode="a")
    ]
)
logger = logging.getLogger(__name__)

class IntegratedEnvironment:
    """
    Integrated Development Environment for the Autonomous AI Software Development Agent.
    """
    def __init__(self, project_dir):
        """
        Initialize the Integrated Environment.
        
        Args:
            project_dir: Path to the project directory.
        """
        self.project_dir = project_dir
        self.frontend_dir = os.path.join(project_dir, "frontend")
        self.backend_dir = os.path.join(project_dir, "desktop-app", "backend")
        self.frontend_url = "http://localhost:4200"
        self.backend_url = "http://localhost:8000"
        
        logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
        os.makedirs(logs_dir, exist_ok=True)
        
        self.vscode_launcher = VSCodeLauncher()
        
        self.frontend_integration = FrontendIntegration(self.frontend_dir, self.backend_dir)
        
        logger.info(f"Initialized Integrated Environment with project_dir: {project_dir}")
    
    def start_integrated_environment(self):
        """
        Start the integrated development environment.
        
        Returns:
            True if the environment was started successfully, False otherwise.
        """
        logger.info("Starting integrated development environment...")
        
        # logger.info("Step 1: Launching VS Code with the project...")
        # if not self.vscode_launcher.launch(self.project_dir, open_terminal=True):
        #     logger.error("Failed to launch VS Code with the project.")
        #     return False
        
        logger.info("Waiting for VS Code to start...")
        time.sleep(5)
        
        logger.info("Step 2: Starting backend server...")
        if not self.frontend_integration.start_backend():
            logger.error("Failed to start backend server.")
            return False
        
        logger.info("Step 3: Starting frontend server...")
        if not self.frontend_integration.start_frontend():
            logger.error("Failed to start frontend server.")
            return False
        
        logger.info("Step 4: Opening application in the browser...")
        if not self.frontend_integration.open_in_browser():
            logger.error("Failed to open application in the browser.")
            return False
        
        logger.info("Integrated development environment started successfully.")
        return True
    
    def run_autonomous_development(self, project_name, task_description):
        """
        Run the autonomous development process.
        
        Args:
            project_name: Name of the project to work on.
            task_description: Description of the task to perform.
            
        Returns:
            True if the process completed successfully, False otherwise.
        """
        logger.info(f"Running autonomous development for project '{project_name}'...")
        
        logger.info("Step 1: Creating/opening project...")
        
        logger.info("Step 2: Generating code...")
        
        logger.info("Step 3: Running tests...")
        
        logger.info("Step 4: Fixing errors...")
        
        logger.info("Step 5: Retesting...")
        
        logger.info("Step 6: Validating in browser...")
        
        logger.info("Autonomous development process completed successfully.")
        return True

def main():
    """Main function to run the integrated environment."""
    parser = argparse.ArgumentParser(description="Run the integrated development environment.")
    parser.add_argument("--project-dir", type=str, help="Path to the project directory.")
    parser.add_argument("--autonomous", action="store_true", help="Run autonomous development after starting the environment.")
    parser.add_argument("--project-name", type=str, default="test-project", help="Name of the project to work on (for autonomous development).")
    parser.add_argument("--task-description", type=str, default="Create a simple web application.", help="Description of the task to perform (for autonomous development).")
    args = parser.parse_args()
    
    if args.project_dir:
        project_dir = args.project_dir
    else:
        project_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
    
    if not os.path.isdir(project_dir):
        logger.error(f"Project directory {project_dir} does not exist or is not a directory.")
        sys.exit(1)
    
    env = IntegratedEnvironment(project_dir)
    
    if not env.start_integrated_environment():
        logger.error("Failed to start integrated environment.")
        sys.exit(1)
    
    if args.autonomous:
        if not env.run_autonomous_development(args.project_name, args.task_description):
            logger.error("Failed to run autonomous development.")
            sys.exit(1)
    
    logger.info("Integrated environment is running. Press Ctrl+C to exit.")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Exiting...")

if __name__ == "__main__":
    main()
