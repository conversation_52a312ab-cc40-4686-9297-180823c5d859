"""
<PERSON><PERSON><PERSON> to run autonomous tests for the AI Software Development Agent.

This script initializes the autonomous testing components and runs tests
for a project, fixing errors and validating functionality automatically.
"""

import os
import sys
import json
import logging
import argparse
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union, Callable

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.integration import Integration
from backend.autonomous_testing import AutonomousTesting

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('run_tests.log')
    ]
)
logger = logging.getLogger('RunTests')

def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Run autonomous tests for the AI Software Development Agent")
    
    parser.add_argument("--config", help="Path to configuration file")
    parser.add_argument("--project-dir", help="Path to project directory")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    parser.add_argument("--test-dir", help="Path to test directory")
    parser.add_argument("--test-file", help="Path to test file")
    parser.add_argument("--test-name", help="Name of test to run")
    parser.add_argument("--no-fix", action="store_true", help="Don't fix errors")
    parser.add_argument("--no-validate", action="store_true", help="Don't validate in browser")
    parser.add_argument("--continuous", action="store_true", help="Run tests continuously")
    parser.add_argument("--interval", type=int, default=30, help="Interval between test runs in seconds")
    
    return parser.parse_args()

def main() -> int:
    """
    Main entry point.
    
    Returns:
        Exit code
    """
    args = parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    integration = Integration(args.config)
    autonomous_testing = AutonomousTesting(args.config, integration)
    
    if args.no_fix:
        autonomous_testing.config["autonomous_testing"]["auto_fix"] = False
    if args.no_validate:
        autonomous_testing.config["autonomous_testing"]["auto_validate"] = False
    
    project_dir = args.project_dir or integration.project_dir
    
    if args.continuous:
        logger.info(f"Running tests continuously with interval {args.interval} seconds")
        
        autonomous_testing.start_continuous_testing(
            project_dir=project_dir,
            test_dir=args.test_dir,
            browser_url="http://localhost:4200",
            interval=args.interval
        )
        
        try:
            logger.info("Press Ctrl+C to stop")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Stopping continuous testing")
    else:
        logger.info("Running tests")
        
        test_results = autonomous_testing.run_autonomous_testing(
            project_dir=project_dir,
            test_dir=args.test_dir,
            browser_url="http://localhost:4200"
        )
        
        if test_results["success"]:
            logger.info("Tests passed")
            return 0
        else:
            logger.error("Tests failed")
            return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
