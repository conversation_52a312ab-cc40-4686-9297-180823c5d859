@echo off
echo Starting Autonomous AI Agent Setup...

REM Create project directory structure
if not exist "C:\SourceProjects\AutonomousAI" (
    echo Creating project directory...
    mkdir "C:\SourceProjects\AutonomousAI"
    mkdir "C:\SourceProjects\AutonomousAI\backend"
    mkdir "C:\SourceProjects\AutonomousAI\frontend"
    mkdir "C:\SourceProjects\AutonomousAI\resources"
    mkdir "C:\SourceProjects\AutonomousAI\data"
    mkdir "C:\SourceProjects\AutonomousAI\logs"
    mkdir "C:\SourceProjects\AutonomousAI\tests"
    mkdir "C:\SourceProjects\AutonomousAI\scripts"
)

REM Copy application files
echo Copying application files...
xcopy /E /I /Y "%~dp0..\backend" "C:\SourceProjects\AutonomousAI\backend"
xcopy /E /I /Y "%~dp0..\frontend" "C:\SourceProjects\AutonomousAI\frontend"
xcopy /E /I /Y "%~dp0..\resources" "C:\SourceProjects\AutonomousAI\resources"
xcopy /E /I /Y "%~dp0" "C:\SourceProjects\AutonomousAI\scripts"

REM Set up Python virtual environment
echo Setting up Python environment...
cd "C:\SourceProjects\AutonomousAI\backend"
if not exist "venv" (
    python -m venv venv
)
call venv\Scripts\activate.bat
pip install -r requirements.txt
pip install pytest flake8

REM Install Node.js dependencies
echo Setting up Node.js environment...
cd "C:\SourceProjects\AutonomousAI\frontend"
call npm install

REM Create configuration file
echo Creating configuration file...
echo {> "C:\SourceProjects\AutonomousAI\config.json"
echo   "openai_api_key": "",>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "use_google_search": true,>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "sqlite_path": "C:\\SourceProjects\\AutonomousAI\\data\\agent.db",>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "log_path": "C:\\SourceProjects\\AutonomousAI\\logs",>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "vscode_path": "C:\\Program Files\\Microsoft VS Code\\Code.exe",>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "testing": {>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "test_command": "pytest",>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "test_args": ["-v"],>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "linting_command": "flake8",>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "linting_args": [],>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "max_retries": 3,>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "retry_delay": 2,>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "auto_fix": true,>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "test_timeout": 60,>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "browser_validation": true,>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "browser_validation_timeout": 10>> "C:\SourceProjects\AutonomousAI\config.json"
echo   },>> "C:\SourceProjects\AutonomousAI\config.json"
echo   "vscode": {>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "path": "C:\\Program Files\\Microsoft VS Code\\Code.exe",>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "extensions": [>> "C:\SourceProjects\AutonomousAI\config.json"
echo       "ms-python.python",>> "C:\SourceProjects\AutonomousAI\config.json"
echo       "dbaeumer.vscode-eslint",>> "C:\SourceProjects\AutonomousAI\config.json"
echo       "esbenp.prettier-vscode",>> "C:\SourceProjects\AutonomousAI\config.json"
echo       "ms-vscode.vscode-typescript-tslint-plugin",>> "C:\SourceProjects\AutonomousAI\config.json"
echo       "ritwickdey.liveserver">> "C:\SourceProjects\AutonomousAI\config.json"
echo     ],>> "C:\SourceProjects\AutonomousAI\config.json"
echo     "settings": {>> "C:\SourceProjects\AutonomousAI\config.json"
echo       "editor.formatOnSave": true,>> "C:\SourceProjects\AutonomousAI\config.json"
echo       "editor.codeActionsOnSave": {>> "C:\SourceProjects\AutonomousAI\config.json"
echo         "source.fixAll.eslint": true>> "C:\SourceProjects\AutonomousAI\config.json"
echo       },>> "C:\SourceProjects\AutonomousAI\config.json"
echo       "python.linting.enabled": true,>> "C:\SourceProjects\AutonomousAI\config.json"
echo       "python.linting.pylintEnabled": true>> "C:\SourceProjects\AutonomousAI\config.json"
echo     }>> "C:\SourceProjects\AutonomousAI\config.json"
echo   }>> "C:\SourceProjects\AutonomousAI\config.json"
echo }>> "C:\SourceProjects\AutonomousAI\config.json"

REM Create start script
echo Creating start script...
echo @echo off> "C:\SourceProjects\AutonomousAI\start.bat"
echo echo Starting Autonomous AI Agent...>> "C:\SourceProjects\AutonomousAI\start.bat"
echo start "" "C:\Program Files\Microsoft VS Code\Code.exe" "C:\SourceProjects\AutonomousAI">> "C:\SourceProjects\AutonomousAI\start.bat"
echo cd "C:\SourceProjects\AutonomousAI\backend">> "C:\SourceProjects\AutonomousAI\start.bat"
echo call venv\Scripts\activate.bat>> "C:\SourceProjects\AutonomousAI\start.bat"
echo start /B python main.py>> "C:\SourceProjects\AutonomousAI\start.bat"
echo cd "C:\SourceProjects\AutonomousAI\frontend">> "C:\SourceProjects\AutonomousAI\start.bat"
echo start /B npm start>> "C:\SourceProjects\AutonomousAI\start.bat"
echo echo Autonomous AI Agent started successfully!>> "C:\SourceProjects\AutonomousAI\start.bat"

REM Create test script
echo Creating test script...
echo @echo off> "C:\SourceProjects\AutonomousAI\run_tests.bat"
echo echo Running Autonomous Tests...>> "C:\SourceProjects\AutonomousAI\run_tests.bat"
echo cd "C:\SourceProjects\AutonomousAI">> "C:\SourceProjects\AutonomousAI\run_tests.bat"
echo python scripts\run_tests.py --project-dir "C:\SourceProjects\AutonomousAI" %%*>> "C:\SourceProjects\AutonomousAI\run_tests.bat"

REM Create VS Code integration script
echo Creating VS Code integration script...
echo @echo off> "C:\SourceProjects\AutonomousAI\open_in_vscode.bat"
echo echo Opening project in VS Code...>> "C:\SourceProjects\AutonomousAI\open_in_vscode.bat"
echo cd "C:\SourceProjects\AutonomousAI">> "C:\SourceProjects\AutonomousAI\open_in_vscode.bat"
echo python scripts\run_in_vscode.py --project-dir "C:\SourceProjects\AutonomousAI" %%*>> "C:\SourceProjects\AutonomousAI\open_in_vscode.bat"

REM Create demo script
echo Creating demo script...
echo @echo off> "C:\SourceProjects\AutonomousAI\run_demo.bat"
echo echo Running Autonomous Testing Demo...>> "C:\SourceProjects\AutonomousAI\run_demo.bat"
echo cd "C:\SourceProjects\AutonomousAI">> "C:\SourceProjects\AutonomousAI\run_demo.bat"
echo python scripts\demo_autonomous_testing.py --project-dir "C:\SourceProjects\AutonomousAI\demo" %%*>> "C:\SourceProjects\AutonomousAI\run_demo.bat"

echo Setup completed successfully!
echo Please edit the config.json file to add your API keys.
echo Run start.bat to launch the application.
echo Run run_tests.bat to run autonomous tests.
echo Run open_in_vscode.bat to open the project in VS Code.
echo Run run_demo.bat to run the autonomous testing demo.
pause
