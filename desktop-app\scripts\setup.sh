echo "Starting Autonomous AI Agent Setup..."

PROJECT_DIR="$HOME/SourceProjects/AutonomousAI"
if [ ! -d "$PROJECT_DIR" ]; then
    echo "Creating project directory..."
    mkdir -p "$PROJECT_DIR"
    mkdir -p "$PROJECT_DIR/backend"
    mkdir -p "$PROJECT_DIR/frontend"
    mkdir -p "$PROJECT_DIR/resources"
    mkdir -p "$PROJECT_DIR/data"
    mkdir -p "$PROJECT_DIR/logs"
    mkdir -p "$PROJECT_DIR/tests"
fi

echo "Copying application files..."
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cp -r "$SCRIPT_DIR/../backend/"* "$PROJECT_DIR/backend/"
cp -r "$SCRIPT_DIR/../frontend/"* "$PROJECT_DIR/frontend/"
cp -r "$SCRIPT_DIR/../resources/"* "$PROJECT_DIR/resources/"
cp -r "$SCRIPT_DIR/"* "$PROJECT_DIR/scripts/"

echo "Setting up Python environment..."
cd "$PROJECT_DIR/backend"
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi
source venv/bin/activate
pip install -r requirements.txt
pip install pytest flake8  # Install testing dependencies

echo "Setting up Node.js environment..."
cd "$PROJECT_DIR/frontend"
npm install

echo "Creating configuration file..."
cat > "$PROJECT_DIR/config.json" << EOL
{
  "openai_api_key": "",
  "use_google_search": true,
  "sqlite_path": "$PROJECT_DIR/data/agent.db",
  "log_path": "$PROJECT_DIR/logs",
  "vscode_path": "/usr/bin/code",
  "testing": {
    "test_command": "pytest",
    "test_args": ["-v"],
    "linting_command": "flake8",
    "linting_args": [],
    "max_retries": 3,
    "retry_delay": 2,
    "auto_fix": true,
    "test_timeout": 60,
    "browser_validation": true,
    "browser_validation_timeout": 10
  },
  "vscode": {
    "path": "/usr/bin/code",
    "extensions": [
      "ms-python.python",
      "dbaeumer.vscode-eslint",
      "esbenp.prettier-vscode",
      "ms-vscode.vscode-typescript-tslint-plugin",
      "ritwickdey.liveserver"
    ],
    "settings": {
      "editor.formatOnSave": true,
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": true
      },
      "python.linting.enabled": true,
      "python.linting.pylintEnabled": true
    }
  }
}
EOL

echo "Creating start script..."
cat > "$PROJECT_DIR/start.sh" << EOL
echo "Starting Autonomous AI Agent..."
code "$PROJECT_DIR" &
cd "$PROJECT_DIR/backend"
source venv/bin/activate
python main.py &
cd "$PROJECT_DIR/frontend"
npm start &
echo "Autonomous AI Agent started successfully!"
EOL
chmod +x "$PROJECT_DIR/start.sh"

echo "Creating test script..."
cat > "$PROJECT_DIR/run_tests.sh" << EOL
echo "Running Autonomous Tests..."
cd "$PROJECT_DIR"
python scripts/run_tests.py --project-dir "$PROJECT_DIR" \$@
EOL
chmod +x "$PROJECT_DIR/run_tests.sh"

echo "Creating VS Code integration script..."
cat > "$PROJECT_DIR/open_in_vscode.sh" << EOL
echo "Opening project in VS Code..."
cd "$PROJECT_DIR"
python scripts/run_in_vscode.py --project-dir "$PROJECT_DIR" \$@
EOL
chmod +x "$PROJECT_DIR/open_in_vscode.sh"

echo "Creating demo script..."
cat > "$PROJECT_DIR/run_demo.sh" << EOL
echo "Running Autonomous Testing Demo..."
cd "$PROJECT_DIR"
python scripts/demo_autonomous_testing.py --project-dir "$PROJECT_DIR/demo" \$@
EOL
chmod +x "$PROJECT_DIR/run_demo.sh"

echo "Setup completed successfully!"
echo "Please edit the config.json file to add your API keys."
echo "Run start.sh to launch the application."
echo "Run run_tests.sh to run autonomous tests."
echo "Run open_in_vscode.sh to open the project in VS Code."
echo "Run run_demo.sh to run the autonomous testing demo."
