@echo off
:: Setup script for the Integrated Development Environment
:: This script sets up the integrated development environment for the Autonomous AI Software Development Agent

echo ======================================================
echo   Autonomous AI Software Development Agent Setup
echo ======================================================

:: Get the directory of the script
set "SCRIPT_DIR=%~dp0"
set "PROJECT_DIR=%SCRIPT_DIR%..\..\"
set "DESKTOP_APP_DIR=%SCRIPT_DIR%..\"
set "FRONTEND_DIR=%PROJECT_DIR%frontend\"
set "BACKEND_DIR=%DESKTOP_APP_DIR%backend\"
set "LOGS_DIR=%DESKTOP_APP_DIR%logs\"

:: Create logs directory if it doesn't exist
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"

echo Setting up the integrated development environment...
echo Project directory: %PROJECT_DIR%
echo Desktop app directory: %DESKTOP_APP_DIR%
echo Frontend directory: %FRONTEND_DIR%
echo Backend directory: %BACKEND_DIR%

:: Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Python is not installed. Please install Python and try again.
    exit /b 1
)

:: Check if Node.js is installed
node --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Node.js is not installed. Please install Node.js and try again.
    exit /b 1
)

:: Check if npm is installed
npm --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo npm is not installed. Please install npm and try again.
    exit /b 1
)

:: Check if VS Code is installed
set "VSCODE_PATH="
where code >nul 2>&1
if %ERRORLEVEL% equ 0 (
    for /f "tokens=*" %%i in ('where code') do set "VSCODE_PATH=%%i"
) else if exist "%LOCALAPPDATA%\Programs\Microsoft VS Code\Code.exe" (
    set "VSCODE_PATH=%LOCALAPPDATA%\Programs\Microsoft VS Code\Code.exe"
) else if exist "%PROGRAMFILES%\Microsoft VS Code\Code.exe" (
    set "VSCODE_PATH=%PROGRAMFILES%\Microsoft VS Code\Code.exe"
) else if exist "%PROGRAMFILES(x86)%\Microsoft VS Code\Code.exe" (
    set "VSCODE_PATH=%PROGRAMFILES(x86)%\Microsoft VS Code\Code.exe"
)

if "%VSCODE_PATH%"=="" (
    echo Visual Studio Code is not installed. Please install Visual Studio Code and try again.
    exit /b 1
)

echo Visual Studio Code found at: %VSCODE_PATH%

:: Set up the backend
echo Setting up the backend...
cd "%BACKEND_DIR%" || exit /b 1

:: Create a virtual environment if it doesn't exist
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

:: Activate the virtual environment
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo Failed to activate virtual environment.
    exit /b 1
)

:: Install backend dependencies
echo Installing backend dependencies...
pip install -r requirements.txt

:: Set up the frontend
echo Setting up the frontend...
cd "%FRONTEND_DIR%" || exit /b 1

:: Install frontend dependencies
echo Installing frontend dependencies...
call npm install --legacy-peer-deps

:: Create a configuration file
echo Creating configuration file...
set "CONFIG_FILE=%DESKTOP_APP_DIR%config.json"

if not exist "%CONFIG_FILE%" (
    echo Creating configuration file...
    echo {> "%CONFIG_FILE%"
    echo   "vscode": {>> "%CONFIG_FILE%"
    echo     "path": "%VSCODE_PATH:\=\\%",>> "%CONFIG_FILE%"
    echo     "extensions": ["ms-python.python", "dbaeumer.vscode-eslint"]>> "%CONFIG_FILE%"
    echo   },>> "%CONFIG_FILE%"
    echo   "browser": {>> "%CONFIG_FILE%"
    echo     "url": "http://localhost:4200">> "%CONFIG_FILE%"
    echo   },>> "%CONFIG_FILE%"
    echo   "backend": {>> "%CONFIG_FILE%"
    echo     "url": "http://localhost:8000">> "%CONFIG_FILE%"
    echo   }>> "%CONFIG_FILE%"
    echo }>> "%CONFIG_FILE%"
    echo Configuration file created at: %CONFIG_FILE%
) else (
    echo Configuration file already exists at: %CONFIG_FILE%
)

echo Setup completed successfully!
echo ======================================================
echo   To start the integrated environment, run:
echo   python %SCRIPT_DIR%run_integrated_environment.py
echo ======================================================
echo   To run the demo, run:
echo   python %SCRIPT_DIR%demo_integrated_environment.py
echo ======================================================

:: Deactivate the virtual environment
call venv\Scripts\deactivate.bat

exit /b 0
