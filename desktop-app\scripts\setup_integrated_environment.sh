
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=======================================================${NC}"
echo -e "${BLUE}  Autonomous AI Software Development Agent Setup        ${NC}"
echo -e "${BLUE}=======================================================${NC}"

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR="$( cd "$SCRIPT_DIR/../.." && pwd )"
DESKTOP_APP_DIR="$SCRIPT_DIR/.."
FRONTEND_DIR="$PROJECT_DIR/frontend"
BACKEND_DIR="$DESKTOP_APP_DIR/backend"
LOGS_DIR="$DESKTOP_APP_DIR/logs"

mkdir -p "$LOGS_DIR"

echo -e "${GREEN}Setting up the integrated development environment...${NC}"
echo "Project directory: $PROJECT_DIR"
echo "Desktop app directory: $DESKTOP_APP_DIR"
echo "Frontend directory: $FRONTEND_DIR"
echo "Backend directory: $BACKEND_DIR"

if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Python 3 is not installed. Please install Python 3 and try again.${NC}"
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js is not installed. Please install Node.js and try again.${NC}"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}npm is not installed. Please install npm and try again.${NC}"
    exit 1
fi

VSCODE_PATH=""
if command -v code &> /dev/null; then
    VSCODE_PATH=$(which code)
elif [ -d "/Applications/Visual Studio Code.app" ]; then
    VSCODE_PATH="/Applications/Visual Studio Code.app/Contents/Resources/app/bin/code"
elif [ -f "/usr/bin/code" ]; then
    VSCODE_PATH="/usr/bin/code"
elif [ -f "/usr/local/bin/code" ]; then
    VSCODE_PATH="/usr/local/bin/code"
elif [ -f "$HOME/.local/bin/code" ]; then
    VSCODE_PATH="$HOME/.local/bin/code"
elif [ -f "$LOCALAPPDATA/Programs/Microsoft VS Code/Code.exe" ]; then
    VSCODE_PATH="$LOCALAPPDATA/Programs/Microsoft VS Code/Code.exe"
elif [ -f "$PROGRAMFILES/Microsoft VS Code/Code.exe" ]; then
    VSCODE_PATH="$PROGRAMFILES/Microsoft VS Code/Code.exe"
elif [ -f "$PROGRAMFILES(x86)/Microsoft VS Code/Code.exe" ]; then
    VSCODE_PATH="$PROGRAMFILES(x86)/Microsoft VS Code/Code.exe"
fi

if [ -z "$VSCODE_PATH" ]; then
    echo -e "${RED}Visual Studio Code is not installed. Please install Visual Studio Code and try again.${NC}"
    exit 1
fi

echo -e "${GREEN}Visual Studio Code found at: $VSCODE_PATH${NC}"

echo -e "${GREEN}Setting up the backend...${NC}"
cd "$BACKEND_DIR" || exit 1

if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

if [ -f "venv/bin/activate" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
elif [ -f "venv/Scripts/activate" ]; then
    echo "Activating virtual environment..."
    source venv/Scripts/activate
else
    echo -e "${RED}Failed to activate virtual environment.${NC}"
    exit 1
fi

echo "Installing backend dependencies..."
pip install -r requirements.txt

echo -e "${GREEN}Setting up the frontend...${NC}"
cd "$FRONTEND_DIR" || exit 1

echo "Installing frontend dependencies..."
npm install --legacy-peer-deps

echo -e "${GREEN}Creating configuration file...${NC}"
CONFIG_FILE="$DESKTOP_APP_DIR/config.json"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "Creating configuration file..."
    cat > "$CONFIG_FILE" << EOL
{
  "vscode": {
    "path": "$VSCODE_PATH",
    "extensions": ["ms-python.python", "dbaeumer.vscode-eslint"]
  },
  "browser": {
    "url": "http://localhost:4200"
  },
  "backend": {
    "url": "http://localhost:8000"
  }
}
EOL
    echo "Configuration file created at: $CONFIG_FILE"
else
    echo "Configuration file already exists at: $CONFIG_FILE"
fi

echo -e "${GREEN}Making scripts executable...${NC}"
chmod +x "$SCRIPT_DIR/run_integrated_environment.py"
chmod +x "$SCRIPT_DIR/test_integration.py"
chmod +x "$SCRIPT_DIR/demo_integrated_environment.py"
chmod +x "$SCRIPT_DIR/launch_vscode.py"
chmod +x "$SCRIPT_DIR/autonomous_development.py"
chmod +x "$SCRIPT_DIR/start_application.py"

echo -e "${GREEN}Setup completed successfully!${NC}"
echo -e "${BLUE}=======================================================${NC}"
echo -e "${BLUE}  To start the integrated environment, run:            ${NC}"
echo -e "${BLUE}  python $SCRIPT_DIR/run_integrated_environment.py     ${NC}"
echo -e "${BLUE}=======================================================${NC}"
echo -e "${BLUE}  To run the demo, run:                                ${NC}"
echo -e "${BLUE}  python $SCRIPT_DIR/demo_integrated_environment.py    ${NC}"
echo -e "${BLUE}=======================================================${NC}"

deactivate

exit 0
