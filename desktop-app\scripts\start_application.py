"""
Start Application Script for the Autonomous AI Software Development Agent.

This script starts the entire application:
1. Backend server
2. Frontend server
3. Opens the application in the browser
4. Opens the project in VS Code
"""
import os
import sys
import logging
import argparse
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))
from backend.frontend_integration import FrontendIntegration

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs", "start_application.log"), mode="a")
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Main function to start the application."""
    parser = argparse.ArgumentParser(description="Start the Autonomous AI Software Development Agent application.")
    parser.add_argument("--no-browser", action="store_true", help="Do not open the application in the browser.")
    parser.add_argument("--no-vscode", action="store_true", help="Do not open the project in VS Code.")
    parser.add_argument("--frontend-only", action="store_true", help="Start only the frontend server.")
    parser.add_argument("--backend-only", action="store_true", help="Start only the backend server.")
    args = parser.parse_args()
    
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
    os.makedirs(logs_dir, exist_ok=True)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    project_dir = os.path.dirname(parent_dir)
    
    frontend_dir = os.path.join(project_dir, "frontend")
    backend_dir = os.path.join(parent_dir, "backend")
    
    integration = FrontendIntegration(frontend_dir, backend_dir)
    
    if args.frontend_only:
        success = integration.start_frontend()
        if success and not args.no_browser:
            integration.open_in_browser()
    elif args.backend_only:
        success = integration.start_backend()
    else:
        if not integration.start_backend():
            logger.error("Failed to start backend application.")
            sys.exit(1)
        
        if not integration.start_frontend():
            logger.error("Failed to start frontend application.")
            sys.exit(1)
        
        if not args.no_browser and not integration.open_in_browser():
            logger.error("Failed to open frontend application in browser.")
            sys.exit(1)
        
        if not args.no_vscode and not integration.open_in_vscode():
            logger.error("Failed to open project in VS Code.")
            sys.exit(1)
        
        success = True
    
    if success:
        logger.info("Application started successfully.")
    else:
        logger.error("Failed to start application.")
        sys.exit(1)

if __name__ == "__main__":
    main()
