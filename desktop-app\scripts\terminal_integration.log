2025-04-16 15:40:21,770 - backend.vscode_launcher - WARNING - VS Code executable not found. Please install VS Code or set the path manually.
2025-04-16 15:40:21,770 - backend.vscode_launcher - INFO - Initialized VS Code Launcher with path: 
2025-04-16 15:40:21,771 - __main__ - INFO - Initialized Autonomous Workflow Demo with project_dir: /home/<USER>/projects/ai-agent/autonomous-agent/desktop-app/scripts/../..
2025-04-16 15:40:21,771 - __main__ - INFO - Running autonomous workflow demonstration for project: Demo Project
2025-04-16 15:40:21,771 - __main__ - INFO - Starting integrated development environment...
2025-04-16 15:40:21,771 - backend.vscode_launcher - ERROR - VS Code executable not found. Cannot launch VS Code.
2025-04-16 15:40:21,771 - __main__ - ERROR - Failed to launch VS Code.
2025-04-16 15:40:21,771 - __main__ - ERROR - Failed to start integrated environment.
2025-04-16 15:40:21,771 - __main__ - ERROR - Autonomous workflow demonstration failed.
