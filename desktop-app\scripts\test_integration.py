"""
Test Integration Script for the Autonomous AI Software Development Agent.

This script tests the integration between the frontend, backend, browser, and VS Code.
"""
import os
import sys
import time
import logging
import argparse
import subprocess
import webbrowser
from pathlib import Path

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))
from backend.vscode_launcher import VSCodeLauncher
from backend.frontend_integration import FrontendIntegration

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs", "test_integration.log"), mode="a")
    ]
)
logger = logging.getLogger(__name__)

class IntegrationTester:
    """
    Tests the integration between the frontend, backend, browser, and VS Code.
    """
    def __init__(self, project_dir):
        """
        Initialize the Integration Tester.
        
        Args:
            project_dir: Path to the project directory.
        """
        self.project_dir = project_dir
        self.frontend_dir = os.path.join(project_dir, "frontend")
        self.backend_dir = os.path.join(project_dir, "desktop-app", "backend")
        self.frontend_url = "http://localhost:4200"
        self.backend_url = "http://localhost:8000"
        
        logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
        os.makedirs(logs_dir, exist_ok=True)
        
        self.vscode_launcher = VSCodeLauncher()
        
        self.frontend_integration = FrontendIntegration(self.frontend_dir, self.backend_dir)
        
        logger.info(f"Initialized Integration Tester with project_dir: {project_dir}")
    
    def test_vscode_integration(self):
        """
        Test the VS Code integration.
        
        Returns:
            True if the test passed, False otherwise.
        """
        logger.info("Testing VS Code integration...")
        
        if not self.vscode_launcher.is_installed():
            logger.error("VS Code is not installed. Cannot test VS Code integration.")
            return False
        
        if not self.vscode_launcher.launch(self.project_dir, open_terminal=True):
            logger.error("Failed to launch VS Code with the project.")
            return False
        
        logger.info("VS Code integration test passed.")
        return True
    
    def test_frontend_backend_integration(self):
        """
        Test the frontend and backend integration.
        
        Returns:
            True if the test passed, False otherwise.
        """
        logger.info("Testing frontend and backend integration...")
        
        if not self.frontend_integration.start_backend():
            logger.error("Failed to start backend application.")
            return False
        
        if not self.frontend_integration.start_frontend():
            logger.error("Failed to start frontend application.")
            return False
        
        logger.info("Frontend and backend integration test passed.")
        return True
    
    def test_browser_integration(self):
        """
        Test the browser integration.
        
        Returns:
            True if the test passed, False otherwise.
        """
        logger.info("Testing browser integration...")
        
        if not self.frontend_integration.open_in_browser():
            logger.error("Failed to open frontend application in browser.")
            return False
        
        logger.info("Browser integration test passed.")
        return True
    
    def run_all_tests(self):
        """
        Run all integration tests.
        
        Returns:
            True if all tests passed, False otherwise.
        """
        logger.info("Running all integration tests...")
        
        if not self.test_frontend_backend_integration():
            return False
        
        if not self.test_browser_integration():
            return False
        
        if not self.test_vscode_integration():
            return False
        
        logger.info("All integration tests passed.")
        return True

def main():
    """Main function to run the integration tests."""
    parser = argparse.ArgumentParser(description="Test the integration between the frontend, backend, browser, and VS Code.")
    parser.add_argument("--project-dir", type=str, help="Path to the project directory.")
    parser.add_argument("--vscode-only", action="store_true", help="Test only the VS Code integration.")
    parser.add_argument("--frontend-backend-only", action="store_true", help="Test only the frontend and backend integration.")
    parser.add_argument("--browser-only", action="store_true", help="Test only the browser integration.")
    args = parser.parse_args()
    
    if args.project_dir:
        project_dir = args.project_dir
    else:
        project_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
    
    if not os.path.isdir(project_dir):
        logger.error(f"Project directory {project_dir} does not exist or is not a directory.")
        sys.exit(1)
    
    tester = IntegrationTester(project_dir)
    
    if args.vscode_only:
        success = tester.test_vscode_integration()
    elif args.frontend_backend_only:
        success = tester.test_frontend_backend_integration()
    elif args.browser_only:
        success = tester.test_browser_integration()
    else:
        success = tester.run_all_tests()
    
    if success:
        logger.info("Integration tests completed successfully.")
    else:
        logger.error("Integration tests failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
