2025-04-18 17:46:19,682 - VSCodeIntegration - WARNING - Could not find VS Code executable, using default command 'code'
2025-04-18 17:46:19,682 - __main__ - INFO - Opening project in VS Code: /home/<USER>/implementation/desktop-app/test_files/vscode_workflow_test
2025-04-18 17:46:19,683 - VSCodeIntegration - ERROR - Error opening folder /home/<USER>/implementation/desktop-app/test_files/vscode_workflow_test in VS Code: [Errno 2] No such file or directory: 'code'
2025-04-18 17:46:19,683 - __main__ - ERROR - Failed to open project folder
