@echo off
echo Autonomous AI Software Development Agent - Setup
echo ================================================

:: Create project directory
if not exist "C:\SourceProjects\AutonomousAI" mkdir "C:\SourceProjects\AutonomousAI"
cd "C:\SourceProjects\AutonomousAI"

:: Clone or extract project files
echo Extracting project files...
:: Assuming files are already extracted to this location

:: Create directories
if not exist "venv" mkdir venv
if not exist "node_modules" mkdir node_modules
if not exist "data" mkdir data

:: Set up Python virtual environment
echo Setting up Python virtual environment...
python -m venv venv
call venv\Scripts\activate.bat

:: Install Python dependencies
echo Installing Python dependencies...
pip install -r backend\requirements.txt

:: Install Node.js dependencies
echo Installing Node.js dependencies...
cd frontend
npm install --legacy-peer-deps
cd ..

:: Set up SQLite database
echo Setting up SQLite database...
python backend\setup_database.py

:: Configure the application
echo Configuring the application...
python backend\configure_app.py

:: Create VS Code workspace file
echo Creating VS Code workspace file...
echo {
echo   "folders": [
echo     {
echo       "path": "."
echo     }
echo   ],
echo   "settings": {
echo     "terminal.integrated.cwd": "${workspaceFolder}",
echo     "terminal.integrated.shell.windows": "cmd.exe",
echo     "python.defaultInterpreterPath": "${workspaceFolder}\\venv\\Scripts\\python.exe",
echo     "python.terminal.activateEnvironment": true
echo   },
echo   "extensions": {
echo     "recommendations": [
echo       "ms-python.python",
echo       "dbaeumer.vscode-eslint",
echo       "esbenp.prettier-vscode",
echo       "angular.ng-template"
echo     ]
echo   }
echo } > AutonomousAI.code-workspace

echo Setup completed successfully!
echo To start the application, run start_application.bat
