@echo off
echo Autonomous AI Software Development Agent - Starting
echo ==================================================

:: Set working directory
cd "C:\SourceProjects\AutonomousAI"

:: Start backend server
start cmd /k "cd backend && ..\venv\Scripts\activate.bat && python main.py"

:: Start frontend server
start cmd /k "cd frontend && npm start"

:: Wait for servers to start
echo Waiting for servers to start...
timeout /t 5 /nobreak

:: Open application in browser
echo Opening application in browser...
start http://localhost:4200

:: Launch VS Code with the project
:: echo Launching VS Code with the project...
:: code "C:\SourceProjects\AutonomousAI\AutonomousAI.code-workspace"

:: Start autonomous testing
echo Starting autonomous testing...
start cmd /k "cd desktop-app\scripts && ..\..\venv\Scripts\activate.bat && python autonomous_testing.py"

echo Application started successfully!
echo The autonomous development process is now running.
echo You can see the progress in the VS Code terminal.
