import asyncio
import logging
import os
import sys

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.agent import Agent
from backend.project_manager import ProjectManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockSocketIO:
    """Mock SocketIO class for testing"""
    async def emit(self, event, data):
        print(f"[{event}] {data.get('message', str(data))}")

async def test_angular_project():
    try:
        # Initialize agent and project manager
        agent = Agent()
        project_name = 'test_angular'
        
        # Create project
        project_manager = ProjectManager()
        project_dir = project_manager.create_project(project_name)
        print(f'Created project directory: {project_dir}')
        
        # Create mock SocketIO
        sio = MockSocketIO()
        
        # Test the ensure_angular_project method
        result = await agent.ensure_angular_project(project_name, project_dir, sio)
        print(f'Angular project created at: {result}')
        
        # Check if npm install was properly deferred
        print(f'Pending npm install flag: {getattr(agent, "pending_npm_install", False)}')
        
        # Check directory structure
        print(f'Directory exists: {os.path.exists(result)}')
        print(f'Files in directory: {os.listdir(result)}')
        
        return "Test completed successfully"
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return f"Test failed: {str(e)}"

if __name__ == "__main__":
    result = asyncio.run(test_angular_project())
    print(result) 