"""
Test Browser Integration

This script tests the browser integration functionality by opening a test HTML file
in the browser and demonstrating real-time feedback.
"""
import os
import sys
import time
import logging

sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "backend"))

from browser_integration import BrowserIntegration

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("TestBrowserIntegration")

def main():
    """Main function to test browser integration."""
    logger.info("Testing browser integration...")
    
    browser = BrowserIntegration()
    
    test_dir = os.path.dirname(os.path.abspath(__file__))
    preview_path = os.path.join(test_dir, "test_preview.html")
    
    if not os.path.exists(preview_path):
        logger.error(f"Test preview file not found at {preview_path}")
        return False
    
    logger.info(f"Previewing file: {preview_path}")
    success = browser.preview_file(preview_path)
    
    if success:
        logger.info("Successfully opened the test preview in the browser.")
    else:
        logger.error("Failed to open the test preview in the browser.")
        return False
    
    logger.info(f"Starting live server for directory: {test_dir}")
    success = browser.start_live_server(test_dir, port=5500)
    
    if success:
        logger.info("Successfully started live server.")
        logger.info(f"Live server URL: {browser.get_live_server_url(port=5500)}")
        
        browser.open_url(browser.get_live_server_url(port=5500))
        
        logger.info("Waiting for 5 seconds to demonstrate real-time feedback...")
        time.sleep(5)
        
        logger.info("Updating test preview file to demonstrate real-time feedback...")
        update_test_preview(preview_path)
        
        logger.info("Waiting for 5 more seconds to see the changes...")
        time.sleep(5)
        
        logger.info("Stopping live server...")
        browser.stop_live_server()
        
        logger.info("Browser integration test completed successfully.")
        return True
    else:
        logger.error("Failed to start live server.")
        return False

def update_test_preview(preview_path):
    """Update the test preview file to demonstrate real-time feedback."""
    try:
        with open(preview_path, "r") as f:
            content = f.read()
        
        content = content.replace(
            '<p class="success">Component rendering successful!</p>',
            '<p class="success">Component rendering successful! (Updated)</p>'
        )
        content = content.replace(
            '<p class="success">Integration test passed!</p>',
            '<p class="success">Integration test passed! (Updated)</p>'
        )
        
        with open(preview_path, "w") as f:
            f.write(content)
        
        logger.info(f"Updated test preview file at {preview_path}")
        return True
    except Exception as e:
        logger.error(f"Error updating test preview file: {e}")
        return False

if __name__ == "__main__":
    main()
