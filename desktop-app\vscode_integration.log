2025-05-04 11:48:02,247 - VSCodeIntegration - WARNING - Could not find VS Code executable, using default command 'code'
2025-05-04 11:48:02,516 - socket_instance - INFO - Socket instance set
2025-05-04 11:48:14,051 - project_manager - INFO - Initialized ProjectManager with base directory: C:/SourceProjects/AutonomousAI/projects
2025-05-04 11:48:22,247 - agent_state - INFO - Initialized AgentState
2025-05-04 11:48:22,408 - __main__ - INFO - Opened VS Code with project directory: C:\SourceProjects\AutonomousAI\desktop-app
2025-05-04 11:48:35,779 - VSCodeIntegration - WARNING - Could not find VS Code executable, using default command 'code'
2025-05-04 11:48:35,969 - socket_instance - INFO - Socket instance set
2025-05-04 11:48:39,169 - project_manager - INFO - Initialized ProjectManager with base directory: C:/SourceProjects/AutonomousAI/projects
2025-05-04 11:48:47,325 - agent_state - INFO - Initialized AgentState
2025-05-04 11:48:47,650 - socket_instance - INFO - Socket instance set
2025-05-04 11:48:47,655 - project_manager - INFO - Initialized ProjectManager with base directory: C:/SourceProjects/AutonomousAI/projects
2025-05-04 11:48:56,926 - agent_state - INFO - Initialized AgentState
