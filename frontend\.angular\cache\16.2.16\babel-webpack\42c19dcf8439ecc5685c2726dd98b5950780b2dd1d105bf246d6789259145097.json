{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Observable } from '../Observable';\nimport { mergeMap } from '../operators/mergeMap';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nconst nodeEventEmitterMethods = ['addListener', 'removeListener'];\nconst eventTargetMethods = ['addEventListener', 'removeEventListener'];\nconst jqueryMethods = ['on', 'off'];\nexport function fromEvent(target, eventName, options, resultSelector) {\n  if (isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n  if (resultSelector) {\n    return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs(resultSelector));\n  }\n  const [add, remove] = isEventTarget(target) ? eventTargetMethods.map(methodName => handler => target[methodName](eventName, handler, options)) : isNodeStyleEventEmitter(target) ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName)) : isJQueryStyleEventEmitter(target) ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName)) : [];\n  if (!add) {\n    if (isArrayLike(target)) {\n      return mergeMap(subTarget => fromEvent(subTarget, eventName, options))(innerFrom(target));\n    }\n  }\n  if (!add) {\n    throw new TypeError('Invalid event target');\n  }\n  return new Observable(subscriber => {\n    const handler = (...args) => subscriber.next(1 < args.length ? args : args[0]);\n    add(handler);\n    return () => remove(handler);\n  });\n}\nfunction toCommonHandlerRegistry(target, eventName) {\n  return methodName => handler => target[methodName](eventName, handler);\n}\nfunction isNodeStyleEventEmitter(target) {\n  return isFunction(target.addListener) && isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n  return isFunction(target.on) && isFunction(target.off);\n}\nfunction isEventTarget(target) {\n  return isFunction(target.addEventListener) && isFunction(target.removeEventListener);\n}", "map": {"version": 3, "names": ["innerFrom", "Observable", "mergeMap", "isArrayLike", "isFunction", "mapOneOrManyArgs", "nodeEventEmitterMethods", "eventTargetMethods", "jqueryMethods", "fromEvent", "target", "eventName", "options", "resultSelector", "undefined", "pipe", "add", "remove", "isEventTarget", "map", "methodName", "handler", "isNodeStyleEventEmitter", "toCommonHandlerRegistry", "isJQueryStyleEventEmitter", "subTarget", "TypeError", "subscriber", "args", "next", "length", "addListener", "removeListener", "on", "off", "addEventListener", "removeEventListener"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/rxjs/dist/esm/internal/observable/fromEvent.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { Observable } from '../Observable';\nimport { mergeMap } from '../operators/mergeMap';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nconst nodeEventEmitterMethods = ['addListener', 'removeListener'];\nconst eventTargetMethods = ['addEventListener', 'removeEventListener'];\nconst jqueryMethods = ['on', 'off'];\nexport function fromEvent(target, eventName, options, resultSelector) {\n    if (isFunction(options)) {\n        resultSelector = options;\n        options = undefined;\n    }\n    if (resultSelector) {\n        return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs(resultSelector));\n    }\n    const [add, remove] = isEventTarget(target)\n        ? eventTargetMethods.map((methodName) => (handler) => target[methodName](eventName, handler, options))\n        :\n            isNodeStyleEventEmitter(target)\n                ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName))\n                : isJQueryStyleEventEmitter(target)\n                    ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName))\n                    : [];\n    if (!add) {\n        if (isArrayLike(target)) {\n            return mergeMap((subTarget) => fromEvent(subTarget, eventName, options))(innerFrom(target));\n        }\n    }\n    if (!add) {\n        throw new TypeError('Invalid event target');\n    }\n    return new Observable((subscriber) => {\n        const handler = (...args) => subscriber.next(1 < args.length ? args : args[0]);\n        add(handler);\n        return () => remove(handler);\n    });\n}\nfunction toCommonHandlerRegistry(target, eventName) {\n    return (methodName) => (handler) => target[methodName](eventName, handler);\n}\nfunction isNodeStyleEventEmitter(target) {\n    return isFunction(target.addListener) && isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n    return isFunction(target.on) && isFunction(target.off);\n}\nfunction isEventTarget(target) {\n    return isFunction(target.addEventListener) && isFunction(target.removeEventListener);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,MAAMC,uBAAuB,GAAG,CAAC,aAAa,EAAE,gBAAgB,CAAC;AACjE,MAAMC,kBAAkB,GAAG,CAAC,kBAAkB,EAAE,qBAAqB,CAAC;AACtE,MAAMC,aAAa,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;AACnC,OAAO,SAASC,SAASA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,cAAc,EAAE;EAClE,IAAIT,UAAU,CAACQ,OAAO,CAAC,EAAE;IACrBC,cAAc,GAAGD,OAAO;IACxBA,OAAO,GAAGE,SAAS;EACvB;EACA,IAAID,cAAc,EAAE;IAChB,OAAOJ,SAAS,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,CAAC,CAACG,IAAI,CAACV,gBAAgB,CAACQ,cAAc,CAAC,CAAC;EACvF;EACA,MAAM,CAACG,GAAG,EAAEC,MAAM,CAAC,GAAGC,aAAa,CAACR,MAAM,CAAC,GACrCH,kBAAkB,CAACY,GAAG,CAAEC,UAAU,IAAMC,OAAO,IAAKX,MAAM,CAACU,UAAU,CAAC,CAACT,SAAS,EAAEU,OAAO,EAAET,OAAO,CAAC,CAAC,GAElGU,uBAAuB,CAACZ,MAAM,CAAC,GACzBJ,uBAAuB,CAACa,GAAG,CAACI,uBAAuB,CAACb,MAAM,EAAEC,SAAS,CAAC,CAAC,GACvEa,yBAAyB,CAACd,MAAM,CAAC,GAC7BF,aAAa,CAACW,GAAG,CAACI,uBAAuB,CAACb,MAAM,EAAEC,SAAS,CAAC,CAAC,GAC7D,EAAE;EACpB,IAAI,CAACK,GAAG,EAAE;IACN,IAAIb,WAAW,CAACO,MAAM,CAAC,EAAE;MACrB,OAAOR,QAAQ,CAAEuB,SAAS,IAAKhB,SAAS,CAACgB,SAAS,EAAEd,SAAS,EAAEC,OAAO,CAAC,CAAC,CAACZ,SAAS,CAACU,MAAM,CAAC,CAAC;IAC/F;EACJ;EACA,IAAI,CAACM,GAAG,EAAE;IACN,MAAM,IAAIU,SAAS,CAAC,sBAAsB,CAAC;EAC/C;EACA,OAAO,IAAIzB,UAAU,CAAE0B,UAAU,IAAK;IAClC,MAAMN,OAAO,GAAGA,CAAC,GAAGO,IAAI,KAAKD,UAAU,CAACE,IAAI,CAAC,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAGF,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9EZ,GAAG,CAACK,OAAO,CAAC;IACZ,OAAO,MAAMJ,MAAM,CAACI,OAAO,CAAC;EAChC,CAAC,CAAC;AACN;AACA,SAASE,uBAAuBA,CAACb,MAAM,EAAEC,SAAS,EAAE;EAChD,OAAQS,UAAU,IAAMC,OAAO,IAAKX,MAAM,CAACU,UAAU,CAAC,CAACT,SAAS,EAAEU,OAAO,CAAC;AAC9E;AACA,SAASC,uBAAuBA,CAACZ,MAAM,EAAE;EACrC,OAAON,UAAU,CAACM,MAAM,CAACqB,WAAW,CAAC,IAAI3B,UAAU,CAACM,MAAM,CAACsB,cAAc,CAAC;AAC9E;AACA,SAASR,yBAAyBA,CAACd,MAAM,EAAE;EACvC,OAAON,UAAU,CAACM,MAAM,CAACuB,EAAE,CAAC,IAAI7B,UAAU,CAACM,MAAM,CAACwB,GAAG,CAAC;AAC1D;AACA,SAAShB,aAAaA,CAACR,MAAM,EAAE;EAC3B,OAAON,UAAU,CAACM,MAAM,CAACyB,gBAAgB,CAAC,IAAI/B,UAAU,CAACM,MAAM,CAAC0B,mBAAmB,CAAC;AACxF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}