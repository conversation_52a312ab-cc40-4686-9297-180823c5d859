{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../../services/project.service\";\nimport * as i3 from \"../../services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction TestingComponent_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", project_r5.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(project_r5.name);\n  }\n}\nfunction TestingComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"span\", 36);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Running tests... This may take a few moments.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TestingComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Tests in progress...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TestingComponent_div_49_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 48);\n  }\n}\nfunction TestingComponent_div_49_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 49);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_25_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Line \", error_r13.line, \"\");\n  }\n}\nfunction TestingComponent_div_49_div_12_div_25_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"span\", 68);\n    i0.ɵɵtext(2, \"Auto-fix:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 69);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const error_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(error_r13.fix);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_25_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"span\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TestingComponent_div_49_div_12_div_25_div_3_span_4_Template, 2, 1, \"span\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 64);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TestingComponent_div_49_div_12_div_25_div_3_div_7_Template, 5, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r13 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(error_r13.file);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", error_r13.line);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(error_r13.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", error_r13.fix);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"h5\");\n    i0.ɵɵtext(2, \"Errors\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TestingComponent_div_49_div_12_div_25_div_3_Template, 8, 4, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.testResults.testing.errors);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_26_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fix_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Line \", fix_r19.line, \"\");\n  }\n}\nfunction TestingComponent_div_49_div_12_div_26_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"span\", 74);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TestingComponent_div_49_div_12_div_26_div_3_span_4_Template, 2, 1, \"span\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 76);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fix_r19 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(fix_r19.file);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", fix_r19.line);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(fix_r19.description);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"h5\");\n    i0.ɵɵtext(2, \"Applied Fixes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TestingComponent_div_49_div_12_div_26_div_3_Template, 7, 3, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.testResults.fixes);\n  }\n}\nfunction TestingComponent_div_49_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"h5\");\n    i0.ɵɵtext(3, \"Test Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 52)(5, \"div\", 53)(6, \"span\", 54);\n    i0.ɵɵtext(7, \"Total Tests:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 55);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 53)(11, \"span\", 54);\n    i0.ɵɵtext(12, \"Passed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 55);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 53)(16, \"span\", 54);\n    i0.ɵɵtext(17, \"Failed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 55);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 53)(21, \"span\", 54);\n    i0.ɵɵtext(22, \"Duration:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 55);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(25, TestingComponent_div_49_div_12_div_25_Template, 4, 1, \"div\", 56);\n    i0.ɵɵtemplate(26, TestingComponent_div_49_div_12_div_26_Template, 4, 1, \"div\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r8.testResults.testing.total_tests || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.testResults.testing.passed_tests || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.testResults.testing.failed_tests || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r8.testResults.testing.duration || 0, \"s\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.testResults.testing.errors && ctx_r8.testResults.testing.errors.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.testResults.fixes && ctx_r8.testResults.fixes.length > 0);\n  }\n}\nfunction TestingComponent_div_49_div_13_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 48);\n  }\n}\nfunction TestingComponent_div_49_div_13_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 49);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"validation-success\": a0,\n    \"validation-failure\": a1\n  };\n};\nfunction TestingComponent_div_49_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"h5\");\n    i0.ɵɵtext(2, \"Browser Validation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79);\n    i0.ɵɵtemplate(4, TestingComponent_div_49_div_13_i_4_Template, 1, 0, \"i\", 43);\n    i0.ɵɵtemplate(5, TestingComponent_div_49_div_13_i_5_Template, 1, 0, \"i\", 44);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, ctx_r9.testResults.validation.success, !ctx_r9.testResults.validation.success));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.testResults.validation.success);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.testResults.validation.success);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.testResults.validation.message);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"test-success\": a0,\n    \"test-failure\": a1\n  };\n};\nfunction TestingComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"h3\");\n    i0.ɵɵtext(2, \"Test Results\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 41)(4, \"div\", 42);\n    i0.ɵɵtemplate(5, TestingComponent_div_49_i_5_Template, 1, 0, \"i\", 43);\n    i0.ɵɵtemplate(6, TestingComponent_div_49_i_6_Template, 1, 0, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 45)(8, \"h4\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, TestingComponent_div_49_div_12_Template, 27, 6, \"div\", 46);\n    i0.ɵɵtemplate(13, TestingComponent_div_49_div_13_Template, 8, 7, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c1, ctx_r3.testResults.success, !ctx_r3.testResults.success));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testResults.success);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.testResults.success);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.testResults.success ? \"Tests Passed\" : \"Tests Failed\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.testResults.message || \"Test execution completed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testResults.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testResults.validation);\n  }\n}\nfunction TestingComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"p\");\n    i0.ɵɵtext(2, \"No test results available. Run tests to see results.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let TestingComponent = /*#__PURE__*/(() => {\n  class TestingComponent {\n    constructor(http, projectService, notificationService) {\n      this.http = http;\n      this.projectService = projectService;\n      this.notificationService = notificationService;\n      this.projects = [];\n      this.selectedProject = '';\n      this.testResults = null;\n      this.isLoading = false;\n      this.testConfig = {\n        autoFix: true,\n        autoValidate: true,\n        browserValidation: true\n      };\n      this.testingInProgress = false;\n      this.continuousTestingActive = false;\n    }\n    ngOnInit() {\n      this.loadProjects();\n      this.loadTestConfig();\n    }\n    loadProjects() {\n      this.projectService.getProjects().subscribe(data => {\n        this.projects = data.projects;\n        if (this.projects.length > 0) {\n          this.selectedProject = this.projects[0].name;\n        }\n      }, error => {\n        console.error('Error loading projects:', error);\n        this.notificationService.showError('Failed to load projects');\n      });\n    }\n    loadTestConfig() {\n      debugger;\n      this.http.get(`${environment.apiUrl}/api/testing/config`).subscribe(data => {\n        if (data.testing) {\n          this.testConfig = {\n            autoFix: data.testing.auto_fix,\n            autoValidate: data.testing.auto_validate,\n            browserValidation: data.testing.browser_validation\n          };\n        }\n      }, error => {\n        console.error('Error loading test config:', error);\n      });\n    }\n    runTests() {\n      if (!this.selectedProject) {\n        this.notificationService.showError('Please select a project');\n        return;\n      }\n      this.isLoading = true;\n      this.testingInProgress = true;\n      this.testResults = null;\n      const projectDir = this.projectService.getProjectDir(this.selectedProject);\n      this.http.post(`${environment.apiUrl}/api/testing/run`, {\n        project_dir: projectDir,\n        auto_fix: this.testConfig.autoFix,\n        auto_validate: this.testConfig.autoValidate,\n        browser_url: `http://localhost:4200/projects/${this.selectedProject}/preview`\n      }).subscribe(data => {\n        this.testResults = data.results;\n        this.isLoading = false;\n        this.testingInProgress = false;\n        if (data.success) {\n          this.notificationService.showSuccess('Tests completed successfully');\n        } else {\n          this.notificationService.showWarning('Tests completed with issues');\n        }\n      }, error => {\n        console.error('Error running tests:', error);\n        this.isLoading = false;\n        this.testingInProgress = false;\n        this.notificationService.showError('Failed to run tests');\n      });\n    }\n    startContinuousTesting() {\n      if (!this.selectedProject) {\n        this.notificationService.showError('Please select a project');\n        return;\n      }\n      const projectDir = this.projectService.getProjectDir(this.selectedProject);\n      this.http.post(`${environment.apiUrl}/api/testing/continuous`, {\n        project_dir: projectDir,\n        auto_fix: this.testConfig.autoFix,\n        auto_validate: this.testConfig.autoValidate,\n        browser_url: `http://localhost:4200/projects/${this.selectedProject}/preview`\n      }).subscribe(data => {\n        this.continuousTestingActive = true;\n        this.notificationService.showSuccess('Continuous testing started');\n      }, error => {\n        console.error('Error starting continuous testing:', error);\n        this.notificationService.showError('Failed to start continuous testing');\n      });\n    }\n    openInVSCode() {\n      if (!this.selectedProject) {\n        this.notificationService.showError('Please select a project');\n        return;\n      }\n      const projectDir = this.projectService.getProjectDir(this.selectedProject);\n      this.http.post(`${environment.apiUrl}/api/testing/vscode`, {\n        project_dir: projectDir,\n        setup_workspace: true\n      }).subscribe(data => {\n        this.notificationService.showSuccess('Project opened in VS Code');\n      }, error => {\n        console.error('Error opening project in VS Code:', error);\n        this.notificationService.showError('Failed to open project in VS Code');\n      });\n    }\n    runDemo() {\n      const demoDir = `${this.projectService.getBaseDir()}/demo`;\n      this.isLoading = true;\n      this.testingInProgress = true;\n      this.testResults = null;\n      this.http.post(`${environment.apiUrl}/api/testing/demo`, {\n        project_dir: demoDir\n      }).subscribe(data => {\n        this.testResults = data.test_results_with_error;\n        this.isLoading = false;\n        this.testingInProgress = false;\n        this.notificationService.showSuccess('Demo completed successfully');\n      }, error => {\n        console.error('Error running demo:', error);\n        this.isLoading = false;\n        this.testingInProgress = false;\n        this.notificationService.showError('Failed to run demo');\n      });\n    }\n    updateTestConfig() {\n      this.http.put(`${environment.apiUrl}/api/testing/config`, {\n        testing: {\n          auto_fix: this.testConfig.autoFix,\n          auto_validate: this.testConfig.autoValidate,\n          browser_validation: this.testConfig.browserValidation\n        }\n      }).subscribe(data => {\n        this.notificationService.showSuccess('Test configuration updated');\n      }, error => {\n        console.error('Error updating test config:', error);\n        this.notificationService.showError('Failed to update test configuration');\n      });\n    }\n    stopTesting() {\n      if (!this.selectedProject) {\n        return;\n      }\n      const projectDir = this.projectService.getProjectDir(this.selectedProject);\n      this.http.post(`${environment.apiUrl}/api/testing/stop`, {\n        project_dir: projectDir\n      }).subscribe(data => {\n        this.testingInProgress = false;\n        this.isLoading = false;\n        this.notificationService.showSuccess('Testing stopped');\n      }, error => {\n        console.error('Error stopping tests:', error);\n        this.notificationService.showError('Failed to stop testing');\n      });\n    }\n    static {\n      this.ɵfac = function TestingComponent_Factory(t) {\n        return new (t || TestingComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TestingComponent,\n        selectors: [[\"app-testing\"]],\n        decls: 51,\n        vars: 14,\n        consts: [[1, \"testing-container\"], [1, \"testing-header\"], [1, \"testing-content\"], [1, \"testing-controls\"], [1, \"form-group\"], [\"for\", \"project-select\"], [\"id\", \"project-select\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"testing-options\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"auto-fix\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"auto-fix\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"auto-validate\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"auto-validate\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"browser-validation\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"browser-validation\", 1, \"form-check-label\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"testing-actions\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-vial\"], [1, \"btn\", \"btn-info\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"btn\", \"btn-danger\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-stop\"], [1, \"btn\", \"btn-secondary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-code\"], [1, \"btn\", \"btn-outline-primary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-play\"], [1, \"testing-results\"], [\"class\", \"testing-loading\", 4, \"ngIf\"], [\"class\", \"testing-progress\", 4, \"ngIf\"], [\"class\", \"test-results-container\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [3, \"value\"], [1, \"testing-loading\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"sr-only\"], [1, \"testing-progress\"], [1, \"progress\"], [\"role\", \"progressbar\", 1, \"progress-bar\", \"progress-bar-striped\", \"progress-bar-animated\", 2, \"width\", \"100%\"], [1, \"test-results-container\"], [1, \"test-summary\", 3, \"ngClass\"], [1, \"test-status\"], [\"class\", \"fas fa-check-circle\", 4, \"ngIf\"], [\"class\", \"fas fa-times-circle\", 4, \"ngIf\"], [1, \"test-info\"], [\"class\", \"test-details\", 4, \"ngIf\"], [\"class\", \"validation-results\", 4, \"ngIf\"], [1, \"fas\", \"fa-check-circle\"], [1, \"fas\", \"fa-times-circle\"], [1, \"test-details\"], [1, \"test-section\"], [1, \"test-stats\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"test-errors\", 4, \"ngIf\"], [\"class\", \"test-fixes\", 4, \"ngIf\"], [1, \"test-errors\"], [\"class\", \"error-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"error-item\"], [1, \"error-header\"], [1, \"error-file\"], [\"class\", \"error-line\", 4, \"ngIf\"], [1, \"error-message\"], [\"class\", \"error-fix\", 4, \"ngIf\"], [1, \"error-line\"], [1, \"error-fix\"], [1, \"fix-label\"], [1, \"fix-message\"], [1, \"test-fixes\"], [\"class\", \"fix-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"fix-item\"], [1, \"fix-header\"], [1, \"fix-file\"], [\"class\", \"fix-line\", 4, \"ngIf\"], [1, \"fix-description\"], [1, \"fix-line\"], [1, \"validation-results\"], [1, \"validation-status\", 3, \"ngClass\"], [1, \"no-results\"]],\n        template: function TestingComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"Autonomous Testing\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Run autonomous tests, fix errors, and validate functionality automatically.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4)(9, \"label\", 5);\n            i0.ɵɵtext(10, \"Select Project\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"select\", 6);\n            i0.ɵɵlistener(\"ngModelChange\", function TestingComponent_Template_select_ngModelChange_11_listener($event) {\n              return ctx.selectedProject = $event;\n            });\n            i0.ɵɵtemplate(12, TestingComponent_option_12_Template, 2, 2, \"option\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 8)(14, \"h3\");\n            i0.ɵɵtext(15, \"Testing Options\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 9)(17, \"input\", 10);\n            i0.ɵɵlistener(\"ngModelChange\", function TestingComponent_Template_input_ngModelChange_17_listener($event) {\n              return ctx.testConfig.autoFix = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"label\", 11);\n            i0.ɵɵtext(19, \"Auto-fix errors\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 9)(21, \"input\", 12);\n            i0.ɵɵlistener(\"ngModelChange\", function TestingComponent_Template_input_ngModelChange_21_listener($event) {\n              return ctx.testConfig.autoValidate = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"label\", 13);\n            i0.ɵɵtext(23, \"Auto-validate in browser\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 9)(25, \"input\", 14);\n            i0.ɵɵlistener(\"ngModelChange\", function TestingComponent_Template_input_ngModelChange_25_listener($event) {\n              return ctx.testConfig.browserValidation = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"label\", 15);\n            i0.ɵɵtext(27, \"Browser validation\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_28_listener() {\n              return ctx.updateTestConfig();\n            });\n            i0.ɵɵtext(29, \"Update Config\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"div\", 17)(31, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_31_listener() {\n              return ctx.runTests();\n            });\n            i0.ɵɵelement(32, \"i\", 19);\n            i0.ɵɵtext(33, \" Run Tests \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_34_listener() {\n              return ctx.startContinuousTesting();\n            });\n            i0.ɵɵelement(35, \"i\", 21);\n            i0.ɵɵtext(36, \" Start Continuous Testing \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_37_listener() {\n              return ctx.stopTesting();\n            });\n            i0.ɵɵelement(38, \"i\", 23);\n            i0.ɵɵtext(39, \" Stop Testing \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_40_listener() {\n              return ctx.openInVSCode();\n            });\n            i0.ɵɵelement(41, \"i\", 25);\n            i0.ɵɵtext(42, \" Open in VS Code \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_43_listener() {\n              return ctx.runDemo();\n            });\n            i0.ɵɵelement(44, \"i\", 27);\n            i0.ɵɵtext(45, \" Run Demo \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(46, \"div\", 28);\n            i0.ɵɵtemplate(47, TestingComponent_div_47_Template, 6, 0, \"div\", 29);\n            i0.ɵɵtemplate(48, TestingComponent_div_48_Template, 5, 0, \"div\", 30);\n            i0.ɵɵtemplate(49, TestingComponent_div_49_Template, 14, 10, \"div\", 31);\n            i0.ɵɵtemplate(50, TestingComponent_div_50_Template, 3, 0, \"div\", 32);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngModel\", ctx.selectedProject);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.projects);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngModel\", ctx.testConfig.autoFix);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngModel\", ctx.testConfig.autoValidate);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngModel\", ctx.testConfig.browserValidation);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.selectedProject);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.selectedProject || ctx.continuousTestingActive);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", !ctx.isLoading && !ctx.testingInProgress);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", !ctx.selectedProject);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.testingInProgress && !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.testResults && !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.testResults && !ctx.isLoading && !ctx.testingInProgress);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.CheckboxControlValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n        styles: [\".testing-container[_ngcontent-%COMP%]{padding:20px;height:100%;display:flex;flex-direction:column}.testing-header[_ngcontent-%COMP%]{margin-bottom:20px}.testing-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin-bottom:8px;color:#333}.testing-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:0}.testing-content[_ngcontent-%COMP%]{display:flex;flex:1;gap:20px}@media (max-width: 992px){.testing-content[_ngcontent-%COMP%]{flex-direction:column}}.testing-controls[_ngcontent-%COMP%]{flex:0 0 300px;background-color:#f8f9fa;border-radius:8px;padding:20px;display:flex;flex-direction:column;gap:20px}@media (max-width: 992px){.testing-controls[_ngcontent-%COMP%]{flex:0 0 auto}}.testing-results[_ngcontent-%COMP%]{flex:1;background-color:#f8f9fa;border-radius:8px;padding:20px;overflow-y:auto}.form-group[_ngcontent-%COMP%]{margin-bottom:15px}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:5px;font-weight:500}.form-group[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%]{width:100%;padding:8px 12px;border:1px solid #ced4da;border-radius:4px;background-color:#fff}.testing-options[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px;margin-bottom:10px}.testing-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]{margin-bottom:8px}.testing-options[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]{margin-right:8px}.testing-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-top:10px}.testing-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px}.testing-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px}.testing-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.btn[_ngcontent-%COMP%]{padding:8px 16px;border-radius:4px;cursor:pointer;font-weight:500;transition:all .2s}.btn[_ngcontent-%COMP%]:disabled{cursor:not-allowed;opacity:.6}.btn-primary[_ngcontent-%COMP%]{background-color:#007bff;border:1px solid #007bff;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#0069d9;border-color:#0062cc}.btn-info[_ngcontent-%COMP%]{background-color:#17a2b8;border:1px solid #17a2b8;color:#fff}.btn-info[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#138496;border-color:#117a8b}.btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;border:1px solid #6c757d;color:#fff}.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#5a6268;border-color:#545b62}.btn-outline-primary[_ngcontent-%COMP%]{background-color:transparent;border:1px solid #007bff;color:#007bff}.btn-outline-primary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#007bff;color:#fff}.testing-loading[_ngcontent-%COMP%], .testing-progress[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0;text-align:center}.testing-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .testing-progress[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:15px;color:#666}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}.progress[_ngcontent-%COMP%]{width:100%;height:8px;margin-bottom:10px;background-color:#e9ecef;border-radius:4px;overflow:hidden}.progress-bar[_ngcontent-%COMP%]{height:100%;background-color:#007bff}.test-results-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:15px;font-size:18px}.test-summary[_ngcontent-%COMP%]{display:flex;align-items:center;padding:15px;border-radius:6px;margin-bottom:20px}.test-summary.test-success[_ngcontent-%COMP%]{background-color:#28a7451a;border:1px solid rgba(40,167,69,.2)}.test-summary.test-failure[_ngcontent-%COMP%]{background-color:#dc35451a;border:1px solid rgba(220,53,69,.2)}.test-summary[_ngcontent-%COMP%]   .test-status[_ngcontent-%COMP%]{font-size:24px;margin-right:15px}.test-summary[_ngcontent-%COMP%]   .test-status[_ngcontent-%COMP%]   i.fa-check-circle[_ngcontent-%COMP%]{color:#28a745}.test-summary[_ngcontent-%COMP%]   .test-status[_ngcontent-%COMP%]   i.fa-times-circle[_ngcontent-%COMP%]{color:#dc3545}.test-summary[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 5px;font-size:16px}.test-summary[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666}.test-details[_ngcontent-%COMP%], .test-section[_ngcontent-%COMP%]{margin-bottom:20px}.test-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:16px;margin-bottom:10px;padding-bottom:5px;border-bottom:1px solid #dee2e6}.test-stats[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:15px}.test-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{background-color:#fff;padding:10px 15px;border-radius:4px;box-shadow:0 1px 3px #0000001a;flex:1 0 calc(25% - 15px);min-width:120px}.test-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{display:block;font-size:12px;color:#6c757d;margin-bottom:5px}.test-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:18px;font-weight:500}.test-errors[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]{margin-top:20px}.test-errors[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:16px;margin-bottom:10px;padding-bottom:5px;border-bottom:1px solid #dee2e6}.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]{background-color:#fff;padding:15px;border-radius:4px;box-shadow:0 1px 3px #0000001a;margin-bottom:10px}.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:8px;font-size:14px}.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%]{font-weight:500}.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%]{color:#6c757d}.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-description[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-description[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-description[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-description[_ngcontent-%COMP%]{font-family:monospace;background-color:#f8f9fa;padding:8px;border-radius:4px;margin-bottom:8px;white-space:pre-wrap;word-break:break-all}.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]{font-size:14px}.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-label[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-label[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-label[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-label[_ngcontent-%COMP%]{font-weight:500;margin-right:5px}.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-message[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-message[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-message[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-message[_ngcontent-%COMP%]{color:#28a745}.validation-results[_ngcontent-%COMP%]{margin-top:20px}.validation-results[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:16px;margin-bottom:10px;padding-bottom:5px;border-bottom:1px solid #dee2e6}.validation-results[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;padding:15px;border-radius:6px}.validation-results[_ngcontent-%COMP%]   .validation-status.validation-success[_ngcontent-%COMP%]{background-color:#28a7451a;border:1px solid rgba(40,167,69,.2)}.validation-results[_ngcontent-%COMP%]   .validation-status.validation-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#28a745}.validation-results[_ngcontent-%COMP%]   .validation-status.validation-failure[_ngcontent-%COMP%]{background-color:#dc35451a;border:1px solid rgba(220,53,69,.2)}.validation-results[_ngcontent-%COMP%]   .validation-status.validation-failure[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#dc3545}.validation-results[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px}\"]\n      });\n    }\n  }\n  return TestingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}