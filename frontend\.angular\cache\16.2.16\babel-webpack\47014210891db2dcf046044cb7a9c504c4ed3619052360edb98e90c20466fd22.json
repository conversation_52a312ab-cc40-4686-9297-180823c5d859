{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/project.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction HomeComponent_div_64_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_64_div_4_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const project_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.openProject(project_r3.name));\n    });\n    i0.ɵɵelementStart(1, \"h3\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 26)(4, \"span\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r3 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r3.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Updated: \", ctx_r2.formatDate(project_r3.updated_at), \"\");\n  }\n}\nfunction HomeComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"h2\");\n    i0.ɵɵtext(2, \"Recent Projects\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtemplate(4, HomeComponent_div_64_div_4_Template, 6, 2, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22)(6, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_64_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToProjects());\n    });\n    i0.ɵɵtext(7, \" View All Projects \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.recentProjects);\n  }\n}\nfunction HomeComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"img\", 29);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading recent projects...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor(projectService, router) {\n      this.projectService = projectService;\n      this.router = router;\n      this.recentProjects = [];\n      this.loading = false;\n    }\n    ngOnInit() {\n      this.loadRecentProjects();\n    }\n    loadRecentProjects() {\n      this.loading = true;\n      this.projectService.getProjects().subscribe(response => {\n        this.recentProjects = (response.projects || []).sort((a, b) => {\n          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();\n        }).slice(0, 5);\n        this.loading = false;\n      }, error => {\n        console.error('Error loading recent projects:', error);\n        this.loading = false;\n      });\n    }\n    navigateToProjects() {\n      this.router.navigate(['/projects']);\n    }\n    navigateToConfig() {\n      this.router.navigate(['/config']);\n    }\n    openProject(projectName) {\n      this.router.navigate(['/projects', projectName]);\n    }\n    navigateToFeature(feature) {\n      switch (feature) {\n        case 'ai-development':\n          this.router.navigate(['/projects']);\n          break;\n        case 'documentation':\n          this.router.navigate(['/projects']);\n          break;\n        case 'feedback':\n          this.router.navigate(['/projects']);\n          break;\n        case 'integrations':\n          this.router.navigate(['/config']);\n          break;\n        case 'vscode':\n          this.router.navigate(['/testing']);\n          break;\n        case 'testing':\n          this.router.navigate(['/testing']);\n          break;\n        default:\n          this.router.navigate(['/']);\n          break;\n      }\n    }\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleString();\n    }\n    static {\n      this.ɵfac = function HomeComponent_Factory(t) {\n        return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ProjectService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"app-home\"]],\n        decls: 66,\n        vars: 2,\n        consts: [[1, \"home-container\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-description\"], [1, \"hero-actions\"], [1, \"primary-btn\", 3, \"click\"], [1, \"secondary-btn\", 3, \"click\"], [1, \"features-section\"], [1, \"features-grid\"], [1, \"feature-card\", \"clickable\", 3, \"click\"], [1, \"feature-icon\", \"ai-icon\"], [1, \"material-icons\"], [1, \"feature-icon\", \"doc-icon\"], [1, \"feature-icon\", \"feedback-icon\"], [1, \"feature-icon\", \"integration-icon\"], [1, \"feature-icon\", \"vscode-icon\"], [1, \"feature-icon\", \"testing-icon\"], [\"class\", \"recent-projects-section\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [1, \"recent-projects-section\"], [1, \"projects-list\"], [\"class\", \"project-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all\"], [1, \"text-btn\", 3, \"click\"], [1, \"project-card\", 3, \"click\"], [1, \"project-name\"], [1, \"project-meta\"], [1, \"project-date\"], [1, \"loading-indicator\"], [\"src\", \"assets/images/loading-spinner.svg\", \"alt\", \"Loading\", 1, \"spinner-image\"]],\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n            i0.ɵɵtext(4, \"Autonomous AI Software Development Agent\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 3);\n            i0.ɵɵtext(6, \" Create complete projects with AI assistance - including source code, configuration files, and documentation. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 4)(8, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_8_listener() {\n              return ctx.navigateToProjects();\n            });\n            i0.ɵɵtext(9, \" Start a New Project \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_10_listener() {\n              return ctx.navigateToConfig();\n            });\n            i0.ɵɵtext(11, \" Configure AI Models \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(12, \"div\", 7)(13, \"h2\");\n            i0.ɵɵtext(14, \"Key Features\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 8)(16, \"div\", 9);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_16_listener() {\n              return ctx.navigateToFeature(\"ai-development\");\n            });\n            i0.ɵɵelementStart(17, \"div\", 10)(18, \"i\", 11);\n            i0.ɵɵtext(19, \"smart_toy\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"h3\");\n            i0.ɵɵtext(21, \"AI-Powered Development\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"p\");\n            i0.ɵɵtext(23, \"Leverage external AI models to generate complete projects based on your specifications.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 9);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_24_listener() {\n              return ctx.navigateToFeature(\"documentation\");\n            });\n            i0.ɵɵelementStart(25, \"div\", 12)(26, \"i\", 11);\n            i0.ɵɵtext(27, \"description\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"h3\");\n            i0.ɵɵtext(29, \"Complete Documentation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"p\");\n            i0.ɵɵtext(31, \"Automatically generate comprehensive documentation for your projects.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 9);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_32_listener() {\n              return ctx.navigateToFeature(\"feedback\");\n            });\n            i0.ɵɵelementStart(33, \"div\", 13)(34, \"i\", 11);\n            i0.ɵɵtext(35, \"sync\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"h3\");\n            i0.ɵɵtext(37, \"Real-Time Feedback\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"p\");\n            i0.ɵɵtext(39, \"See your changes in real-time with integrated code editor and browser preview.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 9);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_40_listener() {\n              return ctx.navigateToFeature(\"integrations\");\n            });\n            i0.ɵɵelementStart(41, \"div\", 14)(42, \"i\", 11);\n            i0.ɵɵtext(43, \"extension\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"h3\");\n            i0.ɵɵtext(45, \"Multiple AI Integrations\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"p\");\n            i0.ɵɵtext(47, \"Connect to OpenAI, Ollama, or LM Studio using your own API keys.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(48, \"div\", 9);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_48_listener() {\n              return ctx.navigateToFeature(\"vscode\");\n            });\n            i0.ɵɵelementStart(49, \"div\", 15)(50, \"i\", 11);\n            i0.ɵɵtext(51, \"code\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"h3\");\n            i0.ɵɵtext(53, \"VS Code Integration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"p\");\n            i0.ɵɵtext(55, \"Seamlessly integrated with Visual Studio Code for a complete development experience.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(56, \"div\", 9);\n            i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_56_listener() {\n              return ctx.navigateToFeature(\"testing\");\n            });\n            i0.ɵɵelementStart(57, \"div\", 16)(58, \"i\", 11);\n            i0.ɵɵtext(59, \"check_circle\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(60, \"h3\");\n            i0.ɵɵtext(61, \"Autonomous Testing\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"p\");\n            i0.ɵɵtext(63, \"Automatically test, fix errors, and validate functionality without manual intervention.\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(64, HomeComponent_div_64_Template, 8, 1, \"div\", 17);\n            i0.ɵɵtemplate(65, HomeComponent_div_65_Template, 4, 0, \"div\", 18);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(64);\n            i0.ɵɵproperty(\"ngIf\", ctx.recentProjects.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf],\n        styles: [\".home-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:20px;max-width:1200px;margin:0 auto}.hero-section[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:80px 20px;background:url(/assets/images/hero-bg.svg) no-repeat center center;background-size:cover;border-radius:8px;margin-bottom:40px;position:relative;overflow:hidden;box-shadow:0 4px 20px #0000001a}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]{text-align:center;max-width:800px}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:36px;font-weight:700;margin-bottom:16px;color:#2196f3}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-description[_ngcontent-%COMP%]{font-size:18px;color:#555;margin-bottom:32px;line-height:1.5}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:16px}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:12px 24px;border-radius:4px;font-weight:500;font-size:16px;cursor:pointer;transition:all .2s}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button.primary-btn[_ngcontent-%COMP%]{background-color:#2196f3;color:#fff;border:none}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button.primary-btn[_ngcontent-%COMP%]:hover{background-color:#1976d2}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button.secondary-btn[_ngcontent-%COMP%]{background-color:#fff;color:#2196f3;border:1px solid #2196f3}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button.secondary-btn[_ngcontent-%COMP%]:hover{background-color:#e3f2fd}.features-section[_ngcontent-%COMP%]{margin-bottom:40px}.features-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:28px;font-weight:600;margin-bottom:24px;text-align:center}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:24px}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;padding:24px;box-shadow:0 2px 8px #0000001a;transition:transform .2s,box-shadow .2s}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 8px 16px #0000001a}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card.clickable[_ngcontent-%COMP%]{cursor:pointer;position:relative}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card.clickable[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 20px #00000026}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card.clickable[_ngcontent-%COMP%]:hover:after{content:\\\"\\\";position:absolute;bottom:12px;right:12px;width:24px;height:24px;background-image:url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"%232196f3\\\" stroke-width=\\\"2\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\"><path d=\\\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\\\"></path><polyline points=\\\"15 3 21 3 21 9\\\"></polyline><line x1=\\\"10\\\" y1=\\\"14\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line></svg>');background-repeat:no-repeat;opacity:.7}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;display:flex;align-items:center;justify-content:center;margin:0 auto 20px}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:32px;color:#fff}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.ai-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6a11cb 0%,#2575fc 100%)}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.doc-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f83600 0%,#f9d423 100%)}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.feedback-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00c6fb 0%,#005bea 100%)}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.integration-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#11998e 0%,#38ef7d 100%)}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.vscode-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007acc 0%,#0097e6 100%)}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.testing-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4caf50 0%,#8bc34a 100%)}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin-bottom:12px;color:#333}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#666;line-height:1.5}.recent-projects-section[_ngcontent-%COMP%]{margin-bottom:40px}.recent-projects-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:28px;font-weight:600;margin-bottom:24px;text-align:center}.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:16px;margin-bottom:24px}.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #e0e0e0;border-radius:8px;padding:16px;cursor:pointer;transition:box-shadow .2s,transform .2s}.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px #0000001a;transform:translateY(-2px)}.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-name[_ngcontent-%COMP%]{margin:0 0 8px;font-size:18px;font-weight:500}.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-meta[_ngcontent-%COMP%]{font-size:12px;color:#666}.recent-projects-section[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]{display:flex;justify-content:center}.recent-projects-section[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]   .text-btn[_ngcontent-%COMP%]{background:none;border:none;color:#2196f3;font-weight:500;cursor:pointer;font-size:16px}.recent-projects-section[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]   .text-btn[_ngcontent-%COMP%]:hover{text-decoration:underline}.loading-indicator[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin:32px 0}.loading-indicator[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{width:32px;height:32px;border:3px solid rgba(0,0,0,.1);border-radius:50%;border-top-color:#3498db;animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite;margin-bottom:8px}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}@media (max-width: 768px){.hero-section[_ngcontent-%COMP%]{padding:40px 16px}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:28px}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-description[_ngcontent-%COMP%]{font-size:16px}.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]{flex-direction:column;gap:12px}.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}