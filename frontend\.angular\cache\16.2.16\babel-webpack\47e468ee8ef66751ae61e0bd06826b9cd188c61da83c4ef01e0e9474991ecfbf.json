{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = [\"iframe\"];\nfunction BrowserPreviewComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"div\", 13);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Running test...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_1_div_5_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const log_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(log_r7);\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Logs:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, BrowserPreviewComponent_div_17_div_1_div_5_li_4_Template, 2, 1, \"li\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.testResult.logs);\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Result:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, BrowserPreviewComponent_div_17_div_1_div_5_Template, 5, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.testResult.result, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.testResult.logs && ctx_r2.testResult.logs.length);\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"img\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r3.testResult.screenshot, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\");\n    i0.ɵɵtext(2, \"No test run yet. Edit the script and click \\\"Run Test\\\" to start.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BrowserPreviewComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BrowserPreviewComponent_div_17_div_1_Template, 6, 2, \"div\", 11);\n    i0.ɵɵtemplate(2, BrowserPreviewComponent_div_17_div_2_Template, 2, 1, \"div\", 11);\n    i0.ɵɵtemplate(3, BrowserPreviewComponent_div_17_div_3_Template, 3, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.testResult);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.testResult == null ? null : ctx_r1.testResult.screenshot);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.testResult);\n  }\n}\nexport class BrowserPreviewComponent {\n  constructor(sanitizer, renderer, http) {\n    this.sanitizer = sanitizer;\n    this.renderer = renderer;\n    this.http = http;\n    this.projectName = '';\n    this.url = '';\n    this.codeServerPort = 8081; // New input for code server port\n    this.expandChange = new EventEmitter();\n    this.loading = true;\n    this.testResult = null;\n    // Only local SearxNG instance for embedded search\n    this.searchEngines = [{\n      name: 'Local SearxNG',\n      url: 'http://localhost:8888'\n    }];\n    this.selectedEngine = this.searchEngines[0];\n    this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n    this.isBrowserMode = false;\n    this.engineUrl = `http://localhost:${this.codeServerPort}`; // Use dynamic port\n    this.searchLogs = [];\n    this.scriptText = `// Basic test: Open localhost:4200 and check for a button\\nawait page.goto('http://localhost:4200');\\nconst button = await page.$('button');\\nif (button) {\\n  console.log('Button found!');\\n  return 'Button found!';\\n} else {\\n  console.log('Button not found!');\\n  return 'Button not found!';\\n}\\n`;\n    this.runner = 'puppeteer';\n  }\n  ngOnInit() {\n    console.log('[BrowserPreviewComponent] ngOnInit called');\n    this.updateUrl();\n    this.engineUrl = `http://localhost:${this.codeServerPort}`; // Update engineUrl with current port\n    this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.engineUrl);\n  }\n  ngOnChanges(changes) {\n    console.log('[BrowserPreviewComponent] ngOnChanges called with:', changes);\n    if (changes['url']) {\n      console.log('[BrowserPreviewComponent] URL input changed:', changes['url'].currentValue);\n    }\n    if (changes['codeServerPort']) {\n      console.log('[BrowserPreviewComponent] Code server port changed:', changes['codeServerPort'].currentValue);\n      this.engineUrl = `http://localhost:${this.codeServerPort}`;\n      this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.engineUrl);\n    }\n    this.updateUrl();\n  }\n  ngAfterViewInit() {\n    // Set up message listener for iframe communication\n    window.addEventListener('message', this.handleIframeMessage.bind(this));\n  }\n  handleIframeMessage(event) {\n    // Check if the message is from our iframe (SearXNG)\n    if (event.source === this.iframe?.nativeElement.contentWindow) {\n      try {\n        const data = event.data;\n        if (data && data.type === 'searxng') {\n          console.log('SearXNG interaction:', data);\n          // Log search queries\n          if (data.action === 'search') {\n            this.logSearch(data.query, data.results);\n          }\n        }\n      } catch (error) {\n        console.error('Error processing iframe message:', error);\n      }\n    }\n  }\n  logSearch(query, results) {\n    this.searchLogs.push({\n      timestamp: new Date(),\n      query,\n      results\n    });\n    console.log('Search log updated:', this.searchLogs);\n  }\n  updateUrl() {\n    console.log('[BrowserPreviewComponent] updateUrl called with input URL:', this.url);\n    if (this.url) {\n      this.loading = true;\n      this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.url);\n      console.log('[BrowserPreviewComponent] Safe URL created and loading set to true:', this.safeUrl);\n    } else {\n      console.warn('[BrowserPreviewComponent] No URL provided. Using default search engine.');\n      this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n    }\n  }\n  onIframeLoad() {\n    console.log('[BrowserPreviewComponent] onIframeLoad triggered — iframe finished loading.');\n    this.loading = false;\n    // Inject message listener into SearXNG iframe\n    if (this.iframe && this.isBrowserMode) {\n      try {\n        const iframeWindow = this.iframe.nativeElement.contentWindow;\n        // Only attempt to inject script if we have access to the iframe (same-origin)\n        if (iframeWindow) {\n          this.injectLoggerScript(iframeWindow);\n        }\n      } catch (e) {\n        console.warn('Could not inject script into iframe due to same-origin policy. This is expected for cross-origin iframes.', e);\n      }\n    }\n  }\n  injectLoggerScript(iframeWindow) {\n    try {\n      const script = this.renderer.createElement('script');\n      const scriptContent = `\n        // SearXNG interaction logger\n        (function() {\n          const originalFetch = window.fetch;\n          window.fetch = function(...args) {\n            const url = args[0].url || args[0];\n            \n            // Check if this is a search request\n            if (url.includes('/search')) {\n              const searchParams = new URL(url, window.location.origin).searchParams;\n              const query = searchParams.get('q');\n              \n              // Send the query to the parent window\n              if (query) {\n                return originalFetch.apply(this, args).then(response => {\n                  // Clone the response so we can read and use it\n                  const responseClone = response.clone();\n                  responseClone.json().then(results => {\n                    window.parent.postMessage({\n                      type: 'searxng',\n                      action: 'search',\n                      query: query,\n                      results: results\n                    }, '*');\n                  }).catch(e => console.error('Error parsing SearXNG response:', e));\n                  \n                  return response;\n                });\n              }\n            }\n            \n            return originalFetch.apply(this, args);\n          };\n          \n          console.log('SearXNG logger injected');\n        })();\n      `;\n      this.renderer.appendChild(script, this.renderer.createText(scriptContent));\n      // Try to append to document if possible\n      try {\n        const iframeDocument = iframeWindow.document;\n        this.renderer.appendChild(iframeDocument.head || iframeDocument.body, script);\n        console.log('Successfully injected SearXNG logger script');\n      } catch (e) {\n        console.warn('Could not inject script into iframe document:', e);\n      }\n    } catch (e) {\n      console.error('Error creating logger script:', e);\n    }\n  }\n  refresh() {\n    console.log('[BrowserPreviewComponent] refresh called');\n    if (this.iframe && this.iframe.nativeElement) {\n      this.loading = true;\n      const currentSrc = this.iframe.nativeElement.src;\n      this.iframe.nativeElement.src = currentSrc;\n      console.log('[BrowserPreviewComponent] Iframe refreshed with current src:', currentSrc);\n    } else {\n      console.warn('[BrowserPreviewComponent] Iframe reference not available');\n    }\n  }\n  onEngineChange() {\n    this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n  }\n  toggleMode() {\n    this.isBrowserMode = !this.isBrowserMode;\n    this.loading = true;\n    if (this.isBrowserMode) {\n      this.url = '';\n      this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n    }\n  }\n  runTest() {\n    this.loading = true;\n    this.testResult = null;\n    this.http.get('http://localhost:4000/run-tests').subscribe({\n      next: res => {\n        this.testResult = res;\n        this.loading = false;\n      },\n      error: err => {\n        this.testResult = {\n          result: 'Error running test: ' + (err?.message || err)\n        };\n        this.loading = false;\n      }\n    });\n  }\n  runDynamicTest() {\n    this.loading = true;\n    this.testResult = null;\n    this.http.post('http://localhost:4000/run-script', {\n      script: this.scriptText,\n      runner: this.runner\n    }).subscribe({\n      next: res => {\n        this.testResult = res;\n        this.loading = false;\n      },\n      error: err => {\n        this.testResult = {\n          result: 'Error running script: ' + (err?.message || err),\n          logs: err?.error?.logs || []\n        };\n        this.loading = false;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function BrowserPreviewComponent_Factory(t) {\n      return new (t || BrowserPreviewComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BrowserPreviewComponent,\n      selectors: [[\"app-browser-preview\"]],\n      viewQuery: function BrowserPreviewComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.iframe = _t.first);\n        }\n      },\n      inputs: {\n        projectName: \"projectName\",\n        url: \"url\",\n        codeServerPort: \"codeServerPort\"\n      },\n      outputs: {\n        expandChange: \"expandChange\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 18,\n      vars: 5,\n      consts: [[1, \"browser-container\"], [1, \"browser-header\"], [1, \"engine-name\"], [1, \"browser-actions\"], [2, \"margin-right\", \"8px\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"puppeteer\"], [\"value\", \"playwright\"], [\"title\", \"Run Dynamic Test\", 1, \"icon-btn\", 3, \"disabled\", \"click\"], [1, \"browser-content\", \"card\"], [\"rows\", \"8\", \"placeholder\", \"Enter Puppeteer/Playwright script here\", 2, \"width\", \"100%\", \"font-family\", \"monospace\", \"margin-bottom\", \"8px\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"loading-overlay\"], [1, \"spinner\"], [4, \"ngFor\", \"ngForOf\"], [\"alt\", \"Test Screenshot\", 2, \"max-width\", \"100%\", \"border-radius\", \"8px\", \"box-shadow\", \"0 2px 8px #0002\", 3, \"src\"]],\n      template: function BrowserPreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵtext(3, \"Test Results \");\n          i0.ɵɵelementStart(4, \"span\", 2);\n          i0.ɵɵtext(5, \"(AI Automated)\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"select\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function BrowserPreviewComponent_Template_select_ngModelChange_7_listener($event) {\n            return ctx.runner = $event;\n          });\n          i0.ɵɵelementStart(8, \"option\", 5);\n          i0.ɵɵtext(9, \"Puppeteer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"option\", 6);\n          i0.ɵɵtext(11, \"Playwright\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function BrowserPreviewComponent_Template_button_click_12_listener() {\n            return ctx.runDynamicTest();\n          });\n          i0.ɵɵtext(13, \" \\u25B6\\uFE0F Run Test \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 8)(15, \"textarea\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function BrowserPreviewComponent_Template_textarea_ngModelChange_15_listener($event) {\n            return ctx.scriptText = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, BrowserPreviewComponent_div_16_Template, 4, 0, \"div\", 10);\n          i0.ɵɵtemplate(17, BrowserPreviewComponent_div_17_Template, 4, 3, \"div\", 11);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.runner);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.scriptText);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgModel],\n      styles: [\".browser-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  background: #fff;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  transition: box-shadow 0.2s, border-radius 0.2s;\\n}\\n\\n.browser-container.expanded[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 10;\\n  height: 80vh;\\n  width: 100%;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);\\n}\\n\\n.browser-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 24px 16px 20px;\\n  background-color: #f7fafd;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.browser-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.engine-name[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 400;\\n  color: #888;\\n  margin-left: 12px;\\n}\\n\\n.browser-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n\\n.icon-btn[_ngcontent-%COMP%] {\\n  background: #f7fafd;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 6px;\\n  padding: 7px 12px;\\n  font-size: 20px;\\n  cursor: pointer;\\n  transition: background 0.2s, border 0.2s, box-shadow 0.2s;\\n  outline: none;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);\\n  color: #1976d2;\\n}\\n\\n.icon-btn[_ngcontent-%COMP%]:disabled {\\n  background: #f5f5f5;\\n  color: #bbb;\\n  cursor: not-allowed;\\n}\\n\\n.icon-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #e3f2fd;\\n  border-color: #90caf9;\\n  color: #0d47a1;\\n}\\n\\n.browser-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  padding: 24px 24px 20px 24px;\\n  background: #fafdff;\\n  min-height: 350px;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  background: #fff;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  padding: 0;\\n  height: 100%;\\n}\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 10;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: 3px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 50%;\\n  border-top-color: #3498db;\\n  animation: _ngcontent-%COMP%_spin 1s ease-in-out infinite;\\n  margin-bottom: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.browser-iframe[_ngcontent-%COMP%] {\\n  height: 60vh;\\n  width: 100%;\\n  border: none;\\n  border-radius: 10px;\\n  background: #fff;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);\\n  margin-top: 8px;\\n}\\n\\n.browser-container.expanded[_ngcontent-%COMP%]   .browser-iframe[_ngcontent-%COMP%] {\\n  height: 75vh;\\n  width: 100%;\\n  border-radius: 10px;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 300px;\\n  color: #888;\\n  font-style: italic;\\n  font-size: 18px;\\n  background: #fafdff;\\n  border-radius: 10px;\\n  margin-top: 30px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "log_r7", "ɵɵtemplate", "BrowserPreviewComponent_div_17_div_1_div_5_li_4_Template", "ɵɵproperty", "ctx_r5", "testResult", "logs", "BrowserPreviewComponent_div_17_div_1_div_5_Template", "ɵɵtextInterpolate1", "ctx_r2", "result", "length", "ctx_r3", "screenshot", "ɵɵsanitizeUrl", "BrowserPreviewComponent_div_17_div_1_Template", "BrowserPreviewComponent_div_17_div_2_Template", "BrowserPreviewComponent_div_17_div_3_Template", "ctx_r1", "BrowserPreviewComponent", "constructor", "sanitizer", "renderer", "http", "projectName", "url", "codeServerPort", "expandChange", "loading", "searchEngines", "name", "<PERSON><PERSON><PERSON><PERSON>", "engineSafeUrl", "bypassSecurityTrustResourceUrl", "isBrowserMode", "engineUrl", "searchLogs", "scriptText", "runner", "ngOnInit", "console", "log", "updateUrl", "ngOnChanges", "changes", "currentValue", "ngAfterViewInit", "window", "addEventListener", "handleIframeMessage", "bind", "event", "source", "iframe", "nativeElement", "contentWindow", "data", "type", "action", "logSearch", "query", "results", "error", "push", "timestamp", "Date", "safeUrl", "warn", "onIframeLoad", "iframeWindow", "injectLoggerScript", "e", "script", "createElement", "scriptContent", "append<PERSON><PERSON><PERSON>", "createText", "iframeDocument", "document", "head", "body", "refresh", "currentSrc", "src", "onEngineChange", "toggleMode", "runTest", "get", "subscribe", "next", "res", "err", "message", "runDynamicTest", "post", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "Renderer2", "i2", "HttpClient", "selectors", "viewQuery", "BrowserPreviewComponent_Query", "rf", "ctx", "ɵɵlistener", "BrowserPreviewComponent_Template_select_ngModelChange_7_listener", "$event", "BrowserPreviewComponent_Template_button_click_12_listener", "BrowserPreviewComponent_Template_textarea_ngModelChange_15_listener", "BrowserPreviewComponent_div_16_Template", "BrowserPreviewComponent_div_17_Template"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\browser-preview\\browser-preview.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\browser-preview\\browser-preview.component.html"], "sourcesContent": ["import { Component, OnInit, Input, ViewChild, ElementRef, OnChanges, SimpleChanges, Output, EventEmitter, AfterViewInit, Renderer2 } from '@angular/core';\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\nimport { environment } from '../../../environments/environment';\nimport { HttpClient } from '@angular/common/http';\n\n@Component({\n  selector: 'app-browser-preview',\n  templateUrl: './browser-preview.component.html',\n  styleUrls: ['./browser-preview.component.scss']\n})\nexport class BrowserPreviewComponent implements OnInit, OnChanges, AfterViewInit {\n  @Input() projectName: string = '';\n  @Input() url: string = '';\n  @Input() codeServerPort: number = 8081;  // New input for code server port\n  @ViewChild('iframe') iframe!: ElementRef<HTMLIFrameElement>;\n  @Output() expandChange = new EventEmitter<boolean>();\n\n  safeUrl!: SafeResourceUrl;\n  loading: boolean = true;\n  testResult: any = null;\n\n  // Only local SearxNG instance for embedded search\n  searchEngines = [\n    { name: 'Local SearxNG', url: 'http://localhost:8888' }\n  ];\n  selectedEngine = this.searchEngines[0];\n  engineSafeUrl: SafeResourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n\n  isBrowserMode = false;\n  engineUrl: string = `http://localhost:${this.codeServerPort}`;  // Use dynamic port\n  searchLogs: Array<{timestamp: Date, query: string, results: any}> = [];\n\n  scriptText: string = `// Basic test: Open localhost:4200 and check for a button\\nawait page.goto('http://localhost:4200');\\nconst button = await page.$('button');\\nif (button) {\\n  console.log('Button found!');\\n  return 'Button found!';\\n} else {\\n  console.log('Button not found!');\\n  return 'Button not found!';\\n}\\n`;\n  runner: string = 'puppeteer';\n\n  constructor(\n    private sanitizer: DomSanitizer,\n    private renderer: Renderer2,\n    private http: HttpClient\n  ) {}\n\n  ngOnInit(): void {\n    console.log('[BrowserPreviewComponent] ngOnInit called');\n    this.updateUrl();\n    this.engineUrl = `http://localhost:${this.codeServerPort}`;  // Update engineUrl with current port\n    this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.engineUrl);\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    console.log('[BrowserPreviewComponent] ngOnChanges called with:', changes);\n\n    if (changes['url']) {\n      console.log('[BrowserPreviewComponent] URL input changed:', changes['url'].currentValue);\n    }\n\n    if (changes['codeServerPort']) {\n      console.log('[BrowserPreviewComponent] Code server port changed:', changes['codeServerPort'].currentValue);\n      this.engineUrl = `http://localhost:${this.codeServerPort}`;\n      this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.engineUrl);\n    }\n\n    this.updateUrl();\n  }\n\n  ngAfterViewInit(): void {\n    // Set up message listener for iframe communication\n    window.addEventListener('message', this.handleIframeMessage.bind(this));\n  }\n\n  handleIframeMessage(event: MessageEvent): void {\n    // Check if the message is from our iframe (SearXNG)\n    if (event.source === this.iframe?.nativeElement.contentWindow) {\n      try {\n        const data = event.data;\n        if (data && data.type === 'searxng') {\n          console.log('SearXNG interaction:', data);\n          \n          // Log search queries\n          if (data.action === 'search') {\n            this.logSearch(data.query, data.results);\n          }\n        }\n      } catch (error) {\n        console.error('Error processing iframe message:', error);\n      }\n    }\n  }\n\n  logSearch(query: string, results: any): void {\n    this.searchLogs.push({\n      timestamp: new Date(),\n      query,\n      results\n    });\n    console.log('Search log updated:', this.searchLogs);\n  }\n\n  updateUrl(): void {\n    console.log('[BrowserPreviewComponent] updateUrl called with input URL:', this.url);\n\n    if (this.url) {\n      this.loading = true;\n      this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.url);\n      console.log('[BrowserPreviewComponent] Safe URL created and loading set to true:', this.safeUrl);\n    } else {\n      console.warn('[BrowserPreviewComponent] No URL provided. Using default search engine.');\n      this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n    }\n  }\n\n  onIframeLoad(): void {\n    console.log('[BrowserPreviewComponent] onIframeLoad triggered — iframe finished loading.');\n    this.loading = false;\n    \n    // Inject message listener into SearXNG iframe\n    if (this.iframe && this.isBrowserMode) {\n      try {\n        const iframeWindow = this.iframe.nativeElement.contentWindow;\n        \n        // Only attempt to inject script if we have access to the iframe (same-origin)\n        if (iframeWindow) {\n          this.injectLoggerScript(iframeWindow);\n        }\n      } catch (e) {\n        console.warn('Could not inject script into iframe due to same-origin policy. This is expected for cross-origin iframes.', e);\n      }\n    }\n  }\n\n  injectLoggerScript(iframeWindow: Window): void {\n    try {\n      const script = this.renderer.createElement('script');\n      const scriptContent = `\n        // SearXNG interaction logger\n        (function() {\n          const originalFetch = window.fetch;\n          window.fetch = function(...args) {\n            const url = args[0].url || args[0];\n            \n            // Check if this is a search request\n            if (url.includes('/search')) {\n              const searchParams = new URL(url, window.location.origin).searchParams;\n              const query = searchParams.get('q');\n              \n              // Send the query to the parent window\n              if (query) {\n                return originalFetch.apply(this, args).then(response => {\n                  // Clone the response so we can read and use it\n                  const responseClone = response.clone();\n                  responseClone.json().then(results => {\n                    window.parent.postMessage({\n                      type: 'searxng',\n                      action: 'search',\n                      query: query,\n                      results: results\n                    }, '*');\n                  }).catch(e => console.error('Error parsing SearXNG response:', e));\n                  \n                  return response;\n                });\n              }\n            }\n            \n            return originalFetch.apply(this, args);\n          };\n          \n          console.log('SearXNG logger injected');\n        })();\n      `;\n      \n      this.renderer.appendChild(script, this.renderer.createText(scriptContent));\n      \n      // Try to append to document if possible\n      try {\n        const iframeDocument = iframeWindow.document;\n        this.renderer.appendChild(iframeDocument.head || iframeDocument.body, script);\n        console.log('Successfully injected SearXNG logger script');\n      } catch (e) {\n        console.warn('Could not inject script into iframe document:', e);\n      }\n    } catch (e) {\n      console.error('Error creating logger script:', e);\n    }\n  }\n\n  refresh(): void {\n    console.log('[BrowserPreviewComponent] refresh called');\n\n    if (this.iframe && this.iframe.nativeElement) {\n      this.loading = true;\n      const currentSrc = this.iframe.nativeElement.src;\n      this.iframe.nativeElement.src = currentSrc;\n\n      console.log('[BrowserPreviewComponent] Iframe refreshed with current src:', currentSrc);\n    } else {\n      console.warn('[BrowserPreviewComponent] Iframe reference not available');\n    }\n  }\n\n  onEngineChange(): void {\n    this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n  }\n\n  toggleMode(): void {\n    this.isBrowserMode = !this.isBrowserMode;\n    this.loading = true;\n    if (this.isBrowserMode) {\n      this.url = '';\n      this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n    }\n  }\n\n  runTest(): void {\n    this.loading = true;\n    this.testResult = null;\n    this.http.get<any>('http://localhost:4000/run-tests').subscribe({\n      next: (res) => {\n        this.testResult = res;\n        this.loading = false;\n      },\n      error: (err) => {\n        this.testResult = { result: 'Error running test: ' + (err?.message || err) };\n        this.loading = false;\n      }\n    });\n  }\n\n  runDynamicTest(): void {\n    this.loading = true;\n    this.testResult = null;\n    this.http.post<any>('http://localhost:4000/run-script', { script: this.scriptText, runner: this.runner }).subscribe({\n      next: (res) => {\n        this.testResult = res;\n        this.loading = false;\n      },\n      error: (err) => {\n        this.testResult = { result: 'Error running script: ' + (err?.message || err), logs: err?.error?.logs || [] };\n        this.loading = false;\n      }\n    });\n  }\n}\n", "<div class=\"browser-container\">\n  <div class=\"browser-header\">\n    <h3>Test Results <span class=\"engine-name\">(AI Automated)</span></h3>\n    <div class=\"browser-actions\">\n      <select [(ngModel)]=\"runner\" style=\"margin-right:8px;\">\n        <option value=\"puppeteer\">Puppeteer</option>\n        <option value=\"playwright\">Playwright</option>\n      </select>\n      <button class=\"icon-btn\" (click)=\"runDynamicTest()\" [disabled]=\"loading\" title=\"Run Dynamic Test\">\n        ▶️ Run Test\n      </button>\n    </div>\n  </div>\n  <div class=\"browser-content card\">\n    <textarea [(ngModel)]=\"scriptText\" rows=\"8\" style=\"width:100%; font-family:monospace; margin-bottom:8px;\" placeholder=\"Enter Puppeteer/Playwright script here\"></textarea>\n    <div *ngIf=\"loading\" class=\"loading-overlay\">\n      <div class=\"spinner\"></div>\n      <p>Running test...</p>\n    </div>\n    <div *ngIf=\"!loading\">\n      <div *ngIf=\"testResult\">\n        <p><strong>Result:</strong> {{ testResult.result }}</p>\n        <div *ngIf=\"testResult.logs && testResult.logs.length\">\n          <strong>Logs:</strong>\n          <ul>\n            <li *ngFor=\"let log of testResult.logs\">{{ log }}</li>\n          </ul>\n        </div>\n      </div>\n      <div *ngIf=\"testResult?.screenshot\">\n        <img [src]=\"testResult.screenshot\" alt=\"Test Screenshot\" style=\"max-width:100%; border-radius:8px; box-shadow:0 2px 8px #0002;\" />\n      </div>\n      <div *ngIf=\"!testResult\">\n        <p>No test run yet. Edit the script and click \"Run Test\" to start.</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAA4FA,YAAY,QAAkC,eAAe;;;;;;;;;ICerJC,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,cAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAQhBJ,EAAA,CAAAC,cAAA,SAAwC;IAAAD,EAAA,CAAAG,MAAA,GAAS;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAAdJ,EAAA,CAAAK,SAAA,GAAS;IAATL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAS;;;;;IAHrDP,EAAA,CAAAC,cAAA,UAAuD;IAC7CD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACtBJ,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAQ,UAAA,IAAAC,wDAAA,iBAAsD;IACxDT,EAAA,CAAAI,YAAA,EAAK;;;;IADiBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAU,UAAA,YAAAC,MAAA,CAAAC,UAAA,CAAAC,IAAA,CAAkB;;;;;IAL5Cb,EAAA,CAAAC,cAAA,UAAwB;IACXD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,GAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACvDJ,EAAA,CAAAQ,UAAA,IAAAM,mDAAA,kBAKM;IACRd,EAAA,CAAAI,YAAA,EAAM;;;;IAPwBJ,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAe,kBAAA,MAAAC,MAAA,CAAAJ,UAAA,CAAAK,MAAA,KAAuB;IAC7CjB,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAU,UAAA,SAAAM,MAAA,CAAAJ,UAAA,CAAAC,IAAA,IAAAG,MAAA,CAAAJ,UAAA,CAAAC,IAAA,CAAAK,MAAA,CAA+C;;;;;IAOvDlB,EAAA,CAAAC,cAAA,UAAoC;IAClCD,EAAA,CAAAE,SAAA,cAAkI;IACpIF,EAAA,CAAAI,YAAA,EAAM;;;;IADCJ,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAU,UAAA,QAAAS,MAAA,CAAAP,UAAA,CAAAQ,UAAA,EAAApB,EAAA,CAAAqB,aAAA,CAA6B;;;;;IAEpCrB,EAAA,CAAAC,cAAA,UAAyB;IACpBD,EAAA,CAAAG,MAAA,wEAA+D;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAd1EJ,EAAA,CAAAC,cAAA,UAAsB;IACpBD,EAAA,CAAAQ,UAAA,IAAAc,6CAAA,kBAQM;IACNtB,EAAA,CAAAQ,UAAA,IAAAe,6CAAA,kBAEM;IACNvB,EAAA,CAAAQ,UAAA,IAAAgB,6CAAA,kBAEM;IACRxB,EAAA,CAAAI,YAAA,EAAM;;;;IAfEJ,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAU,UAAA,SAAAe,MAAA,CAAAb,UAAA,CAAgB;IAShBZ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAU,UAAA,SAAAe,MAAA,CAAAb,UAAA,kBAAAa,MAAA,CAAAb,UAAA,CAAAQ,UAAA,CAA4B;IAG5BpB,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAU,UAAA,UAAAe,MAAA,CAAAb,UAAA,CAAiB;;;ADtB7B,OAAM,MAAOc,uBAAuB;EAyBlCC,YACUC,SAAuB,EACvBC,QAAmB,EACnBC,IAAgB;IAFhB,KAAAF,SAAS,GAATA,SAAS;IACT,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,IAAI,GAAJA,IAAI;IA3BL,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,GAAG,GAAW,EAAE;IAChB,KAAAC,cAAc,GAAW,IAAI,CAAC,CAAE;IAE/B,KAAAC,YAAY,GAAG,IAAInC,YAAY,EAAW;IAGpD,KAAAoC,OAAO,GAAY,IAAI;IACvB,KAAAvB,UAAU,GAAQ,IAAI;IAEtB;IACA,KAAAwB,aAAa,GAAG,CACd;MAAEC,IAAI,EAAE,eAAe;MAAEL,GAAG,EAAE;IAAuB,CAAE,CACxD;IACD,KAAAM,cAAc,GAAG,IAAI,CAACF,aAAa,CAAC,CAAC,CAAC;IACtC,KAAAG,aAAa,GAAoB,IAAI,CAACX,SAAS,CAACY,8BAA8B,CAAC,IAAI,CAACF,cAAc,CAACN,GAAG,CAAC;IAEvG,KAAAS,aAAa,GAAG,KAAK;IACrB,KAAAC,SAAS,GAAW,oBAAoB,IAAI,CAACT,cAAc,EAAE,CAAC,CAAE;IAChE,KAAAU,UAAU,GAA0D,EAAE;IAEtE,KAAAC,UAAU,GAAW,4SAA4S;IACjU,KAAAC,MAAM,GAAW,WAAW;EAMzB;EAEHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxD,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACP,SAAS,GAAG,oBAAoB,IAAI,CAACT,cAAc,EAAE,CAAC,CAAE;IAC7D,IAAI,CAACM,aAAa,GAAG,IAAI,CAACX,SAAS,CAACY,8BAA8B,CAAC,IAAI,CAACE,SAAS,CAAC;EACpF;EAEAQ,WAAWA,CAACC,OAAsB;IAChCJ,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEG,OAAO,CAAC;IAE1E,IAAIA,OAAO,CAAC,KAAK,CAAC,EAAE;MAClBJ,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEG,OAAO,CAAC,KAAK,CAAC,CAACC,YAAY,CAAC;;IAG1F,IAAID,OAAO,CAAC,gBAAgB,CAAC,EAAE;MAC7BJ,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEG,OAAO,CAAC,gBAAgB,CAAC,CAACC,YAAY,CAAC;MAC1G,IAAI,CAACV,SAAS,GAAG,oBAAoB,IAAI,CAACT,cAAc,EAAE;MAC1D,IAAI,CAACM,aAAa,GAAG,IAAI,CAACX,SAAS,CAACY,8BAA8B,CAAC,IAAI,CAACE,SAAS,CAAC;;IAGpF,IAAI,CAACO,SAAS,EAAE;EAClB;EAEAI,eAAeA,CAAA;IACb;IACAC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACC,mBAAmB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACzE;EAEAD,mBAAmBA,CAACE,KAAmB;IACrC;IACA,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,CAACC,MAAM,EAAEC,aAAa,CAACC,aAAa,EAAE;MAC7D,IAAI;QACF,MAAMC,IAAI,GAAGL,KAAK,CAACK,IAAI;QACvB,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;UACnCjB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEe,IAAI,CAAC;UAEzC;UACA,IAAIA,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5B,IAAI,CAACC,SAAS,CAACH,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAACK,OAAO,CAAC;;;OAG7C,CAAC,OAAOC,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;;EAG9D;EAEAH,SAASA,CAACC,KAAa,EAAEC,OAAY;IACnC,IAAI,CAACzB,UAAU,CAAC2B,IAAI,CAAC;MACnBC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBL,KAAK;MACLC;KACD,CAAC;IACFrB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACL,UAAU,CAAC;EACrD;EAEAM,SAASA,CAAA;IACPF,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE,IAAI,CAAChB,GAAG,CAAC;IAEnF,IAAI,IAAI,CAACA,GAAG,EAAE;MACZ,IAAI,CAACG,OAAO,GAAG,IAAI;MACnB,IAAI,CAACsC,OAAO,GAAG,IAAI,CAAC7C,SAAS,CAACY,8BAA8B,CAAC,IAAI,CAACR,GAAG,CAAC;MACtEe,OAAO,CAACC,GAAG,CAAC,qEAAqE,EAAE,IAAI,CAACyB,OAAO,CAAC;KACjG,MAAM;MACL1B,OAAO,CAAC2B,IAAI,CAAC,yEAAyE,CAAC;MACvF,IAAI,CAACnC,aAAa,GAAG,IAAI,CAACX,SAAS,CAACY,8BAA8B,CAAC,IAAI,CAACF,cAAc,CAACN,GAAG,CAAC;;EAE/F;EAEA2C,YAAYA,CAAA;IACV5B,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;IAC1F,IAAI,CAACb,OAAO,GAAG,KAAK;IAEpB;IACA,IAAI,IAAI,CAACyB,MAAM,IAAI,IAAI,CAACnB,aAAa,EAAE;MACrC,IAAI;QACF,MAAMmC,YAAY,GAAG,IAAI,CAAChB,MAAM,CAACC,aAAa,CAACC,aAAa;QAE5D;QACA,IAAIc,YAAY,EAAE;UAChB,IAAI,CAACC,kBAAkB,CAACD,YAAY,CAAC;;OAExC,CAAC,OAAOE,CAAC,EAAE;QACV/B,OAAO,CAAC2B,IAAI,CAAC,2GAA2G,EAAEI,CAAC,CAAC;;;EAGlI;EAEAD,kBAAkBA,CAACD,YAAoB;IACrC,IAAI;MACF,MAAMG,MAAM,GAAG,IAAI,CAAClD,QAAQ,CAACmD,aAAa,CAAC,QAAQ,CAAC;MACpD,MAAMC,aAAa,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCrB;MAED,IAAI,CAACpD,QAAQ,CAACqD,WAAW,CAACH,MAAM,EAAE,IAAI,CAAClD,QAAQ,CAACsD,UAAU,CAACF,aAAa,CAAC,CAAC;MAE1E;MACA,IAAI;QACF,MAAMG,cAAc,GAAGR,YAAY,CAACS,QAAQ;QAC5C,IAAI,CAACxD,QAAQ,CAACqD,WAAW,CAACE,cAAc,CAACE,IAAI,IAAIF,cAAc,CAACG,IAAI,EAAER,MAAM,CAAC;QAC7EhC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;OAC3D,CAAC,OAAO8B,CAAC,EAAE;QACV/B,OAAO,CAAC2B,IAAI,CAAC,+CAA+C,EAAEI,CAAC,CAAC;;KAEnE,CAAC,OAAOA,CAAC,EAAE;MACV/B,OAAO,CAACsB,KAAK,CAAC,+BAA+B,EAAES,CAAC,CAAC;;EAErD;EAEAU,OAAOA,CAAA;IACLzC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IAEvD,IAAI,IAAI,CAACY,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,aAAa,EAAE;MAC5C,IAAI,CAAC1B,OAAO,GAAG,IAAI;MACnB,MAAMsD,UAAU,GAAG,IAAI,CAAC7B,MAAM,CAACC,aAAa,CAAC6B,GAAG;MAChD,IAAI,CAAC9B,MAAM,CAACC,aAAa,CAAC6B,GAAG,GAAGD,UAAU;MAE1C1C,OAAO,CAACC,GAAG,CAAC,8DAA8D,EAAEyC,UAAU,CAAC;KACxF,MAAM;MACL1C,OAAO,CAAC2B,IAAI,CAAC,0DAA0D,CAAC;;EAE5E;EAEAiB,cAAcA,CAAA;IACZ,IAAI,CAACpD,aAAa,GAAG,IAAI,CAACX,SAAS,CAACY,8BAA8B,CAAC,IAAI,CAACF,cAAc,CAACN,GAAG,CAAC;EAC7F;EAEA4D,UAAUA,CAAA;IACR,IAAI,CAACnD,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAACN,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAACM,aAAa,EAAE;MACtB,IAAI,CAACT,GAAG,GAAG,EAAE;MACb,IAAI,CAACO,aAAa,GAAG,IAAI,CAACX,SAAS,CAACY,8BAA8B,CAAC,IAAI,CAACF,cAAc,CAACN,GAAG,CAAC;;EAE/F;EAEA6D,OAAOA,CAAA;IACL,IAAI,CAAC1D,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACkB,IAAI,CAACgE,GAAG,CAAM,iCAAiC,CAAC,CAACC,SAAS,CAAC;MAC9DC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACrF,UAAU,GAAGqF,GAAG;QACrB,IAAI,CAAC9D,OAAO,GAAG,KAAK;MACtB,CAAC;MACDkC,KAAK,EAAG6B,GAAG,IAAI;QACb,IAAI,CAACtF,UAAU,GAAG;UAAEK,MAAM,EAAE,sBAAsB,IAAIiF,GAAG,EAAEC,OAAO,IAAID,GAAG;QAAC,CAAE;QAC5E,IAAI,CAAC/D,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAiE,cAAcA,CAAA;IACZ,IAAI,CAACjE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACkB,IAAI,CAACuE,IAAI,CAAM,kCAAkC,EAAE;MAAEtB,MAAM,EAAE,IAAI,CAACnC,UAAU;MAAEC,MAAM,EAAE,IAAI,CAACA;IAAM,CAAE,CAAC,CAACkD,SAAS,CAAC;MAClHC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACrF,UAAU,GAAGqF,GAAG;QACrB,IAAI,CAAC9D,OAAO,GAAG,KAAK;MACtB,CAAC;MACDkC,KAAK,EAAG6B,GAAG,IAAI;QACb,IAAI,CAACtF,UAAU,GAAG;UAAEK,MAAM,EAAE,wBAAwB,IAAIiF,GAAG,EAAEC,OAAO,IAAID,GAAG,CAAC;UAAErF,IAAI,EAAEqF,GAAG,EAAE7B,KAAK,EAAExD,IAAI,IAAI;QAAE,CAAE;QAC5G,IAAI,CAACsB,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;;;uBAtOWT,uBAAuB,EAAA1B,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAxG,EAAA,CAAAsG,iBAAA,CAAAtG,EAAA,CAAAyG,SAAA,GAAAzG,EAAA,CAAAsG,iBAAA,CAAAI,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAvBjF,uBAAuB;MAAAkF,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;UCVpC/G,EAAA,CAAAC,cAAA,aAA+B;UAEvBD,EAAA,CAAAG,MAAA,oBAAa;UAAAH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAG,MAAA,qBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAChEJ,EAAA,CAAAC,cAAA,aAA6B;UACnBD,EAAA,CAAAiH,UAAA,2BAAAC,iEAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAnE,MAAA,GAAAsE,MAAA;UAAA,EAAoB;UAC1BnH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAG,MAAA,gBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAC5CJ,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAEhDJ,EAAA,CAAAC,cAAA,iBAAkG;UAAzED,EAAA,CAAAiH,UAAA,mBAAAG,0DAAA;YAAA,OAASJ,GAAA,CAAAZ,cAAA,EAAgB;UAAA,EAAC;UACjDpG,EAAA,CAAAG,MAAA,+BACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAGbJ,EAAA,CAAAC,cAAA,cAAkC;UACtBD,EAAA,CAAAiH,UAAA,2BAAAI,oEAAAF,MAAA;YAAA,OAAAH,GAAA,CAAApE,UAAA,GAAAuE,MAAA;UAAA,EAAwB;UAA6HnH,EAAA,CAAAI,YAAA,EAAW;UAC1KJ,EAAA,CAAAQ,UAAA,KAAA8G,uCAAA,kBAGM;UACNtH,EAAA,CAAAQ,UAAA,KAAA+G,uCAAA,kBAgBM;UACRvH,EAAA,CAAAI,YAAA,EAAM;;;UAhCMJ,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAU,UAAA,YAAAsG,GAAA,CAAAnE,MAAA,CAAoB;UAIwB7C,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAU,UAAA,aAAAsG,GAAA,CAAA7E,OAAA,CAAoB;UAMhEnC,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAAU,UAAA,YAAAsG,GAAA,CAAApE,UAAA,CAAwB;UAC5B5C,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAU,UAAA,SAAAsG,GAAA,CAAA7E,OAAA,CAAa;UAIbnC,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAU,UAAA,UAAAsG,GAAA,CAAA7E,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}