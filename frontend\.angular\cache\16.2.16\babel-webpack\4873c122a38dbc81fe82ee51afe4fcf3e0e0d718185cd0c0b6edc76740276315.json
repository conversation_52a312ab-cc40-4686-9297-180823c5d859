{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipWhile(predicate) {\n  return operate((source, subscriber) => {\n    let taking = false;\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => (taking || (taking = !predicate(value, index++))) && subscriber.next(value)));\n  });\n}\n//# sourceMappingURL=skipWhile.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}