{"ast": null, "code": "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n  const xdomain = opts.xdomain;\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n    } catch (e) {}\n  }\n}\nexport function createCookieJar() {}", "map": {"version": 3, "names": ["hasCORS", "globalThisShim", "globalThis", "XHR", "opts", "xdomain", "XMLHttpRequest", "e", "concat", "join", "createCookieJar"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js"], "sourcesContent": ["// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\nexport function createCookieJar() { }\n"], "mappings": "AAAA;AACA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,cAAc,IAAIC,UAAU,QAAQ,kBAAkB;AAC/D,OAAO,SAASC,GAAGA,CAACC,IAAI,EAAE;EACtB,MAAMC,OAAO,GAAGD,IAAI,CAACC,OAAO;EAC5B;EACA,IAAI;IACA,IAAI,WAAW,KAAK,OAAOC,cAAc,KAAK,CAACD,OAAO,IAAIL,OAAO,CAAC,EAAE;MAChE,OAAO,IAAIM,cAAc,CAAC,CAAC;IAC/B;EACJ,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;EACZ,IAAI,CAACF,OAAO,EAAE;IACV,IAAI;MACA,OAAO,IAAIH,UAAU,CAAC,CAAC,QAAQ,CAAC,CAACM,MAAM,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACrF,CAAC,CACD,OAAOF,CAAC,EAAE,CAAE;EAChB;AACJ;AACA,OAAO,SAASG,eAAeA,CAAA,EAAG,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}