{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // Return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = cssWithMappingToString(item);\n      if (item[2]) {\n        return \"@media \".concat(item[2], \" {\").concat(content, \"}\");\n      }\n      return content;\n    }).join(\"\");\n  };\n  return list;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}