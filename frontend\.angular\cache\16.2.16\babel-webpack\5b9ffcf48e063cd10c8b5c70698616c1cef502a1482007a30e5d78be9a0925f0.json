{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Component, Inject, Input, ViewChild, Output, forwardRef, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { fromEvent } from 'rxjs';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"editorContainer\"];\nconst _c1 = \"[_nghost-%COMP%]{display:block;height:200px}.editor-container[_ngcontent-%COMP%]{width:100%;height:98%}\";\nconst NGX_MONACO_EDITOR_CONFIG = new InjectionToken('NGX_MONACO_EDITOR_CONFIG');\nlet loadedMonaco = false;\nlet loadPromise;\nclass BaseEditor {\n  set insideNg(insideNg) {\n    this._insideNg = insideNg;\n    if (this._editor) {\n      this._editor.dispose();\n      this.initMonaco(this._options, this.insideNg);\n    }\n  }\n  get insideNg() {\n    return this._insideNg;\n  }\n  constructor(config) {\n    this.config = config;\n    this.onInit = new EventEmitter();\n    this._insideNg = false;\n  }\n  ngAfterViewInit() {\n    if (loadedMonaco) {\n      // Wait until monaco editor is available\n      loadPromise.then(() => {\n        this.initMonaco(this._options, this.insideNg);\n      });\n    } else {\n      loadedMonaco = true;\n      loadPromise = new Promise(resolve => {\n        const baseUrl = this.config.baseUrl || \"./assets\";\n        if (typeof window.monaco === 'object') {\n          this.initMonaco(this._options, this.insideNg);\n          resolve();\n          return;\n        }\n        const onGotAmdLoader = require => {\n          let usedRequire = require || window.require;\n          let requireConfig = {\n            paths: {\n              vs: `${baseUrl}/monaco/min/vs`\n            }\n          };\n          Object.assign(requireConfig, this.config.requireConfig || {});\n          // Load monaco\n          usedRequire.config(requireConfig);\n          usedRequire([`vs/editor/editor.main`], () => {\n            if (typeof this.config.onMonacoLoad === 'function') {\n              this.config.onMonacoLoad();\n            }\n            this.initMonaco(this._options, this.insideNg);\n            resolve();\n          });\n        };\n        if (this.config.monacoRequire) {\n          onGotAmdLoader(this.config.monacoRequire);\n          // Load AMD loader if necessary\n        } else if (!window.require) {\n          const loaderScript = document.createElement('script');\n          loaderScript.type = 'text/javascript';\n          loaderScript.src = `${baseUrl}/monaco/min/vs/loader.js`;\n          loaderScript.addEventListener('load', () => {\n            onGotAmdLoader();\n          });\n          document.body.appendChild(loaderScript);\n          // Load AMD loader without over-riding node's require\n        } else if (!window.require.config) {\n          var src = `${baseUrl}/monaco/min/vs/loader.js`;\n          var loaderRequest = new XMLHttpRequest();\n          loaderRequest.addEventListener(\"load\", () => {\n            let scriptElem = document.createElement('script');\n            scriptElem.type = 'text/javascript';\n            scriptElem.text = [\n            // Monaco uses a custom amd loader that over-rides node's require.\n            // Keep a reference to node's require so we can restore it after executing the amd loader file.\n            'var nodeRequire = require;', loaderRequest.responseText.replace('\"use strict\";', ''),\n            // Save Monaco's amd require and restore Node's require\n            'var monacoAmdRequire = require;', 'require = nodeRequire;', 'require.nodeRequire = require;'].join('\\n');\n            document.body.appendChild(scriptElem);\n            onGotAmdLoader(window.monacoAmdRequire);\n          });\n          loaderRequest.open(\"GET\", src);\n          loaderRequest.send();\n        } else {\n          onGotAmdLoader();\n        }\n      });\n    }\n  }\n  ngOnDestroy() {\n    if (this._windowResizeSubscription) {\n      this._windowResizeSubscription.unsubscribe();\n    }\n    if (this._editor) {\n      this._editor.dispose();\n      this._editor = undefined;\n    }\n  }\n  static {\n    this.ɵfac = function BaseEditor_Factory(t) {\n      return new (t || BaseEditor)(i0.ɵɵdirectiveInject(NGX_MONACO_EDITOR_CONFIG));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: BaseEditor,\n      selectors: [[\"ng-component\"]],\n      viewQuery: function BaseEditor_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._editorContainer = _t.first);\n        }\n      },\n      inputs: {\n        insideNg: \"insideNg\"\n      },\n      outputs: {\n        onInit: \"onInit\"\n      },\n      decls: 0,\n      vars: 0,\n      template: function BaseEditor_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseEditor, [{\n    type: Component,\n    args: [{\n      template: ''\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NGX_MONACO_EDITOR_CONFIG]\n      }]\n    }];\n  }, {\n    insideNg: [{\n      type: Input,\n      args: ['insideNg']\n    }],\n    _editorContainer: [{\n      type: ViewChild,\n      args: ['editorContainer', {\n        static: true\n      }]\n    }],\n    onInit: [{\n      type: Output\n    }]\n  });\n})();\nclass EditorComponent extends BaseEditor {\n  set options(options) {\n    this._options = Object.assign({}, this.config.defaultOptions, options);\n    if (this._editor) {\n      this._editor.dispose();\n      this.initMonaco(options, this.insideNg);\n    }\n  }\n  get options() {\n    return this._options;\n  }\n  set model(model) {\n    this.options.model = model;\n    if (this._editor) {\n      this._editor.dispose();\n      this.initMonaco(this.options, this.insideNg);\n    }\n  }\n  constructor(zone, editorConfig) {\n    super(editorConfig);\n    this.zone = zone;\n    this.editorConfig = editorConfig;\n    this._value = '';\n    this.propagateChange = _ => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this._value = value || '';\n    // Fix for value change while dispose in process.\n    setTimeout(() => {\n      if (this._editor && !this.options.model) {\n        this._editor.setValue(this._value);\n      }\n    });\n  }\n  registerOnChange(fn) {\n    this.propagateChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  initMonaco(options, insideNg) {\n    const hasModel = !!options.model;\n    if (hasModel) {\n      const model = monaco.editor.getModel(options.model.uri || '');\n      if (model) {\n        options.model = model;\n        options.model.setValue(this._value);\n      } else {\n        options.model = monaco.editor.createModel(options.model.value, options.model.language, options.model.uri);\n      }\n    }\n    if (insideNg) {\n      this._editor = monaco.editor.create(this._editorContainer.nativeElement, options);\n    } else {\n      this.zone.runOutsideAngular(() => {\n        this._editor = monaco.editor.create(this._editorContainer.nativeElement, options);\n      });\n    }\n    if (!hasModel) {\n      this._editor.setValue(this._value);\n    }\n    this._editor.onDidChangeModelContent(e => {\n      const value = this._editor.getValue();\n      // value is not propagated to parent when executing outside zone.\n      this.zone.run(() => {\n        this.propagateChange(value);\n        this._value = value;\n      });\n    });\n    this._editor.onDidBlurEditorWidget(() => {\n      this.onTouched();\n    });\n    // refresh layout on resize event.\n    if (this._windowResizeSubscription) {\n      this._windowResizeSubscription.unsubscribe();\n    }\n    this._windowResizeSubscription = fromEvent(window, 'resize').subscribe(() => this._editor.layout());\n    this.onInit.emit(this._editor);\n  }\n  static {\n    this.ɵfac = function EditorComponent_Factory(t) {\n      return new (t || EditorComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(NGX_MONACO_EDITOR_CONFIG));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: EditorComponent,\n      selectors: [[\"ngx-monaco-editor\"]],\n      inputs: {\n        options: \"options\",\n        model: \"model\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => EditorComponent),\n        multi: true\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"editor-container\"], [\"editorContainer\", \"\"]],\n      template: function EditorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0, 1);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{display:block;height:200px}.editor-container[_ngcontent-%COMP%]{width:100%;height:98%}\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EditorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-monaco-editor',\n      template: '<div class=\"editor-container\" #editorContainer></div>',\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => EditorComponent),\n        multi: true\n      }],\n      styles: [\":host{display:block;height:200px}.editor-container{width:100%;height:98%}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NGX_MONACO_EDITOR_CONFIG]\n      }]\n    }];\n  }, {\n    options: [{\n      type: Input,\n      args: ['options']\n    }],\n    model: [{\n      type: Input,\n      args: ['model']\n    }]\n  });\n})();\nclass DiffEditorComponent extends BaseEditor {\n  set options(options) {\n    this._options = Object.assign({}, this.config.defaultOptions, options);\n    if (this._editor) {\n      this._editor.dispose();\n      this.initMonaco(options, this.insideNg);\n    }\n  }\n  get options() {\n    return this._options;\n  }\n  set originalModel(model) {\n    this._originalModel = model;\n    if (this._editor) {\n      this._editor.dispose();\n      this.initMonaco(this.options, this.insideNg);\n    }\n  }\n  set modifiedModel(model) {\n    this._modifiedModel = model;\n    if (this._editor) {\n      this._editor.dispose();\n      this.initMonaco(this.options, this.insideNg);\n    }\n  }\n  constructor(zone, editorConfig) {\n    super(editorConfig);\n    this.zone = zone;\n    this.editorConfig = editorConfig;\n  }\n  initMonaco(options, insideNg) {\n    if (!this._originalModel || !this._modifiedModel) {\n      throw new Error('originalModel or modifiedModel not found for ngx-monaco-diff-editor');\n    }\n    this._originalModel.language = this._originalModel.language || options.language;\n    this._modifiedModel.language = this._modifiedModel.language || options.language;\n    let originalModel = monaco.editor.createModel(this._originalModel.code, this._originalModel.language);\n    let modifiedModel = monaco.editor.createModel(this._modifiedModel.code, this._modifiedModel.language);\n    this._editorContainer.nativeElement.innerHTML = '';\n    const theme = options.theme;\n    if (insideNg) {\n      this._editor = monaco.editor.createDiffEditor(this._editorContainer.nativeElement, options);\n    } else {\n      this.zone.runOutsideAngular(() => {\n        this._editor = monaco.editor.createDiffEditor(this._editorContainer.nativeElement, options);\n      });\n    }\n    options.theme = theme;\n    this._editor.setModel({\n      original: originalModel,\n      modified: modifiedModel\n    });\n    // refresh layout on resize event.\n    if (this._windowResizeSubscription) {\n      this._windowResizeSubscription.unsubscribe();\n    }\n    this._windowResizeSubscription = fromEvent(window, 'resize').subscribe(() => this._editor.layout());\n    this.onInit.emit(this._editor);\n  }\n  static {\n    this.ɵfac = function DiffEditorComponent_Factory(t) {\n      return new (t || DiffEditorComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(NGX_MONACO_EDITOR_CONFIG));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DiffEditorComponent,\n      selectors: [[\"ngx-monaco-diff-editor\"]],\n      inputs: {\n        options: \"options\",\n        originalModel: \"originalModel\",\n        modifiedModel: \"modifiedModel\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"editor-container\"], [\"editorContainer\", \"\"]],\n      template: function DiffEditorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0, 1);\n        }\n      },\n      styles: [_c1]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DiffEditorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-monaco-diff-editor',\n      template: '<div class=\"editor-container\" #editorContainer></div>',\n      styles: [\":host{display:block;height:200px}.editor-container{width:100%;height:98%}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NGX_MONACO_EDITOR_CONFIG]\n      }]\n    }];\n  }, {\n    options: [{\n      type: Input,\n      args: ['options']\n    }],\n    originalModel: [{\n      type: Input,\n      args: ['originalModel']\n    }],\n    modifiedModel: [{\n      type: Input,\n      args: ['modifiedModel']\n    }]\n  });\n})();\nclass MonacoEditorModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: MonacoEditorModule,\n      providers: [{\n        provide: NGX_MONACO_EDITOR_CONFIG,\n        useValue: config\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function MonacoEditorModule_Factory(t) {\n      return new (t || MonacoEditorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MonacoEditorModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MonacoEditorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [EditorComponent, DiffEditorComponent],\n      exports: [EditorComponent, DiffEditorComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DiffEditorComponent, EditorComponent, MonacoEditorModule, NGX_MONACO_EDITOR_CONFIG };", "map": {"version": 3, "names": ["i0", "InjectionToken", "EventEmitter", "Component", "Inject", "Input", "ViewChild", "Output", "forwardRef", "NgModule", "NG_VALUE_ACCESSOR", "fromEvent", "CommonModule", "_c0", "_c1", "NGX_MONACO_EDITOR_CONFIG", "loadedMonaco", "loadPromise", "BaseEditor", "insideNg", "_insideNg", "_editor", "dispose", "initMonaco", "_options", "constructor", "config", "onInit", "ngAfterViewInit", "then", "Promise", "resolve", "baseUrl", "window", "monaco", "onGotAmdLoader", "require", "usedRequire", "requireConfig", "paths", "vs", "Object", "assign", "onMonacoLoad", "monacoRequire", "loaderScript", "document", "createElement", "type", "src", "addEventListener", "body", "append<PERSON><PERSON><PERSON>", "loaderRequest", "XMLHttpRequest", "scriptElem", "text", "responseText", "replace", "join", "monacoAmdRequire", "open", "send", "ngOnDestroy", "_windowResizeSubscription", "unsubscribe", "undefined", "ɵfac", "BaseEditor_Factory", "t", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "BaseEditor_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "_editor<PERSON><PERSON><PERSON>", "first", "inputs", "outputs", "decls", "vars", "template", "BaseEditor_Template", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "decorators", "static", "EditorComponent", "options", "defaultOptions", "model", "zone", "editorConfig", "_value", "propagateChange", "_", "onTouched", "writeValue", "value", "setTimeout", "setValue", "registerOnChange", "fn", "registerOnTouched", "hasModel", "editor", "getModel", "uri", "createModel", "language", "create", "nativeElement", "runOutsideAngular", "onDidChangeModelContent", "e", "getValue", "run", "onDidBlurEditorWidget", "subscribe", "layout", "emit", "EditorComponent_Factory", "NgZone", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵInheritDefinitionFeature", "consts", "EditorComponent_Template", "ɵɵelement", "styles", "selector", "providers", "DiffEditorComponent", "originalModel", "_originalModel", "modifiedModel", "_modifiedModel", "Error", "code", "innerHTML", "theme", "createDiffEditor", "setModel", "original", "modified", "DiffEditorComponent_Factory", "DiffEditorComponent_Template", "MonacoEditorModule", "forRoot", "ngModule", "useValue", "MonacoEditorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/ngx-monaco-editor-v2/fesm2022/ngx-monaco-editor-v2.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Component, Inject, Input, ViewChild, Output, forwardRef, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { fromEvent } from 'rxjs';\nimport { CommonModule } from '@angular/common';\n\nconst NGX_MONACO_EDITOR_CONFIG = new InjectionToken('NGX_MONACO_EDITOR_CONFIG');\n\nlet loadedMonaco = false;\nlet loadPromise;\nclass BaseEditor {\n    set insideNg(insideNg) {\n        this._insideNg = insideNg;\n        if (this._editor) {\n            this._editor.dispose();\n            this.initMonaco(this._options, this.insideNg);\n        }\n    }\n    get insideNg() {\n        return this._insideNg;\n    }\n    constructor(config) {\n        this.config = config;\n        this.onInit = new EventEmitter();\n        this._insideNg = false;\n    }\n    ngAfterViewInit() {\n        if (loadedMonaco) {\n            // Wait until monaco editor is available\n            loadPromise.then(() => {\n                this.initMonaco(this._options, this.insideNg);\n            });\n        }\n        else {\n            loadedMonaco = true;\n            loadPromise = new Promise((resolve) => {\n                const baseUrl = this.config.baseUrl || \"./assets\";\n                if (typeof (window.monaco) === 'object') {\n                    this.initMonaco(this._options, this.insideNg);\n                    resolve();\n                    return;\n                }\n                const onGotAmdLoader = (require) => {\n                    let usedRequire = require || window.require;\n                    let requireConfig = { paths: { vs: `${baseUrl}/monaco/min/vs` } };\n                    Object.assign(requireConfig, this.config.requireConfig || {});\n                    // Load monaco\n                    usedRequire.config(requireConfig);\n                    usedRequire([`vs/editor/editor.main`], () => {\n                        if (typeof this.config.onMonacoLoad === 'function') {\n                            this.config.onMonacoLoad();\n                        }\n                        this.initMonaco(this._options, this.insideNg);\n                        resolve();\n                    });\n                };\n                if (this.config.monacoRequire) {\n                    onGotAmdLoader(this.config.monacoRequire);\n                    // Load AMD loader if necessary\n                }\n                else if (!window.require) {\n                    const loaderScript = document.createElement('script');\n                    loaderScript.type = 'text/javascript';\n                    loaderScript.src = `${baseUrl}/monaco/min/vs/loader.js`;\n                    loaderScript.addEventListener('load', () => { onGotAmdLoader(); });\n                    document.body.appendChild(loaderScript);\n                    // Load AMD loader without over-riding node's require\n                }\n                else if (!window.require.config) {\n                    var src = `${baseUrl}/monaco/min/vs/loader.js`;\n                    var loaderRequest = new XMLHttpRequest();\n                    loaderRequest.addEventListener(\"load\", () => {\n                        let scriptElem = document.createElement('script');\n                        scriptElem.type = 'text/javascript';\n                        scriptElem.text = [\n                            // Monaco uses a custom amd loader that over-rides node's require.\n                            // Keep a reference to node's require so we can restore it after executing the amd loader file.\n                            'var nodeRequire = require;',\n                            loaderRequest.responseText.replace('\"use strict\";', ''),\n                            // Save Monaco's amd require and restore Node's require\n                            'var monacoAmdRequire = require;',\n                            'require = nodeRequire;',\n                            'require.nodeRequire = require;'\n                        ].join('\\n');\n                        document.body.appendChild(scriptElem);\n                        onGotAmdLoader(window.monacoAmdRequire);\n                    });\n                    loaderRequest.open(\"GET\", src);\n                    loaderRequest.send();\n                }\n                else {\n                    onGotAmdLoader();\n                }\n            });\n        }\n    }\n    ngOnDestroy() {\n        if (this._windowResizeSubscription) {\n            this._windowResizeSubscription.unsubscribe();\n        }\n        if (this._editor) {\n            this._editor.dispose();\n            this._editor = undefined;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.4\", ngImport: i0, type: BaseEditor, deps: [{ token: NGX_MONACO_EDITOR_CONFIG }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.0.4\", type: BaseEditor, selector: \"ng-component\", inputs: { insideNg: \"insideNg\" }, outputs: { onInit: \"onInit\" }, viewQueries: [{ propertyName: \"_editorContainer\", first: true, predicate: [\"editorContainer\"], descendants: true, static: true }], ngImport: i0, template: '', isInline: true }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.4\", ngImport: i0, type: BaseEditor, decorators: [{\n            type: Component,\n            args: [{\n                    template: ''\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGX_MONACO_EDITOR_CONFIG]\n                }] }]; }, propDecorators: { insideNg: [{\n                type: Input,\n                args: ['insideNg']\n            }], _editorContainer: [{\n                type: ViewChild,\n                args: ['editorContainer', { static: true }]\n            }], onInit: [{\n                type: Output\n            }] } });\n\nclass EditorComponent extends BaseEditor {\n    set options(options) {\n        this._options = Object.assign({}, this.config.defaultOptions, options);\n        if (this._editor) {\n            this._editor.dispose();\n            this.initMonaco(options, this.insideNg);\n        }\n    }\n    get options() {\n        return this._options;\n    }\n    set model(model) {\n        this.options.model = model;\n        if (this._editor) {\n            this._editor.dispose();\n            this.initMonaco(this.options, this.insideNg);\n        }\n    }\n    constructor(zone, editorConfig) {\n        super(editorConfig);\n        this.zone = zone;\n        this.editorConfig = editorConfig;\n        this._value = '';\n        this.propagateChange = (_) => { };\n        this.onTouched = () => { };\n    }\n    writeValue(value) {\n        this._value = value || '';\n        // Fix for value change while dispose in process.\n        setTimeout(() => {\n            if (this._editor && !this.options.model) {\n                this._editor.setValue(this._value);\n            }\n        });\n    }\n    registerOnChange(fn) {\n        this.propagateChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    initMonaco(options, insideNg) {\n        const hasModel = !!options.model;\n        if (hasModel) {\n            const model = monaco.editor.getModel(options.model.uri || '');\n            if (model) {\n                options.model = model;\n                options.model.setValue(this._value);\n            }\n            else {\n                options.model = monaco.editor.createModel(options.model.value, options.model.language, options.model.uri);\n            }\n        }\n        if (insideNg) {\n            this._editor = monaco.editor.create(this._editorContainer.nativeElement, options);\n        }\n        else {\n            this.zone.runOutsideAngular(() => {\n                this._editor = monaco.editor.create(this._editorContainer.nativeElement, options);\n            });\n        }\n        if (!hasModel) {\n            this._editor.setValue(this._value);\n        }\n        this._editor.onDidChangeModelContent((e) => {\n            const value = this._editor.getValue();\n            // value is not propagated to parent when executing outside zone.\n            this.zone.run(() => {\n                this.propagateChange(value);\n                this._value = value;\n            });\n        });\n        this._editor.onDidBlurEditorWidget(() => {\n            this.onTouched();\n        });\n        // refresh layout on resize event.\n        if (this._windowResizeSubscription) {\n            this._windowResizeSubscription.unsubscribe();\n        }\n        this._windowResizeSubscription = fromEvent(window, 'resize').subscribe(() => this._editor.layout());\n        this.onInit.emit(this._editor);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.4\", ngImport: i0, type: EditorComponent, deps: [{ token: i0.NgZone }, { token: NGX_MONACO_EDITOR_CONFIG }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.0.4\", type: EditorComponent, selector: \"ngx-monaco-editor\", inputs: { options: \"options\", model: \"model\" }, providers: [{\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => EditorComponent),\n                multi: true\n            }], usesInheritance: true, ngImport: i0, template: '<div class=\"editor-container\" #editorContainer></div>', isInline: true, styles: [\":host{display:block;height:200px}.editor-container{width:100%;height:98%}\\n\"] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.4\", ngImport: i0, type: EditorComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-monaco-editor', template: '<div class=\"editor-container\" #editorContainer></div>', providers: [{\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => EditorComponent),\n                            multi: true\n                        }], styles: [\":host{display:block;height:200px}.editor-container{width:100%;height:98%}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGX_MONACO_EDITOR_CONFIG]\n                }] }]; }, propDecorators: { options: [{\n                type: Input,\n                args: ['options']\n            }], model: [{\n                type: Input,\n                args: ['model']\n            }] } });\n\nclass DiffEditorComponent extends BaseEditor {\n    set options(options) {\n        this._options = Object.assign({}, this.config.defaultOptions, options);\n        if (this._editor) {\n            this._editor.dispose();\n            this.initMonaco(options, this.insideNg);\n        }\n    }\n    get options() {\n        return this._options;\n    }\n    set originalModel(model) {\n        this._originalModel = model;\n        if (this._editor) {\n            this._editor.dispose();\n            this.initMonaco(this.options, this.insideNg);\n        }\n    }\n    set modifiedModel(model) {\n        this._modifiedModel = model;\n        if (this._editor) {\n            this._editor.dispose();\n            this.initMonaco(this.options, this.insideNg);\n        }\n    }\n    constructor(zone, editorConfig) {\n        super(editorConfig);\n        this.zone = zone;\n        this.editorConfig = editorConfig;\n    }\n    initMonaco(options, insideNg) {\n        if (!this._originalModel || !this._modifiedModel) {\n            throw new Error('originalModel or modifiedModel not found for ngx-monaco-diff-editor');\n        }\n        this._originalModel.language = this._originalModel.language || options.language;\n        this._modifiedModel.language = this._modifiedModel.language || options.language;\n        let originalModel = monaco.editor.createModel(this._originalModel.code, this._originalModel.language);\n        let modifiedModel = monaco.editor.createModel(this._modifiedModel.code, this._modifiedModel.language);\n        this._editorContainer.nativeElement.innerHTML = '';\n        const theme = options.theme;\n        if (insideNg) {\n            this._editor = monaco.editor.createDiffEditor(this._editorContainer.nativeElement, options);\n        }\n        else {\n            this.zone.runOutsideAngular(() => {\n                this._editor = monaco.editor.createDiffEditor(this._editorContainer.nativeElement, options);\n            });\n        }\n        options.theme = theme;\n        this._editor.setModel({\n            original: originalModel,\n            modified: modifiedModel\n        });\n        // refresh layout on resize event.\n        if (this._windowResizeSubscription) {\n            this._windowResizeSubscription.unsubscribe();\n        }\n        this._windowResizeSubscription = fromEvent(window, 'resize').subscribe(() => this._editor.layout());\n        this.onInit.emit(this._editor);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.4\", ngImport: i0, type: DiffEditorComponent, deps: [{ token: i0.NgZone }, { token: NGX_MONACO_EDITOR_CONFIG }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.0.4\", type: DiffEditorComponent, selector: \"ngx-monaco-diff-editor\", inputs: { options: \"options\", originalModel: \"originalModel\", modifiedModel: \"modifiedModel\" }, usesInheritance: true, ngImport: i0, template: '<div class=\"editor-container\" #editorContainer></div>', isInline: true, styles: [\":host{display:block;height:200px}.editor-container{width:100%;height:98%}\\n\"] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.4\", ngImport: i0, type: DiffEditorComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-monaco-diff-editor', template: '<div class=\"editor-container\" #editorContainer></div>', styles: [\":host{display:block;height:200px}.editor-container{width:100%;height:98%}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGX_MONACO_EDITOR_CONFIG]\n                }] }]; }, propDecorators: { options: [{\n                type: Input,\n                args: ['options']\n            }], originalModel: [{\n                type: Input,\n                args: ['originalModel']\n            }], modifiedModel: [{\n                type: Input,\n                args: ['modifiedModel']\n            }] } });\n\nclass MonacoEditorModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: MonacoEditorModule,\n            providers: [\n                { provide: NGX_MONACO_EDITOR_CONFIG, useValue: config }\n            ]\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.4\", ngImport: i0, type: MonacoEditorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.4\", ngImport: i0, type: MonacoEditorModule, declarations: [EditorComponent,\n            DiffEditorComponent], imports: [CommonModule], exports: [EditorComponent,\n            DiffEditorComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.4\", ngImport: i0, type: MonacoEditorModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.4\", ngImport: i0, type: MonacoEditorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule\n                    ],\n                    declarations: [\n                        EditorComponent,\n                        DiffEditorComponent\n                    ],\n                    exports: [\n                        EditorComponent,\n                        DiffEditorComponent\n                    ]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DiffEditorComponent, EditorComponent, MonacoEditorModule, NGX_MONACO_EDITOR_CONFIG };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC/H,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,SAAS,QAAQ,MAAM;AAChC,SAASC,YAAY,QAAQ,iBAAiB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAE/C,MAAMC,wBAAwB,GAAG,IAAId,cAAc,CAAC,0BAA0B,CAAC;AAE/E,IAAIe,YAAY,GAAG,KAAK;AACxB,IAAIC,WAAW;AACf,MAAMC,UAAU,CAAC;EACb,IAAIC,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,IAAI,IAAI,CAACE,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACC,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACL,QAAQ,CAAC;IACjD;EACJ;EACA,IAAIA,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACAK,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAG,IAAIzB,YAAY,CAAC,CAAC;IAChC,IAAI,CAACkB,SAAS,GAAG,KAAK;EAC1B;EACAQ,eAAeA,CAAA,EAAG;IACd,IAAIZ,YAAY,EAAE;MACd;MACAC,WAAW,CAACY,IAAI,CAAC,MAAM;QACnB,IAAI,CAACN,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACL,QAAQ,CAAC;MACjD,CAAC,CAAC;IACN,CAAC,MACI;MACDH,YAAY,GAAG,IAAI;MACnBC,WAAW,GAAG,IAAIa,OAAO,CAAEC,OAAO,IAAK;QACnC,MAAMC,OAAO,GAAG,IAAI,CAACN,MAAM,CAACM,OAAO,IAAI,UAAU;QACjD,IAAI,OAAQC,MAAM,CAACC,MAAO,KAAK,QAAQ,EAAE;UACrC,IAAI,CAACX,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACL,QAAQ,CAAC;UAC7CY,OAAO,CAAC,CAAC;UACT;QACJ;QACA,MAAMI,cAAc,GAAIC,OAAO,IAAK;UAChC,IAAIC,WAAW,GAAGD,OAAO,IAAIH,MAAM,CAACG,OAAO;UAC3C,IAAIE,aAAa,GAAG;YAAEC,KAAK,EAAE;cAAEC,EAAE,EAAG,GAAER,OAAQ;YAAgB;UAAE,CAAC;UACjES,MAAM,CAACC,MAAM,CAACJ,aAAa,EAAE,IAAI,CAACZ,MAAM,CAACY,aAAa,IAAI,CAAC,CAAC,CAAC;UAC7D;UACAD,WAAW,CAACX,MAAM,CAACY,aAAa,CAAC;UACjCD,WAAW,CAAC,CAAE,uBAAsB,CAAC,EAAE,MAAM;YACzC,IAAI,OAAO,IAAI,CAACX,MAAM,CAACiB,YAAY,KAAK,UAAU,EAAE;cAChD,IAAI,CAACjB,MAAM,CAACiB,YAAY,CAAC,CAAC;YAC9B;YACA,IAAI,CAACpB,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACL,QAAQ,CAAC;YAC7CY,OAAO,CAAC,CAAC;UACb,CAAC,CAAC;QACN,CAAC;QACD,IAAI,IAAI,CAACL,MAAM,CAACkB,aAAa,EAAE;UAC3BT,cAAc,CAAC,IAAI,CAACT,MAAM,CAACkB,aAAa,CAAC;UACzC;QACJ,CAAC,MACI,IAAI,CAACX,MAAM,CAACG,OAAO,EAAE;UACtB,MAAMS,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UACrDF,YAAY,CAACG,IAAI,GAAG,iBAAiB;UACrCH,YAAY,CAACI,GAAG,GAAI,GAAEjB,OAAQ,0BAAyB;UACvDa,YAAY,CAACK,gBAAgB,CAAC,MAAM,EAAE,MAAM;YAAEf,cAAc,CAAC,CAAC;UAAE,CAAC,CAAC;UAClEW,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,YAAY,CAAC;UACvC;QACJ,CAAC,MACI,IAAI,CAACZ,MAAM,CAACG,OAAO,CAACV,MAAM,EAAE;UAC7B,IAAIuB,GAAG,GAAI,GAAEjB,OAAQ,0BAAyB;UAC9C,IAAIqB,aAAa,GAAG,IAAIC,cAAc,CAAC,CAAC;UACxCD,aAAa,CAACH,gBAAgB,CAAC,MAAM,EAAE,MAAM;YACzC,IAAIK,UAAU,GAAGT,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YACjDQ,UAAU,CAACP,IAAI,GAAG,iBAAiB;YACnCO,UAAU,CAACC,IAAI,GAAG;YACd;YACA;YACA,4BAA4B,EAC5BH,aAAa,CAACI,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;YACvD;YACA,iCAAiC,EACjC,wBAAwB,EACxB,gCAAgC,CACnC,CAACC,IAAI,CAAC,IAAI,CAAC;YACZb,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACG,UAAU,CAAC;YACrCpB,cAAc,CAACF,MAAM,CAAC2B,gBAAgB,CAAC;UAC3C,CAAC,CAAC;UACFP,aAAa,CAACQ,IAAI,CAAC,KAAK,EAAEZ,GAAG,CAAC;UAC9BI,aAAa,CAACS,IAAI,CAAC,CAAC;QACxB,CAAC,MACI;UACD3B,cAAc,CAAC,CAAC;QACpB;MACJ,CAAC,CAAC;IACN;EACJ;EACA4B,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACC,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACC,WAAW,CAAC,CAAC;IAChD;IACA,IAAI,IAAI,CAAC5C,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACD,OAAO,GAAG6C,SAAS;IAC5B;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFnD,UAAU,EAApBlB,EAAE,CAAAsE,iBAAA,CAAoCvD,wBAAwB;IAAA,CAA4C;EAAE;EAC5M;IAAS,IAAI,CAACwD,IAAI,kBAD8EvE,EAAE,CAAAwE,iBAAA;MAAAxB,IAAA,EACJ9B,UAAU;MAAAuD,SAAA;MAAAC,SAAA,WAAAC,iBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADR5E,EAAE,CAAA8E,WAAA,CAAAjE,GAAA;QAAA;QAAA,IAAA+D,EAAA;UAAA,IAAAG,EAAA;UAAF/E,EAAE,CAAAgF,cAAA,CAAAD,EAAA,GAAF/E,EAAE,CAAAiF,WAAA,QAAAJ,GAAA,CAAAK,gBAAA,GAAAH,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAC,MAAA;QAAAjE,QAAA;MAAA;MAAAkE,OAAA;QAAA1D,MAAA;MAAA;MAAA2D,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,oBAAAb,EAAA,EAAAC,GAAA;MAAAa,aAAA;IAAA,EACmR;EAAE;AAC3X;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG3F,EAAE,CAAA4F,iBAAA,CAGX1E,UAAU,EAAc,CAAC;IACxG8B,IAAI,EAAE7C,SAAS;IACf0F,IAAI,EAAE,CAAC;MACCL,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExC,IAAI,EAAEkB,SAAS;MAAE4B,UAAU,EAAE,CAAC;QAC9D9C,IAAI,EAAE5C,MAAM;QACZyF,IAAI,EAAE,CAAC9E,wBAAwB;MACnC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEI,QAAQ,EAAE,CAAC;MACvC6B,IAAI,EAAE3C,KAAK;MACXwF,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEX,gBAAgB,EAAE,CAAC;MACnBlC,IAAI,EAAE1C,SAAS;MACfuF,IAAI,EAAE,CAAC,iBAAiB,EAAE;QAAEE,MAAM,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEpE,MAAM,EAAE,CAAC;MACTqB,IAAI,EAAEzC;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyF,eAAe,SAAS9E,UAAU,CAAC;EACrC,IAAI+E,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACzE,QAAQ,GAAGiB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAChB,MAAM,CAACwE,cAAc,EAAED,OAAO,CAAC;IACtE,IAAI,IAAI,CAAC5E,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACC,UAAU,CAAC0E,OAAO,EAAE,IAAI,CAAC9E,QAAQ,CAAC;IAC3C;EACJ;EACA,IAAI8E,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACzE,QAAQ;EACxB;EACA,IAAI2E,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACF,OAAO,CAACE,KAAK,GAAGA,KAAK;IAC1B,IAAI,IAAI,CAAC9E,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC0E,OAAO,EAAE,IAAI,CAAC9E,QAAQ,CAAC;IAChD;EACJ;EACAM,WAAWA,CAAC2E,IAAI,EAAEC,YAAY,EAAE;IAC5B,KAAK,CAACA,YAAY,CAAC;IACnB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,eAAe,GAAIC,CAAC,IAAK,CAAE,CAAC;IACjC,IAAI,CAACC,SAAS,GAAG,MAAM,CAAE,CAAC;EAC9B;EACAC,UAAUA,CAACC,KAAK,EAAE;IACd,IAAI,CAACL,MAAM,GAAGK,KAAK,IAAI,EAAE;IACzB;IACAC,UAAU,CAAC,MAAM;MACb,IAAI,IAAI,CAACvF,OAAO,IAAI,CAAC,IAAI,CAAC4E,OAAO,CAACE,KAAK,EAAE;QACrC,IAAI,CAAC9E,OAAO,CAACwF,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC;MACtC;IACJ,CAAC,CAAC;EACN;EACAQ,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACR,eAAe,GAAGQ,EAAE;EAC7B;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACN,SAAS,GAAGM,EAAE;EACvB;EACAxF,UAAUA,CAAC0E,OAAO,EAAE9E,QAAQ,EAAE;IAC1B,MAAM8F,QAAQ,GAAG,CAAC,CAAChB,OAAO,CAACE,KAAK;IAChC,IAAIc,QAAQ,EAAE;MACV,MAAMd,KAAK,GAAGjE,MAAM,CAACgF,MAAM,CAACC,QAAQ,CAAClB,OAAO,CAACE,KAAK,CAACiB,GAAG,IAAI,EAAE,CAAC;MAC7D,IAAIjB,KAAK,EAAE;QACPF,OAAO,CAACE,KAAK,GAAGA,KAAK;QACrBF,OAAO,CAACE,KAAK,CAACU,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC;MACvC,CAAC,MACI;QACDL,OAAO,CAACE,KAAK,GAAGjE,MAAM,CAACgF,MAAM,CAACG,WAAW,CAACpB,OAAO,CAACE,KAAK,CAACQ,KAAK,EAAEV,OAAO,CAACE,KAAK,CAACmB,QAAQ,EAAErB,OAAO,CAACE,KAAK,CAACiB,GAAG,CAAC;MAC7G;IACJ;IACA,IAAIjG,QAAQ,EAAE;MACV,IAAI,CAACE,OAAO,GAAGa,MAAM,CAACgF,MAAM,CAACK,MAAM,CAAC,IAAI,CAACrC,gBAAgB,CAACsC,aAAa,EAAEvB,OAAO,CAAC;IACrF,CAAC,MACI;MACD,IAAI,CAACG,IAAI,CAACqB,iBAAiB,CAAC,MAAM;QAC9B,IAAI,CAACpG,OAAO,GAAGa,MAAM,CAACgF,MAAM,CAACK,MAAM,CAAC,IAAI,CAACrC,gBAAgB,CAACsC,aAAa,EAAEvB,OAAO,CAAC;MACrF,CAAC,CAAC;IACN;IACA,IAAI,CAACgB,QAAQ,EAAE;MACX,IAAI,CAAC5F,OAAO,CAACwF,QAAQ,CAAC,IAAI,CAACP,MAAM,CAAC;IACtC;IACA,IAAI,CAACjF,OAAO,CAACqG,uBAAuB,CAAEC,CAAC,IAAK;MACxC,MAAMhB,KAAK,GAAG,IAAI,CAACtF,OAAO,CAACuG,QAAQ,CAAC,CAAC;MACrC;MACA,IAAI,CAACxB,IAAI,CAACyB,GAAG,CAAC,MAAM;QAChB,IAAI,CAACtB,eAAe,CAACI,KAAK,CAAC;QAC3B,IAAI,CAACL,MAAM,GAAGK,KAAK;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACtF,OAAO,CAACyG,qBAAqB,CAAC,MAAM;MACrC,IAAI,CAACrB,SAAS,CAAC,CAAC;IACpB,CAAC,CAAC;IACF;IACA,IAAI,IAAI,CAACzC,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACC,WAAW,CAAC,CAAC;IAChD;IACA,IAAI,CAACD,yBAAyB,GAAGrD,SAAS,CAACsB,MAAM,EAAE,QAAQ,CAAC,CAAC8F,SAAS,CAAC,MAAM,IAAI,CAAC1G,OAAO,CAAC2G,MAAM,CAAC,CAAC,CAAC;IACnG,IAAI,CAACrG,MAAM,CAACsG,IAAI,CAAC,IAAI,CAAC5G,OAAO,CAAC;EAClC;EACA;IAAS,IAAI,CAAC8C,IAAI,YAAA+D,wBAAA7D,CAAA;MAAA,YAAAA,CAAA,IAAwF2B,eAAe,EAvGzBhG,EAAE,CAAAsE,iBAAA,CAuGyCtE,EAAE,CAACmI,MAAM,GAvGpDnI,EAAE,CAAAsE,iBAAA,CAuG+DvD,wBAAwB;IAAA,CAA4C;EAAE;EACvO;IAAS,IAAI,CAACwD,IAAI,kBAxG8EvE,EAAE,CAAAwE,iBAAA;MAAAxB,IAAA,EAwGJgD,eAAe;MAAAvB,SAAA;MAAAW,MAAA;QAAAa,OAAA;QAAAE,KAAA;MAAA;MAAAiC,QAAA,GAxGbpI,EAAE,CAAAqI,kBAAA,CAwGuG,CAAC;QAC9LC,OAAO,EAAE5H,iBAAiB;QAC1B6H,WAAW,EAAE/H,UAAU,CAAC,MAAMwF,eAAe,CAAC;QAC9CwC,KAAK,EAAE;MACX,CAAC,CAAC,GA5GsFxI,EAAE,CAAAyI,0BAAA;MAAAnD,KAAA;MAAAC,IAAA;MAAAmD,MAAA;MAAAlD,QAAA,WAAAmD,yBAAA/D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5E,EAAE,CAAA4I,SAAA,eA4Gc,CAAC;QAAA;MAAA;MAAAC,MAAA;IAAA,EAA6G;EAAE;AACpO;AACA;EAAA,QAAAlD,SAAA,oBAAAA,SAAA,KA9GoG3F,EAAE,CAAA4F,iBAAA,CA8GXI,eAAe,EAAc,CAAC;IAC7GhD,IAAI,EAAE7C,SAAS;IACf0F,IAAI,EAAE,CAAC;MAAEiD,QAAQ,EAAE,mBAAmB;MAAEtD,QAAQ,EAAE,uDAAuD;MAAEuD,SAAS,EAAE,CAAC;QACvGT,OAAO,EAAE5H,iBAAiB;QAC1B6H,WAAW,EAAE/H,UAAU,CAAC,MAAMwF,eAAe,CAAC;QAC9CwC,KAAK,EAAE;MACX,CAAC,CAAC;MAAEK,MAAM,EAAE,CAAC,6EAA6E;IAAE,CAAC;EAC7G,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7F,IAAI,EAAEhD,EAAE,CAACmI;IAAO,CAAC,EAAE;MAAEnF,IAAI,EAAEkB,SAAS;MAAE4B,UAAU,EAAE,CAAC;QACnF9C,IAAI,EAAE5C,MAAM;QACZyF,IAAI,EAAE,CAAC9E,wBAAwB;MACnC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkF,OAAO,EAAE,CAAC;MACtCjD,IAAI,EAAE3C,KAAK;MACXwF,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEM,KAAK,EAAE,CAAC;MACRnD,IAAI,EAAE3C,KAAK;MACXwF,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmD,mBAAmB,SAAS9H,UAAU,CAAC;EACzC,IAAI+E,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACzE,QAAQ,GAAGiB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAChB,MAAM,CAACwE,cAAc,EAAED,OAAO,CAAC;IACtE,IAAI,IAAI,CAAC5E,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACC,UAAU,CAAC0E,OAAO,EAAE,IAAI,CAAC9E,QAAQ,CAAC;IAC3C;EACJ;EACA,IAAI8E,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACzE,QAAQ;EACxB;EACA,IAAIyH,aAAaA,CAAC9C,KAAK,EAAE;IACrB,IAAI,CAAC+C,cAAc,GAAG/C,KAAK;IAC3B,IAAI,IAAI,CAAC9E,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC0E,OAAO,EAAE,IAAI,CAAC9E,QAAQ,CAAC;IAChD;EACJ;EACA,IAAIgI,aAAaA,CAAChD,KAAK,EAAE;IACrB,IAAI,CAACiD,cAAc,GAAGjD,KAAK;IAC3B,IAAI,IAAI,CAAC9E,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC0E,OAAO,EAAE,IAAI,CAAC9E,QAAQ,CAAC;IAChD;EACJ;EACAM,WAAWA,CAAC2E,IAAI,EAAEC,YAAY,EAAE;IAC5B,KAAK,CAACA,YAAY,CAAC;IACnB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,YAAY,GAAGA,YAAY;EACpC;EACA9E,UAAUA,CAAC0E,OAAO,EAAE9E,QAAQ,EAAE;IAC1B,IAAI,CAAC,IAAI,CAAC+H,cAAc,IAAI,CAAC,IAAI,CAACE,cAAc,EAAE;MAC9C,MAAM,IAAIC,KAAK,CAAC,qEAAqE,CAAC;IAC1F;IACA,IAAI,CAACH,cAAc,CAAC5B,QAAQ,GAAG,IAAI,CAAC4B,cAAc,CAAC5B,QAAQ,IAAIrB,OAAO,CAACqB,QAAQ;IAC/E,IAAI,CAAC8B,cAAc,CAAC9B,QAAQ,GAAG,IAAI,CAAC8B,cAAc,CAAC9B,QAAQ,IAAIrB,OAAO,CAACqB,QAAQ;IAC/E,IAAI2B,aAAa,GAAG/G,MAAM,CAACgF,MAAM,CAACG,WAAW,CAAC,IAAI,CAAC6B,cAAc,CAACI,IAAI,EAAE,IAAI,CAACJ,cAAc,CAAC5B,QAAQ,CAAC;IACrG,IAAI6B,aAAa,GAAGjH,MAAM,CAACgF,MAAM,CAACG,WAAW,CAAC,IAAI,CAAC+B,cAAc,CAACE,IAAI,EAAE,IAAI,CAACF,cAAc,CAAC9B,QAAQ,CAAC;IACrG,IAAI,CAACpC,gBAAgB,CAACsC,aAAa,CAAC+B,SAAS,GAAG,EAAE;IAClD,MAAMC,KAAK,GAAGvD,OAAO,CAACuD,KAAK;IAC3B,IAAIrI,QAAQ,EAAE;MACV,IAAI,CAACE,OAAO,GAAGa,MAAM,CAACgF,MAAM,CAACuC,gBAAgB,CAAC,IAAI,CAACvE,gBAAgB,CAACsC,aAAa,EAAEvB,OAAO,CAAC;IAC/F,CAAC,MACI;MACD,IAAI,CAACG,IAAI,CAACqB,iBAAiB,CAAC,MAAM;QAC9B,IAAI,CAACpG,OAAO,GAAGa,MAAM,CAACgF,MAAM,CAACuC,gBAAgB,CAAC,IAAI,CAACvE,gBAAgB,CAACsC,aAAa,EAAEvB,OAAO,CAAC;MAC/F,CAAC,CAAC;IACN;IACAA,OAAO,CAACuD,KAAK,GAAGA,KAAK;IACrB,IAAI,CAACnI,OAAO,CAACqI,QAAQ,CAAC;MAClBC,QAAQ,EAAEV,aAAa;MACvBW,QAAQ,EAAET;IACd,CAAC,CAAC;IACF;IACA,IAAI,IAAI,CAACnF,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACC,WAAW,CAAC,CAAC;IAChD;IACA,IAAI,CAACD,yBAAyB,GAAGrD,SAAS,CAACsB,MAAM,EAAE,QAAQ,CAAC,CAAC8F,SAAS,CAAC,MAAM,IAAI,CAAC1G,OAAO,CAAC2G,MAAM,CAAC,CAAC,CAAC;IACnG,IAAI,CAACrG,MAAM,CAACsG,IAAI,CAAC,IAAI,CAAC5G,OAAO,CAAC;EAClC;EACA;IAAS,IAAI,CAAC8C,IAAI,YAAA0F,4BAAAxF,CAAA;MAAA,YAAAA,CAAA,IAAwF2E,mBAAmB,EA5L7BhJ,EAAE,CAAAsE,iBAAA,CA4L6CtE,EAAE,CAACmI,MAAM,GA5LxDnI,EAAE,CAAAsE,iBAAA,CA4LmEvD,wBAAwB;IAAA,CAA4C;EAAE;EAC3O;IAAS,IAAI,CAACwD,IAAI,kBA7L8EvE,EAAE,CAAAwE,iBAAA;MAAAxB,IAAA,EA6LJgG,mBAAmB;MAAAvE,SAAA;MAAAW,MAAA;QAAAa,OAAA;QAAAgD,aAAA;QAAAE,aAAA;MAAA;MAAAf,QAAA,GA7LjBpI,EAAE,CAAAyI,0BAAA;MAAAnD,KAAA;MAAAC,IAAA;MAAAmD,MAAA;MAAAlD,QAAA,WAAAsE,6BAAAlF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5E,EAAE,CAAA4I,SAAA,eA6LyP,CAAC;QAAA;MAAA;MAAAC,MAAA,GAAA/H,GAAA;IAAA,EAA6G;EAAE;AAC/c;AACA;EAAA,QAAA6E,SAAA,oBAAAA,SAAA,KA/LoG3F,EAAE,CAAA4F,iBAAA,CA+LXoD,mBAAmB,EAAc,CAAC;IACjHhG,IAAI,EAAE7C,SAAS;IACf0F,IAAI,EAAE,CAAC;MAAEiD,QAAQ,EAAE,wBAAwB;MAAEtD,QAAQ,EAAE,uDAAuD;MAAEqD,MAAM,EAAE,CAAC,6EAA6E;IAAE,CAAC;EAC7M,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7F,IAAI,EAAEhD,EAAE,CAACmI;IAAO,CAAC,EAAE;MAAEnF,IAAI,EAAEkB,SAAS;MAAE4B,UAAU,EAAE,CAAC;QACnF9C,IAAI,EAAE5C,MAAM;QACZyF,IAAI,EAAE,CAAC9E,wBAAwB;MACnC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEkF,OAAO,EAAE,CAAC;MACtCjD,IAAI,EAAE3C,KAAK;MACXwF,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEoD,aAAa,EAAE,CAAC;MAChBjG,IAAI,EAAE3C,KAAK;MACXwF,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEsD,aAAa,EAAE,CAAC;MAChBnG,IAAI,EAAE3C,KAAK;MACXwF,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkE,kBAAkB,CAAC;EACrB,OAAOC,OAAOA,CAACtI,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHuI,QAAQ,EAAEF,kBAAkB;MAC5BhB,SAAS,EAAE,CACP;QAAET,OAAO,EAAEvH,wBAAwB;QAAEmJ,QAAQ,EAAExI;MAAO,CAAC;IAE/D,CAAC;EACL;EACA;IAAS,IAAI,CAACyC,IAAI,YAAAgG,2BAAA9F,CAAA;MAAA,YAAAA,CAAA,IAAwF0F,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAACK,IAAI,kBA1N8EpK,EAAE,CAAAqK,gBAAA;MAAArH,IAAA,EA0NS+G;IAAkB,EAE9F;EAAE;EACjC;IAAS,IAAI,CAACO,IAAI,kBA7N8EtK,EAAE,CAAAuK,gBAAA;MAAAC,OAAA,GA6NuC5J,YAAY;IAAA,EAAI;EAAE;AAC/J;AACA;EAAA,QAAA+E,SAAA,oBAAAA,SAAA,KA/NoG3F,EAAE,CAAA4F,iBAAA,CA+NXmE,kBAAkB,EAAc,CAAC;IAChH/G,IAAI,EAAEvC,QAAQ;IACdoF,IAAI,EAAE,CAAC;MACC2E,OAAO,EAAE,CACL5J,YAAY,CACf;MACD6J,YAAY,EAAE,CACVzE,eAAe,EACfgD,mBAAmB,CACtB;MACD0B,OAAO,EAAE,CACL1E,eAAe,EACfgD,mBAAmB;IAE3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,mBAAmB,EAAEhD,eAAe,EAAE+D,kBAAkB,EAAEhJ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}