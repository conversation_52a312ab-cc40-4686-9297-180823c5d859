{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function ignoreElements() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, noop));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "noop", "ignoreElements", "source", "subscriber", "subscribe"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/rxjs/dist/esm/internal/operators/ignoreElements.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function ignoreElements() {\n    return operate((source, subscriber) => {\n        source.subscribe(createOperatorSubscriber(subscriber, noop));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC7B,OAAOH,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnCD,MAAM,CAACE,SAAS,CAACL,wBAAwB,CAACI,UAAU,EAAEH,IAAI,CAAC,CAAC;EAChE,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}