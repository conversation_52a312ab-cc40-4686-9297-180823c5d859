{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/api.service\";\nimport * as i3 from \"../../services/agent.service\";\nimport * as i4 from \"@angular/common\";\nfunction ConfigComponent_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Test Connection\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConfigComponent_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Testing...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConfigComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Test Connection\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConfigComponent_span_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Testing...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConfigComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Test Connection\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConfigComponent_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Testing...\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"success\": a0,\n    \"error\": a1\n  };\n};\nfunction ConfigComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, ctx_r6.testResult.success, !ctx_r6.testResult.success));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.testResult.message);\n  }\n}\nfunction ConfigComponent_span_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Configuration\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConfigComponent_span_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Saving...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ConfigComponent {\n  constructor(fb, apiService, agentService) {\n    this.fb = fb;\n    this.apiService = apiService;\n    this.agentService = agentService;\n    this.loading = false;\n    this.testingModel = '';\n    this.testResult = null;\n  }\n  ngOnInit() {\n    console.log('[ConfigComponent] ngOnInit called');\n    this.initForm();\n    this.loadConfig();\n  }\n  initForm() {\n    console.log('[ConfigComponent] Initializing config form');\n    this.configForm = this.fb.group({\n      ollama_base_url: ['http://localhost:11434', Validators.required],\n      ollama_model: ['llama3', Validators.required],\n      lm_studio_base_url: ['http://localhost:1234', Validators.required],\n      lm_studio_model: ['default', Validators.required],\n      openai_api_key: ['', Validators.required],\n      openai_model: ['gpt-4o-mini', Validators.required],\n      default_model: ['ollama/llama3', Validators.required]\n    });\n  }\n  loadConfig() {\n    console.log('[ConfigComponent] loadConfig called');\n    this.loading = true;\n    this.apiService.getConfig().subscribe(response => {\n      console.log('[ConfigComponent] Config loaded:', response);\n      if (response.config) {\n        const config = response.config;\n        this.configForm.patchValue(config);\n        console.log('[ConfigComponent] Form patched with config:', config);\n      } else {\n        console.warn('[ConfigComponent] No config object found in response');\n      }\n      this.loading = false;\n    }, error => {\n      console.error('[ConfigComponent] ❌ Error loading config:', error);\n      this.loading = false;\n    });\n  }\n  saveConfig() {\n    console.log('[ConfigComponent] saveConfig called');\n    if (this.configForm.invalid) {\n      console.warn('[ConfigComponent] Config form is invalid. Aborting save.');\n      return;\n    }\n    this.loading = true;\n    const formValue = this.configForm.value;\n    console.log('[ConfigComponent] Saving config with values:', formValue);\n    this.apiService.updateConfig(formValue).subscribe(response => {\n      console.log('[ConfigComponent] ✅ Config saved successfully. Reloading config.');\n      this.loading = false;\n      this.loadConfig(); // Reload to get masked values\n    }, error => {\n      console.error('[ConfigComponent] ❌ Error saving config:', error);\n      this.loading = false;\n    });\n  }\n  testModelConnection(modelId) {\n    console.log('[ConfigComponent] testModelConnection called for model:', modelId);\n    this.testingModel = modelId;\n    this.testResult = null;\n    this.agentService.testModelConnection(modelId).subscribe(response => {\n      console.log('[ConfigComponent] ✅ Model test success:', response);\n      this.testResult = {\n        success: response.success,\n        message: response.message\n      };\n      this.testingModel = '';\n    }, error => {\n      console.error('[ConfigComponent] ❌ Model test failed:', error);\n      this.testResult = {\n        success: false,\n        message: error.error?.message || 'Connection failed'\n      };\n      this.testingModel = '';\n    });\n  }\n  maskApiKey(key) {\n    if (!key) return '';\n    if (key.length <= 8) return '*'.repeat(key.length);\n    const masked = key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);\n    console.log('[ConfigComponent] Masked API key:', masked);\n    return masked;\n  }\n  isMaskedApiKey(key) {\n    const result = key.includes('*');\n    console.log('[ConfigComponent] Checking if key is masked:', result);\n    return result;\n  }\n  static {\n    this.ɵfac = function ConfigComponent_Factory(t) {\n      return new (t || ConfigComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ApiService), i0.ɵɵdirectiveInject(i3.AgentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ConfigComponent,\n      selectors: [[\"app-config\"]],\n      decls: 86,\n      vars: 14,\n      consts: [[1, \"config-container\"], [1, \"config-header\"], [1, \"config-description\"], [1, \"config-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-section\"], [1, \"form-group\"], [\"for\", \"ollama_base_url\"], [\"type\", \"text\", \"id\", \"ollama_base_url\", \"formControlName\", \"ollama_base_url\", \"placeholder\", \"http://localhost:11434\"], [\"for\", \"ollama_model\"], [1, \"input-with-action\"], [\"type\", \"text\", \"id\", \"ollama_model\", \"formControlName\", \"ollama_model\", \"placeholder\", \"Enter model name (e.g., llama3)\"], [\"type\", \"button\", 1, \"test-btn\", 3, \"disabled\", \"click\"], [4, \"ngIf\"], [\"for\", \"lm_studio_base_url\"], [\"type\", \"text\", \"id\", \"lm_studio_base_url\", \"formControlName\", \"lm_studio_base_url\", \"placeholder\", \"http://localhost:1234\"], [\"for\", \"lm_studio_model\"], [\"type\", \"text\", \"id\", \"lm_studio_model\", \"formControlName\", \"lm_studio_model\", \"placeholder\", \"Enter model name\"], [\"for\", \"openai_api_key\"], [\"type\", \"password\", \"id\", \"openai_api_key\", \"formControlName\", \"openai_api_key\", \"placeholder\", \"Enter your OpenAI API key\"], [\"for\", \"openai_model\"], [\"id\", \"openai_model\", \"formControlName\", \"openai_model\"], [\"value\", \"gpt-4\"], [\"value\", \"gpt-4o\"], [\"value\", \"gpt-4o-mini\"], [\"value\", \"gpt-3.5-turbo\"], [\"for\", \"default_model\"], [\"id\", \"default_model\", \"formControlName\", \"default_model\"], [\"value\", \"ollama/llama3\"], [\"value\", \"ollama/mistral\"], [\"value\", \"lm_studio/default\"], [\"value\", \"openai/gpt-4\"], [\"value\", \"openai/gpt-4o\"], [\"value\", \"openai/gpt-4o-mini\"], [\"value\", \"openai/gpt-3.5-turbo\"], [\"class\", \"test-result\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"form-actions\"], [\"type\", \"submit\", 1, \"save-btn\", 3, \"disabled\"], [1, \"test-result\", 3, \"ngClass\"]],\n      template: function ConfigComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 2);\n          i0.ɵɵtext(5, \" Configure your AI model connections. You'll need to provide API keys for the models you want to use. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"form\", 3);\n          i0.ɵɵlistener(\"ngSubmit\", function ConfigComponent_Template_form_ngSubmit_6_listener() {\n            return ctx.saveConfig();\n          });\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"h3\");\n          i0.ɵɵtext(9, \"Ollama Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"label\", 6);\n          i0.ɵɵtext(12, \"Base URL\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"input\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 5)(15, \"label\", 8);\n          i0.ɵɵtext(16, \"Model\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 9);\n          i0.ɵɵelement(18, \"input\", 10);\n          i0.ɵɵelementStart(19, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function ConfigComponent_Template_button_click_19_listener() {\n            let tmp_b_0;\n            return ctx.testModelConnection(\"ollama/\" + ((tmp_b_0 = ctx.configForm.get(\"ollama_model\")) == null ? null : tmp_b_0.value));\n          });\n          i0.ɵɵtemplate(20, ConfigComponent_span_20_Template, 2, 0, \"span\", 12);\n          i0.ɵɵtemplate(21, ConfigComponent_span_21_Template, 2, 0, \"span\", 12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 4)(23, \"h3\");\n          i0.ɵɵtext(24, \"LM Studio Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 5)(26, \"label\", 13);\n          i0.ɵɵtext(27, \"Base URL\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 5)(30, \"label\", 15);\n          i0.ɵɵtext(31, \"Model\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 9);\n          i0.ɵɵelement(33, \"input\", 16);\n          i0.ɵɵelementStart(34, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function ConfigComponent_Template_button_click_34_listener() {\n            let tmp_b_0;\n            return ctx.testModelConnection(\"lm_studio/\" + ((tmp_b_0 = ctx.configForm.get(\"lm_studio_model\")) == null ? null : tmp_b_0.value));\n          });\n          i0.ɵɵtemplate(35, ConfigComponent_span_35_Template, 2, 0, \"span\", 12);\n          i0.ɵɵtemplate(36, ConfigComponent_span_36_Template, 2, 0, \"span\", 12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"div\", 4)(38, \"h3\");\n          i0.ɵɵtext(39, \"OpenAI Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 5)(41, \"label\", 17);\n          i0.ɵɵtext(42, \"API Key\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"input\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 5)(45, \"label\", 19);\n          i0.ɵɵtext(46, \"Model\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 9)(48, \"select\", 20)(49, \"option\", 21);\n          i0.ɵɵtext(50, \"GPT-4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"option\", 22);\n          i0.ɵɵtext(52, \"GPT-4o\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"option\", 23);\n          i0.ɵɵtext(54, \"GPT-4o Mini\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"option\", 24);\n          i0.ɵɵtext(56, \"GPT-3.5 Turbo\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function ConfigComponent_Template_button_click_57_listener() {\n            let tmp_b_0;\n            return ctx.testModelConnection(\"openai/\" + ((tmp_b_0 = ctx.configForm.get(\"openai_model\")) == null ? null : tmp_b_0.value));\n          });\n          i0.ɵɵtemplate(58, ConfigComponent_span_58_Template, 2, 0, \"span\", 12);\n          i0.ɵɵtemplate(59, ConfigComponent_span_59_Template, 2, 0, \"span\", 12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(60, \"div\", 4)(61, \"h3\");\n          i0.ɵɵtext(62, \"Default Model\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 5)(64, \"label\", 25);\n          i0.ɵɵtext(65, \"Select Default Model\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"select\", 26)(67, \"option\", 27);\n          i0.ɵɵtext(68, \"Ollama - Llama 3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"option\", 28);\n          i0.ɵɵtext(70, \"Ollama - Mistral\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"option\", 29);\n          i0.ɵɵtext(72, \"LM Studio - Default\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"option\", 30);\n          i0.ɵɵtext(74, \"OpenAI - GPT-4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"option\", 31);\n          i0.ɵɵtext(76, \"OpenAI - GPT-4o\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"option\", 32);\n          i0.ɵɵtext(78, \"OpenAI - GPT-4o Mini\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"option\", 33);\n          i0.ɵɵtext(80, \"OpenAI - GPT-3.5 Turbo\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(81, ConfigComponent_div_81_Template, 3, 5, \"div\", 34);\n          i0.ɵɵelementStart(82, \"div\", 35)(83, \"button\", 36);\n          i0.ɵɵtemplate(84, ConfigComponent_span_84_Template, 2, 0, \"span\", 12);\n          i0.ɵɵtemplate(85, ConfigComponent_span_85_Template, 2, 0, \"span\", 12);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_4_0;\n          let tmp_7_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.configForm);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.testingModel === \"ollama\" || !((tmp_1_0 = ctx.configForm.get(\"ollama_model\")) == null ? null : tmp_1_0.value));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.testingModel !== \"ollama\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.testingModel === \"ollama\");\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.testingModel === \"lm_studio\" || !((tmp_4_0 = ctx.configForm.get(\"lm_studio_model\")) == null ? null : tmp_4_0.value));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.testingModel !== \"lm_studio\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.testingModel === \"lm_studio\");\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.testingModel === \"openai\" || !((tmp_7_0 = ctx.configForm.get(\"openai_api_key\")) == null ? null : tmp_7_0.value));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.testingModel !== \"openai\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.testingModel === \"openai\");\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngIf\", ctx.testResult);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.configForm.invalid || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\".config-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 20px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.config-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.config-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n}\\n.config-header[_ngcontent-%COMP%]   .config-description[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  line-height: 1.5;\\n}\\n\\n.config-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 20px;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  margin: 0 0 16px 0;\\n  color: #333;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 6px;\\n  color: #555;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  border: 1px solid #ccc;\\n  border-radius: 4px;\\n  font-size: 14px;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #2196f3;\\n  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-with-action[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-with-action[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-with-action[_ngcontent-%COMP%]   .test-btn[_ngcontent-%COMP%] {\\n  padding: 0 12px;\\n  background-color: #4caf50;\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n  white-space: nowrap;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-with-action[_ngcontent-%COMP%]   .test-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #388e3c;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-with-action[_ngcontent-%COMP%]   .test-btn[_ngcontent-%COMP%]:disabled {\\n  background-color: #a5d6a7;\\n  cursor: not-allowed;\\n}\\n.config-form[_ngcontent-%COMP%]   .test-result[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-radius: 4px;\\n  font-size: 14px;\\n}\\n.config-form[_ngcontent-%COMP%]   .test-result.success[_ngcontent-%COMP%] {\\n  background-color: #e8f5e9;\\n  border: 1px solid #a5d6a7;\\n  color: #2e7d32;\\n}\\n.config-form[_ngcontent-%COMP%]   .test-result.error[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  border: 1px solid #ffcdd2;\\n  color: #c62828;\\n}\\n.config-form[_ngcontent-%COMP%]   .test-result[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  margin-top: 8px;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%] {\\n  padding: 10px 24px;\\n  background-color: #2196f3;\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1976d2;\\n}\\n.config-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]:disabled {\\n  background-color: #bbdefb;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .config-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .config-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .config-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-with-action[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .config-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-with-action[_ngcontent-%COMP%]   .test-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "ctx_r6", "testResult", "success", "ɵɵadvance", "ɵɵtextInterpolate", "message", "ConfigComponent", "constructor", "fb", "apiService", "agentService", "loading", "testingModel", "ngOnInit", "console", "log", "initForm", "loadConfig", "configForm", "group", "ollama_base_url", "required", "ollama_model", "lm_studio_base_url", "lm_studio_model", "openai_api_key", "openai_model", "default_model", "getConfig", "subscribe", "response", "config", "patchValue", "warn", "error", "saveConfig", "invalid", "formValue", "value", "updateConfig", "testModelConnection", "modelId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "length", "repeat", "masked", "substring", "isMaskedApiKey", "result", "includes", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ApiService", "i3", "AgentService", "selectors", "decls", "vars", "consts", "template", "ConfigComponent_Template", "rf", "ctx", "ɵɵlistener", "ConfigComponent_Template_form_ngSubmit_6_listener", "ɵɵelement", "ConfigComponent_Template_button_click_19_listener", "tmp_b_0", "get", "ɵɵtemplate", "ConfigComponent_span_20_Template", "ConfigComponent_span_21_Template", "ConfigComponent_Template_button_click_34_listener", "ConfigComponent_span_35_Template", "ConfigComponent_span_36_Template", "ConfigComponent_Template_button_click_57_listener", "ConfigComponent_span_58_Template", "ConfigComponent_span_59_Template", "ConfigComponent_div_81_Template", "ConfigComponent_span_84_Template", "ConfigComponent_span_85_Template", "tmp_1_0", "tmp_4_0", "tmp_7_0"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\config\\config.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\config\\config.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\nimport { ApiService } from '../../services/api.service';\nimport { AgentService } from '../../services/agent.service';\n\n@Component({\n  selector: 'app-config',\n  templateUrl: './config.component.html',\n  styleUrls: ['./config.component.scss']\n})\nexport class ConfigComponent implements OnInit {\n  configForm!: FormGroup;\n  loading: boolean = false;\n  testingModel: string = '';\n  testResult: { success: boolean, message: string } | null = null;\n\n  constructor(\n    private fb: FormBuilder,\n    private apiService: ApiService,\n    private agentService: AgentService\n  ) {}\n\n  ngOnInit(): void {\n    console.log('[ConfigComponent] ngOnInit called');\n    this.initForm();\n    this.loadConfig();\n  }\n\n  initForm(): void {\n    console.log('[ConfigComponent] Initializing config form');\n    this.configForm = this.fb.group({\n      ollama_base_url: ['http://localhost:11434', Validators.required],\n      ollama_model: ['llama3', Validators.required],\n      lm_studio_base_url: ['http://localhost:1234', Validators.required],\n      lm_studio_model: ['default', Validators.required],\n      openai_api_key: ['', Validators.required],\n      openai_model: ['gpt-4o-mini', Validators.required],\n      default_model: ['ollama/llama3', Validators.required]\n    });\n  }\n\n  loadConfig(): void {\n    console.log('[ConfigComponent] loadConfig called');\n    this.loading = true;\n\n    this.apiService.getConfig().subscribe(\n      (response) => {\n        console.log('[ConfigComponent] Config loaded:', response);\n\n        if (response.config) {\n          const config = response.config;\n          this.configForm.patchValue(config);\n          console.log('[ConfigComponent] Form patched with config:', config);\n        } else {\n          console.warn('[ConfigComponent] No config object found in response');\n        }\n\n        this.loading = false;\n      },\n      (error) => {\n        console.error('[ConfigComponent] ❌ Error loading config:', error);\n        this.loading = false;\n      }\n    );\n  }\n\n  saveConfig(): void {\n    console.log('[ConfigComponent] saveConfig called');\n\n    if (this.configForm.invalid) {\n      console.warn('[ConfigComponent] Config form is invalid. Aborting save.');\n      return;\n    }\n\n    this.loading = true;\n    const formValue = this.configForm.value;\n    console.log('[ConfigComponent] Saving config with values:', formValue);\n\n    this.apiService.updateConfig(formValue).subscribe(\n      (response) => {\n        console.log('[ConfigComponent] ✅ Config saved successfully. Reloading config.');\n        this.loading = false;\n        this.loadConfig(); // Reload to get masked values\n      },\n      (error) => {\n        console.error('[ConfigComponent] ❌ Error saving config:', error);\n        this.loading = false;\n      }\n    );\n  }\n\n  testModelConnection(modelId: string): void {\n    console.log('[ConfigComponent] testModelConnection called for model:', modelId);\n    this.testingModel = modelId;\n    this.testResult = null;\n\n    this.agentService.testModelConnection(modelId).subscribe(\n      (response) => {\n        console.log('[ConfigComponent] ✅ Model test success:', response);\n        this.testResult = {\n          success: response.success,\n          message: response.message\n        };\n        this.testingModel = '';\n      },\n      (error) => {\n        console.error('[ConfigComponent] ❌ Model test failed:', error);\n        this.testResult = {\n          success: false,\n          message: error.error?.message || 'Connection failed'\n        };\n        this.testingModel = '';\n      }\n    );\n  }\n\n  private maskApiKey(key: string): string {\n    if (!key) return '';\n    if (key.length <= 8) return '*'.repeat(key.length);\n\n    const masked = key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);\n    console.log('[ConfigComponent] Masked API key:', masked);\n    return masked;\n  }\n\n  private isMaskedApiKey(key: string): boolean {\n    const result = key.includes('*');\n    console.log('[ConfigComponent] Checking if key is masked:', result);\n    return result;\n  }\n}\n", "<div class=\"config-container\">\n  <div class=\"config-header\">\n    <h2>Configuration</h2>\n    <p class=\"config-description\">\n      Configure your AI model connections. You'll need to provide API keys for the models you want to use.\n    </p>\n  </div>\n  \n  <form [formGroup]=\"configForm\" (ngSubmit)=\"saveConfig()\" class=\"config-form\">\n\n    \n    <div class=\"form-section\">\n      <h3>Ollama Configuration</h3>\n      \n      <div class=\"form-group\">\n        <label for=\"ollama_base_url\">Base URL</label>\n        <input \n          type=\"text\" \n          id=\"ollama_base_url\" \n          formControlName=\"ollama_base_url\" \n          placeholder=\"http://localhost:11434\"\n        />\n      </div>\n      \n      <div class=\"form-group\">\n        <label for=\"ollama_model\">Model</label>\n        <div class=\"input-with-action\">\n          <input \n            type=\"text\" \n            id=\"ollama_model\" \n            formControlName=\"ollama_model\" \n            placeholder=\"Enter model name (e.g., llama3)\"\n          />\n          <button \n            type=\"button\" \n            class=\"test-btn\" \n            [disabled]=\"loading || testingModel === 'ollama' || !configForm.get('ollama_model')?.value\" \n            (click)=\"testModelConnection('ollama/'+configForm.get('ollama_model')?.value)\"\n          >\n            <span *ngIf=\"testingModel !== 'ollama'\">Test Connection</span>\n            <span *ngIf=\"testingModel === 'ollama'\">Testing...</span>\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"form-section\">\n      <h3>LM Studio Configuration</h3>\n      \n      <div class=\"form-group\">\n        <label for=\"lm_studio_base_url\">Base URL</label>\n        <input \n          type=\"text\" \n          id=\"lm_studio_base_url\" \n          formControlName=\"lm_studio_base_url\" \n          placeholder=\"http://localhost:1234\"\n        />\n      </div>\n      \n      <div class=\"form-group\">\n        <label for=\"lm_studio_model\">Model</label>\n        <div class=\"input-with-action\">\n          <input \n            type=\"text\" \n            id=\"lm_studio_model\" \n            formControlName=\"lm_studio_model\" \n            placeholder=\"Enter model name\"\n          />\n          <button \n            type=\"button\" \n            class=\"test-btn\" \n            [disabled]=\"loading || testingModel === 'lm_studio' || !configForm.get('lm_studio_model')?.value\" \n            (click)=\"testModelConnection('lm_studio/'+configForm.get('lm_studio_model')?.value)\"\n          >\n            <span *ngIf=\"testingModel !== 'lm_studio'\">Test Connection</span>\n            <span *ngIf=\"testingModel === 'lm_studio'\">Testing...</span>\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"form-section\">\n      <h3>OpenAI Configuration</h3>\n      \n      <div class=\"form-group\">\n        <label for=\"openai_api_key\">API Key</label>\n        <input \n          type=\"password\" \n          id=\"openai_api_key\" \n          formControlName=\"openai_api_key\" \n          placeholder=\"Enter your OpenAI API key\"\n        />\n      </div>\n      \n      <div class=\"form-group\">\n        <label for=\"openai_model\">Model</label>\n        <div class=\"input-with-action\">\n          <select id=\"openai_model\" formControlName=\"openai_model\">\n            <option value=\"gpt-4\">GPT-4</option>\n            <option value=\"gpt-4o\">GPT-4o</option>\n            <option value=\"gpt-4o-mini\">GPT-4o Mini</option>\n            <option value=\"gpt-3.5-turbo\">GPT-3.5 Turbo</option>\n          </select>\n          <button \n            type=\"button\" \n            class=\"test-btn\" \n            [disabled]=\"loading || testingModel === 'openai' || !configForm.get('openai_api_key')?.value\" \n            (click)=\"testModelConnection('openai/'+configForm.get('openai_model')?.value)\"\n          >\n            <span *ngIf=\"testingModel !== 'openai'\">Test Connection</span>\n            <span *ngIf=\"testingModel === 'openai'\">Testing...</span>\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"form-section\">\n      <h3>Default Model</h3>\n      \n      <div class=\"form-group\">\n        <label for=\"default_model\">Select Default Model</label>\n        <select id=\"default_model\" formControlName=\"default_model\">\n          <option value=\"ollama/llama3\">Ollama - Llama 3</option>\n          <option value=\"ollama/mistral\">Ollama - Mistral</option>\n          <option value=\"lm_studio/default\">LM Studio - Default</option>\n          <option value=\"openai/gpt-4\">OpenAI - GPT-4</option>\n          <option value=\"openai/gpt-4o\">OpenAI - GPT-4o</option>\n          <option value=\"openai/gpt-4o-mini\">OpenAI - GPT-4o Mini</option>\n          <option value=\"openai/gpt-3.5-turbo\">OpenAI - GPT-3.5 Turbo</option>\n        </select>\n      </div>\n    </div>\n    \n    <div *ngIf=\"testResult\" class=\"test-result\" [ngClass]=\"{'success': testResult.success, 'error': !testResult.success}\">\n      <p>{{ testResult.message }}</p>\n    </div>\n    \n    <div class=\"form-actions\">\n      <button type=\"submit\" class=\"save-btn\" [disabled]=\"configForm.invalid || loading\">\n        <span *ngIf=\"!loading\">Save Configuration</span>\n        <span *ngIf=\"loading\">Saving...</span>\n      </button>\n    </div>\n  </form>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICsCvDC,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC9DH,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAkCzDH,EAAA,CAAAC,cAAA,WAA2C;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACjEH,EAAA,CAAAC,cAAA,WAA2C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAkC5DH,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC9DH,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;;;;IAuBjEH,EAAA,CAAAC,cAAA,cAAsH;IACjHD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADWH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,GAAAF,MAAA,CAAAC,UAAA,CAAAC,OAAA,EAAyE;IAChHT,EAAA,CAAAU,SAAA,GAAwB;IAAxBV,EAAA,CAAAW,iBAAA,CAAAJ,MAAA,CAAAC,UAAA,CAAAI,OAAA,CAAwB;;;;;IAKzBZ,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChDH,EAAA,CAAAC,cAAA,WAAsB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADlI9C,OAAM,MAAOU,eAAe;EAM1BC,YACUC,EAAe,EACfC,UAAsB,EACtBC,YAA0B;IAF1B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,YAAY,GAAZA,YAAY;IAPtB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAX,UAAU,GAAiD,IAAI;EAM5D;EAEHY,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAD,QAAQA,CAAA;IACNF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzD,IAAI,CAACG,UAAU,GAAG,IAAI,CAACV,EAAE,CAACW,KAAK,CAAC;MAC9BC,eAAe,EAAE,CAAC,wBAAwB,EAAE5B,UAAU,CAAC6B,QAAQ,CAAC;MAChEC,YAAY,EAAE,CAAC,QAAQ,EAAE9B,UAAU,CAAC6B,QAAQ,CAAC;MAC7CE,kBAAkB,EAAE,CAAC,uBAAuB,EAAE/B,UAAU,CAAC6B,QAAQ,CAAC;MAClEG,eAAe,EAAE,CAAC,SAAS,EAAEhC,UAAU,CAAC6B,QAAQ,CAAC;MACjDI,cAAc,EAAE,CAAC,EAAE,EAAEjC,UAAU,CAAC6B,QAAQ,CAAC;MACzCK,YAAY,EAAE,CAAC,aAAa,EAAElC,UAAU,CAAC6B,QAAQ,CAAC;MAClDM,aAAa,EAAE,CAAC,eAAe,EAAEnC,UAAU,CAAC6B,QAAQ;KACrD,CAAC;EACJ;EAEAJ,UAAUA,CAAA;IACRH,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAACJ,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACF,UAAU,CAACmB,SAAS,EAAE,CAACC,SAAS,CAClCC,QAAQ,IAAI;MACXhB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEe,QAAQ,CAAC;MAEzD,IAAIA,QAAQ,CAACC,MAAM,EAAE;QACnB,MAAMA,MAAM,GAAGD,QAAQ,CAACC,MAAM;QAC9B,IAAI,CAACb,UAAU,CAACc,UAAU,CAACD,MAAM,CAAC;QAClCjB,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEgB,MAAM,CAAC;OACnE,MAAM;QACLjB,OAAO,CAACmB,IAAI,CAAC,sDAAsD,CAAC;;MAGtE,IAAI,CAACtB,OAAO,GAAG,KAAK;IACtB,CAAC,EACAuB,KAAK,IAAI;MACRpB,OAAO,CAACoB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,IAAI,CAACvB,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEAwB,UAAUA,CAAA;IACRrB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAElD,IAAI,IAAI,CAACG,UAAU,CAACkB,OAAO,EAAE;MAC3BtB,OAAO,CAACmB,IAAI,CAAC,0DAA0D,CAAC;MACxE;;IAGF,IAAI,CAACtB,OAAO,GAAG,IAAI;IACnB,MAAM0B,SAAS,GAAG,IAAI,CAACnB,UAAU,CAACoB,KAAK;IACvCxB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEsB,SAAS,CAAC;IAEtE,IAAI,CAAC5B,UAAU,CAAC8B,YAAY,CAACF,SAAS,CAAC,CAACR,SAAS,CAC9CC,QAAQ,IAAI;MACXhB,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/E,IAAI,CAACJ,OAAO,GAAG,KAAK;MACpB,IAAI,CAACM,UAAU,EAAE,CAAC,CAAC;IACrB,CAAC,EACAiB,KAAK,IAAI;MACRpB,OAAO,CAACoB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,IAAI,CAACvB,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEA6B,mBAAmBA,CAACC,OAAe;IACjC3B,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAE0B,OAAO,CAAC;IAC/E,IAAI,CAAC7B,YAAY,GAAG6B,OAAO;IAC3B,IAAI,CAACxC,UAAU,GAAG,IAAI;IAEtB,IAAI,CAACS,YAAY,CAAC8B,mBAAmB,CAACC,OAAO,CAAC,CAACZ,SAAS,CACrDC,QAAQ,IAAI;MACXhB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEe,QAAQ,CAAC;MAChE,IAAI,CAAC7B,UAAU,GAAG;QAChBC,OAAO,EAAE4B,QAAQ,CAAC5B,OAAO;QACzBG,OAAO,EAAEyB,QAAQ,CAACzB;OACnB;MACD,IAAI,CAACO,YAAY,GAAG,EAAE;IACxB,CAAC,EACAsB,KAAK,IAAI;MACRpB,OAAO,CAACoB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,IAAI,CAACjC,UAAU,GAAG;QAChBC,OAAO,EAAE,KAAK;QACdG,OAAO,EAAE6B,KAAK,CAACA,KAAK,EAAE7B,OAAO,IAAI;OAClC;MACD,IAAI,CAACO,YAAY,GAAG,EAAE;IACxB,CAAC,CACF;EACH;EAEQ8B,UAAUA,CAACC,GAAW;IAC5B,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;IACnB,IAAIA,GAAG,CAACC,MAAM,IAAI,CAAC,EAAE,OAAO,GAAG,CAACC,MAAM,CAACF,GAAG,CAACC,MAAM,CAAC;IAElD,MAAME,MAAM,GAAGH,GAAG,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAACF,MAAM,CAACF,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,GAAGD,GAAG,CAACI,SAAS,CAACJ,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;IAC/F9B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE+B,MAAM,CAAC;IACxD,OAAOA,MAAM;EACf;EAEQE,cAAcA,CAACL,GAAW;IAChC,MAAMM,MAAM,GAAGN,GAAG,CAACO,QAAQ,CAAC,GAAG,CAAC;IAChCpC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEkC,MAAM,CAAC;IACnE,OAAOA,MAAM;EACf;;;uBAvHW3C,eAAe,EAAAb,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5D,EAAA,CAAA0D,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA9D,EAAA,CAAA0D,iBAAA,CAAAK,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAfnD,eAAe;MAAAoD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BvE,EAAA,CAAAC,cAAA,aAA8B;UAEtBD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,WAA8B;UAC5BD,EAAA,CAAAE,MAAA,6GACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,cAA6E;UAA9CD,EAAA,CAAAyE,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAA9B,UAAA,EAAY;UAAA,EAAC;UAGtD1C,EAAA,CAAAC,cAAA,aAA0B;UACpBD,EAAA,CAAAE,MAAA,2BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7BH,EAAA,CAAAC,cAAA,cAAwB;UACOD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAA2E,SAAA,gBAKE;UACJ3E,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAwB;UACID,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,cAA+B;UAC7BD,EAAA,CAAA2E,SAAA,iBAKE;UACF3E,EAAA,CAAAC,cAAA,kBAKC;UADCD,EAAA,CAAAyE,UAAA,mBAAAG,kDAAA;YAAA,IAAAC,OAAA;YAAA,OAASL,GAAA,CAAAzB,mBAAA,CAAoB,SAAS,KAAA8B,OAAA,GAACL,GAAA,CAAA/C,UAAA,CAAAqD,GAAA,CAAe,cAAc,CAAC,mBAAAD,OAAA,CAAAhC,KAAA,EAAQ;UAAA,EAAC;UAE9E7C,EAAA,CAAA+E,UAAA,KAAAC,gCAAA,mBAA8D;UAC9DhF,EAAA,CAAA+E,UAAA,KAAAE,gCAAA,mBAAyD;UAC3DjF,EAAA,CAAAG,YAAA,EAAS;UAKfH,EAAA,CAAAC,cAAA,cAA0B;UACpBD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEhCH,EAAA,CAAAC,cAAA,cAAwB;UACUD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChDH,EAAA,CAAA2E,SAAA,iBAKE;UACJ3E,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAwB;UACOD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,cAA+B;UAC7BD,EAAA,CAAA2E,SAAA,iBAKE;UACF3E,EAAA,CAAAC,cAAA,kBAKC;UADCD,EAAA,CAAAyE,UAAA,mBAAAS,kDAAA;YAAA,IAAAL,OAAA;YAAA,OAASL,GAAA,CAAAzB,mBAAA,CAAoB,YAAY,KAAA8B,OAAA,GAACL,GAAA,CAAA/C,UAAA,CAAAqD,GAAA,CAAe,iBAAiB,CAAC,mBAAAD,OAAA,CAAAhC,KAAA,EAAQ;UAAA,EAAC;UAEpF7C,EAAA,CAAA+E,UAAA,KAAAI,gCAAA,mBAAiE;UACjEnF,EAAA,CAAA+E,UAAA,KAAAK,gCAAA,mBAA4D;UAC9DpF,EAAA,CAAAG,YAAA,EAAS;UAKfH,EAAA,CAAAC,cAAA,cAA0B;UACpBD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE7BH,EAAA,CAAAC,cAAA,cAAwB;UACMD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAA2E,SAAA,iBAKE;UACJ3E,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAwB;UACID,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,cAA+B;UAELD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpCH,EAAA,CAAAC,cAAA,kBAAuB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChDH,EAAA,CAAAC,cAAA,kBAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEtDH,EAAA,CAAAC,cAAA,kBAKC;UADCD,EAAA,CAAAyE,UAAA,mBAAAY,kDAAA;YAAA,IAAAR,OAAA;YAAA,OAASL,GAAA,CAAAzB,mBAAA,CAAoB,SAAS,KAAA8B,OAAA,GAACL,GAAA,CAAA/C,UAAA,CAAAqD,GAAA,CAAe,cAAc,CAAC,mBAAAD,OAAA,CAAAhC,KAAA,EAAQ;UAAA,EAAC;UAE9E7C,EAAA,CAAA+E,UAAA,KAAAO,gCAAA,mBAA8D;UAC9DtF,EAAA,CAAA+E,UAAA,KAAAQ,gCAAA,mBAAyD;UAC3DvF,EAAA,CAAAG,YAAA,EAAS;UAKfH,EAAA,CAAAC,cAAA,cAA0B;UACpBD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEtBH,EAAA,CAAAC,cAAA,cAAwB;UACKD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,kBAA2D;UAC3BD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvDH,EAAA,CAAAC,cAAA,kBAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxDH,EAAA,CAAAC,cAAA,kBAAkC;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9DH,EAAA,CAAAC,cAAA,kBAA6B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpDH,EAAA,CAAAC,cAAA,kBAA8B;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtDH,EAAA,CAAAC,cAAA,kBAAmC;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChEH,EAAA,CAAAC,cAAA,kBAAqC;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAK1EH,EAAA,CAAA+E,UAAA,KAAAS,+BAAA,kBAEM;UAENxF,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAA+E,UAAA,KAAAU,gCAAA,mBAAgD;UAChDzF,EAAA,CAAA+E,UAAA,KAAAW,gCAAA,mBAAsC;UACxC1F,EAAA,CAAAG,YAAA,EAAS;;;;;;UArIPH,EAAA,CAAAU,SAAA,GAAwB;UAAxBV,EAAA,CAAAI,UAAA,cAAAoE,GAAA,CAAA/C,UAAA,CAAwB;UA4BpBzB,EAAA,CAAAU,SAAA,IAA2F;UAA3FV,EAAA,CAAAI,UAAA,aAAAoE,GAAA,CAAAtD,OAAA,IAAAsD,GAAA,CAAArD,YAAA,oBAAAwE,OAAA,GAAAnB,GAAA,CAAA/C,UAAA,CAAAqD,GAAA,mCAAAa,OAAA,CAAA9C,KAAA,EAA2F;UAGpF7C,EAAA,CAAAU,SAAA,GAA+B;UAA/BV,EAAA,CAAAI,UAAA,SAAAoE,GAAA,CAAArD,YAAA,cAA+B;UAC/BnB,EAAA,CAAAU,SAAA,GAA+B;UAA/BV,EAAA,CAAAI,UAAA,SAAAoE,GAAA,CAAArD,YAAA,cAA+B;UA+BtCnB,EAAA,CAAAU,SAAA,IAAiG;UAAjGV,EAAA,CAAAI,UAAA,aAAAoE,GAAA,CAAAtD,OAAA,IAAAsD,GAAA,CAAArD,YAAA,uBAAAyE,OAAA,GAAApB,GAAA,CAAA/C,UAAA,CAAAqD,GAAA,sCAAAc,OAAA,CAAA/C,KAAA,EAAiG;UAG1F7C,EAAA,CAAAU,SAAA,GAAkC;UAAlCV,EAAA,CAAAI,UAAA,SAAAoE,GAAA,CAAArD,YAAA,iBAAkC;UAClCnB,EAAA,CAAAU,SAAA,GAAkC;UAAlCV,EAAA,CAAAI,UAAA,SAAAoE,GAAA,CAAArD,YAAA,iBAAkC;UA+BzCnB,EAAA,CAAAU,SAAA,IAA6F;UAA7FV,EAAA,CAAAI,UAAA,aAAAoE,GAAA,CAAAtD,OAAA,IAAAsD,GAAA,CAAArD,YAAA,oBAAA0E,OAAA,GAAArB,GAAA,CAAA/C,UAAA,CAAAqD,GAAA,qCAAAe,OAAA,CAAAhD,KAAA,EAA6F;UAGtF7C,EAAA,CAAAU,SAAA,GAA+B;UAA/BV,EAAA,CAAAI,UAAA,SAAAoE,GAAA,CAAArD,YAAA,cAA+B;UAC/BnB,EAAA,CAAAU,SAAA,GAA+B;UAA/BV,EAAA,CAAAI,UAAA,SAAAoE,GAAA,CAAArD,YAAA,cAA+B;UAuBxCnB,EAAA,CAAAU,SAAA,IAAgB;UAAhBV,EAAA,CAAAI,UAAA,SAAAoE,GAAA,CAAAhE,UAAA,CAAgB;UAKmBR,EAAA,CAAAU,SAAA,GAA0C;UAA1CV,EAAA,CAAAI,UAAA,aAAAoE,GAAA,CAAA/C,UAAA,CAAAkB,OAAA,IAAA6B,GAAA,CAAAtD,OAAA,CAA0C;UACxElB,EAAA,CAAAU,SAAA,GAAc;UAAdV,EAAA,CAAAI,UAAA,UAAAoE,GAAA,CAAAtD,OAAA,CAAc;UACdlB,EAAA,CAAAU,SAAA,GAAa;UAAbV,EAAA,CAAAI,UAAA,SAAAoE,GAAA,CAAAtD,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}