{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./socket-factory.service\";\nexport class SocketService {\n  constructor(socketFactory) {\n    this.socketFactory = socketFactory;\n  }\n  connect() {\n    this.socketFactory.connect();\n  }\n  disconnect() {\n    this.socketFactory.disconnect();\n  }\n  on(eventName) {\n    return this.socketFactory.on(eventName);\n  }\n  emit(eventName, data) {\n    this.socketFactory.emit(eventName, data);\n  }\n  sendMessage(projectName, message, modelId) {\n    this.socketFactory.emit('user_message', {\n      project_name: projectName,\n      message: message,\n      model_id: modelId\n    });\n  }\n  static {\n    this.ɵfac = function SocketService_Factory(t) {\n      return new (t || SocketService)(i0.ɵɵinject(i1.SocketFactoryService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SocketService,\n      factory: SocketService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["SocketService", "constructor", "socketFactory", "connect", "disconnect", "on", "eventName", "emit", "data", "sendMessage", "projectName", "message", "modelId", "project_name", "model_id", "i0", "ɵɵinject", "i1", "SocketFactoryService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\services\\socket.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable } from 'rxjs';\nimport { SocketFactoryService } from './socket-factory.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SocketService {\n  constructor(private socketFactory: SocketFactoryService) { }\n  \n  connect(): void {\n    this.socketFactory.connect();\n  }\n  \n  disconnect(): void {\n    this.socketFactory.disconnect();\n  }\n  \n  on(eventName: string): Observable<any> {\n    return this.socketFactory.on(eventName);\n  }\n  \n  emit(eventName: string, data: any): void {\n    this.socketFactory.emit(eventName, data);\n  }\n  \n  sendMessage(projectName: string, message: string, modelId: string): void {\n    this.socketFactory.emit('user_message', {\n      project_name: projectName,\n      message: message,\n      model_id: modelId\n    });\n  }\n}\n"], "mappings": ";;AAOA,OAAM,MAAOA,aAAa;EACxBC,YAAoBC,aAAmC;IAAnC,KAAAA,aAAa,GAAbA,aAAa;EAA0B;EAE3DC,OAAOA,CAAA;IACL,IAAI,CAACD,aAAa,CAACC,OAAO,EAAE;EAC9B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACF,aAAa,CAACE,UAAU,EAAE;EACjC;EAEAC,EAAEA,CAACC,SAAiB;IAClB,OAAO,IAAI,CAACJ,aAAa,CAACG,EAAE,CAACC,SAAS,CAAC;EACzC;EAEAC,IAAIA,CAACD,SAAiB,EAAEE,IAAS;IAC/B,IAAI,CAACN,aAAa,CAACK,IAAI,CAACD,SAAS,EAAEE,IAAI,CAAC;EAC1C;EAEAC,WAAWA,CAACC,WAAmB,EAAEC,OAAe,EAAEC,OAAe;IAC/D,IAAI,CAACV,aAAa,CAACK,IAAI,CAAC,cAAc,EAAE;MACtCM,YAAY,EAAEH,WAAW;MACzBC,OAAO,EAAEA,OAAO;MAChBG,QAAQ,EAAEF;KACX,CAAC;EACJ;;;uBAzBWZ,aAAa,EAAAe,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;aAAblB,aAAa;MAAAmB,OAAA,EAAbnB,aAAa,CAAAoB,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}