{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { MonacoEditorModule } from 'ngx-monaco-editor-v2';\nimport { AppComponent } from './app.component';\nimport { ChatComponent } from './components/chat/chat.component';\nimport { CodeEditorComponent } from './components/code-editor/code-editor.component';\nimport { BrowserPreviewComponent } from './components/browser-preview/browser-preview.component';\nimport { ProjectListComponent } from './components/project-list/project-list.component';\nimport { ProjectDetailComponent } from './components/project-detail/project-detail.component';\nimport { FileExplorerComponent } from './components/file-explorer/file-explorer.component';\nimport { ConfigComponent } from './components/config/config.component';\nimport { HomeComponent } from './components/home/<USER>';\nimport { TestingComponent } from './components/testing/testing.component';\nimport { ReversePipe } from './components/chat/reverse.pipe';\n// ✅ Import your ChatGPTCopilotComponent here\nimport { ChatGPTCopilotComponent } from './components/chatgpt-copilot/chatgpt-copilot.component';\nimport { ApiService } from './services/api.service';\nimport { SocketService } from './services/socket.service';\nimport { SocketFactoryService } from './services/socket-factory.service';\nimport { ProjectService } from './services/project.service';\nimport { FileService } from './services/file.service';\nimport { AgentService } from './services/agent.service';\nimport { NotificationService } from './services/notification.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-monaco-editor-v2\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [ApiService, SocketService, SocketFactoryService, ProjectService, FileService, AgentService, NotificationService],\n      imports: [BrowserModule, CommonModule, FormsModule, ReactiveFormsModule, HttpClientModule, AppRoutingModule, MonacoEditorModule.forRoot()]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, ChatComponent, CodeEditorComponent, BrowserPreviewComponent, ProjectListComponent, ProjectDetailComponent, FileExplorerComponent, ConfigComponent, HomeComponent, TestingComponent, ReversePipe, ChatGPTCopilotComponent // ✅ Declared component\n    ],\n    imports: [BrowserModule, CommonModule, FormsModule, ReactiveFormsModule, HttpClientModule, AppRoutingModule, i1.MonacoEditorModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "ReactiveFormsModule", "CommonModule", "HttpClientModule", "AppRoutingModule", "MonacoEditorModule", "AppComponent", "ChatComponent", "CodeEditorComponent", "BrowserPreviewComponent", "ProjectListComponent", "ProjectDetailComponent", "FileExplorerComponent", "ConfigComponent", "HomeComponent", "TestingComponent", "ReversePipe", "ChatGPTCopilotComponent", "ApiService", "SocketService", "SocketFactoryService", "ProjectService", "FileService", "AgentService", "NotificationService", "AppModule", "bootstrap", "imports", "forRoot", "declarations", "i1"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { MonacoEditorModule } from 'ngx-monaco-editor-v2';\n\nimport { AppComponent } from './app.component';\nimport { ChatComponent } from './components/chat/chat.component';\nimport { CodeEditorComponent } from './components/code-editor/code-editor.component';\nimport { BrowserPreviewComponent } from './components/browser-preview/browser-preview.component';\nimport { ProjectListComponent } from './components/project-list/project-list.component';\nimport { ProjectDetailComponent } from './components/project-detail/project-detail.component';\nimport { FileExplorerComponent } from './components/file-explorer/file-explorer.component';\nimport { ConfigComponent } from './components/config/config.component';\nimport { HomeComponent } from './components/home/<USER>';\nimport { TestingComponent } from './components/testing/testing.component';\n\nimport { ReversePipe } from './components/chat/reverse.pipe';\n\n// ✅ Import your ChatGPTCopilotComponent here\nimport { ChatGPTCopilotComponent } from './components/chatgpt-copilot/chatgpt-copilot.component';\n\nimport { ApiService } from './services/api.service';\nimport { SocketService } from './services/socket.service';\nimport { SocketFactoryService } from './services/socket-factory.service';\nimport { ProjectService } from './services/project.service';\nimport { FileService } from './services/file.service';\nimport { AgentService } from './services/agent.service';\nimport { NotificationService } from './services/notification.service';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    ChatComponent,\n    CodeEditorComponent,\n    BrowserPreviewComponent,\n    ProjectListComponent,\n    ProjectDetailComponent,\n    FileExplorerComponent,\n    ConfigComponent,\n    HomeComponent,\n    TestingComponent,\n    ReversePipe,\n    ChatGPTCopilotComponent // ✅ Declared component\n  ],\n  imports: [\n    BrowserModule,\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n    AppRoutingModule,\n    MonacoEditorModule.forRoot()\n  ],\n  providers: [\n    ApiService,\n    SocketService,\n    SocketFactoryService,\n    ProjectService,\n    FileService,\n    AgentService,\n    NotificationService\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,kBAAkB,QAAQ,sBAAsB;AAEzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,gBAAgB,QAAQ,wCAAwC;AAEzE,SAASC,WAAW,QAAQ,gCAAgC;AAE5D;AACA,SAASC,uBAAuB,QAAQ,wDAAwD;AAEhG,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,mBAAmB,QAAQ,iCAAiC;;;AAqCrE,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRpB,YAAY;IAAA;EAAA;;;iBATb,CACTY,UAAU,EACVC,aAAa,EACbC,oBAAoB,EACpBC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,mBAAmB,CACpB;MAAAG,OAAA,GAhBC5B,aAAa,EACbG,YAAY,EACZF,WAAW,EACXC,mBAAmB,EACnBE,gBAAgB,EAChBC,gBAAgB,EAChBC,kBAAkB,CAACuB,OAAO,EAAE;IAAA;EAAA;;;2EAanBH,SAAS;IAAAI,YAAA,GAjClBvB,YAAY,EACZC,aAAa,EACbC,mBAAmB,EACnBC,uBAAuB,EACvBC,oBAAoB,EACpBC,sBAAsB,EACtBC,qBAAqB,EACrBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,WAAW,EACXC,uBAAuB,CAAC;IAAA,C;cAGxBlB,aAAa,EACbG,YAAY,EACZF,WAAW,EACXC,mBAAmB,EACnBE,gBAAgB,EAChBC,gBAAgB,EAAA0B,EAAA,CAAAzB,kBAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}