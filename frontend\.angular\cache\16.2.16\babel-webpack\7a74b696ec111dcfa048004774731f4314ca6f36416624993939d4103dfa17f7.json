{"ast": null, "code": "// imported from https://github.com/unshiftio/yeast\n'use strict';\n\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''),\n  length = 64,\n  map = {};\nlet seed = 0,\n  i = 0,\n  prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n  let encoded = '';\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n  return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n  let decoded = 0;\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n  return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n  const now = encode(+new Date());\n  if (now !== prev) return seed = 0, prev = now;\n  return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;", "map": {"version": 3, "names": ["alphabet", "split", "length", "map", "seed", "i", "prev", "encode", "num", "encoded", "Math", "floor", "decode", "str", "decoded", "char<PERSON>t", "yeast", "now", "Date"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/engine.io-client/build/esm/contrib/yeast.js"], "sourcesContent": ["// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n"], "mappings": "AAAA;AACA,YAAY;;AACZ,MAAMA,QAAQ,GAAG,kEAAkE,CAACC,KAAK,CAAC,EAAE,CAAC;EAAEC,MAAM,GAAG,EAAE;EAAEC,GAAG,GAAG,CAAC,CAAC;AACpH,IAAIC,IAAI,GAAG,CAAC;EAAEC,CAAC,GAAG,CAAC;EAAEC,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,GAAG,EAAE;EACxB,IAAIC,OAAO,GAAG,EAAE;EAChB,GAAG;IACCA,OAAO,GAAGT,QAAQ,CAACQ,GAAG,GAAGN,MAAM,CAAC,GAAGO,OAAO;IAC1CD,GAAG,GAAGE,IAAI,CAACC,KAAK,CAACH,GAAG,GAAGN,MAAM,CAAC;EAClC,CAAC,QAAQM,GAAG,GAAG,CAAC;EAChB,OAAOC,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,MAAMA,CAACC,GAAG,EAAE;EACxB,IAAIC,OAAO,GAAG,CAAC;EACf,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,GAAG,CAACX,MAAM,EAAEG,CAAC,EAAE,EAAE;IAC7BS,OAAO,GAAGA,OAAO,GAAGZ,MAAM,GAAGC,GAAG,CAACU,GAAG,CAACE,MAAM,CAACV,CAAC,CAAC,CAAC;EACnD;EACA,OAAOS,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,KAAKA,CAAA,EAAG;EACpB,MAAMC,GAAG,GAAGV,MAAM,CAAC,CAAC,IAAIW,IAAI,CAAC,CAAC,CAAC;EAC/B,IAAID,GAAG,KAAKX,IAAI,EACZ,OAAOF,IAAI,GAAG,CAAC,EAAEE,IAAI,GAAGW,GAAG;EAC/B,OAAOA,GAAG,GAAG,GAAG,GAAGV,MAAM,CAACH,IAAI,EAAE,CAAC;AACrC;AACA;AACA;AACA;AACA,OAAOC,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAClBF,GAAG,CAACH,QAAQ,CAACK,CAAC,CAAC,CAAC,GAAGA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}