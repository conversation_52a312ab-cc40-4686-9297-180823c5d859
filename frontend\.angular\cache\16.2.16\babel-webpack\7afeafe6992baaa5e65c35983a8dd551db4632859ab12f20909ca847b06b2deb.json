{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class ProjectService {\n  constructor(apiService) {\n    this.apiService = apiService;\n    this.baseDir = 'C:/SourceProjects/AutonomousAI/projects';\n    console.log('[ProjectService] Initializing ProjectService...');\n    const platform = navigator.platform.toLowerCase();\n    if (platform.includes('linux')) {\n      this.baseDir = '/home/<USER>/SourceProjects/AutonomousAI/projects';\n      console.log('[ProjectService] Platform: Linux -> baseDir set to', this.baseDir);\n    } else if (platform.includes('mac')) {\n      this.baseDir = '/Users/<USER>/SourceProjects/AutonomousAI/projects';\n      console.log('[ProjectService] Platform: macOS -> baseDir set to', this.baseDir);\n    } else {\n      console.log('[ProjectService] Platform: Windows/Other -> baseDir remains', this.baseDir);\n    }\n  }\n  getProjects() {\n    console.log('[ProjectService] getProjects called');\n    return this.apiService.getProjects();\n  }\n  getProject(name) {\n    console.log('[ProjectService] getProject called for:', name);\n    return this.apiService.getProject(name);\n  }\n  createProject(name, description) {\n    console.log('[ProjectService] createProject called with:', {\n      name,\n      description\n    });\n    return this.apiService.createProject(name, description);\n  }\n  deleteProject(name) {\n    console.log('[ProjectService] deleteProject called for:', name);\n    return this.apiService.deleteProject(name);\n  }\n  getProjectFiles(projectName) {\n    console.log('[ProjectService] getProjectFiles called for:', projectName);\n    return this.apiService.getProjectFiles(projectName);\n  }\n  getProjectMessages(projectName) {\n    console.log('[ProjectService] getProjectMessages called for:', projectName);\n    return this.apiService.getProjectMessages(projectName);\n  }\n  deleteProjectMessages(projectName) {\n    console.log('[ProjectService] deleteProjectMessages called for:', projectName);\n    return this.apiService.deleteProjectMessages(projectName);\n  }\n  /**\n   * Save messages for a project to persist chat history and expanded state\n   * @param projectName Project name\n   * @param messages Array of messages to save\n   * @param chatExpanded Boolean indicating if chat is expanded\n   * @returns Observable with save result\n   */\n  saveProjectMessages(projectName, messages, chatExpanded = false) {\n    console.log('[ProjectService] saveProjectMessages called for:', projectName);\n    return this.apiService.saveProjectMessages(projectName, messages, chatExpanded);\n  }\n  getProjectDir(projectName) {\n    const path = `${this.baseDir}/${projectName}`;\n    console.log('[ProjectService] getProjectDir ->', path);\n    return path;\n  }\n  getBaseDir() {\n    console.log('[ProjectService] getBaseDir ->', this.baseDir);\n    return this.baseDir;\n  }\n  setBaseDir(dir) {\n    console.log('[ProjectService] setBaseDir called. New baseDir:', dir);\n    this.baseDir = dir;\n  }\n  resetProject(name) {\n    console.log('[ProjectService] resetProject called for:', name);\n    return this.apiService.resetProject(name);\n  }\n  exportProjectChat(name) {\n    console.log('[ProjectService] exportProjectChat called for:', name);\n    return this.apiService.exportProjectChat(name);\n  }\n  deleteAllProjects() {\n    console.log('[ProjectService] deleteAllProjects called');\n    return this.apiService.deleteAllProjects();\n  }\n  static {\n    this.ɵfac = function ProjectService_Factory(t) {\n      return new (t || ProjectService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProjectService,\n      factory: ProjectService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ProjectService", "constructor", "apiService", "baseDir", "console", "log", "platform", "navigator", "toLowerCase", "includes", "getProjects", "getProject", "name", "createProject", "description", "deleteProject", "getProjectFiles", "projectName", "getProjectMessages", "deleteProjectMessages", "saveProjectMessages", "messages", "chatExpanded", "getProjectDir", "path", "getBaseDir", "setBaseDir", "dir", "resetProject", "exportProjectChat", "deleteAllProjects", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\services\\project.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { ApiService } from './api.service';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProjectService {\n  private baseDir: string = 'C:/SourceProjects/AutonomousAI/projects';\n\n  constructor(private apiService: ApiService) {\n    console.log('[ProjectService] Initializing ProjectService...');\n    const platform = navigator.platform.toLowerCase();\n\n    if (platform.includes('linux')) {\n      this.baseDir = '/home/<USER>/SourceProjects/AutonomousAI/projects';\n      console.log('[ProjectService] Platform: Linux -> baseDir set to', this.baseDir);\n    } else if (platform.includes('mac')) {\n      this.baseDir = '/Users/<USER>/SourceProjects/AutonomousAI/projects';\n      console.log('[ProjectService] Platform: macOS -> baseDir set to', this.baseDir);\n    } else {\n      console.log('[ProjectService] Platform: Windows/Other -> baseDir remains', this.baseDir);\n    }\n  }\n\n  getProjects(): Observable<any> {\n    console.log('[ProjectService] getProjects called');\n    return this.apiService.getProjects();\n  }\n\n  getProject(name: string): Observable<any> {\n    console.log('[ProjectService] getProject called for:', name);\n    return this.apiService.getProject(name);\n  }\n\n  createProject(name: string, description?: string): Observable<any> {\n    console.log('[ProjectService] createProject called with:', { name, description });\n    return this.apiService.createProject(name, description);\n  }\n\n  deleteProject(name: string): Observable<any> {\n    console.log('[ProjectService] deleteProject called for:', name);\n    return this.apiService.deleteProject(name);\n  }\n\n  getProjectFiles(projectName: string): Observable<any> {\n    console.log('[ProjectService] getProjectFiles called for:', projectName);\n    return this.apiService.getProjectFiles(projectName);\n  }\n\n  getProjectMessages(projectName: string): Observable<any> {\n    console.log('[ProjectService] getProjectMessages called for:', projectName);\n    return this.apiService.getProjectMessages(projectName);\n  }\n\n  deleteProjectMessages(projectName: string): Observable<any> {\n    console.log('[ProjectService] deleteProjectMessages called for:', projectName);\n    return this.apiService.deleteProjectMessages(projectName);\n  }\n\n  /**\n   * Save messages for a project to persist chat history and expanded state\n   * @param projectName Project name\n   * @param messages Array of messages to save\n   * @param chatExpanded Boolean indicating if chat is expanded\n   * @returns Observable with save result\n   */\n  saveProjectMessages(projectName: string, messages: any[], chatExpanded: boolean = false): Observable<any> {\n    console.log('[ProjectService] saveProjectMessages called for:', projectName);\n    return this.apiService.saveProjectMessages(projectName, messages, chatExpanded);\n  }\n\n  getProjectDir(projectName: string): string {\n    const path = `${this.baseDir}/${projectName}`;\n    console.log('[ProjectService] getProjectDir ->', path);\n    return path;\n  }\n\n  getBaseDir(): string {\n    console.log('[ProjectService] getBaseDir ->', this.baseDir);\n    return this.baseDir;\n  }\n\n  setBaseDir(dir: string): void {\n    console.log('[ProjectService] setBaseDir called. New baseDir:', dir);\n    this.baseDir = dir;\n  }\n\n  resetProject(name: string): Observable<any> {\n    console.log('[ProjectService] resetProject called for:', name);\n    return this.apiService.resetProject(name);\n  }\n\n  exportProjectChat(name: string): Observable<any> {\n    console.log('[ProjectService] exportProjectChat called for:', name);\n    return this.apiService.exportProjectChat(name);\n  }\n\n  deleteAllProjects(): Observable<any> {\n    console.log('[ProjectService] deleteAllProjects called');\n    return this.apiService.deleteAllProjects();\n  }\n}\n"], "mappings": ";;AAQA,OAAM,MAAOA,cAAc;EAGzBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAFtB,KAAAC,OAAO,GAAW,yCAAyC;IAGjEC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9D,MAAMC,QAAQ,GAAGC,SAAS,CAACD,QAAQ,CAACE,WAAW,EAAE;IAEjD,IAAIF,QAAQ,CAACG,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9B,IAAI,CAACN,OAAO,GAAG,iDAAiD;MAChEC,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE,IAAI,CAACF,OAAO,CAAC;KAChF,MAAM,IAAIG,QAAQ,CAACG,QAAQ,CAAC,KAAK,CAAC,EAAE;MACnC,IAAI,CAACN,OAAO,GAAG,kDAAkD;MACjEC,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE,IAAI,CAACF,OAAO,CAAC;KAChF,MAAM;MACLC,OAAO,CAACC,GAAG,CAAC,6DAA6D,EAAE,IAAI,CAACF,OAAO,CAAC;;EAE5F;EAEAO,WAAWA,CAAA;IACTN,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,OAAO,IAAI,CAACH,UAAU,CAACQ,WAAW,EAAE;EACtC;EAEAC,UAAUA,CAACC,IAAY;IACrBR,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEO,IAAI,CAAC;IAC5D,OAAO,IAAI,CAACV,UAAU,CAACS,UAAU,CAACC,IAAI,CAAC;EACzC;EAEAC,aAAaA,CAACD,IAAY,EAAEE,WAAoB;IAC9CV,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE;MAAEO,IAAI;MAAEE;IAAW,CAAE,CAAC;IACjF,OAAO,IAAI,CAACZ,UAAU,CAACW,aAAa,CAACD,IAAI,EAAEE,WAAW,CAAC;EACzD;EAEAC,aAAaA,CAACH,IAAY;IACxBR,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEO,IAAI,CAAC;IAC/D,OAAO,IAAI,CAACV,UAAU,CAACa,aAAa,CAACH,IAAI,CAAC;EAC5C;EAEAI,eAAeA,CAACC,WAAmB;IACjCb,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEY,WAAW,CAAC;IACxE,OAAO,IAAI,CAACf,UAAU,CAACc,eAAe,CAACC,WAAW,CAAC;EACrD;EAEAC,kBAAkBA,CAACD,WAAmB;IACpCb,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEY,WAAW,CAAC;IAC3E,OAAO,IAAI,CAACf,UAAU,CAACgB,kBAAkB,CAACD,WAAW,CAAC;EACxD;EAEAE,qBAAqBA,CAACF,WAAmB;IACvCb,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEY,WAAW,CAAC;IAC9E,OAAO,IAAI,CAACf,UAAU,CAACiB,qBAAqB,CAACF,WAAW,CAAC;EAC3D;EAEA;;;;;;;EAOAG,mBAAmBA,CAACH,WAAmB,EAAEI,QAAe,EAAEC,YAAA,GAAwB,KAAK;IACrFlB,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEY,WAAW,CAAC;IAC5E,OAAO,IAAI,CAACf,UAAU,CAACkB,mBAAmB,CAACH,WAAW,EAAEI,QAAQ,EAAEC,YAAY,CAAC;EACjF;EAEAC,aAAaA,CAACN,WAAmB;IAC/B,MAAMO,IAAI,GAAG,GAAG,IAAI,CAACrB,OAAO,IAAIc,WAAW,EAAE;IAC7Cb,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEmB,IAAI,CAAC;IACtD,OAAOA,IAAI;EACb;EAEAC,UAAUA,CAAA;IACRrB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACF,OAAO,CAAC;IAC3D,OAAO,IAAI,CAACA,OAAO;EACrB;EAEAuB,UAAUA,CAACC,GAAW;IACpBvB,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEsB,GAAG,CAAC;IACpE,IAAI,CAACxB,OAAO,GAAGwB,GAAG;EACpB;EAEAC,YAAYA,CAAChB,IAAY;IACvBR,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEO,IAAI,CAAC;IAC9D,OAAO,IAAI,CAACV,UAAU,CAAC0B,YAAY,CAAChB,IAAI,CAAC;EAC3C;EAEAiB,iBAAiBA,CAACjB,IAAY;IAC5BR,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEO,IAAI,CAAC;IACnE,OAAO,IAAI,CAACV,UAAU,CAAC2B,iBAAiB,CAACjB,IAAI,CAAC;EAChD;EAEAkB,iBAAiBA,CAAA;IACf1B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxD,OAAO,IAAI,CAACH,UAAU,CAAC4B,iBAAiB,EAAE;EAC5C;;;uBA9FW9B,cAAc,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdlC,cAAc;MAAAmC,OAAA,EAAdnC,cAAc,CAAAoC,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}