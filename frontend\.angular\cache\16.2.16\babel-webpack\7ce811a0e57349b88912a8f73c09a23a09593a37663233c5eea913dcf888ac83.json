{"ast": null, "code": "import { Socket as Engine, installTimerFunctions, nextTick } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n  constructor(uri, opts) {\n    var _a;\n    super();\n    this.nsps = {};\n    this.subs = [];\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = undefined;\n    }\n    opts = opts || {};\n    opts.path = opts.path || \"/socket.io\";\n    this.opts = opts;\n    installTimerFunctions(this, opts);\n    this.reconnection(opts.reconnection !== false);\n    this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n    this.reconnectionDelay(opts.reconnectionDelay || 1000);\n    this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n    this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n    this.backoff = new Backoff({\n      min: this.reconnectionDelay(),\n      max: this.reconnectionDelayMax(),\n      jitter: this.randomizationFactor()\n    });\n    this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n    this._readyState = \"closed\";\n    this.uri = uri;\n    const _parser = opts.parser || parser;\n    this.encoder = new _parser.Encoder();\n    this.decoder = new _parser.Decoder();\n    this._autoConnect = opts.autoConnect !== false;\n    if (this._autoConnect) this.open();\n  }\n  reconnection(v) {\n    if (!arguments.length) return this._reconnection;\n    this._reconnection = !!v;\n    return this;\n  }\n  reconnectionAttempts(v) {\n    if (v === undefined) return this._reconnectionAttempts;\n    this._reconnectionAttempts = v;\n    return this;\n  }\n  reconnectionDelay(v) {\n    var _a;\n    if (v === undefined) return this._reconnectionDelay;\n    this._reconnectionDelay = v;\n    (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n    return this;\n  }\n  randomizationFactor(v) {\n    var _a;\n    if (v === undefined) return this._randomizationFactor;\n    this._randomizationFactor = v;\n    (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n    return this;\n  }\n  reconnectionDelayMax(v) {\n    var _a;\n    if (v === undefined) return this._reconnectionDelayMax;\n    this._reconnectionDelayMax = v;\n    (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n    return this;\n  }\n  timeout(v) {\n    if (!arguments.length) return this._timeout;\n    this._timeout = v;\n    return this;\n  }\n  /**\n   * Starts trying to reconnect if reconnection is enabled and we have not\n   * started reconnecting yet\n   *\n   * @private\n   */\n  maybeReconnectOnOpen() {\n    // Only try to reconnect if it's the first time we're connecting\n    if (!this._reconnecting && this._reconnection && this.backoff.attempts === 0) {\n      // keeps reconnection from firing twice for the same reconnection loop\n      this.reconnect();\n    }\n  }\n  /**\n   * Sets the current transport `socket`.\n   *\n   * @param {Function} fn - optional, callback\n   * @return self\n   * @public\n   */\n  open(fn) {\n    if (~this._readyState.indexOf(\"open\")) return this;\n    this.engine = new Engine(this.uri, this.opts);\n    const socket = this.engine;\n    const self = this;\n    this._readyState = \"opening\";\n    this.skipReconnect = false;\n    // emit `open`\n    const openSubDestroy = on(socket, \"open\", function () {\n      self.onopen();\n      fn && fn();\n    });\n    const onError = err => {\n      this.cleanup();\n      this._readyState = \"closed\";\n      this.emitReserved(\"error\", err);\n      if (fn) {\n        fn(err);\n      } else {\n        // Only do this if there is no fn to handle the error\n        this.maybeReconnectOnOpen();\n      }\n    };\n    // emit `error`\n    const errorSub = on(socket, \"error\", onError);\n    if (false !== this._timeout) {\n      const timeout = this._timeout;\n      // set timer\n      const timer = this.setTimeoutFn(() => {\n        openSubDestroy();\n        onError(new Error(\"timeout\"));\n        socket.close();\n      }, timeout);\n      if (this.opts.autoUnref) {\n        timer.unref();\n      }\n      this.subs.push(() => {\n        this.clearTimeoutFn(timer);\n      });\n    }\n    this.subs.push(openSubDestroy);\n    this.subs.push(errorSub);\n    return this;\n  }\n  /**\n   * Alias for open()\n   *\n   * @return self\n   * @public\n   */\n  connect(fn) {\n    return this.open(fn);\n  }\n  /**\n   * Called upon transport open.\n   *\n   * @private\n   */\n  onopen() {\n    // clear old subs\n    this.cleanup();\n    // mark as open\n    this._readyState = \"open\";\n    this.emitReserved(\"open\");\n    // add new subs\n    const socket = this.engine;\n    this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n  }\n  /**\n   * Called upon a ping.\n   *\n   * @private\n   */\n  onping() {\n    this.emitReserved(\"ping\");\n  }\n  /**\n   * Called with data.\n   *\n   * @private\n   */\n  ondata(data) {\n    try {\n      this.decoder.add(data);\n    } catch (e) {\n      this.onclose(\"parse error\", e);\n    }\n  }\n  /**\n   * Called when parser fully decodes a packet.\n   *\n   * @private\n   */\n  ondecoded(packet) {\n    // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n    nextTick(() => {\n      this.emitReserved(\"packet\", packet);\n    }, this.setTimeoutFn);\n  }\n  /**\n   * Called upon socket error.\n   *\n   * @private\n   */\n  onerror(err) {\n    this.emitReserved(\"error\", err);\n  }\n  /**\n   * Creates a new socket for the given `nsp`.\n   *\n   * @return {Socket}\n   * @public\n   */\n  socket(nsp, opts) {\n    let socket = this.nsps[nsp];\n    if (!socket) {\n      socket = new Socket(this, nsp, opts);\n      this.nsps[nsp] = socket;\n    } else if (this._autoConnect && !socket.active) {\n      socket.connect();\n    }\n    return socket;\n  }\n  /**\n   * Called upon a socket close.\n   *\n   * @param socket\n   * @private\n   */\n  _destroy(socket) {\n    const nsps = Object.keys(this.nsps);\n    for (const nsp of nsps) {\n      const socket = this.nsps[nsp];\n      if (socket.active) {\n        return;\n      }\n    }\n    this._close();\n  }\n  /**\n   * Writes a packet.\n   *\n   * @param packet\n   * @private\n   */\n  _packet(packet) {\n    const encodedPackets = this.encoder.encode(packet);\n    for (let i = 0; i < encodedPackets.length; i++) {\n      this.engine.write(encodedPackets[i], packet.options);\n    }\n  }\n  /**\n   * Clean up transport subscriptions and packet buffer.\n   *\n   * @private\n   */\n  cleanup() {\n    this.subs.forEach(subDestroy => subDestroy());\n    this.subs.length = 0;\n    this.decoder.destroy();\n  }\n  /**\n   * Close the current socket.\n   *\n   * @private\n   */\n  _close() {\n    this.skipReconnect = true;\n    this._reconnecting = false;\n    this.onclose(\"forced close\");\n    if (this.engine) this.engine.close();\n  }\n  /**\n   * Alias for close()\n   *\n   * @private\n   */\n  disconnect() {\n    return this._close();\n  }\n  /**\n   * Called upon engine close.\n   *\n   * @private\n   */\n  onclose(reason, description) {\n    this.cleanup();\n    this.backoff.reset();\n    this._readyState = \"closed\";\n    this.emitReserved(\"close\", reason, description);\n    if (this._reconnection && !this.skipReconnect) {\n      this.reconnect();\n    }\n  }\n  /**\n   * Attempt a reconnection.\n   *\n   * @private\n   */\n  reconnect() {\n    if (this._reconnecting || this.skipReconnect) return this;\n    const self = this;\n    if (this.backoff.attempts >= this._reconnectionAttempts) {\n      this.backoff.reset();\n      this.emitReserved(\"reconnect_failed\");\n      this._reconnecting = false;\n    } else {\n      const delay = this.backoff.duration();\n      this._reconnecting = true;\n      const timer = this.setTimeoutFn(() => {\n        if (self.skipReconnect) return;\n        this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n        // check again for the case socket closed in above events\n        if (self.skipReconnect) return;\n        self.open(err => {\n          if (err) {\n            self._reconnecting = false;\n            self.reconnect();\n            this.emitReserved(\"reconnect_error\", err);\n          } else {\n            self.onreconnect();\n          }\n        });\n      }, delay);\n      if (this.opts.autoUnref) {\n        timer.unref();\n      }\n      this.subs.push(() => {\n        this.clearTimeoutFn(timer);\n      });\n    }\n  }\n  /**\n   * Called upon successful reconnect.\n   *\n   * @private\n   */\n  onreconnect() {\n    const attempt = this.backoff.attempts;\n    this._reconnecting = false;\n    this.backoff.reset();\n    this.emitReserved(\"reconnect\", attempt);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}