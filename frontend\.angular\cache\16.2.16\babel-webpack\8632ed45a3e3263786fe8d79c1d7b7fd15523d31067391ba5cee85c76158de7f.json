{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport let FileService = /*#__PURE__*/(() => {\n  class FileService {\n    constructor(apiService) {\n      this.apiService = apiService;\n    }\n    getProjectFiles(projectName) {\n      return this.apiService.getProjectFiles(projectName);\n    }\n    getFileContent(projectName, filePath) {\n      return this.apiService.getFileContent(projectName, filePath);\n    }\n    updateFileContent(projectName, filePath, content) {\n      return this.apiService.updateFileContent(projectName, filePath, content);\n    }\n    deleteFile(projectName, filePath) {\n      return this.apiService.deleteFile(projectName, filePath);\n    }\n    openProjectInExplorer(projectName) {\n      return this.apiService.openProjectInExplorer(projectName);\n    }\n    uploadFile(projectName, formData) {\n      return this.apiService.uploadFile(projectName, formData);\n    }\n    resetProject(projectName) {\n      return this.apiService.resetProject(projectName);\n    }\n    static {\n      this.ɵfac = function FileService_Factory(t) {\n        return new (t || FileService)(i0.ɵɵinject(i1.ApiService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FileService,\n        factory: FileService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}