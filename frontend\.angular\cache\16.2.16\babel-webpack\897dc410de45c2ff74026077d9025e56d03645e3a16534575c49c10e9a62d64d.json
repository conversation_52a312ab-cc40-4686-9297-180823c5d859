{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { HttpClientModule } from '@angular/common/http';\nimport { RouterModule } from '@angular/router';\nimport { MonacoEditorModule } from 'ngx-monaco-editor-v2';\nimport { AppComponent } from './app.component';\nimport { ProjectListComponent } from './components/project-list/project-list.component';\nimport { ProjectDetailComponent } from './components/project-detail/project-detail.component';\nimport { ConfigComponent } from './components/config/config.component';\nimport { HomeComponent } from './components/home/<USER>';\nimport { TestingComponent } from './components/testing/testing.component';\nimport { ApiService } from './services/api.service';\nimport { SocketService } from './services/socket.service';\nimport { SocketFactoryService } from './services/socket-factory.service';\nimport { ProjectService } from './services/project.service';\nimport { FileService } from './services/file.service';\nimport { AgentService } from './services/agent.service';\nimport { NotificationService } from './services/notification.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-monaco-editor-v2\";\nconst routes = [{\n  path: '',\n  component: HomeComponent\n}, {\n  path: 'projects',\n  component: ProjectListComponent\n}, {\n  path: 'projects/:name',\n  component: ProjectDetailComponent\n}, {\n  path: 'config',\n  component: ConfigComponent\n}, {\n  path: 'testing',\n  component: TestingComponent\n},\n// Add a route if needed for ChatGPTCopilotComponent\n// { path: 'copilot', component: ChatGPTCopilotComponent },\n{\n  path: '**',\n  redirectTo: ''\n}];\nexport let AppModule = /*#__PURE__*/(() => {\n  class AppModule {\n    static {\n      this.ɵfac = function AppModule_Factory(t) {\n        return new (t || AppModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppModule,\n        bootstrap: [AppComponent]\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [ApiService, SocketService, SocketFactoryService, ProjectService, FileService, AgentService, NotificationService],\n        imports: [BrowserModule, CommonModule, FormsModule, ReactiveFormsModule, HttpClientModule, RouterModule.forRoot(routes), MonacoEditorModule.forRoot()]\n      });\n    }\n  }\n  return AppModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}