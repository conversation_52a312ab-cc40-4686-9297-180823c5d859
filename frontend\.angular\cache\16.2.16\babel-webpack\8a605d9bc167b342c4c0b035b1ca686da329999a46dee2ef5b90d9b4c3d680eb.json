{"ast": null, "code": "import { Subject, throwError, of } from 'rxjs';\nimport { debounceTime, catchError, retry, finalize, first } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/project.service\";\nimport * as i3 from \"../../services/socket.service\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../chat/chat.component\";\nimport * as i7 from \"../code-editor/code-editor.component\";\nimport * as i8 from \"../browser-preview/browser-preview.component\";\nimport * as i9 from \"../file-explorer/file-explorer.component\";\nfunction ProjectDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"div\", 11);\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"div\", 13)(4, \"span\", 14);\n    i0.ɵɵtext(5, \"Current Stage: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 15);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 16)(10, \"div\", 17);\n    i0.ɵɵelement(11, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 19);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 20);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 5, ctx_r0.agentStage));\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.agentProgress, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.agentProgress, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.agentStatusMessage);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_13_div_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.toggleFilesMenu());\n    });\n    i0.ɵɵtext(3, \"\\u25C0\\uFE0F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"app-file-explorer\", 32);\n    i0.ɵɵlistener(\"fileSelectEvent\", function ProjectDetailComponent_div_13_div_1_Template_app_file_explorer_fileSelectEvent_4_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onFileSelect($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"projectName\", ctx_r3.projectName);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_13_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.toggleFilesMenu());\n    });\n    i0.ɵɵtext(2, \"\\u25B6\\uFE0F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectDetailComponent_div_13_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-code-editor\", 37);\n    i0.ɵɵlistener(\"fileChangeEvent\", function ProjectDetailComponent_div_13_div_4_ng_container_1_Template_app_code_editor_fileChangeEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r14.onFileChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"projectName\", ctx_r12.projectName)(\"filePath\", ctx_r12.selectedFile == null ? null : ctx_r12.selectedFile.path);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"iframe\", 38);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r13.codeServerUrl, i0.ɵɵsanitizeResourceUrl);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_13_div_4_ng_container_1_Template, 2, 2, \"ng-container\", 36);\n    i0.ɵɵtemplate(2, ProjectDetailComponent_div_13_div_4_ng_container_2_Template, 2, 1, \"ng-container\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.showVSCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showVSCode);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-chat\", 42);\n    i0.ɵɵlistener(\"messageEvent\", function ProjectDetailComponent_div_13_div_7_ng_container_1_Template_app_chat_messageEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.onMessageEvent($event));\n    })(\"chatExpandChange\", function ProjectDetailComponent_div_13_div_7_ng_container_1_Template_app_chat_chatExpandChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.setChatExpanded($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"projectName\", ctx_r16.projectName)(\"messagesLoading\", ctx_r16.chatMessagesLoading)(\"messagesSaving\", ctx_r16.chatMessagesSaving);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"span\", 44);\n    i0.ɵɵtext(2, \"Chat with AI Agent\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_13_div_7_ng_template_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.setChatExpanded(true));\n    });\n    i0.ɵɵtext(4, \"\\u25B2\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"collapsed\": a0,\n    \"expanded\": a1\n  };\n};\nfunction ProjectDetailComponent_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_13_div_7_ng_container_1_Template, 2, 3, \"ng-container\", 40);\n    i0.ɵɵtemplate(2, ProjectDetailComponent_div_13_div_7_ng_template_2_Template, 5, 0, \"ng-template\", null, 41, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r17 = i0.ɵɵreference(3);\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c0, !ctx_r6.isChatExpanded, ctx_r6.isChatExpanded));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isChatExpanded)(\"ngIfElse\", _r17);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"files-collapsed\": a0\n  };\n};\nfunction ProjectDetailComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_13_div_1_Template, 5, 1, \"div\", 22);\n    i0.ɵɵtemplate(2, ProjectDetailComponent_div_13_div_2_Template, 3, 0, \"div\", 23);\n    i0.ɵɵelementStart(3, \"div\", 24);\n    i0.ɵɵtemplate(4, ProjectDetailComponent_div_13_div_4_Template, 3, 2, \"div\", 25);\n    i0.ɵɵelementStart(5, \"div\", 26)(6, \"app-browser-preview\", 27);\n    i0.ɵɵlistener(\"expandChange\", function ProjectDetailComponent_div_13_Template_app_browser_preview_expandChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.onBrowserPreviewExpand($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, ProjectDetailComponent_div_13_div_7_Template, 4, 6, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r1.filesCollapsed));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isBrowserPreviewExpanded && !ctx_r1.filesCollapsed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filesCollapsed && !ctx_r1.isBrowserPreviewExpanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isBrowserPreviewExpanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"projectName\", ctx_r1.projectName)(\"url\", ctx_r1.previewUrl)(\"codeServerPort\", ctx_r1.codeServerPort);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isBrowserPreviewExpanded);\n  }\n}\nfunction ProjectDetailComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"div\", 47);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading project...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ProjectDetailComponent {\n  constructor(route, projectService, socketService, router, sanitizer) {\n    this.route = route;\n    this.projectService = projectService;\n    this.socketService = socketService;\n    this.router = router;\n    this.sanitizer = sanitizer;\n    this.projectName = '';\n    this.previewUrl = '';\n    this.loading = false;\n    this.isBrowserPreviewExpanded = false;\n    this.filesCollapsed = false;\n    this.isChatExpanded = false;\n    this.showVSCode = false;\n    this.codeServerUrl = '';\n    this.codeServerPort = 8081; // Default code server port\n    // Agent workflow status tracking\n    this.agentActive = false;\n    this.agentStage = '';\n    this.agentProgress = 0;\n    this.agentStatusMessage = '';\n    this.workflowStages = {\n      'setup': {\n        status: 'pending',\n        progress: 0\n      },\n      'research': {\n        status: 'pending',\n        progress: 0\n      },\n      'planning': {\n        status: 'pending',\n        progress: 0\n      },\n      'implementation': {\n        status: 'pending',\n        progress: 0\n      },\n      'testing': {\n        status: 'pending',\n        progress: 0\n      },\n      'validation': {\n        status: 'pending',\n        progress: 0\n      }\n    };\n    // For debounced message saving\n    this.saveMessagesSubject = new Subject();\n    this.subscriptions = [];\n    // Loading states\n    this.chatMessagesLoading = false;\n    this.chatMessagesSaving = false;\n  }\n  ngOnInit() {\n    console.log('[ProjectDetailComponent] ngOnInit called');\n    // Set up debounced message saving (500ms delay)\n    const saveMessagesSub = this.saveMessagesSubject.pipe(debounceTime(500) // Wait for 500ms of quiet time before saving\n    ).subscribe(() => this.saveCurrentMessages());\n    this.subscriptions.push(saveMessagesSub);\n    const routeSub = this.route.params.subscribe(params => {\n      this.projectName = params['name'];\n      console.log('[ProjectDetailComponent] Route param project name:', this.projectName);\n      this.setCodeServerUrl();\n      this.loadProject();\n      this.setupSocketListeners();\n    });\n    this.subscriptions.push(routeSub);\n  }\n  loadProject() {\n    console.log('[ProjectDetailComponent] loadProject called');\n    if (!this.projectName) {\n      console.warn('[ProjectDetailComponent] No project name available. Skipping project load.');\n      return;\n    }\n    this.loading = true;\n    console.log('[ProjectDetailComponent] Fetching project:', this.projectName);\n    this.projectService.getProject(this.projectName).subscribe(response => {\n      console.log('[ProjectDetailComponent] Project fetched successfully:', response);\n      this.project = response.project;\n      this.loading = false;\n      if (this.project.preview_url) {\n        this.previewUrl = this.project.preview_url;\n        console.log('[ProjectDetailComponent] Initial preview URL set:', this.previewUrl);\n      }\n      // Load chat messages and expanded state\n      this.loadChatState();\n    }, error => {\n      console.error('[ProjectDetailComponent] ❌ Error loading project:', error);\n      this.loading = false;\n    });\n  }\n  /**\n   * Load chat messages and expanded state from the backend\n   */\n  loadChatState() {\n    console.log('[ProjectDetailComponent] Loading chat state for:', this.projectName);\n    if (!this.projectName) return;\n    this.chatMessagesLoading = true;\n    this.projectService.getProjectMessages(this.projectName).pipe(retry(1), finalize(() => {\n      this.chatMessagesLoading = false;\n      console.log('[ProjectDetailComponent] Chat state loading completed');\n    })).subscribe(response => {\n      console.log('[ProjectDetailComponent] Chat state loaded:', response);\n      // Set the chat expanded state from the server response\n      if (response.chatExpanded !== undefined) {\n        this.isChatExpanded = response.chatExpanded;\n        console.log('[ProjectDetailComponent] Chat expanded state set to:', this.isChatExpanded);\n      }\n    }, error => {\n      console.error('[ProjectDetailComponent] Error loading chat state:', error);\n    });\n  }\n  setupSocketListeners() {\n    console.log('[ProjectDetailComponent] Setting up socket listeners');\n    // Listen for agent status updates\n    this.socketService.on('agent_status').subscribe(data => {\n      console.log('[ProjectDetailComponent] 🤖 Received agent_status event:', data);\n      if (data.project_name === this.projectName) {\n        this.agentActive = data.active || false;\n        if (data.stage) {\n          this.agentStage = data.stage;\n        }\n        if (data.progress !== undefined) {\n          this.agentProgress = data.progress;\n        }\n        if (data.message) {\n          this.agentStatusMessage = data.message;\n        }\n        // Update specific stage status if provided\n        if (data.stages && typeof data.stages === 'object') {\n          Object.keys(data.stages).forEach(stage => {\n            if (this.workflowStages[stage]) {\n              this.workflowStages[stage] = data.stages[stage];\n            }\n          });\n        }\n      }\n    });\n    this.socketService.on('file_updated').subscribe(data => {\n      console.log('[ProjectDetailComponent] 🔄 Received file_updated event from socket:', data);\n      if (data.project_name === this.projectName) {\n        console.log('[ProjectDetailComponent] file_updated matches current project');\n        if (this.selectedFile && this.selectedFile.path === data.file_path) {\n          console.log('[ProjectDetailComponent] Updating selected file view due to match:', data.file_path);\n          this.onFileSelect({\n            type: 'file_selected',\n            file: this.selectedFile\n          });\n        }\n        if (data.file_path.endsWith('.html')) {\n          console.log('[ProjectDetailComponent] HTML file updated, refreshing preview');\n          this.updatePreview();\n        }\n      }\n    });\n    this.socketService.on('preview_updated').subscribe(data => {\n      console.log('[ProjectDetailComponent] 📺 Received preview_updated event:', data);\n      if (data.project_name === this.projectName) {\n        this.previewUrl = data.preview_url;\n        console.log('[ProjectDetailComponent] Preview URL updated via socket:', this.previewUrl);\n      }\n    });\n    // Listen for project setup completion\n    this.socketService.on('project_setup_complete').subscribe(data => {\n      console.log('[ProjectDetailComponent] 🚀 Received project_setup_complete event:', data);\n      if (data.project_name === this.projectName) {\n        // Update project data with new information\n        if (!this.project) this.project = {};\n        this.project.port = data.port;\n        this.project.actual_dir = data.project_dir;\n        // Update preview URL with the assigned port\n        this.previewUrl = `http://localhost:${data.port}`;\n        console.log('[ProjectDetailComponent] Preview URL updated for new project:', this.previewUrl);\n        // Show VS Code with the correct project directory\n        this.showVSCode = true;\n        this.setCodeServerUrl();\n      }\n    });\n    // Listen for agent_complete to show VS Code/code-server\n    this.socketService.on('agent_complete').subscribe(data => {\n      if (data.project_name === this.projectName) {\n        this.showVSCode = true;\n        // Reset agent workflow status\n        this.agentActive = false;\n        this.agentProgress = 100;\n        this.agentStatusMessage = 'Agent workflow completed successfully';\n        // Set all stages to complete\n        Object.keys(this.workflowStages).forEach(stage => {\n          this.workflowStages[stage].status = 'completed';\n          this.workflowStages[stage].progress = 100;\n        });\n      }\n    });\n  }\n  onFileSelect(event) {\n    console.log('[ProjectDetailComponent] onFileSelect triggered:', event);\n    if (event.type === 'file_selected') {\n      this.selectedFile = event.file;\n      console.log('[ProjectDetailComponent] Selected file set:', this.selectedFile);\n      if (this.selectedFile.path.endsWith('.html')) {\n        console.log('[ProjectDetailComponent] Selected file is HTML. Calling updatePreview.');\n        this.updatePreview();\n      }\n    }\n  }\n  onFileChange(event) {\n    console.log('[ProjectDetailComponent] onFileChange triggered:', event);\n    if (event.type === 'file_saved') {\n      if (event.filePath.endsWith('.html')) {\n        console.log('[ProjectDetailComponent] Saved file is HTML. Updating preview.');\n        this.updatePreview();\n      }\n    }\n  }\n  onMessageEvent(event) {\n    console.log('[ProjectDetailComponent] onMessageEvent triggered:', event);\n    if (event.type === 'message_sent' || event.type === 'message_received') {\n      console.log('[ProjectDetailComponent] Message event received. Triggering debounced save.');\n      // Trigger debounced save\n      this.saveMessagesSubject.next();\n    }\n  }\n  updatePreview() {\n    console.log('[ProjectDetailComponent] updatePreview called');\n    if (this.selectedFile && this.selectedFile.path.endsWith('.html')) {\n      const newPreviewUrl = `http://localhost:5000/projects/${this.projectName}/preview/${this.selectedFile.path}`;\n      console.log('[ProjectDetailComponent] Updating preview URL to:', newPreviewUrl);\n      this.previewUrl = newPreviewUrl;\n    } else {\n      console.warn('[ProjectDetailComponent] No HTML file selected. Skipping preview update.');\n    }\n  }\n  onBrowserPreviewExpand(expanded) {\n    this.isBrowserPreviewExpanded = expanded;\n  }\n  toggleFilesMenu() {\n    this.filesCollapsed = !this.filesCollapsed;\n  }\n  setChatExpanded(expanded) {\n    this.isChatExpanded = expanded;\n    console.log('[ProjectDetailComponent] Chat expanded state changed to:', expanded);\n    // Trigger debounced save to persist expanded state\n    this.saveMessagesSubject.next();\n  }\n  deleteProject() {\n    if (!this.projectName) return;\n    if (!confirm('Are you sure you want to delete this project? This cannot be undone.')) return;\n    this.loading = true;\n    this.projectService.deleteProject(this.projectName).subscribe(() => {\n      this.loading = false;\n      this.router.navigate(['/projects']);\n    }, error => {\n      this.loading = false;\n      alert('Failed to delete project: ' + (error?.error?.detail || error));\n    });\n  }\n  resetProject() {\n    if (!this.projectName) return;\n    if (!confirm('Are you sure you want to reset this project? All files will be deleted, but the folder will remain.')) return;\n    this.loading = true;\n    this.projectService.resetProject(this.projectName).subscribe(() => {\n      this.loading = false;\n      this.loadProject();\n    }, error => {\n      this.loading = false;\n      alert('Failed to reset project: ' + (error?.error?.detail || error));\n    });\n  }\n  toggleVSCode() {\n    this.showVSCode = !this.showVSCode;\n  }\n  setCodeServerUrl() {\n    // Get the port from the project if available, otherwise use default\n    this.codeServerPort = this.project?.port || 8081;\n    // Get the actual project directory path if available\n    const projectPath = this.project?.actual_dir || `/home/<USER>/projects/${this.projectName}`;\n    // Create the code-server URL with the correct port and project path\n    const url = `http://localhost:${this.codeServerPort}/?folder=${encodeURIComponent(projectPath)}`;\n    console.log('[ProjectDetailComponent] Setting code-server URL:', url);\n    this.codeServerUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);\n  }\n  /**\n   * Clean up resources when component is destroyed and save any pending changes\n   */\n  ngOnDestroy() {\n    console.log('[ProjectDetailComponent] ngOnDestroy called, cleaning up subscriptions');\n    // Save any pending changes immediately (don't wait for debounce)\n    this.saveCurrentMessagesImmediately();\n    // Unsubscribe from all subscriptions to prevent memory leaks\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.saveMessagesSubject.complete();\n  }\n  /**\n   * Save the current chat messages immediately without debouncing\n   * This is used when the component is about to be destroyed\n   */\n  saveCurrentMessagesImmediately() {\n    if (!this.projectName || !this.isChatExpanded) return;\n    console.log('[ProjectDetailComponent] Saving chat messages immediately before navigation');\n    // Get the messages and save them with current expanded state\n    this.projectService.getProjectMessages(this.projectName).pipe(\n    // Use first() to complete after getting the first response\n    // so we don't need to unsubscribe from it\n    // This is important for navigation events\n    first(), catchError(error => {\n      console.error('[ProjectDetailComponent] Error fetching messages for immediate save:', error);\n      return of({\n        messages: []\n      });\n    })).subscribe(response => {\n      const messages = response.messages || [];\n      this.projectService.saveProjectMessages(this.projectName, messages, this.isChatExpanded).pipe(first()).subscribe({\n        next: () => console.log('[ProjectDetailComponent] Chat messages saved successfully before navigation'),\n        error: error => console.error('[ProjectDetailComponent] Error saving messages before navigation:', error)\n      });\n    });\n  }\n  /**\n   * Save the current chat messages and expanded state\n   * This is called after debouncing to prevent excessive API calls\n   */\n  saveCurrentMessages() {\n    if (!this.projectName) return;\n    // Set saving state to display UI indicators if needed\n    this.chatMessagesSaving = true;\n    console.log('[ProjectDetailComponent] Saving chat messages (debounced)');\n    this.projectService.getProjectMessages(this.projectName).pipe(\n    // Retry up to 2 times with increasing delays\n    retry(2), catchError(error => {\n      console.error('[ProjectDetailComponent] Error fetching messages to save:', error);\n      // Return empty array instead of error to allow chain to continue\n      return of({\n        messages: [],\n        chatExpanded: this.isChatExpanded\n      });\n    })).subscribe(response => {\n      const messages = response.messages || [];\n      // Save the messages along with the current expanded state\n      this.projectService.saveProjectMessages(this.projectName, messages, this.isChatExpanded).pipe(\n      // Retry saving once if it fails\n      retry(1), catchError(error => {\n        console.error('[ProjectDetailComponent] Error saving chat messages:', error);\n        return throwError(() => new Error('Failed to save chat messages after retry'));\n      }),\n      // Always turn off saving state when operation completes (success or error)\n      finalize(() => {\n        this.chatMessagesSaving = false;\n        console.log('[ProjectDetailComponent] Message save operation finished');\n      })).subscribe({\n        next: () => console.log('[ProjectDetailComponent] Chat messages saved successfully'),\n        error: error => console.error('[ProjectDetailComponent] Final error saving messages:', error)\n      });\n    });\n  }\n  static {\n    this.ɵfac = function ProjectDetailComponent_Factory(t) {\n      return new (t || ProjectDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.SocketService), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i4.DomSanitizer));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectDetailComponent,\n      selectors: [[\"app-project-detail\"]],\n      decls: 15,\n      vars: 7,\n      consts: [[1, \"project-container\"], [1, \"project-header\"], [1, \"project-title-and-status\"], [\"class\", \"agent-workflow-status\", 4, \"ngIf\"], [1, \"project-actions\"], [3, \"disabled\", \"click\"], [2, \"margin-left\", \"8px\", \"color\", \"red\", 3, \"disabled\", \"click\"], [2, \"margin-left\", \"8px\", 3, \"disabled\", \"click\"], [\"class\", \"project-content\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"agent-workflow-status\"], [1, \"status-indicator\", \"pulsing\"], [1, \"status-details\"], [1, \"current-stage\"], [1, \"stage-label\"], [1, \"stage-value\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"status-message\"], [1, \"project-content\", 3, \"ngClass\"], [\"class\", \"sidebar\", 4, \"ngIf\"], [\"class\", \"sidebar-collapsed-bar\", 4, \"ngIf\"], [1, \"main-content\"], [\"class\", \"editor-section\", 4, \"ngIf\"], [1, \"preview-section\"], [3, \"projectName\", \"url\", \"codeServerPort\", \"expandChange\"], [\"class\", \"chat-section\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"sidebar\"], [1, \"sidebar-header\", 2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"flex-end\", \"padding\", \"4px\"], [\"title\", \"Collapse Files Menu\", 3, \"click\"], [3, \"projectName\", \"fileSelectEvent\"], [1, \"sidebar-collapsed-bar\"], [\"title\", \"Expand Files Menu\", 3, \"click\"], [1, \"editor-section\"], [4, \"ngIf\"], [3, \"projectName\", \"filePath\", \"fileChangeEvent\"], [\"title\", \"VS Code (code-server)\", 1, \"code-server-iframe\", 3, \"src\"], [1, \"chat-section\", 3, \"ngClass\"], [4, \"ngIf\", \"ngIfElse\"], [\"collapsedHeader\", \"\"], [3, \"projectName\", \"messagesLoading\", \"messagesSaving\", \"messageEvent\", \"chatExpandChange\"], [1, \"chat-header-collapsed\"], [1, \"chat-label\"], [\"aria-label\", \"Expand Chat\", 1, \"expand-chat-btn\", 3, \"click\"], [1, \"loading-container\"], [1, \"spinner\"]],\n      template: function ProjectDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, ProjectDetailComponent_div_5_Template, 16, 7, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function ProjectDetailComponent_Template_button_click_7_listener() {\n            return ctx.loadProject();\n          });\n          i0.ɵɵtext(8, \" Refresh \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function ProjectDetailComponent_Template_button_click_9_listener() {\n            return ctx.deleteProject();\n          });\n          i0.ɵɵtext(10, \"Delete Project\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function ProjectDetailComponent_Template_button_click_11_listener() {\n            return ctx.resetProject();\n          });\n          i0.ɵɵtext(12, \"Reset Project\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(13, ProjectDetailComponent_div_13_Template, 8, 10, \"div\", 8);\n          i0.ɵɵtemplate(14, ProjectDetailComponent_div_14_Template, 4, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.projectName);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.agentActive);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.agentActive);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.agentActive);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.agentActive);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i6.ChatComponent, i7.CodeEditorComponent, i8.BrowserPreviewComponent, i9.FileExplorerComponent, i5.TitleCasePipe],\n      styles: [\".project-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n  padding: 0;\\n  overflow: hidden;\\n}\\n\\n.project-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  padding: 18px 32px 18px 32px;\\n  background: linear-gradient(90deg, #4f8cff 70%, #a0c4ff 100%);\\n  color: #fff;\\n  border-top-left-radius: 18px;\\n  border-top-right-radius: 18px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  font-size: 22px;\\n  font-weight: 700;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 500;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: rgba(255, 255, 255, 0.15);\\n  border-radius: 8px;\\n  padding: 8px 12px;\\n  margin-top: 5px;\\n  max-width: 500px;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background-color: #4caf50;\\n  margin-right: 10px;\\n  flex-shrink: 0;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-indicator.pulsing[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .current-stage[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .current-stage[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .current-stage[_ngcontent-%COMP%]   .stage-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 4px;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background-color: rgba(255, 255, 255, 0.2);\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-right: 8px;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background-color: #4caf50;\\n  border-radius: 4px;\\n  transition: width 0.5s ease-in-out;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  min-width: 35px;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .status-message[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  opacity: 0.9;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 8px 18px;\\n  background: linear-gradient(90deg, #4f8cff 80%, #a0c4ff 100%);\\n  color: #fff;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 15px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  margin: 0 6px 0 0;\\n  transition: background 0.2s, box-shadow 0.2s;\\n}\\n.project-header[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #4f8cff;\\n  box-shadow: 0 2px 8px rgba(79, 140, 255, 0.18);\\n}\\n.project-header[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  background-color: #bbdefb;\\n  cursor: not-allowed;\\n}\\n\\n.project-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 0;\\n  background: linear-gradient(135deg, #fafdff 80%, #e3f0ff 100%);\\n  padding: 0;\\n  gap: 0;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  min-height: 0;\\n  overflow: auto;\\n  display: flex;\\n  flex-direction: row;\\n  gap: 0;\\n  padding: 24px 24px 0 24px;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.7s;\\n}\\n\\n.editor-section[_ngcontent-%COMP%], .preview-section[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n  overflow: hidden;\\n}\\n\\n.editor-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  min-height: 400px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.code-server-iframe[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  min-height: 400px;\\n  border: none;\\n  border-radius: 8px;\\n  background: #fff;\\n  flex: 1 1 auto;\\n  display: block;\\n}\\n\\n.chat-section[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  width: 100%;\\n  background: #fafdff;\\n  border-top: 1px solid #e0e0e0;\\n  box-shadow: 0 -2px 12px rgba(79, 140, 255, 0.08);\\n  z-index: 2;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-end;\\n  border-bottom-left-radius: 8px;\\n  border-bottom-right-radius: 8px;\\n  animation: _ngcontent-%COMP%_fadeInChat 0.8s;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(40px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInChat {\\n  from {\\n    opacity: 0;\\n    transform: translateY(60px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);\\n    opacity: 1;\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.project-content.files-collapsed[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  flex: 1 1 100%;\\n  width: 100%;\\n  margin-left: 0;\\n}\\n\\n.project-content.files-collapsed[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\\n  display: none !important;\\n}\\n\\n.chat-section.collapsed[_ngcontent-%COMP%] {\\n  height: 40px;\\n  min-height: 40px;\\n  max-height: 40px;\\n  overflow: hidden;\\n  transition: max-height 0.3s, min-height 0.3s, height 0.3s;\\n}\\n\\n.chat-section.expanded[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 120px;\\n  max-height: 600px;\\n  transition: max-height 0.3s, min-height 0.3s, height 0.3s;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border: 5px solid #f3f3f3;\\n  border-top: 5px solid #4f8cff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  font-size: 16px;\\n  color: #666;\\n}\\n\\n\\n\\n@media (max-width: 900px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: 12px 6px 0 6px;\\n  }\\n  .project-header[_ngcontent-%COMP%] {\\n    padding: 12px 8px 12px 8px;\\n    font-size: 18px;\\n  }\\n  .chat-section[_ngcontent-%COMP%] {\\n    border-bottom-left-radius: 6px;\\n    border-bottom-right-radius: 6px;\\n  }\\n}\\n.sidebar-collapsed-bar[_ngcontent-%COMP%] {\\n  width: 20px;\\n  min-width: 20px;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #f5f5f5;\\n  border-right: 1px solid #e0e0e0;\\n  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.03);\\n  z-index: 2;\\n}\\n\\n.sidebar-collapsed-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: #2196f3;\\n  color: #fff;\\n  border: none;\\n  border-radius: 4px;\\n  padding: 4px 6px;\\n  font-size: 16px;\\n  cursor: pointer;\\n  transition: background 0.2s;\\n}\\n\\n.sidebar-collapsed-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #1976d2;\\n}\\n\\n.chat-header-collapsed[_ngcontent-%COMP%] {\\n  position: sticky;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n  background: linear-gradient(90deg, #4f8cff 80%, #a0c4ff 100%);\\n  color: #fff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 40px;\\n  min-height: 40px;\\n  max-height: 40px;\\n  padding: 0 24px;\\n  border-top: 1px solid #e0e0e0;\\n  border-bottom-left-radius: 8px;\\n  border-bottom-right-radius: 8px;\\n  box-shadow: 0 -2px 12px rgba(79, 140, 255, 0.08);\\n  font-size: 16px;\\n  font-weight: 600;\\n}\\n\\n.chat-header-collapsed[_ngcontent-%COMP%]   .expand-chat-btn[_ngcontent-%COMP%] {\\n  background: #fff;\\n  color: #4f8cff;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 18px;\\n  font-weight: 700;\\n  cursor: pointer;\\n  padding: 2px 12px;\\n  margin-left: 12px;\\n  transition: background 0.2s, color 0.2s;\\n}\\n\\n.chat-header-collapsed[_ngcontent-%COMP%]   .expand-chat-btn[_ngcontent-%COMP%]:hover {\\n  background: #e3f0ff;\\n  color: #1976d2;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "throwError", "of", "debounceTime", "catchError", "retry", "finalize", "first", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ctx_r0", "agentStage", "ɵɵstyleProp", "agentProgress", "ɵɵtextInterpolate1", "agentStatusMessage", "ɵɵlistener", "ProjectDetailComponent_div_13_div_1_Template_button_click_2_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "toggleFilesMenu", "ProjectDetailComponent_div_13_div_1_Template_app_file_explorer_fileSelectEvent_4_listener", "$event", "ctx_r9", "onFileSelect", "ɵɵproperty", "ctx_r3", "projectName", "ProjectDetailComponent_div_13_div_2_Template_button_click_1_listener", "_r11", "ctx_r10", "ɵɵelementContainerStart", "ProjectDetailComponent_div_13_div_4_ng_container_1_Template_app_code_editor_fileChangeEvent_1_listener", "_r15", "ctx_r14", "onFileChange", "ɵɵelementContainerEnd", "ctx_r12", "selectedFile", "path", "ctx_r13", "codeServerUrl", "ɵɵsanitizeResourceUrl", "ɵɵtemplate", "ProjectDetailComponent_div_13_div_4_ng_container_1_Template", "ProjectDetailComponent_div_13_div_4_ng_container_2_Template", "ctx_r5", "showVSCode", "ProjectDetailComponent_div_13_div_7_ng_container_1_Template_app_chat_messageEvent_1_listener", "_r20", "ctx_r19", "onMessageEvent", "ProjectDetailComponent_div_13_div_7_ng_container_1_Template_app_chat_chatExpandChange_1_listener", "ctx_r21", "setChatExpanded", "ctx_r16", "chatMessagesLoading", "chatMessagesSaving", "ProjectDetailComponent_div_13_div_7_ng_template_2_Template_button_click_3_listener", "_r23", "ctx_r22", "ProjectDetailComponent_div_13_div_7_ng_container_1_Template", "ProjectDetailComponent_div_13_div_7_ng_template_2_Template", "ɵɵtemplateRefExtractor", "ɵɵpureFunction2", "_c0", "ctx_r6", "isChatExpanded", "_r17", "ProjectDetailComponent_div_13_div_1_Template", "ProjectDetailComponent_div_13_div_2_Template", "ProjectDetailComponent_div_13_div_4_Template", "ProjectDetailComponent_div_13_Template_app_browser_preview_expandChange_6_listener", "_r25", "ctx_r24", "onBrowserPreviewExpand", "ProjectDetailComponent_div_13_div_7_Template", "ɵɵpureFunction1", "_c1", "ctx_r1", "filesCollapsed", "isBrowserPreviewExpanded", "previewUrl", "codeServerPort", "ProjectDetailComponent", "constructor", "route", "projectService", "socketService", "router", "sanitizer", "loading", "agentActive", "workflowStages", "status", "progress", "saveMessagesSubject", "subscriptions", "ngOnInit", "console", "log", "saveMessagesSub", "pipe", "subscribe", "saveCurrentMessages", "push", "routeSub", "params", "setCodeServerUrl", "loadProject", "setupSocketListeners", "warn", "getProject", "response", "project", "preview_url", "loadChatState", "error", "getProjectMessages", "chatExpanded", "undefined", "on", "data", "project_name", "active", "stage", "message", "stages", "Object", "keys", "for<PERSON>ach", "file_path", "type", "file", "endsWith", "updatePreview", "port", "actual_dir", "project_dir", "event", "filePath", "next", "newPreviewUrl", "expanded", "deleteProject", "confirm", "navigate", "alert", "detail", "resetProject", "toggleVSCode", "projectPath", "url", "encodeURIComponent", "bypassSecurityTrustResourceUrl", "ngOnDestroy", "saveCurrentMessagesImmediately", "sub", "unsubscribe", "complete", "messages", "saveProjectMessages", "Error", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ProjectService", "i3", "SocketService", "Router", "i4", "Dom<PERSON><PERSON><PERSON>zer", "selectors", "decls", "vars", "consts", "template", "ProjectDetailComponent_Template", "rf", "ctx", "ProjectDetailComponent_div_5_Template", "ProjectDetailComponent_Template_button_click_7_listener", "ProjectDetailComponent_Template_button_click_9_listener", "ProjectDetailComponent_Template_button_click_11_listener", "ProjectDetailComponent_div_13_Template", "ProjectDetailComponent_div_14_Template"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\project-detail\\project-detail.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\project-detail\\project-detail.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ProjectService } from '../../services/project.service';\nimport { SocketService } from '../../services/socket.service';\nimport { Dom<PERSON>anitizer, SafeResourceUrl } from '@angular/platform-browser';\nimport { Subject, Subscription, throwError, of } from 'rxjs';\nimport { debounceTime, catchError, retry, finalize, first } from 'rxjs/operators';\n\ninterface FileUpdateData {\n  project_name: string;\n  file_path: string;\n}\n\ninterface PreviewUpdateData {\n  project_name: string;\n  preview_url: string;\n}\n\n@Component({\n  selector: 'app-project-detail',\n  templateUrl: './project-detail.component.html',\n  styleUrls: ['./project-detail.component.scss']\n})\nexport class ProjectDetailComponent implements OnInit, OnDestroy {\n  projectName: string = '';\n  project: any;\n  selectedFile: any;\n  previewUrl: string = '';\n  loading: boolean = false;\n  isBrowserPreviewExpanded = false;\n  filesCollapsed = false;\n  isChatExpanded = false;\n  showVSCode: boolean = false;\n  codeServerUrl: SafeResourceUrl = '';\n  codeServerPort: number = 8081;  // Default code server port\n  \n  // Agent workflow status tracking\n  agentActive: boolean = false;\n  agentStage: string = '';\n  agentProgress: number = 0;\n  agentStatusMessage: string = '';\n  workflowStages: {[key: string]: {status: string, progress: number}} = {\n    'setup': {status: 'pending', progress: 0},\n    'research': {status: 'pending', progress: 0},\n    'planning': {status: 'pending', progress: 0},\n    'implementation': {status: 'pending', progress: 0},\n    'testing': {status: 'pending', progress: 0},\n    'validation': {status: 'pending', progress: 0}\n  }\n  \n  // For debounced message saving\n  private saveMessagesSubject = new Subject<void>();\n  private subscriptions: Subscription[] = [];\n  \n  // Loading states\n  chatMessagesLoading: boolean = false;\n  chatMessagesSaving: boolean = false;\n  \n  constructor(\n    private route: ActivatedRoute,\n    private projectService: ProjectService,\n    private socketService: SocketService,\n    private router: Router,\n    private sanitizer: DomSanitizer\n  ) { }\n\n  ngOnInit(): void {\n    console.log('[ProjectDetailComponent] ngOnInit called');\n    \n    // Set up debounced message saving (500ms delay)\n    const saveMessagesSub = this.saveMessagesSubject.pipe(\n      debounceTime(500) // Wait for 500ms of quiet time before saving\n    ).subscribe(() => this.saveCurrentMessages());\n    this.subscriptions.push(saveMessagesSub);\n    \n    const routeSub = this.route.params.subscribe(params => {\n      this.projectName = params['name'];\n      console.log('[ProjectDetailComponent] Route param project name:', this.projectName);\n      this.setCodeServerUrl();\n      this.loadProject();\n      this.setupSocketListeners();\n    });\n    this.subscriptions.push(routeSub);\n  }\n\n  loadProject(): void {\n    console.log('[ProjectDetailComponent] loadProject called');\n    if (!this.projectName) {\n      console.warn('[ProjectDetailComponent] No project name available. Skipping project load.');\n      return;\n    }\n\n    this.loading = true;\n    console.log('[ProjectDetailComponent] Fetching project:', this.projectName);\n\n    this.projectService.getProject(this.projectName).subscribe(\n      (response) => {\n        console.log('[ProjectDetailComponent] Project fetched successfully:', response);\n        this.project = response.project;\n        this.loading = false;\n\n        if (this.project.preview_url) {\n          this.previewUrl = this.project.preview_url;\n          console.log('[ProjectDetailComponent] Initial preview URL set:', this.previewUrl);\n        }\n        \n        // Load chat messages and expanded state\n        this.loadChatState();\n      },\n      (error) => {\n        console.error('[ProjectDetailComponent] ❌ Error loading project:', error);\n        this.loading = false;\n      }\n    );\n  }\n  \n  /**\n   * Load chat messages and expanded state from the backend\n   */\n  loadChatState(): void {\n    console.log('[ProjectDetailComponent] Loading chat state for:', this.projectName);\n    if (!this.projectName) return;\n    \n    this.chatMessagesLoading = true;\n    \n    this.projectService.getProjectMessages(this.projectName).pipe(\n      retry(1),\n      finalize(() => {\n        this.chatMessagesLoading = false;\n        console.log('[ProjectDetailComponent] Chat state loading completed');\n      })\n    ).subscribe(\n      (response) => {\n        console.log('[ProjectDetailComponent] Chat state loaded:', response);\n        // Set the chat expanded state from the server response\n        if (response.chatExpanded !== undefined) {\n          this.isChatExpanded = response.chatExpanded;\n          console.log('[ProjectDetailComponent] Chat expanded state set to:', this.isChatExpanded);\n        }\n      },\n      (error) => {\n        console.error('[ProjectDetailComponent] Error loading chat state:', error);\n      }\n    );\n  }\n\n  setupSocketListeners(): void {\n    console.log('[ProjectDetailComponent] Setting up socket listeners');\n\n    // Listen for agent status updates\n    this.socketService.on('agent_status').subscribe((data: any) => {\n      console.log('[ProjectDetailComponent] 🤖 Received agent_status event:', data);\n      \n      if (data.project_name === this.projectName) {\n        this.agentActive = data.active || false;\n        \n        if (data.stage) {\n          this.agentStage = data.stage;\n        }\n        \n        if (data.progress !== undefined) {\n          this.agentProgress = data.progress;\n        }\n        \n        if (data.message) {\n          this.agentStatusMessage = data.message;\n        }\n        \n        // Update specific stage status if provided\n        if (data.stages && typeof data.stages === 'object') {\n          Object.keys(data.stages).forEach(stage => {\n            if (this.workflowStages[stage]) {\n              this.workflowStages[stage] = data.stages[stage];\n            }\n          });\n        }\n      }\n    });\n\n    this.socketService.on('file_updated').subscribe((data: FileUpdateData) => {\n      console.log('[ProjectDetailComponent] 🔄 Received file_updated event from socket:', data);\n\n      if (data.project_name === this.projectName) {\n        console.log('[ProjectDetailComponent] file_updated matches current project');\n\n        if (this.selectedFile && this.selectedFile.path === data.file_path) {\n          console.log('[ProjectDetailComponent] Updating selected file view due to match:', data.file_path);\n          this.onFileSelect({ type: 'file_selected', file: this.selectedFile });\n        }\n\n        if (data.file_path.endsWith('.html')) {\n          console.log('[ProjectDetailComponent] HTML file updated, refreshing preview');\n          this.updatePreview();\n        }\n      }\n    });\n\n    this.socketService.on('preview_updated').subscribe((data: PreviewUpdateData) => {\n      console.log('[ProjectDetailComponent] 📺 Received preview_updated event:', data);\n\n      if (data.project_name === this.projectName) {\n        this.previewUrl = data.preview_url;\n        console.log('[ProjectDetailComponent] Preview URL updated via socket:', this.previewUrl);\n      }\n    });\n\n    // Listen for project setup completion\n    this.socketService.on('project_setup_complete').subscribe((data: any) => {\n      console.log('[ProjectDetailComponent] 🚀 Received project_setup_complete event:', data);\n      \n      if (data.project_name === this.projectName) {\n        // Update project data with new information\n        if (!this.project) this.project = {};\n        this.project.port = data.port;\n        this.project.actual_dir = data.project_dir;\n        \n        // Update preview URL with the assigned port\n        this.previewUrl = `http://localhost:${data.port}`;\n        console.log('[ProjectDetailComponent] Preview URL updated for new project:', this.previewUrl);\n        \n        // Show VS Code with the correct project directory\n        this.showVSCode = true;\n        this.setCodeServerUrl();\n      }\n    });\n\n    // Listen for agent_complete to show VS Code/code-server\n    this.socketService.on('agent_complete').subscribe((data: any) => {\n      if (data.project_name === this.projectName) {\n        this.showVSCode = true;\n        // Reset agent workflow status\n        this.agentActive = false;\n        this.agentProgress = 100;\n        this.agentStatusMessage = 'Agent workflow completed successfully';\n        // Set all stages to complete\n        Object.keys(this.workflowStages).forEach(stage => {\n          this.workflowStages[stage].status = 'completed';\n          this.workflowStages[stage].progress = 100;\n        });\n      }\n    });\n  }\n\n  onFileSelect(event: any): void {\n    console.log('[ProjectDetailComponent] onFileSelect triggered:', event);\n\n    if (event.type === 'file_selected') {\n      this.selectedFile = event.file;\n      console.log('[ProjectDetailComponent] Selected file set:', this.selectedFile);\n\n      if (this.selectedFile.path.endsWith('.html')) {\n        console.log('[ProjectDetailComponent] Selected file is HTML. Calling updatePreview.');\n        this.updatePreview();\n      }\n    }\n  }\n\n  onFileChange(event: any): void {\n    console.log('[ProjectDetailComponent] onFileChange triggered:', event);\n\n    if (event.type === 'file_saved') {\n      if (event.filePath.endsWith('.html')) {\n        console.log('[ProjectDetailComponent] Saved file is HTML. Updating preview.');\n        this.updatePreview();\n      }\n    }\n  }\n\n  onMessageEvent(event: any): void {\n    console.log('[ProjectDetailComponent] onMessageEvent triggered:', event);\n    if (event.type === 'message_sent' || event.type === 'message_received') {\n      console.log('[ProjectDetailComponent] Message event received. Triggering debounced save.');\n      // Trigger debounced save\n      this.saveMessagesSubject.next();\n    }\n  }\n\n  updatePreview(): void {\n    console.log('[ProjectDetailComponent] updatePreview called');\n\n    if (this.selectedFile && this.selectedFile.path.endsWith('.html')) {\n      const newPreviewUrl = `http://localhost:5000/projects/${this.projectName}/preview/${this.selectedFile.path}`;\n      console.log('[ProjectDetailComponent] Updating preview URL to:', newPreviewUrl);\n      this.previewUrl = newPreviewUrl;\n    } else {\n      console.warn('[ProjectDetailComponent] No HTML file selected. Skipping preview update.');\n    }\n  }\n\n  onBrowserPreviewExpand(expanded: boolean): void {\n    this.isBrowserPreviewExpanded = expanded;\n  }\n\n  toggleFilesMenu(): void {\n    this.filesCollapsed = !this.filesCollapsed;\n  }\n\n  setChatExpanded(expanded: boolean): void {\n    this.isChatExpanded = expanded;\n    console.log('[ProjectDetailComponent] Chat expanded state changed to:', expanded);\n    \n    // Trigger debounced save to persist expanded state\n    this.saveMessagesSubject.next();\n  }\n\n  deleteProject(): void {\n    if (!this.projectName) return;\n    if (!confirm('Are you sure you want to delete this project? This cannot be undone.')) return;\n    this.loading = true;\n    this.projectService.deleteProject(this.projectName).subscribe(\n      () => {\n        this.loading = false;\n        this.router.navigate(['/projects']);\n      },\n      (error) => {\n        this.loading = false;\n        alert('Failed to delete project: ' + (error?.error?.detail || error));\n      }\n    );\n  }\n\n  resetProject(): void {\n    if (!this.projectName) return;\n    if (!confirm('Are you sure you want to reset this project? All files will be deleted, but the folder will remain.')) return;\n    this.loading = true;\n    this.projectService.resetProject(this.projectName).subscribe(\n      () => {\n        this.loading = false;\n        this.loadProject();\n      },\n      (error) => {\n        this.loading = false;\n        alert('Failed to reset project: ' + (error?.error?.detail || error));\n      }\n    );\n  }\n\n  toggleVSCode(): void {\n    this.showVSCode = !this.showVSCode;\n  }\n\n  setCodeServerUrl(): void {\n    // Get the port from the project if available, otherwise use default\n    this.codeServerPort = this.project?.port || 8081;\n    \n    // Get the actual project directory path if available\n    const projectPath = this.project?.actual_dir || `/home/<USER>/projects/${this.projectName}`;\n    \n    // Create the code-server URL with the correct port and project path\n    const url = `http://localhost:${this.codeServerPort}/?folder=${encodeURIComponent(projectPath)}`;\n    console.log('[ProjectDetailComponent] Setting code-server URL:', url);\n    \n    this.codeServerUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);\n  }\n  \n  /**\n   * Clean up resources when component is destroyed and save any pending changes\n   */\n  ngOnDestroy(): void {\n    console.log('[ProjectDetailComponent] ngOnDestroy called, cleaning up subscriptions');\n    \n    // Save any pending changes immediately (don't wait for debounce)\n    this.saveCurrentMessagesImmediately();\n    \n    // Unsubscribe from all subscriptions to prevent memory leaks\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.saveMessagesSubject.complete();\n  }\n  \n  /**\n   * Save the current chat messages immediately without debouncing\n   * This is used when the component is about to be destroyed\n   */\n  private saveCurrentMessagesImmediately(): void {\n    if (!this.projectName || !this.isChatExpanded) return;\n    \n    console.log('[ProjectDetailComponent] Saving chat messages immediately before navigation');\n    \n    // Get the messages and save them with current expanded state\n    this.projectService.getProjectMessages(this.projectName).pipe(\n      // Use first() to complete after getting the first response\n      // so we don't need to unsubscribe from it\n      // This is important for navigation events\n      first(),\n      catchError(error => {\n        console.error('[ProjectDetailComponent] Error fetching messages for immediate save:', error);\n        return of({ messages: [] });\n      })\n    ).subscribe((response: any) => {\n      const messages = response.messages || [];\n      this.projectService.saveProjectMessages(this.projectName, messages, this.isChatExpanded).pipe(\n        first()\n      ).subscribe({\n        next: () => console.log('[ProjectDetailComponent] Chat messages saved successfully before navigation'),\n        error: (error) => console.error('[ProjectDetailComponent] Error saving messages before navigation:', error)\n      });\n    });\n  }\n  \n  /**\n   * Save the current chat messages and expanded state\n   * This is called after debouncing to prevent excessive API calls\n   */\n  private saveCurrentMessages(): void {\n    if (!this.projectName) return;\n    \n    // Set saving state to display UI indicators if needed\n    this.chatMessagesSaving = true;\n    console.log('[ProjectDetailComponent] Saving chat messages (debounced)');\n    \n    this.projectService.getProjectMessages(this.projectName).pipe(\n      // Retry up to 2 times with increasing delays\n      retry(2),\n      catchError(error => {\n        console.error('[ProjectDetailComponent] Error fetching messages to save:', error);\n        // Return empty array instead of error to allow chain to continue\n        return of({ messages: [], chatExpanded: this.isChatExpanded });\n      })\n    ).subscribe(response => {\n      const messages = response.messages || [];\n      \n      // Save the messages along with the current expanded state\n      this.projectService.saveProjectMessages(this.projectName, messages, this.isChatExpanded).pipe(\n        // Retry saving once if it fails\n        retry(1),\n        catchError(error => {\n          console.error('[ProjectDetailComponent] Error saving chat messages:', error);\n          return throwError(() => new Error('Failed to save chat messages after retry'));\n        }),\n        // Always turn off saving state when operation completes (success or error)\n        finalize(() => {\n          this.chatMessagesSaving = false;\n          console.log('[ProjectDetailComponent] Message save operation finished');\n        })\n      ).subscribe({\n        next: () => console.log('[ProjectDetailComponent] Chat messages saved successfully'),\n        error: (error) => console.error('[ProjectDetailComponent] Final error saving messages:', error)\n      });\n    });\n  }\n}\n", "<div class=\"project-container\">\n  <div class=\"project-header\">\n    <div class=\"project-title-and-status\">\n      <h2>{{ projectName }}</h2>\n      \n      <!-- Agent Workflow Status Indicator -->\n      <div *ngIf=\"agentActive\" class=\"agent-workflow-status\">\n        <div class=\"status-indicator pulsing\"></div>\n        <div class=\"status-details\">\n          <div class=\"current-stage\">\n            <span class=\"stage-label\">Current Stage: </span>\n            <span class=\"stage-value\">{{ agentStage | titlecase }}</span>\n          </div>\n          <div class=\"progress-container\">\n            <div class=\"progress-bar\">\n              <div class=\"progress-fill\" [style.width.%]=\"agentProgress\"></div>\n            </div>\n            <div class=\"progress-text\">{{ agentProgress }}%</div>\n          </div>\n          <div class=\"status-message\">{{ agentStatusMessage }}</div>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"project-actions\">\n      <button (click)=\"loadProject()\" [disabled]=\"loading || agentActive\">\n        Refresh\n      </button>\n      <button (click)=\"deleteProject()\" [disabled]=\"loading || agentActive\" style=\"margin-left: 8px; color: red;\">Delete Project</button>\n      <button (click)=\"resetProject()\" [disabled]=\"loading || agentActive\" style=\"margin-left: 8px;\">Reset Project</button>\n    </div>\n  </div>\n  \n  <div class=\"project-content\" *ngIf=\"!loading\" [ngClass]=\"{'files-collapsed': filesCollapsed}\">\n    <div class=\"sidebar\" *ngIf=\"!isBrowserPreviewExpanded && !filesCollapsed\">\n      <div class=\"sidebar-header\" style=\"display: flex; align-items: center; justify-content: flex-end; padding: 4px;\">\n        <button (click)=\"toggleFilesMenu()\" title=\"Collapse Files Menu\">◀️</button>\n      </div>\n      <app-file-explorer \n        [projectName]=\"projectName\" \n        (fileSelectEvent)=\"onFileSelect($event)\">\n      </app-file-explorer>\n    </div>\n    <div *ngIf=\"filesCollapsed && !isBrowserPreviewExpanded\" class=\"sidebar-collapsed-bar\">\n      <button (click)=\"toggleFilesMenu()\" title=\"Expand Files Menu\">▶️</button>\n    </div>\n    \n    <div class=\"main-content\">\n      <div class=\"editor-section\" *ngIf=\"!isBrowserPreviewExpanded\">\n        <ng-container *ngIf=\"!showVSCode\">\n          <app-code-editor \n            [projectName]=\"projectName\" \n            [filePath]=\"selectedFile?.path\" \n            (fileChangeEvent)=\"onFileChange($event)\">\n          </app-code-editor>\n        </ng-container>\n        <ng-container *ngIf=\"showVSCode\">\n          <iframe \n            [src]=\"codeServerUrl\"\n            class=\"code-server-iframe\"\n            title=\"VS Code (code-server)\">\n          </iframe>\n        </ng-container>\n      </div>\n      \n      <div class=\"preview-section\">\n        <app-browser-preview \n          [projectName]=\"projectName\" \n          [url]=\"previewUrl\"\n          [codeServerPort]=\"codeServerPort\"\n          (expandChange)=\"onBrowserPreviewExpand($event)\">\n        </app-browser-preview>\n      </div>\n    </div>\n    \n    <div class=\"chat-section\" [ngClass]=\"{ 'collapsed': !isChatExpanded, 'expanded': isChatExpanded }\" *ngIf=\"!isBrowserPreviewExpanded\">\n      <ng-container *ngIf=\"isChatExpanded; else collapsedHeader\">\n        <app-chat \n          [projectName]=\"projectName\" \n          [messagesLoading]=\"chatMessagesLoading\"\n          [messagesSaving]=\"chatMessagesSaving\"\n          (messageEvent)=\"onMessageEvent($event)\"\n          (chatExpandChange)=\"setChatExpanded($event)\">\n        </app-chat>\n      </ng-container>\n      <ng-template #collapsedHeader>\n        <div class=\"chat-header-collapsed\">\n          <span class=\"chat-label\">Chat with AI Agent</span>\n          <button class=\"expand-chat-btn\" (click)=\"setChatExpanded(true)\" aria-label=\"Expand Chat\">▲</button>\n        </div>\n      </ng-template>\n    </div>\n  </div>\n  \n  <div *ngIf=\"loading\" class=\"loading-container\">\n    <div class=\"spinner\"></div>\n    <p>Loading project...</p>\n  </div>\n</div>\n"], "mappings": "AAKA,SAASA,OAAO,EAAgBC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAC5D,SAASC,YAAY,EAAEC,UAAU,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,gBAAgB;;;;;;;;;;;;;ICA3EC,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAE,SAAA,cAA4C;IAC5CF,EAAA,CAAAC,cAAA,cAA4B;IAEED,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChDJ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAG,MAAA,GAA4B;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE/DJ,EAAA,CAAAC,cAAA,cAAgC;IAE5BD,EAAA,CAAAE,SAAA,eAAiE;IACnEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,IAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEvDJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IAR9BJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAO,WAAA,OAAAC,MAAA,CAAAC,UAAA,EAA4B;IAIzBT,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAU,WAAA,UAAAF,MAAA,CAAAG,aAAA,MAA+B;IAEjCX,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAY,kBAAA,KAAAJ,MAAA,CAAAG,aAAA,MAAoB;IAErBX,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAE,MAAA,CAAAK,kBAAA,CAAwB;;;;;;IAe1Db,EAAA,CAAAC,cAAA,cAA0E;IAE9DD,EAAA,CAAAc,UAAA,mBAAAC,qEAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAA6BrB,EAAA,CAAAG,MAAA,mBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAE7EJ,EAAA,CAAAC,cAAA,4BAE2C;IAAzCD,EAAA,CAAAc,UAAA,6BAAAQ,0FAAAC,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAxB,EAAA,CAAAmB,aAAA;MAAA,OAAmBnB,EAAA,CAAAoB,WAAA,CAAAI,MAAA,CAAAC,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IAC1CvB,EAAA,CAAAI,YAAA,EAAoB;;;;IAFlBJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,gBAAAC,MAAA,CAAAC,WAAA,CAA2B;;;;;;IAI/B5B,EAAA,CAAAC,cAAA,cAAuF;IAC7ED,EAAA,CAAAc,UAAA,mBAAAe,qEAAA;MAAA7B,EAAA,CAAAgB,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAW,OAAA,CAAAV,eAAA,EAAiB;IAAA,EAAC;IAA2BrB,EAAA,CAAAG,MAAA,mBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IAKvEJ,EAAA,CAAAgC,uBAAA,GAAkC;IAChChC,EAAA,CAAAC,cAAA,0BAG2C;IAAzCD,EAAA,CAAAc,UAAA,6BAAAmB,uGAAAV,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAAkB,IAAA;MAAA,MAAAC,OAAA,GAAAnC,EAAA,CAAAmB,aAAA;MAAA,OAAmBnB,EAAA,CAAAoB,WAAA,CAAAe,OAAA,CAAAC,YAAA,CAAAb,MAAA,CAAoB;IAAA,EAAC;IAC1CvB,EAAA,CAAAI,YAAA,EAAkB;IACpBJ,EAAA,CAAAqC,qBAAA,EAAe;;;;IAJXrC,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,gBAAAY,OAAA,CAAAV,WAAA,CAA2B,aAAAU,OAAA,CAAAC,YAAA,kBAAAD,OAAA,CAAAC,YAAA,CAAAC,IAAA;;;;;IAK/BxC,EAAA,CAAAgC,uBAAA,GAAiC;IAC/BhC,EAAA,CAAAE,SAAA,iBAIS;IACXF,EAAA,CAAAqC,qBAAA,EAAe;;;;IAJXrC,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA0B,UAAA,QAAAe,OAAA,CAAAC,aAAA,EAAA1C,EAAA,CAAA2C,qBAAA,CAAqB;;;;;IAV3B3C,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAA4C,UAAA,IAAAC,2DAAA,2BAMe;IACf7C,EAAA,CAAA4C,UAAA,IAAAE,2DAAA,2BAMe;IACjB9C,EAAA,CAAAI,YAAA,EAAM;;;;IAdWJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA0B,UAAA,UAAAqB,MAAA,CAAAC,UAAA,CAAiB;IAOjBhD,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA0B,UAAA,SAAAqB,MAAA,CAAAC,UAAA,CAAgB;;;;;;IAoBjChD,EAAA,CAAAgC,uBAAA,GAA2D;IACzDhC,EAAA,CAAAC,cAAA,mBAK+C;IAD7CD,EAAA,CAAAc,UAAA,0BAAAmC,6FAAA1B,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAmB,aAAA;MAAA,OAAgBnB,EAAA,CAAAoB,WAAA,CAAA+B,OAAA,CAAAC,cAAA,CAAA7B,MAAA,CAAsB;IAAA,EAAC,8BAAA8B,iGAAA9B,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAAkC,IAAA;MAAA,MAAAI,OAAA,GAAAtD,EAAA,CAAAmB,aAAA;MAAA,OACnBnB,EAAA,CAAAoB,WAAA,CAAAkC,OAAA,CAAAC,eAAA,CAAAhC,MAAA,CAAuB;IAAA,EADJ;IAEzCvB,EAAA,CAAAI,YAAA,EAAW;IACbJ,EAAA,CAAAqC,qBAAA,EAAe;;;;IANXrC,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,gBAAA8B,OAAA,CAAA5B,WAAA,CAA2B,oBAAA4B,OAAA,CAAAC,mBAAA,oBAAAD,OAAA,CAAAE,kBAAA;;;;;;IAQ7B1D,EAAA,CAAAC,cAAA,cAAmC;IACRD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClDJ,EAAA,CAAAC,cAAA,iBAAyF;IAAzDD,EAAA,CAAAc,UAAA,mBAAA6C,mFAAA;MAAA3D,EAAA,CAAAgB,aAAA,CAAA4C,IAAA;MAAA,MAAAC,OAAA,GAAA7D,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAyC,OAAA,CAAAN,eAAA,CAAgB,IAAI,CAAC;IAAA,EAAC;IAA0BvD,EAAA,CAAAG,MAAA,aAAC;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;;;;;;IAbzGJ,EAAA,CAAAC,cAAA,cAAqI;IACnID,EAAA,CAAA4C,UAAA,IAAAkB,2DAAA,2BAQe;IACf9D,EAAA,CAAA4C,UAAA,IAAAmB,0DAAA,iCAAA/D,EAAA,CAAAgE,sBAAA,CAKc;IAChBhE,EAAA,CAAAI,YAAA,EAAM;;;;;IAhBoBJ,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAAiE,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,cAAA,EAAAD,MAAA,CAAAC,cAAA,EAAwE;IACjFpE,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA0B,UAAA,SAAAyC,MAAA,CAAAC,cAAA,CAAsB,aAAAC,IAAA;;;;;;;;;;;IA3CzCrE,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAA4C,UAAA,IAAA0B,4CAAA,kBAQM;IACNtE,EAAA,CAAA4C,UAAA,IAAA2B,4CAAA,kBAEM;IAENvE,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA4C,UAAA,IAAA4B,4CAAA,kBAeM;IAENxE,EAAA,CAAAC,cAAA,cAA6B;IAKzBD,EAAA,CAAAc,UAAA,0BAAA2D,mFAAAlD,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAmB,aAAA;MAAA,OAAgBnB,EAAA,CAAAoB,WAAA,CAAAuD,OAAA,CAAAC,sBAAA,CAAArD,MAAA,CAA8B;IAAA,EAAC;IACjDvB,EAAA,CAAAI,YAAA,EAAsB;IAI1BJ,EAAA,CAAA4C,UAAA,IAAAiC,4CAAA,kBAgBM;IACR7E,EAAA,CAAAI,YAAA,EAAM;;;;IA3DwCJ,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAA8E,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,cAAA,EAA+C;IACrEjF,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAA0B,UAAA,UAAAsD,MAAA,CAAAE,wBAAA,KAAAF,MAAA,CAAAC,cAAA,CAAkD;IASlEjF,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAA0B,UAAA,SAAAsD,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAiD;IAKxBlF,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA0B,UAAA,UAAAsD,MAAA,CAAAE,wBAAA,CAA+B;IAmBxDlF,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,gBAAAsD,MAAA,CAAApD,WAAA,CAA2B,QAAAoD,MAAA,CAAAG,UAAA,oBAAAH,MAAA,CAAAI,cAAA;IAQmEpF,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA0B,UAAA,UAAAsD,MAAA,CAAAE,wBAAA,CAA+B;;;;;IAmBrIlF,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,SAAA,cAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;ADzE7B,OAAM,MAAOiF,sBAAsB;EAmCjCC,YACUC,KAAqB,EACrBC,cAA8B,EAC9BC,aAA4B,EAC5BC,MAAc,EACdC,SAAuB;IAJvB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,SAAS,GAATA,SAAS;IAvCnB,KAAA/D,WAAW,GAAW,EAAE;IAGxB,KAAAuD,UAAU,GAAW,EAAE;IACvB,KAAAS,OAAO,GAAY,KAAK;IACxB,KAAAV,wBAAwB,GAAG,KAAK;IAChC,KAAAD,cAAc,GAAG,KAAK;IACtB,KAAAb,cAAc,GAAG,KAAK;IACtB,KAAApB,UAAU,GAAY,KAAK;IAC3B,KAAAN,aAAa,GAAoB,EAAE;IACnC,KAAA0C,cAAc,GAAW,IAAI,CAAC,CAAE;IAEhC;IACA,KAAAS,WAAW,GAAY,KAAK;IAC5B,KAAApF,UAAU,GAAW,EAAE;IACvB,KAAAE,aAAa,GAAW,CAAC;IACzB,KAAAE,kBAAkB,GAAW,EAAE;IAC/B,KAAAiF,cAAc,GAAwD;MACpE,OAAO,EAAE;QAACC,MAAM,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAC,CAAC;MACzC,UAAU,EAAE;QAACD,MAAM,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAC,CAAC;MAC5C,UAAU,EAAE;QAACD,MAAM,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAC,CAAC;MAC5C,gBAAgB,EAAE;QAACD,MAAM,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAC,CAAC;MAClD,SAAS,EAAE;QAACD,MAAM,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAC,CAAC;MAC3C,YAAY,EAAE;QAACD,MAAM,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAC;KAC9C;IAED;IACQ,KAAAC,mBAAmB,GAAG,IAAIzG,OAAO,EAAQ;IACzC,KAAA0G,aAAa,GAAmB,EAAE;IAE1C;IACA,KAAAzC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,kBAAkB,GAAY,KAAK;EAQ/B;EAEJyC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IAEvD;IACA,MAAMC,eAAe,GAAG,IAAI,CAACL,mBAAmB,CAACM,IAAI,CACnD5G,YAAY,CAAC,GAAG,CAAC,CAAC;KACnB,CAAC6G,SAAS,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,CAAC;IAC7C,IAAI,CAACP,aAAa,CAACQ,IAAI,CAACJ,eAAe,CAAC;IAExC,MAAMK,QAAQ,GAAG,IAAI,CAACpB,KAAK,CAACqB,MAAM,CAACJ,SAAS,CAACI,MAAM,IAAG;MACpD,IAAI,CAAChF,WAAW,GAAGgF,MAAM,CAAC,MAAM,CAAC;MACjCR,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE,IAAI,CAACzE,WAAW,CAAC;MACnF,IAAI,CAACiF,gBAAgB,EAAE;MACvB,IAAI,CAACC,WAAW,EAAE;MAClB,IAAI,CAACC,oBAAoB,EAAE;IAC7B,CAAC,CAAC;IACF,IAAI,CAACb,aAAa,CAACQ,IAAI,CAACC,QAAQ,CAAC;EACnC;EAEAG,WAAWA,CAAA;IACTV,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1D,IAAI,CAAC,IAAI,CAACzE,WAAW,EAAE;MACrBwE,OAAO,CAACY,IAAI,CAAC,4EAA4E,CAAC;MAC1F;;IAGF,IAAI,CAACpB,OAAO,GAAG,IAAI;IACnBQ,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACzE,WAAW,CAAC;IAE3E,IAAI,CAAC4D,cAAc,CAACyB,UAAU,CAAC,IAAI,CAACrF,WAAW,CAAC,CAAC4E,SAAS,CACvDU,QAAQ,IAAI;MACXd,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEa,QAAQ,CAAC;MAC/E,IAAI,CAACC,OAAO,GAAGD,QAAQ,CAACC,OAAO;MAC/B,IAAI,CAACvB,OAAO,GAAG,KAAK;MAEpB,IAAI,IAAI,CAACuB,OAAO,CAACC,WAAW,EAAE;QAC5B,IAAI,CAACjC,UAAU,GAAG,IAAI,CAACgC,OAAO,CAACC,WAAW;QAC1ChB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAAClB,UAAU,CAAC;;MAGnF;MACA,IAAI,CAACkC,aAAa,EAAE;IACtB,CAAC,EACAC,KAAK,IAAI;MACRlB,OAAO,CAACkB,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzE,IAAI,CAAC1B,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEA;;;EAGAyB,aAAaA,CAAA;IACXjB,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE,IAAI,CAACzE,WAAW,CAAC;IACjF,IAAI,CAAC,IAAI,CAACA,WAAW,EAAE;IAEvB,IAAI,CAAC6B,mBAAmB,GAAG,IAAI;IAE/B,IAAI,CAAC+B,cAAc,CAAC+B,kBAAkB,CAAC,IAAI,CAAC3F,WAAW,CAAC,CAAC2E,IAAI,CAC3D1G,KAAK,CAAC,CAAC,CAAC,EACRC,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC2D,mBAAmB,GAAG,KAAK;MAChC2C,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACtE,CAAC,CAAC,CACH,CAACG,SAAS,CACRU,QAAQ,IAAI;MACXd,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEa,QAAQ,CAAC;MACpE;MACA,IAAIA,QAAQ,CAACM,YAAY,KAAKC,SAAS,EAAE;QACvC,IAAI,CAACrD,cAAc,GAAG8C,QAAQ,CAACM,YAAY;QAC3CpB,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE,IAAI,CAACjC,cAAc,CAAC;;IAE5F,CAAC,EACAkD,KAAK,IAAI;MACRlB,OAAO,CAACkB,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;IAC5E,CAAC,CACF;EACH;EAEAP,oBAAoBA,CAAA;IAClBX,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IAEnE;IACA,IAAI,CAACZ,aAAa,CAACiC,EAAE,CAAC,cAAc,CAAC,CAAClB,SAAS,CAAEmB,IAAS,IAAI;MAC5DvB,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAEsB,IAAI,CAAC;MAE7E,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAAChG,WAAW,EAAE;QAC1C,IAAI,CAACiE,WAAW,GAAG8B,IAAI,CAACE,MAAM,IAAI,KAAK;QAEvC,IAAIF,IAAI,CAACG,KAAK,EAAE;UACd,IAAI,CAACrH,UAAU,GAAGkH,IAAI,CAACG,KAAK;;QAG9B,IAAIH,IAAI,CAAC3B,QAAQ,KAAKyB,SAAS,EAAE;UAC/B,IAAI,CAAC9G,aAAa,GAAGgH,IAAI,CAAC3B,QAAQ;;QAGpC,IAAI2B,IAAI,CAACI,OAAO,EAAE;UAChB,IAAI,CAAClH,kBAAkB,GAAG8G,IAAI,CAACI,OAAO;;QAGxC;QACA,IAAIJ,IAAI,CAACK,MAAM,IAAI,OAAOL,IAAI,CAACK,MAAM,KAAK,QAAQ,EAAE;UAClDC,MAAM,CAACC,IAAI,CAACP,IAAI,CAACK,MAAM,CAAC,CAACG,OAAO,CAACL,KAAK,IAAG;YACvC,IAAI,IAAI,CAAChC,cAAc,CAACgC,KAAK,CAAC,EAAE;cAC9B,IAAI,CAAChC,cAAc,CAACgC,KAAK,CAAC,GAAGH,IAAI,CAACK,MAAM,CAACF,KAAK,CAAC;;UAEnD,CAAC,CAAC;;;IAGR,CAAC,CAAC;IAEF,IAAI,CAACrC,aAAa,CAACiC,EAAE,CAAC,cAAc,CAAC,CAAClB,SAAS,CAAEmB,IAAoB,IAAI;MACvEvB,OAAO,CAACC,GAAG,CAAC,sEAAsE,EAAEsB,IAAI,CAAC;MAEzF,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAAChG,WAAW,EAAE;QAC1CwE,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;QAE5E,IAAI,IAAI,CAAC9D,YAAY,IAAI,IAAI,CAACA,YAAY,CAACC,IAAI,KAAKmF,IAAI,CAACS,SAAS,EAAE;UAClEhC,OAAO,CAACC,GAAG,CAAC,oEAAoE,EAAEsB,IAAI,CAACS,SAAS,CAAC;UACjG,IAAI,CAAC3G,YAAY,CAAC;YAAE4G,IAAI,EAAE,eAAe;YAAEC,IAAI,EAAE,IAAI,CAAC/F;UAAY,CAAE,CAAC;;QAGvE,IAAIoF,IAAI,CAACS,SAAS,CAACG,QAAQ,CAAC,OAAO,CAAC,EAAE;UACpCnC,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7E,IAAI,CAACmC,aAAa,EAAE;;;IAG1B,CAAC,CAAC;IAEF,IAAI,CAAC/C,aAAa,CAACiC,EAAE,CAAC,iBAAiB,CAAC,CAAClB,SAAS,CAAEmB,IAAuB,IAAI;MAC7EvB,OAAO,CAACC,GAAG,CAAC,6DAA6D,EAAEsB,IAAI,CAAC;MAEhF,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAAChG,WAAW,EAAE;QAC1C,IAAI,CAACuD,UAAU,GAAGwC,IAAI,CAACP,WAAW;QAClChB,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE,IAAI,CAAClB,UAAU,CAAC;;IAE5F,CAAC,CAAC;IAEF;IACA,IAAI,CAACM,aAAa,CAACiC,EAAE,CAAC,wBAAwB,CAAC,CAAClB,SAAS,CAAEmB,IAAS,IAAI;MACtEvB,OAAO,CAACC,GAAG,CAAC,oEAAoE,EAAEsB,IAAI,CAAC;MAEvF,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAAChG,WAAW,EAAE;QAC1C;QACA,IAAI,CAAC,IAAI,CAACuF,OAAO,EAAE,IAAI,CAACA,OAAO,GAAG,EAAE;QACpC,IAAI,CAACA,OAAO,CAACsB,IAAI,GAAGd,IAAI,CAACc,IAAI;QAC7B,IAAI,CAACtB,OAAO,CAACuB,UAAU,GAAGf,IAAI,CAACgB,WAAW;QAE1C;QACA,IAAI,CAACxD,UAAU,GAAG,oBAAoBwC,IAAI,CAACc,IAAI,EAAE;QACjDrC,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAE,IAAI,CAAClB,UAAU,CAAC;QAE7F;QACA,IAAI,CAACnC,UAAU,GAAG,IAAI;QACtB,IAAI,CAAC6D,gBAAgB,EAAE;;IAE3B,CAAC,CAAC;IAEF;IACA,IAAI,CAACpB,aAAa,CAACiC,EAAE,CAAC,gBAAgB,CAAC,CAAClB,SAAS,CAAEmB,IAAS,IAAI;MAC9D,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAAChG,WAAW,EAAE;QAC1C,IAAI,CAACoB,UAAU,GAAG,IAAI;QACtB;QACA,IAAI,CAAC6C,WAAW,GAAG,KAAK;QACxB,IAAI,CAAClF,aAAa,GAAG,GAAG;QACxB,IAAI,CAACE,kBAAkB,GAAG,uCAAuC;QACjE;QACAoH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpC,cAAc,CAAC,CAACqC,OAAO,CAACL,KAAK,IAAG;UAC/C,IAAI,CAAChC,cAAc,CAACgC,KAAK,CAAC,CAAC/B,MAAM,GAAG,WAAW;UAC/C,IAAI,CAACD,cAAc,CAACgC,KAAK,CAAC,CAAC9B,QAAQ,GAAG,GAAG;QAC3C,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAvE,YAAYA,CAACmH,KAAU;IACrBxC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEuC,KAAK,CAAC;IAEtE,IAAIA,KAAK,CAACP,IAAI,KAAK,eAAe,EAAE;MAClC,IAAI,CAAC9F,YAAY,GAAGqG,KAAK,CAACN,IAAI;MAC9BlC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE,IAAI,CAAC9D,YAAY,CAAC;MAE7E,IAAI,IAAI,CAACA,YAAY,CAACC,IAAI,CAAC+F,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC5CnC,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;QACrF,IAAI,CAACmC,aAAa,EAAE;;;EAG1B;EAEApG,YAAYA,CAACwG,KAAU;IACrBxC,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEuC,KAAK,CAAC;IAEtE,IAAIA,KAAK,CAACP,IAAI,KAAK,YAAY,EAAE;MAC/B,IAAIO,KAAK,CAACC,QAAQ,CAACN,QAAQ,CAAC,OAAO,CAAC,EAAE;QACpCnC,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;QAC7E,IAAI,CAACmC,aAAa,EAAE;;;EAG1B;EAEApF,cAAcA,CAACwF,KAAU;IACvBxC,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEuC,KAAK,CAAC;IACxE,IAAIA,KAAK,CAACP,IAAI,KAAK,cAAc,IAAIO,KAAK,CAACP,IAAI,KAAK,kBAAkB,EAAE;MACtEjC,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;MAC1F;MACA,IAAI,CAACJ,mBAAmB,CAAC6C,IAAI,EAAE;;EAEnC;EAEAN,aAAaA,CAAA;IACXpC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D,IAAI,IAAI,CAAC9D,YAAY,IAAI,IAAI,CAACA,YAAY,CAACC,IAAI,CAAC+F,QAAQ,CAAC,OAAO,CAAC,EAAE;MACjE,MAAMQ,aAAa,GAAG,kCAAkC,IAAI,CAACnH,WAAW,YAAY,IAAI,CAACW,YAAY,CAACC,IAAI,EAAE;MAC5G4D,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE0C,aAAa,CAAC;MAC/E,IAAI,CAAC5D,UAAU,GAAG4D,aAAa;KAChC,MAAM;MACL3C,OAAO,CAACY,IAAI,CAAC,0EAA0E,CAAC;;EAE5F;EAEApC,sBAAsBA,CAACoE,QAAiB;IACtC,IAAI,CAAC9D,wBAAwB,GAAG8D,QAAQ;EAC1C;EAEA3H,eAAeA,CAAA;IACb,IAAI,CAAC4D,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA1B,eAAeA,CAACyF,QAAiB;IAC/B,IAAI,CAAC5E,cAAc,GAAG4E,QAAQ;IAC9B5C,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE2C,QAAQ,CAAC;IAEjF;IACA,IAAI,CAAC/C,mBAAmB,CAAC6C,IAAI,EAAE;EACjC;EAEAG,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACrH,WAAW,EAAE;IACvB,IAAI,CAACsH,OAAO,CAAC,sEAAsE,CAAC,EAAE;IACtF,IAAI,CAACtD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,cAAc,CAACyD,aAAa,CAAC,IAAI,CAACrH,WAAW,CAAC,CAAC4E,SAAS,CAC3D,MAAK;MACH,IAAI,CAACZ,OAAO,GAAG,KAAK;MACpB,IAAI,CAACF,MAAM,CAACyD,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC,EACA7B,KAAK,IAAI;MACR,IAAI,CAAC1B,OAAO,GAAG,KAAK;MACpBwD,KAAK,CAAC,4BAA4B,IAAI9B,KAAK,EAAEA,KAAK,EAAE+B,MAAM,IAAI/B,KAAK,CAAC,CAAC;IACvE,CAAC,CACF;EACH;EAEAgC,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC1H,WAAW,EAAE;IACvB,IAAI,CAACsH,OAAO,CAAC,qGAAqG,CAAC,EAAE;IACrH,IAAI,CAACtD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,cAAc,CAAC8D,YAAY,CAAC,IAAI,CAAC1H,WAAW,CAAC,CAAC4E,SAAS,CAC1D,MAAK;MACH,IAAI,CAACZ,OAAO,GAAG,KAAK;MACpB,IAAI,CAACkB,WAAW,EAAE;IACpB,CAAC,EACAQ,KAAK,IAAI;MACR,IAAI,CAAC1B,OAAO,GAAG,KAAK;MACpBwD,KAAK,CAAC,2BAA2B,IAAI9B,KAAK,EAAEA,KAAK,EAAE+B,MAAM,IAAI/B,KAAK,CAAC,CAAC;IACtE,CAAC,CACF;EACH;EAEAiC,YAAYA,CAAA;IACV,IAAI,CAACvG,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEA6D,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACzB,cAAc,GAAG,IAAI,CAAC+B,OAAO,EAAEsB,IAAI,IAAI,IAAI;IAEhD;IACA,MAAMe,WAAW,GAAG,IAAI,CAACrC,OAAO,EAAEuB,UAAU,IAAI,wBAAwB,IAAI,CAAC9G,WAAW,EAAE;IAE1F;IACA,MAAM6H,GAAG,GAAG,oBAAoB,IAAI,CAACrE,cAAc,YAAYsE,kBAAkB,CAACF,WAAW,CAAC,EAAE;IAChGpD,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEoD,GAAG,CAAC;IAErE,IAAI,CAAC/G,aAAa,GAAG,IAAI,CAACiD,SAAS,CAACgE,8BAA8B,CAACF,GAAG,CAAC;EACzE;EAEA;;;EAGAG,WAAWA,CAAA;IACTxD,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;IAErF;IACA,IAAI,CAACwD,8BAA8B,EAAE;IAErC;IACA,IAAI,CAAC3D,aAAa,CAACiC,OAAO,CAAC2B,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAAC9D,mBAAmB,CAAC+D,QAAQ,EAAE;EACrC;EAEA;;;;EAIQH,8BAA8BA,CAAA;IACpC,IAAI,CAAC,IAAI,CAACjI,WAAW,IAAI,CAAC,IAAI,CAACwC,cAAc,EAAE;IAE/CgC,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;IAE1F;IACA,IAAI,CAACb,cAAc,CAAC+B,kBAAkB,CAAC,IAAI,CAAC3F,WAAW,CAAC,CAAC2E,IAAI;IAC3D;IACA;IACA;IACAxG,KAAK,EAAE,EACPH,UAAU,CAAC0H,KAAK,IAAG;MACjBlB,OAAO,CAACkB,KAAK,CAAC,sEAAsE,EAAEA,KAAK,CAAC;MAC5F,OAAO5H,EAAE,CAAC;QAAEuK,QAAQ,EAAE;MAAE,CAAE,CAAC;IAC7B,CAAC,CAAC,CACH,CAACzD,SAAS,CAAEU,QAAa,IAAI;MAC5B,MAAM+C,QAAQ,GAAG/C,QAAQ,CAAC+C,QAAQ,IAAI,EAAE;MACxC,IAAI,CAACzE,cAAc,CAAC0E,mBAAmB,CAAC,IAAI,CAACtI,WAAW,EAAEqI,QAAQ,EAAE,IAAI,CAAC7F,cAAc,CAAC,CAACmC,IAAI,CAC3FxG,KAAK,EAAE,CACR,CAACyG,SAAS,CAAC;QACVsC,IAAI,EAAEA,CAAA,KAAM1C,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;QACtGiB,KAAK,EAAGA,KAAK,IAAKlB,OAAO,CAACkB,KAAK,CAAC,mEAAmE,EAAEA,KAAK;OAC3G,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;EAIQb,mBAAmBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAAC7E,WAAW,EAAE;IAEvB;IACA,IAAI,CAAC8B,kBAAkB,GAAG,IAAI;IAC9B0C,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;IAExE,IAAI,CAACb,cAAc,CAAC+B,kBAAkB,CAAC,IAAI,CAAC3F,WAAW,CAAC,CAAC2E,IAAI;IAC3D;IACA1G,KAAK,CAAC,CAAC,CAAC,EACRD,UAAU,CAAC0H,KAAK,IAAG;MACjBlB,OAAO,CAACkB,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;MACjF;MACA,OAAO5H,EAAE,CAAC;QAAEuK,QAAQ,EAAE,EAAE;QAAEzC,YAAY,EAAE,IAAI,CAACpD;MAAc,CAAE,CAAC;IAChE,CAAC,CAAC,CACH,CAACoC,SAAS,CAACU,QAAQ,IAAG;MACrB,MAAM+C,QAAQ,GAAG/C,QAAQ,CAAC+C,QAAQ,IAAI,EAAE;MAExC;MACA,IAAI,CAACzE,cAAc,CAAC0E,mBAAmB,CAAC,IAAI,CAACtI,WAAW,EAAEqI,QAAQ,EAAE,IAAI,CAAC7F,cAAc,CAAC,CAACmC,IAAI;MAC3F;MACA1G,KAAK,CAAC,CAAC,CAAC,EACRD,UAAU,CAAC0H,KAAK,IAAG;QACjBlB,OAAO,CAACkB,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;QAC5E,OAAO7H,UAAU,CAAC,MAAM,IAAI0K,KAAK,CAAC,0CAA0C,CAAC,CAAC;MAChF,CAAC,CAAC;MACF;MACArK,QAAQ,CAAC,MAAK;QACZ,IAAI,CAAC4D,kBAAkB,GAAG,KAAK;QAC/B0C,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACzE,CAAC,CAAC,CACH,CAACG,SAAS,CAAC;QACVsC,IAAI,EAAEA,CAAA,KAAM1C,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QACpFiB,KAAK,EAAGA,KAAK,IAAKlB,OAAO,CAACkB,KAAK,CAAC,uDAAuD,EAAEA,KAAK;OAC/F,CAAC;IACJ,CAAC,CAAC;EACJ;;;uBAhaWjC,sBAAsB,EAAArF,EAAA,CAAAoK,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtK,EAAA,CAAAoK,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxK,EAAA,CAAAoK,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAA1K,EAAA,CAAAoK,iBAAA,CAAAC,EAAA,CAAAM,MAAA,GAAA3K,EAAA,CAAAoK,iBAAA,CAAAQ,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAtBxF,sBAAsB;MAAAyF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvBnCpL,EAAA,CAAAC,cAAA,aAA+B;UAGrBD,EAAA,CAAAG,MAAA,GAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAG1BJ,EAAA,CAAA4C,UAAA,IAAA0I,qCAAA,kBAeM;UACRtL,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAC,cAAA,aAA6B;UACnBD,EAAA,CAAAc,UAAA,mBAAAyK,wDAAA;YAAA,OAASF,GAAA,CAAAvE,WAAA,EAAa;UAAA,EAAC;UAC7B9G,EAAA,CAAAG,MAAA,gBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,gBAA4G;UAApGD,EAAA,CAAAc,UAAA,mBAAA0K,wDAAA;YAAA,OAASH,GAAA,CAAApC,aAAA,EAAe;UAAA,EAAC;UAA2EjJ,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACnIJ,EAAA,CAAAC,cAAA,iBAA+F;UAAvFD,EAAA,CAAAc,UAAA,mBAAA2K,yDAAA;YAAA,OAASJ,GAAA,CAAA/B,YAAA,EAAc;UAAA,EAAC;UAA+DtJ,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAIzHJ,EAAA,CAAA4C,UAAA,KAAA8I,sCAAA,kBA2DM;UAEN1L,EAAA,CAAA4C,UAAA,KAAA+I,sCAAA,iBAGM;UACR3L,EAAA,CAAAI,YAAA,EAAM;;;UA/FIJ,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAM,iBAAA,CAAA+K,GAAA,CAAAzJ,WAAA,CAAiB;UAGf5B,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAA0B,UAAA,SAAA2J,GAAA,CAAAxF,WAAA,CAAiB;UAmBS7F,EAAA,CAAAK,SAAA,GAAmC;UAAnCL,EAAA,CAAA0B,UAAA,aAAA2J,GAAA,CAAAzF,OAAA,IAAAyF,GAAA,CAAAxF,WAAA,CAAmC;UAGjC7F,EAAA,CAAAK,SAAA,GAAmC;UAAnCL,EAAA,CAAA0B,UAAA,aAAA2J,GAAA,CAAAzF,OAAA,IAAAyF,GAAA,CAAAxF,WAAA,CAAmC;UACpC7F,EAAA,CAAAK,SAAA,GAAmC;UAAnCL,EAAA,CAAA0B,UAAA,aAAA2J,GAAA,CAAAzF,OAAA,IAAAyF,GAAA,CAAAxF,WAAA,CAAmC;UAI1C7F,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAA0B,UAAA,UAAA2J,GAAA,CAAAzF,OAAA,CAAc;UA6DtC5F,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA0B,UAAA,SAAA2J,GAAA,CAAAzF,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}