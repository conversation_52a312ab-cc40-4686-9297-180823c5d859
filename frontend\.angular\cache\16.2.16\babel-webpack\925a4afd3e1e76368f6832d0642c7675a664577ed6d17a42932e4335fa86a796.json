{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = [\"iframe\"];\nfunction BrowserPreviewComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"div\", 13);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Running test...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_1_div_5_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const log_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(log_r7);\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Logs:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, BrowserPreviewComponent_div_17_div_1_div_5_li_4_Template, 2, 1, \"li\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.testResult.logs);\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Result:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, BrowserPreviewComponent_div_17_div_1_div_5_Template, 5, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.testResult.result, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.testResult.logs && ctx_r2.testResult.logs.length);\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"img\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r3.testResult.screenshot, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BrowserPreviewComponent_div_17_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\");\n    i0.ɵɵtext(2, \"No test run yet. Edit the script and click \\\"Run Test\\\" to start.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BrowserPreviewComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, BrowserPreviewComponent_div_17_div_1_Template, 6, 2, \"div\", 11);\n    i0.ɵɵtemplate(2, BrowserPreviewComponent_div_17_div_2_Template, 2, 1, \"div\", 11);\n    i0.ɵɵtemplate(3, BrowserPreviewComponent_div_17_div_3_Template, 3, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.testResult);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.testResult == null ? null : ctx_r1.testResult.screenshot);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.testResult);\n  }\n}\nexport let BrowserPreviewComponent = /*#__PURE__*/(() => {\n  class BrowserPreviewComponent {\n    constructor(sanitizer, renderer, http) {\n      this.sanitizer = sanitizer;\n      this.renderer = renderer;\n      this.http = http;\n      this.projectName = '';\n      this.url = '';\n      this.codeServerPort = 8081; // New input for code server port\n      this.expandChange = new EventEmitter();\n      this.loading = true;\n      this.testResult = null;\n      // Only local SearxNG instance for embedded search\n      this.searchEngines = [{\n        name: 'Local SearxNG',\n        url: 'http://localhost:8888'\n      }];\n      this.selectedEngine = this.searchEngines[0];\n      this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n      this.isBrowserMode = false;\n      this.engineUrl = `http://localhost:${this.codeServerPort}`; // Use dynamic port\n      this.searchLogs = [];\n      this.scriptText = `// Basic test: Open localhost:4200 and check for a button\\nawait page.goto('http://localhost:4200');\\nconst button = await page.$('button');\\nif (button) {\\n  console.log('Button found!');\\n  return 'Button found!';\\n} else {\\n  console.log('Button not found!');\\n  return 'Button not found!';\\n}\\n`;\n      this.runner = 'puppeteer';\n    }\n    ngOnInit() {\n      console.log('[BrowserPreviewComponent] ngOnInit called');\n      this.updateUrl();\n      this.engineUrl = `http://localhost:${this.codeServerPort}`; // Update engineUrl with current port\n      this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.engineUrl);\n    }\n    ngOnChanges(changes) {\n      console.log('[BrowserPreviewComponent] ngOnChanges called with:', changes);\n      if (changes['url']) {\n        console.log('[BrowserPreviewComponent] URL input changed:', changes['url'].currentValue);\n      }\n      if (changes['codeServerPort']) {\n        console.log('[BrowserPreviewComponent] Code server port changed:', changes['codeServerPort'].currentValue);\n        this.engineUrl = `http://localhost:${this.codeServerPort}`;\n        this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.engineUrl);\n      }\n      this.updateUrl();\n    }\n    ngAfterViewInit() {\n      // Set up message listener for iframe communication\n      window.addEventListener('message', this.handleIframeMessage.bind(this));\n    }\n    handleIframeMessage(event) {\n      // Check if the message is from our iframe (SearXNG)\n      if (event.source === this.iframe?.nativeElement.contentWindow) {\n        try {\n          const data = event.data;\n          if (data && data.type === 'searxng') {\n            console.log('SearXNG interaction:', data);\n            // Log search queries\n            if (data.action === 'search') {\n              this.logSearch(data.query, data.results);\n            }\n          }\n        } catch (error) {\n          console.error('Error processing iframe message:', error);\n        }\n      }\n    }\n    logSearch(query, results) {\n      this.searchLogs.push({\n        timestamp: new Date(),\n        query,\n        results\n      });\n      console.log('Search log updated:', this.searchLogs);\n    }\n    updateUrl() {\n      console.log('[BrowserPreviewComponent] updateUrl called with input URL:', this.url);\n      if (this.url) {\n        this.loading = true;\n        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.url);\n        console.log('[BrowserPreviewComponent] Safe URL created and loading set to true:', this.safeUrl);\n      } else {\n        console.warn('[BrowserPreviewComponent] No URL provided. Using default search engine.');\n        this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n      }\n    }\n    onIframeLoad() {\n      console.log('[BrowserPreviewComponent] onIframeLoad triggered — iframe finished loading.');\n      this.loading = false;\n      // Inject message listener into SearXNG iframe\n      if (this.iframe && this.isBrowserMode) {\n        try {\n          const iframeWindow = this.iframe.nativeElement.contentWindow;\n          // Only attempt to inject script if we have access to the iframe (same-origin)\n          if (iframeWindow) {\n            this.injectLoggerScript(iframeWindow);\n          }\n        } catch (e) {\n          console.warn('Could not inject script into iframe due to same-origin policy. This is expected for cross-origin iframes.', e);\n        }\n      }\n    }\n    injectLoggerScript(iframeWindow) {\n      try {\n        const script = this.renderer.createElement('script');\n        const scriptContent = `\n        // SearXNG interaction logger\n        (function() {\n          const originalFetch = window.fetch;\n          window.fetch = function(...args) {\n            const url = args[0].url || args[0];\n            \n            // Check if this is a search request\n            if (url.includes('/search')) {\n              const searchParams = new URL(url, window.location.origin).searchParams;\n              const query = searchParams.get('q');\n              \n              // Send the query to the parent window\n              if (query) {\n                return originalFetch.apply(this, args).then(response => {\n                  // Clone the response so we can read and use it\n                  const responseClone = response.clone();\n                  responseClone.json().then(results => {\n                    window.parent.postMessage({\n                      type: 'searxng',\n                      action: 'search',\n                      query: query,\n                      results: results\n                    }, '*');\n                  }).catch(e => console.error('Error parsing SearXNG response:', e));\n                  \n                  return response;\n                });\n              }\n            }\n            \n            return originalFetch.apply(this, args);\n          };\n          \n          console.log('SearXNG logger injected');\n        })();\n      `;\n        this.renderer.appendChild(script, this.renderer.createText(scriptContent));\n        // Try to append to document if possible\n        try {\n          const iframeDocument = iframeWindow.document;\n          this.renderer.appendChild(iframeDocument.head || iframeDocument.body, script);\n          console.log('Successfully injected SearXNG logger script');\n        } catch (e) {\n          console.warn('Could not inject script into iframe document:', e);\n        }\n      } catch (e) {\n        console.error('Error creating logger script:', e);\n      }\n    }\n    refresh() {\n      console.log('[BrowserPreviewComponent] refresh called');\n      if (this.iframe && this.iframe.nativeElement) {\n        this.loading = true;\n        const currentSrc = this.iframe.nativeElement.src;\n        this.iframe.nativeElement.src = currentSrc;\n        console.log('[BrowserPreviewComponent] Iframe refreshed with current src:', currentSrc);\n      } else {\n        console.warn('[BrowserPreviewComponent] Iframe reference not available');\n      }\n    }\n    onEngineChange() {\n      this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n    }\n    toggleMode() {\n      this.isBrowserMode = !this.isBrowserMode;\n      this.loading = true;\n      if (this.isBrowserMode) {\n        this.url = '';\n        this.engineSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedEngine.url);\n      }\n    }\n    runTest() {\n      this.loading = true;\n      this.testResult = null;\n      this.http.get('http://localhost:4000/run-tests').subscribe({\n        next: res => {\n          this.testResult = res;\n          this.loading = false;\n        },\n        error: err => {\n          this.testResult = {\n            result: 'Error running test: ' + (err?.message || err)\n          };\n          this.loading = false;\n        }\n      });\n    }\n    runDynamicTest() {\n      this.loading = true;\n      this.testResult = null;\n      this.http.post('http://localhost:4000/run-script', {\n        script: this.scriptText,\n        runner: this.runner\n      }).subscribe({\n        next: res => {\n          this.testResult = res;\n          this.loading = false;\n        },\n        error: err => {\n          this.testResult = {\n            result: 'Error running script: ' + (err?.message || err),\n            logs: err?.error?.logs || []\n          };\n          this.loading = false;\n        }\n      });\n    }\n    static {\n      this.ɵfac = function BrowserPreviewComponent_Factory(t) {\n        return new (t || BrowserPreviewComponent)(i0.ɵɵdirectiveInject(i1.DomSanitizer), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: BrowserPreviewComponent,\n        selectors: [[\"app-browser-preview\"]],\n        viewQuery: function BrowserPreviewComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.iframe = _t.first);\n          }\n        },\n        inputs: {\n          projectName: \"projectName\",\n          url: \"url\",\n          codeServerPort: \"codeServerPort\"\n        },\n        outputs: {\n          expandChange: \"expandChange\"\n        },\n        features: [i0.ɵɵNgOnChangesFeature],\n        decls: 18,\n        vars: 5,\n        consts: [[1, \"browser-container\"], [1, \"browser-header\"], [1, \"engine-name\"], [1, \"browser-actions\"], [2, \"margin-right\", \"8px\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"puppeteer\"], [\"value\", \"playwright\"], [\"title\", \"Run Dynamic Test\", 1, \"icon-btn\", 3, \"disabled\", \"click\"], [1, \"browser-content\", \"card\"], [\"rows\", \"8\", \"placeholder\", \"Enter Puppeteer/Playwright script here\", 2, \"width\", \"100%\", \"font-family\", \"monospace\", \"margin-bottom\", \"8px\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"loading-overlay\"], [1, \"spinner\"], [4, \"ngFor\", \"ngForOf\"], [\"alt\", \"Test Screenshot\", 2, \"max-width\", \"100%\", \"border-radius\", \"8px\", \"box-shadow\", \"0 2px 8px #0002\", 3, \"src\"]],\n        template: function BrowserPreviewComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n            i0.ɵɵtext(3, \"Test Results \");\n            i0.ɵɵelementStart(4, \"span\", 2);\n            i0.ɵɵtext(5, \"(AI Automated)\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 3)(7, \"select\", 4);\n            i0.ɵɵlistener(\"ngModelChange\", function BrowserPreviewComponent_Template_select_ngModelChange_7_listener($event) {\n              return ctx.runner = $event;\n            });\n            i0.ɵɵelementStart(8, \"option\", 5);\n            i0.ɵɵtext(9, \"Puppeteer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"option\", 6);\n            i0.ɵɵtext(11, \"Playwright\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function BrowserPreviewComponent_Template_button_click_12_listener() {\n              return ctx.runDynamicTest();\n            });\n            i0.ɵɵtext(13, \" \\u25B6\\uFE0F Run Test \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(14, \"div\", 8)(15, \"textarea\", 9);\n            i0.ɵɵlistener(\"ngModelChange\", function BrowserPreviewComponent_Template_textarea_ngModelChange_15_listener($event) {\n              return ctx.scriptText = $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(16, BrowserPreviewComponent_div_16_Template, 4, 0, \"div\", 10);\n            i0.ɵɵtemplate(17, BrowserPreviewComponent_div_17_Template, 4, 3, \"div\", 11);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngModel\", ctx.runner);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngModel\", ctx.scriptText);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgModel],\n        styles: [\".browser-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100%;border:1px solid #e0e0e0;border-radius:12px;overflow:hidden;background:#fff;box-shadow:0 2px 12px #0000000f;transition:box-shadow .2s,border-radius .2s}.browser-container.expanded[_ngcontent-%COMP%]{position:relative;z-index:10;height:80vh;width:100%;border-radius:12px;box-shadow:0 4px 24px #0000001a}.browser-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 24px 16px 20px;background-color:#f7fafd;border-bottom:1px solid #e0e0e0}.browser-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:20px;font-weight:700;display:flex;align-items:center}.engine-name[_ngcontent-%COMP%]{font-size:15px;font-weight:400;color:#888;margin-left:12px}.browser-actions[_ngcontent-%COMP%]{display:flex;gap:12px}.icon-btn[_ngcontent-%COMP%]{background:#f7fafd;border:1px solid #e0e0e0;border-radius:6px;padding:7px 12px;font-size:20px;cursor:pointer;transition:background .2s,border .2s,box-shadow .2s;outline:none;box-shadow:0 1px 2px #0000000a;color:#1976d2}.icon-btn[_ngcontent-%COMP%]:disabled{background:#f5f5f5;color:#bbb;cursor:not-allowed}.icon-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#e3f2fd;border-color:#90caf9;color:#0d47a1}.browser-content[_ngcontent-%COMP%]{flex:1;position:relative;padding:24px 24px 20px;background:#fafdff;min-height:350px}.card[_ngcontent-%COMP%]{border-radius:12px;background:#fff;box-shadow:0 2px 12px #0000000f;padding:0;height:100%}.loading-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background-color:#fffc;display:flex;flex-direction:column;justify-content:center;align-items:center;z-index:10}.spinner[_ngcontent-%COMP%]{width:32px;height:32px;border:3px solid rgba(0,0,0,.1);border-radius:50%;border-top-color:#3498db;animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite;margin-bottom:8px}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}.browser-iframe[_ngcontent-%COMP%]{height:60vh;width:100%;border:none;border-radius:10px;background:#fff;box-shadow:0 1px 4px #0000000a;margin-top:8px}.browser-container.expanded[_ngcontent-%COMP%]   .browser-iframe[_ngcontent-%COMP%]{height:75vh;width:100%;border-radius:10px}.empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;height:300px;color:#888;font-style:italic;font-size:18px;background:#fafdff;border-radius:10px;margin-top:30px}\"]\n      });\n    }\n  }\n  return BrowserPreviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}