{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./socket-factory.service\";\nexport let SocketService = /*#__PURE__*/(() => {\n  class SocketService {\n    constructor(socketFactory) {\n      this.socketFactory = socketFactory;\n    }\n    connect() {\n      this.socketFactory.connect();\n    }\n    disconnect() {\n      this.socketFactory.disconnect();\n    }\n    on(eventName) {\n      return this.socketFactory.on(eventName);\n    }\n    emit(eventName, data) {\n      this.socketFactory.emit(eventName, data);\n    }\n    sendMessage(projectName, message, modelId) {\n      this.socketFactory.emit('user_message', {\n        project_name: projectName,\n        message: message,\n        model_id: modelId\n      });\n    }\n    static {\n      this.ɵfac = function SocketService_Factory(t) {\n        return new (t || SocketService)(i0.ɵɵinject(i1.SocketFactoryService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SocketService,\n        factory: SocketService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SocketService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}