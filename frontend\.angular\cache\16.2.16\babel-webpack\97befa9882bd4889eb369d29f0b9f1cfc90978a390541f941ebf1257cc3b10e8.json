{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { operate } from '../util/lift';\nexport function switchScan(accumulator, seed) {\n  return operate((source, subscriber) => {\n    let state = seed;\n    switchMap((value, index) => accumulator(state, value, index), (_, innerValue) => (state = innerValue, innerValue))(source).subscribe(subscriber);\n    return () => {\n      state = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["switchMap", "operate", "switchScan", "accumulator", "seed", "source", "subscriber", "state", "value", "index", "_", "innerValue", "subscribe"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/rxjs/dist/esm/internal/operators/switchScan.js"], "sourcesContent": ["import { switchMap } from './switchMap';\nimport { operate } from '../util/lift';\nexport function switchScan(accumulator, seed) {\n    return operate((source, subscriber) => {\n        let state = seed;\n        switchMap((value, index) => accumulator(state, value, index), (_, innerValue) => ((state = innerValue), innerValue))(source).subscribe(subscriber);\n        return () => {\n            state = null;\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASC,UAAUA,CAACC,WAAW,EAAEC,IAAI,EAAE;EAC1C,OAAOH,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,KAAK,GAAGH,IAAI;IAChBJ,SAAS,CAAC,CAACQ,KAAK,EAAEC,KAAK,KAAKN,WAAW,CAACI,KAAK,EAAEC,KAAK,EAAEC,KAAK,CAAC,EAAE,CAACC,CAAC,EAAEC,UAAU,MAAOJ,KAAK,GAAGI,UAAU,EAAGA,UAAU,CAAC,CAAC,CAACN,MAAM,CAAC,CAACO,SAAS,CAACN,UAAU,CAAC;IAClJ,OAAO,MAAM;MACTC,KAAK,GAAG,IAAI;IAChB,CAAC;EACL,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}