{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ReversePipe {\n  transform(value) {\n    if (!Array.isArray(value)) return value;\n    return [...value].reverse();\n  }\n  static {\n    this.ɵfac = function ReversePipe_Factory(t) {\n      return new (t || ReversePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"reverse\",\n      type: ReversePipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["ReversePipe", "transform", "value", "Array", "isArray", "reverse", "pure"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\chat\\reverse.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n  name: 'reverse'\r\n})\r\nexport class ReversePipe implements PipeTransform {\r\n  transform(value: any[]): any[] {\r\n    if (!Array.isArray(value)) return value;\r\n    return [...value].reverse();\r\n  }\r\n} "], "mappings": ";AAKA,OAAM,MAAOA,WAAW;EACtBC,SAASA,CAACC,KAAY;IACpB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE,OAAOA,KAAK;IACvC,OAAO,CAAC,GAAGA,KAAK,CAAC,CAACG,OAAO,EAAE;EAC7B;;;uBAJWL,WAAW;IAAA;EAAA;;;;YAAXA,WAAW;MAAAM,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}