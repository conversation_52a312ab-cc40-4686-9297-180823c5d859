{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { io } from 'socket.io-client';\nimport * as i0 from \"@angular/core\";\nexport let SocketFactoryService = /*#__PURE__*/(() => {\n  class SocketFactoryService {\n    constructor() {\n      this.socket = io('http://localhost:5000', {\n        transports: ['websocket']\n      });\n      console.log('Socket connection initialized');\n    }\n    connect() {\n      if (!this.socket.connected) {\n        this.socket.connect();\n      }\n    }\n    disconnect() {\n      if (this.socket.connected) {\n        this.socket.disconnect();\n      }\n    }\n    emit(eventName, data) {\n      this.socket.emit(eventName, data);\n    }\n    on(eventName) {\n      return new Observable(observer => {\n        this.socket.on(eventName, data => {\n          observer.next(data);\n        });\n        return () => {\n          this.socket.off(eventName);\n        };\n      });\n    }\n    once(eventName) {\n      return new Observable(observer => {\n        this.socket.once(eventName, data => {\n          observer.next(data);\n          observer.complete();\n        });\n        return () => {\n          this.socket.off(eventName);\n        };\n      });\n    }\n    static {\n      this.ɵfac = function SocketFactoryService_Factory(t) {\n        return new (t || SocketFactoryService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SocketFactoryService,\n        factory: SocketFactoryService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SocketFactoryService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}