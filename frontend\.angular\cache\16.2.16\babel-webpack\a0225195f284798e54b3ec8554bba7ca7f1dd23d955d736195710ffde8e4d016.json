{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // Return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = cssWithMappingToString(item);\n      if (item[2]) {\n        return \"@media \".concat(item[2], \" {\").concat(content, \"}\");\n      }\n      return content;\n    }).join(\"\");\n  };\n  return list;\n};", "map": {"version": 3, "names": ["module", "exports", "cssWithMappingToString", "list", "toString", "map", "item", "content", "concat", "join"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/css-loader/dist/runtime/api.js"], "sourcesContent": ["\n\"use strict\";\n\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n  \n  // Return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = cssWithMappingToString(item);\n      \n      if (item[2]) {\n        return \"@media \".concat(item[2], \" {\").concat(content, \"}\");\n      }\n      \n      return content;\n    }).join(\"\");\n  };\n  \n  return list;\n};\n"], "mappings": "AACA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,UAAUC,sBAAsB,EAAE;EACjD,IAAIC,IAAI,GAAG,EAAE;;EAEb;EACAA,IAAI,CAACC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IAClC,OAAO,IAAI,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC9B,IAAIC,OAAO,GAAGL,sBAAsB,CAACI,IAAI,CAAC;MAE1C,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;QACX,OAAO,SAAS,CAACE,MAAM,CAACF,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACE,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC;MAC7D;MAEA,OAAOA,OAAO;IAChB,CAAC,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;EACb,CAAC;EAED,OAAON,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}