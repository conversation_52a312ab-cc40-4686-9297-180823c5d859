{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let ReversePipe = /*#__PURE__*/(() => {\n  class ReversePipe {\n    transform(value) {\n      if (!Array.isArray(value)) return value;\n      return [...value].reverse();\n    }\n    static {\n      this.ɵfac = function ReversePipe_Factory(t) {\n        return new (t || ReversePipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"reverse\",\n        type: ReversePipe,\n        pure: true\n      });\n    }\n  }\n  return ReversePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}