{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nexport function throwError(errorOrErrorFactory, scheduler) {\n  const errorFactory = isFunction(errorOrErrorFactory) ? errorOrErrorFactory : () => errorOrErrorFactory;\n  const init = subscriber => subscriber.error(errorFactory());\n  return new Observable(scheduler ? subscriber => scheduler.schedule(init, 0, subscriber) : init);\n}\n//# sourceMappingURL=throwError.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}