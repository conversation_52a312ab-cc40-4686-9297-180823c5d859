{"ast": null, "code": "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";", "map": {"version": 3, "names": ["Socket", "protocol", "Transport", "TransportError", "transports", "installTimerFunctions", "parse", "nextTick"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/engine.io-client/build/esm/index.js"], "sourcesContent": ["import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASA,MAAM;AACf,OAAO,MAAMC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;AACvC,SAASC,SAAS,EAAEC,cAAc,QAAQ,gBAAgB;AAC1D,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,qBAAqB,QAAQ,WAAW;AACjD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,QAAQ,QAAQ,uCAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}