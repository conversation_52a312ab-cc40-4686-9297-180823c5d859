{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/socket.service\";\nimport * as i2 from \"@angular/router\";\nconst _c0 = function () {\n  return [\"/\"];\n};\nconst _c1 = function () {\n  return [\"/projects\"];\n};\nconst _c2 = function () {\n  return [\"/testing\"];\n};\nconst _c3 = function () {\n  return [\"/config\"];\n};\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(socketService) {\n      this.socketService = socketService;\n      this.title = 'Autonomous AI Software Development Agent';\n    }\n    ngOnInit() {\n      this.socketService.connect();\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.SocketService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        decls: 18,\n        vars: 9,\n        consts: [[1, \"app-container\"], [1, \"app-header\"], [3, \"routerLink\"], [1, \"app-content\"], [1, \"app-footer\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"h1\");\n            i0.ɵɵtext(3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"nav\")(5, \"a\", 2);\n            i0.ɵɵtext(6, \"Home\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"a\", 2);\n            i0.ɵɵtext(8, \"Projects\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"a\", 2);\n            i0.ɵɵtext(10, \"Testing\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"a\", 2);\n            i0.ɵɵtext(12, \"Configuration\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"main\", 3);\n            i0.ɵɵelement(14, \"router-outlet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"footer\", 4)(16, \"p\");\n            i0.ɵɵtext(17, \"Autonomous AI Software Development Agent \\u00A9 2025\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.title);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c0));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c1));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c2));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(8, _c3));\n          }\n        },\n        dependencies: [i2.RouterOutlet, i2.RouterLink],\n        styles: [\".app-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:100vh}.app-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#2196f3 0%,#1976d2 100%);color:#fff;padding:16px 24px;box-shadow:0 2px 10px #0000001a;display:flex;justify-content:space-between;align-items:center}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;font-weight:500;margin:0;display:flex;align-items:center}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:before{content:\\\"code\\\";font-family:Material Icons;margin-right:10px;font-size:28px}.app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]{display:flex;gap:20px}.app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff;text-decoration:none;font-weight:500;padding:8px 12px;border-radius:4px;position:relative;transition:all .3s ease}.app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{background-color:#ffffff1a}.app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%]{background-color:#fff3}.app-content[_ngcontent-%COMP%]{flex:1;padding:20px;background-color:#f5f7fa}.app-footer[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px 24px;text-align:center;border-top:1px solid #e0e0e0}.app-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:14px}@media (max-width: 768px){.app-header[_ngcontent-%COMP%]{flex-direction:column;padding:16px}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin-bottom:16px}.app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]{width:100%;justify-content:space-between}}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}