{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../../services/project.service\";\nimport * as i3 from \"../../services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction TestingComponent_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const project_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", project_r5.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(project_r5.name);\n  }\n}\nfunction TestingComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"span\", 36);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Running tests... This may take a few moments.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TestingComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Tests in progress...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TestingComponent_div_49_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 48);\n  }\n}\nfunction TestingComponent_div_49_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 49);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_25_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Line \", error_r13.line, \"\");\n  }\n}\nfunction TestingComponent_div_49_div_12_div_25_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"span\", 68);\n    i0.ɵɵtext(2, \"Auto-fix:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 69);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const error_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(error_r13.fix);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_25_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"span\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TestingComponent_div_49_div_12_div_25_div_3_span_4_Template, 2, 1, \"span\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 64);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, TestingComponent_div_49_div_12_div_25_div_3_div_7_Template, 5, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r13 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(error_r13.file);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", error_r13.line);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(error_r13.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", error_r13.fix);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"h5\");\n    i0.ɵɵtext(2, \"Errors\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TestingComponent_div_49_div_12_div_25_div_3_Template, 8, 4, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.testResults.testing.errors);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_26_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fix_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Line \", fix_r19.line, \"\");\n  }\n}\nfunction TestingComponent_div_49_div_12_div_26_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"span\", 74);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TestingComponent_div_49_div_12_div_26_div_3_span_4_Template, 2, 1, \"span\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 76);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fix_r19 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(fix_r19.file);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", fix_r19.line);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(fix_r19.description);\n  }\n}\nfunction TestingComponent_div_49_div_12_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"h5\");\n    i0.ɵɵtext(2, \"Applied Fixes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TestingComponent_div_49_div_12_div_26_div_3_Template, 7, 3, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.testResults.fixes);\n  }\n}\nfunction TestingComponent_div_49_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"h5\");\n    i0.ɵɵtext(3, \"Test Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 52)(5, \"div\", 53)(6, \"span\", 54);\n    i0.ɵɵtext(7, \"Total Tests:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 55);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 53)(11, \"span\", 54);\n    i0.ɵɵtext(12, \"Passed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 55);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 53)(16, \"span\", 54);\n    i0.ɵɵtext(17, \"Failed:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 55);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 53)(21, \"span\", 54);\n    i0.ɵɵtext(22, \"Duration:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 55);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(25, TestingComponent_div_49_div_12_div_25_Template, 4, 1, \"div\", 56);\n    i0.ɵɵtemplate(26, TestingComponent_div_49_div_12_div_26_Template, 4, 1, \"div\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r8.testResults.testing.total_tests || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.testResults.testing.passed_tests || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.testResults.testing.failed_tests || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r8.testResults.testing.duration || 0, \"s\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.testResults.testing.errors && ctx_r8.testResults.testing.errors.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.testResults.fixes && ctx_r8.testResults.fixes.length > 0);\n  }\n}\nfunction TestingComponent_div_49_div_13_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 48);\n  }\n}\nfunction TestingComponent_div_49_div_13_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 49);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"validation-success\": a0,\n    \"validation-failure\": a1\n  };\n};\nfunction TestingComponent_div_49_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"h5\");\n    i0.ɵɵtext(2, \"Browser Validation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79);\n    i0.ɵɵtemplate(4, TestingComponent_div_49_div_13_i_4_Template, 1, 0, \"i\", 43);\n    i0.ɵɵtemplate(5, TestingComponent_div_49_div_13_i_5_Template, 1, 0, \"i\", 44);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, ctx_r9.testResults.validation.success, !ctx_r9.testResults.validation.success));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.testResults.validation.success);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.testResults.validation.success);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.testResults.validation.message);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"test-success\": a0,\n    \"test-failure\": a1\n  };\n};\nfunction TestingComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"h3\");\n    i0.ɵɵtext(2, \"Test Results\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 41)(4, \"div\", 42);\n    i0.ɵɵtemplate(5, TestingComponent_div_49_i_5_Template, 1, 0, \"i\", 43);\n    i0.ɵɵtemplate(6, TestingComponent_div_49_i_6_Template, 1, 0, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 45)(8, \"h4\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, TestingComponent_div_49_div_12_Template, 27, 6, \"div\", 46);\n    i0.ɵɵtemplate(13, TestingComponent_div_49_div_13_Template, 8, 7, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c1, ctx_r3.testResults.success, !ctx_r3.testResults.success));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testResults.success);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.testResults.success);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.testResults.success ? \"Tests Passed\" : \"Tests Failed\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.testResults.message || \"Test execution completed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testResults.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testResults.validation);\n  }\n}\nfunction TestingComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"p\");\n    i0.ɵɵtext(2, \"No test results available. Run tests to see results.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TestingComponent {\n  constructor(http, projectService, notificationService) {\n    this.http = http;\n    this.projectService = projectService;\n    this.notificationService = notificationService;\n    this.projects = [];\n    this.selectedProject = '';\n    this.testResults = null;\n    this.isLoading = false;\n    this.testConfig = {\n      autoFix: true,\n      autoValidate: true,\n      browserValidation: true\n    };\n    this.testingInProgress = false;\n    this.continuousTestingActive = false;\n  }\n  ngOnInit() {\n    this.loadProjects();\n    this.loadTestConfig();\n  }\n  loadProjects() {\n    this.projectService.getProjects().subscribe(data => {\n      this.projects = data.projects;\n      if (this.projects.length > 0) {\n        this.selectedProject = this.projects[0].name;\n      }\n    }, error => {\n      console.error('Error loading projects:', error);\n      this.notificationService.showError('Failed to load projects');\n    });\n  }\n  loadTestConfig() {\n    debugger;\n    this.http.get(`${environment.apiUrl}/api/testing/config`).subscribe(data => {\n      if (data.testing) {\n        this.testConfig = {\n          autoFix: data.testing.auto_fix,\n          autoValidate: data.testing.auto_validate,\n          browserValidation: data.testing.browser_validation\n        };\n      }\n    }, error => {\n      console.error('Error loading test config:', error);\n    });\n  }\n  runTests() {\n    if (!this.selectedProject) {\n      this.notificationService.showError('Please select a project');\n      return;\n    }\n    this.isLoading = true;\n    this.testingInProgress = true;\n    this.testResults = null;\n    const projectDir = this.projectService.getProjectDir(this.selectedProject);\n    this.http.post(`${environment.apiUrl}/api/testing/run`, {\n      project_dir: projectDir,\n      auto_fix: this.testConfig.autoFix,\n      auto_validate: this.testConfig.autoValidate,\n      browser_url: `http://localhost:4200/projects/${this.selectedProject}/preview`\n    }).subscribe(data => {\n      this.testResults = data.results;\n      this.isLoading = false;\n      this.testingInProgress = false;\n      if (data.success) {\n        this.notificationService.showSuccess('Tests completed successfully');\n      } else {\n        this.notificationService.showWarning('Tests completed with issues');\n      }\n    }, error => {\n      console.error('Error running tests:', error);\n      this.isLoading = false;\n      this.testingInProgress = false;\n      this.notificationService.showError('Failed to run tests');\n    });\n  }\n  startContinuousTesting() {\n    if (!this.selectedProject) {\n      this.notificationService.showError('Please select a project');\n      return;\n    }\n    const projectDir = this.projectService.getProjectDir(this.selectedProject);\n    this.http.post(`${environment.apiUrl}/api/testing/continuous`, {\n      project_dir: projectDir,\n      auto_fix: this.testConfig.autoFix,\n      auto_validate: this.testConfig.autoValidate,\n      browser_url: `http://localhost:4200/projects/${this.selectedProject}/preview`\n    }).subscribe(data => {\n      this.continuousTestingActive = true;\n      this.notificationService.showSuccess('Continuous testing started');\n    }, error => {\n      console.error('Error starting continuous testing:', error);\n      this.notificationService.showError('Failed to start continuous testing');\n    });\n  }\n  openInVSCode() {\n    if (!this.selectedProject) {\n      this.notificationService.showError('Please select a project');\n      return;\n    }\n    const projectDir = this.projectService.getProjectDir(this.selectedProject);\n    this.http.post(`${environment.apiUrl}/api/testing/vscode`, {\n      project_dir: projectDir,\n      setup_workspace: true\n    }).subscribe(data => {\n      this.notificationService.showSuccess('Project opened in VS Code');\n    }, error => {\n      console.error('Error opening project in VS Code:', error);\n      this.notificationService.showError('Failed to open project in VS Code');\n    });\n  }\n  runDemo() {\n    const demoDir = `${this.projectService.getBaseDir()}/demo`;\n    this.isLoading = true;\n    this.testingInProgress = true;\n    this.testResults = null;\n    this.http.post(`${environment.apiUrl}/api/testing/demo`, {\n      project_dir: demoDir\n    }).subscribe(data => {\n      this.testResults = data.test_results_with_error;\n      this.isLoading = false;\n      this.testingInProgress = false;\n      this.notificationService.showSuccess('Demo completed successfully');\n    }, error => {\n      console.error('Error running demo:', error);\n      this.isLoading = false;\n      this.testingInProgress = false;\n      this.notificationService.showError('Failed to run demo');\n    });\n  }\n  updateTestConfig() {\n    this.http.put(`${environment.apiUrl}/api/testing/config`, {\n      testing: {\n        auto_fix: this.testConfig.autoFix,\n        auto_validate: this.testConfig.autoValidate,\n        browser_validation: this.testConfig.browserValidation\n      }\n    }).subscribe(data => {\n      this.notificationService.showSuccess('Test configuration updated');\n    }, error => {\n      console.error('Error updating test config:', error);\n      this.notificationService.showError('Failed to update test configuration');\n    });\n  }\n  stopTesting() {\n    if (!this.selectedProject) {\n      return;\n    }\n    const projectDir = this.projectService.getProjectDir(this.selectedProject);\n    this.http.post(`${environment.apiUrl}/api/testing/stop`, {\n      project_dir: projectDir\n    }).subscribe(data => {\n      this.testingInProgress = false;\n      this.isLoading = false;\n      this.notificationService.showSuccess('Testing stopped');\n    }, error => {\n      console.error('Error stopping tests:', error);\n      this.notificationService.showError('Failed to stop testing');\n    });\n  }\n  static {\n    this.ɵfac = function TestingComponent_Factory(t) {\n      return new (t || TestingComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TestingComponent,\n      selectors: [[\"app-testing\"]],\n      decls: 51,\n      vars: 14,\n      consts: [[1, \"testing-container\"], [1, \"testing-header\"], [1, \"testing-content\"], [1, \"testing-controls\"], [1, \"form-group\"], [\"for\", \"project-select\"], [\"id\", \"project-select\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"testing-options\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"auto-fix\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"auto-fix\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"auto-validate\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"auto-validate\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"browser-validation\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"browser-validation\", 1, \"form-check-label\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"testing-actions\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-vial\"], [1, \"btn\", \"btn-info\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"btn\", \"btn-danger\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-stop\"], [1, \"btn\", \"btn-secondary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-code\"], [1, \"btn\", \"btn-outline-primary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-play\"], [1, \"testing-results\"], [\"class\", \"testing-loading\", 4, \"ngIf\"], [\"class\", \"testing-progress\", 4, \"ngIf\"], [\"class\", \"test-results-container\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [3, \"value\"], [1, \"testing-loading\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"sr-only\"], [1, \"testing-progress\"], [1, \"progress\"], [\"role\", \"progressbar\", 1, \"progress-bar\", \"progress-bar-striped\", \"progress-bar-animated\", 2, \"width\", \"100%\"], [1, \"test-results-container\"], [1, \"test-summary\", 3, \"ngClass\"], [1, \"test-status\"], [\"class\", \"fas fa-check-circle\", 4, \"ngIf\"], [\"class\", \"fas fa-times-circle\", 4, \"ngIf\"], [1, \"test-info\"], [\"class\", \"test-details\", 4, \"ngIf\"], [\"class\", \"validation-results\", 4, \"ngIf\"], [1, \"fas\", \"fa-check-circle\"], [1, \"fas\", \"fa-times-circle\"], [1, \"test-details\"], [1, \"test-section\"], [1, \"test-stats\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"test-errors\", 4, \"ngIf\"], [\"class\", \"test-fixes\", 4, \"ngIf\"], [1, \"test-errors\"], [\"class\", \"error-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"error-item\"], [1, \"error-header\"], [1, \"error-file\"], [\"class\", \"error-line\", 4, \"ngIf\"], [1, \"error-message\"], [\"class\", \"error-fix\", 4, \"ngIf\"], [1, \"error-line\"], [1, \"error-fix\"], [1, \"fix-label\"], [1, \"fix-message\"], [1, \"test-fixes\"], [\"class\", \"fix-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"fix-item\"], [1, \"fix-header\"], [1, \"fix-file\"], [\"class\", \"fix-line\", 4, \"ngIf\"], [1, \"fix-description\"], [1, \"fix-line\"], [1, \"validation-results\"], [1, \"validation-status\", 3, \"ngClass\"], [1, \"no-results\"]],\n      template: function TestingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Autonomous Testing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"Run autonomous tests, fix errors, and validate functionality automatically.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"Select Project\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"select\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function TestingComponent_Template_select_ngModelChange_11_listener($event) {\n            return ctx.selectedProject = $event;\n          });\n          i0.ɵɵtemplate(12, TestingComponent_option_12_Template, 2, 2, \"option\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"h3\");\n          i0.ɵɵtext(15, \"Testing Options\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 9)(17, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function TestingComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.testConfig.autoFix = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"label\", 11);\n          i0.ɵɵtext(19, \"Auto-fix errors\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 9)(21, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function TestingComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.testConfig.autoValidate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"label\", 13);\n          i0.ɵɵtext(23, \"Auto-validate in browser\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 9)(25, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function TestingComponent_Template_input_ngModelChange_25_listener($event) {\n            return ctx.testConfig.browserValidation = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"label\", 15);\n          i0.ɵɵtext(27, \"Browser validation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_28_listener() {\n            return ctx.updateTestConfig();\n          });\n          i0.ɵɵtext(29, \"Update Config\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 17)(31, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_31_listener() {\n            return ctx.runTests();\n          });\n          i0.ɵɵelement(32, \"i\", 19);\n          i0.ɵɵtext(33, \" Run Tests \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_34_listener() {\n            return ctx.startContinuousTesting();\n          });\n          i0.ɵɵelement(35, \"i\", 21);\n          i0.ɵɵtext(36, \" Start Continuous Testing \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_37_listener() {\n            return ctx.stopTesting();\n          });\n          i0.ɵɵelement(38, \"i\", 23);\n          i0.ɵɵtext(39, \" Stop Testing \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_40_listener() {\n            return ctx.openInVSCode();\n          });\n          i0.ɵɵelement(41, \"i\", 25);\n          i0.ɵɵtext(42, \" Open in VS Code \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function TestingComponent_Template_button_click_43_listener() {\n            return ctx.runDemo();\n          });\n          i0.ɵɵelement(44, \"i\", 27);\n          i0.ɵɵtext(45, \" Run Demo \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"div\", 28);\n          i0.ɵɵtemplate(47, TestingComponent_div_47_Template, 6, 0, \"div\", 29);\n          i0.ɵɵtemplate(48, TestingComponent_div_48_Template, 5, 0, \"div\", 30);\n          i0.ɵɵtemplate(49, TestingComponent_div_49_Template, 14, 10, \"div\", 31);\n          i0.ɵɵtemplate(50, TestingComponent_div_50_Template, 3, 0, \"div\", 32);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedProject);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.projects);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngModel\", ctx.testConfig.autoFix);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.testConfig.autoValidate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.testConfig.browserValidation);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.selectedProject);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.selectedProject || ctx.continuousTestingActive);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.isLoading && !ctx.testingInProgress);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedProject);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.testingInProgress && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.testResults && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.testResults && !ctx.isLoading && !ctx.testingInProgress);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.CheckboxControlValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".testing-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.testing-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.testing-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  color: #333;\\n}\\n.testing-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.testing-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n  gap: 20px;\\n}\\n@media (max-width: 992px) {\\n  .testing-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.testing-controls[_ngcontent-%COMP%] {\\n  flex: 0 0 300px;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n@media (max-width: 992px) {\\n  .testing-controls[_ngcontent-%COMP%] {\\n    flex: 0 0 auto;\\n  }\\n}\\n\\n.testing-results[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 20px;\\n  overflow-y: auto;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n}\\n.form-group[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 8px 12px;\\n  border: 1px solid #ced4da;\\n  border-radius: 4px;\\n  background-color: #fff;\\n}\\n\\n.testing-options[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin-bottom: 10px;\\n}\\n.testing-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.testing-options[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.testing-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n\\n.testing-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n}\\n.testing-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n}\\n.testing-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-weight: 500;\\n  transition: all 0.2s;\\n}\\n.btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n  opacity: 0.6;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border: 1px solid #007bff;\\n  color: white;\\n}\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #0069d9;\\n  border-color: #0062cc;\\n}\\n\\n.btn-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  border: 1px solid #17a2b8;\\n  color: white;\\n}\\n.btn-info[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #138496;\\n  border-color: #117a8b;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border: 1px solid #6c757d;\\n  color: white;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #5a6268;\\n  border-color: #545b62;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border: 1px solid #007bff;\\n  color: #007bff;\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.testing-loading[_ngcontent-%COMP%], .testing-progress[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 0;\\n  text-align: center;\\n}\\n.testing-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .testing-progress[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  color: #666;\\n}\\n\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n.progress[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  margin-bottom: 10px;\\n  background-color: #e9ecef;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background-color: #007bff;\\n}\\n\\n.test-results-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n  font-size: 18px;\\n}\\n\\n.test-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px;\\n  border-radius: 6px;\\n  margin-bottom: 20px;\\n}\\n.test-summary.test-success[_ngcontent-%COMP%] {\\n  background-color: rgba(40, 167, 69, 0.1);\\n  border: 1px solid rgba(40, 167, 69, 0.2);\\n}\\n.test-summary.test-failure[_ngcontent-%COMP%] {\\n  background-color: rgba(220, 53, 69, 0.1);\\n  border: 1px solid rgba(220, 53, 69, 0.2);\\n}\\n.test-summary[_ngcontent-%COMP%]   .test-status[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-right: 15px;\\n}\\n.test-summary[_ngcontent-%COMP%]   .test-status[_ngcontent-%COMP%]   i.fa-check-circle[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.test-summary[_ngcontent-%COMP%]   .test-status[_ngcontent-%COMP%]   i.fa-times-circle[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.test-summary[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 16px;\\n}\\n.test-summary[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n}\\n\\n.test-details[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.test-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.test-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin-bottom: 10px;\\n  padding-bottom: 5px;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n\\n.test-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 15px;\\n}\\n.test-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 10px 15px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  flex: 1 0 calc(25% - 15px);\\n  min-width: 120px;\\n}\\n.test-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: #6c757d;\\n  margin-bottom: 5px;\\n}\\n.test-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n}\\n\\n.test-errors[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n.test-errors[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin-bottom: 10px;\\n  padding-bottom: 5px;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 15px;\\n  border-radius: 4px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 10px;\\n}\\n.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-file[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-file[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .error-line[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-header[_ngcontent-%COMP%]   .fix-line[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-description[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-description[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .fix-description[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .fix-description[_ngcontent-%COMP%] {\\n  font-family: monospace;\\n  background-color: #f8f9fa;\\n  padding: 8px;\\n  border-radius: 4px;\\n  margin-bottom: 8px;\\n  white-space: pre-wrap;\\n  word-break: break-all;\\n}\\n.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-label[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-label[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-label[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-right: 5px;\\n}\\n.test-errors[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-message[_ngcontent-%COMP%], .test-errors[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-message[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .error-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-message[_ngcontent-%COMP%], .test-fixes[_ngcontent-%COMP%]   .fix-item[_ngcontent-%COMP%]   .error-fix[_ngcontent-%COMP%]   .fix-message[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n\\n.validation-results[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n.validation-results[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin-bottom: 10px;\\n  padding-bottom: 5px;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.validation-results[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 15px;\\n  border-radius: 6px;\\n}\\n.validation-results[_ngcontent-%COMP%]   .validation-status.validation-success[_ngcontent-%COMP%] {\\n  background-color: rgba(40, 167, 69, 0.1);\\n  border: 1px solid rgba(40, 167, 69, 0.2);\\n}\\n.validation-results[_ngcontent-%COMP%]   .validation-status.validation-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.validation-results[_ngcontent-%COMP%]   .validation-status.validation-failure[_ngcontent-%COMP%] {\\n  background-color: rgba(220, 53, 69, 0.1);\\n  border: 1px solid rgba(220, 53, 69, 0.2);\\n}\\n.validation-results[_ngcontent-%COMP%]   .validation-status.validation-failure[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.validation-results[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "project_r5", "name", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵelement", "ɵɵtextInterpolate1", "error_r13", "line", "fix", "ɵɵtemplate", "TestingComponent_div_49_div_12_div_25_div_3_span_4_Template", "TestingComponent_div_49_div_12_div_25_div_3_div_7_Template", "file", "message", "TestingComponent_div_49_div_12_div_25_div_3_Template", "ctx_r10", "testResults", "testing", "errors", "fix_r19", "TestingComponent_div_49_div_12_div_26_div_3_span_4_Template", "description", "TestingComponent_div_49_div_12_div_26_div_3_Template", "ctx_r11", "fixes", "TestingComponent_div_49_div_12_div_25_Template", "TestingComponent_div_49_div_12_div_26_Template", "ctx_r8", "total_tests", "passed_tests", "failed_tests", "duration", "length", "TestingComponent_div_49_div_13_i_4_Template", "TestingComponent_div_49_div_13_i_5_Template", "ɵɵpureFunction2", "_c0", "ctx_r9", "validation", "success", "TestingComponent_div_49_i_5_Template", "TestingComponent_div_49_i_6_Template", "TestingComponent_div_49_div_12_Template", "TestingComponent_div_49_div_13_Template", "_c1", "ctx_r3", "TestingComponent", "constructor", "http", "projectService", "notificationService", "projects", "selectedProject", "isLoading", "testConfig", "autoFix", "autoValidate", "browserValidation", "testingInProgress", "continuousTestingActive", "ngOnInit", "loadProjects", "loadTestConfig", "getProjects", "subscribe", "data", "error", "console", "showError", "get", "apiUrl", "auto_fix", "auto_validate", "browser_validation", "runTests", "projectDir", "getProjectDir", "post", "project_dir", "browser_url", "results", "showSuccess", "showWarning", "startContinuousTesting", "openInVSCode", "setup_workspace", "runDemo", "demoDir", "getBaseDir", "test_results_with_error", "updateTestConfig", "put", "stopTesting", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "ProjectService", "i3", "NotificationService", "selectors", "decls", "vars", "consts", "template", "TestingComponent_Template", "rf", "ctx", "ɵɵlistener", "TestingComponent_Template_select_ngModelChange_11_listener", "$event", "TestingComponent_option_12_Template", "TestingComponent_Template_input_ngModelChange_17_listener", "TestingComponent_Template_input_ngModelChange_21_listener", "TestingComponent_Template_input_ngModelChange_25_listener", "TestingComponent_Template_button_click_28_listener", "TestingComponent_Template_button_click_31_listener", "TestingComponent_Template_button_click_34_listener", "TestingComponent_Template_button_click_37_listener", "TestingComponent_Template_button_click_40_listener", "TestingComponent_Template_button_click_43_listener", "TestingComponent_div_47_Template", "TestingComponent_div_48_Template", "TestingComponent_div_49_Template", "TestingComponent_div_50_Template"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\testing\\testing.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\testing\\testing.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { environment } from '../../../environments/environment';\nimport { ProjectService } from '../../services/project.service';\nimport { NotificationService } from '../../services/notification.service';\n\n@Component({\n  selector: 'app-testing',\n  templateUrl: './testing.component.html',\n  styleUrls: ['./testing.component.scss']\n})\nexport class TestingComponent implements OnInit {\n  projects: any[] = [];\n  selectedProject: string = '';\n  testResults: any = null;\n  isLoading: boolean = false;\n  testConfig: any = {\n    autoFix: true,\n    autoValidate: true,\n    browserValidation: true\n  };\n  testingInProgress: boolean = false;\n  continuousTestingActive: boolean = false;\n\n  constructor(\n    private http: HttpClient,\n    private projectService: ProjectService,\n    private notificationService: NotificationService\n  ) { }\n\n  ngOnInit(): void {\n    this.loadProjects();\n    this.loadTestConfig();\n  }\n\n  loadProjects(): void {\n    this.projectService.getProjects().subscribe(\n      (data: any) => {\n        this.projects = data.projects;\n        if (this.projects.length > 0) {\n          this.selectedProject = this.projects[0].name;\n        }\n      },\n      (error) => {\n        console.error('Error loading projects:', error);\n        this.notificationService.showError('Failed to load projects');\n      }\n    );\n  }\n\n  loadTestConfig(): void {\n    debugger\n    this.http.get(`${environment.apiUrl}/api/testing/config`).subscribe(\n      (data: any) => {\n        if (data.testing) {\n          this.testConfig = {\n            autoFix: data.testing.auto_fix,\n            autoValidate: data.testing.auto_validate,\n            browserValidation: data.testing.browser_validation\n          };\n        }\n      },\n      (error) => {\n        console.error('Error loading test config:', error);\n      }\n    );\n  }\n\n  runTests(): void {\n    if (!this.selectedProject) {\n      this.notificationService.showError('Please select a project');\n      return;\n    }\n\n    this.isLoading = true;\n    this.testingInProgress = true;\n    this.testResults = null;\n\n    const projectDir = this.projectService.getProjectDir(this.selectedProject);\n    \n    this.http.post(`${environment.apiUrl}/api/testing/run`, {\n      project_dir: projectDir,\n      auto_fix: this.testConfig.autoFix,\n      auto_validate: this.testConfig.autoValidate,\n      browser_url: `http://localhost:4200/projects/${this.selectedProject}/preview`\n    }).subscribe(\n      (data: any) => {\n        this.testResults = data.results;\n        this.isLoading = false;\n        this.testingInProgress = false;\n        \n        if (data.success) {\n          this.notificationService.showSuccess('Tests completed successfully');\n        } else {\n          this.notificationService.showWarning('Tests completed with issues');\n        }\n      },\n      (error) => {\n        console.error('Error running tests:', error);\n        this.isLoading = false;\n        this.testingInProgress = false;\n        this.notificationService.showError('Failed to run tests');\n      }\n    );\n  }\n\n  startContinuousTesting(): void {\n    if (!this.selectedProject) {\n      this.notificationService.showError('Please select a project');\n      return;\n    }\n\n    const projectDir = this.projectService.getProjectDir(this.selectedProject);\n    \n    this.http.post(`${environment.apiUrl}/api/testing/continuous`, {\n      project_dir: projectDir,\n      auto_fix: this.testConfig.autoFix,\n      auto_validate: this.testConfig.autoValidate,\n      browser_url: `http://localhost:4200/projects/${this.selectedProject}/preview`\n    }).subscribe(\n      (data: any) => {\n        this.continuousTestingActive = true;\n        this.notificationService.showSuccess('Continuous testing started');\n      },\n      (error) => {\n        console.error('Error starting continuous testing:', error);\n        this.notificationService.showError('Failed to start continuous testing');\n      }\n    );\n  }\n\n  openInVSCode(): void {\n    if (!this.selectedProject) {\n      this.notificationService.showError('Please select a project');\n      return;\n    }\n\n    const projectDir = this.projectService.getProjectDir(this.selectedProject);\n    \n    this.http.post(`${environment.apiUrl}/api/testing/vscode`, {\n      project_dir: projectDir,\n      setup_workspace: true\n    }).subscribe(\n      (data: any) => {\n        this.notificationService.showSuccess('Project opened in VS Code');\n      },\n      (error) => {\n        console.error('Error opening project in VS Code:', error);\n        this.notificationService.showError('Failed to open project in VS Code');\n      }\n    );\n  }\n\n  runDemo(): void {\n    const demoDir = `${this.projectService.getBaseDir()}/demo`;\n    \n    this.isLoading = true;\n    this.testingInProgress = true;\n    this.testResults = null;\n    \n    this.http.post(`${environment.apiUrl}/api/testing/demo`, {\n      project_dir: demoDir\n    }).subscribe(\n      (data: any) => {\n        this.testResults = data.test_results_with_error;\n        this.isLoading = false;\n        this.testingInProgress = false;\n        this.notificationService.showSuccess('Demo completed successfully');\n      },\n      (error) => {\n        console.error('Error running demo:', error);\n        this.isLoading = false;\n        this.testingInProgress = false;\n        this.notificationService.showError('Failed to run demo');\n      }\n    );\n  }\n\n  updateTestConfig(): void {\n    this.http.put(`${environment.apiUrl}/api/testing/config`, {\n      testing: {\n        auto_fix: this.testConfig.autoFix,\n        auto_validate: this.testConfig.autoValidate,\n        browser_validation: this.testConfig.browserValidation\n      }\n    }).subscribe(\n      (data: any) => {\n        this.notificationService.showSuccess('Test configuration updated');\n      },\n      (error) => {\n        console.error('Error updating test config:', error);\n        this.notificationService.showError('Failed to update test configuration');\n      }\n    );\n  }\n\n  stopTesting(): void {\n    if (!this.selectedProject) {\n      return;\n    }\n\n    const projectDir = this.projectService.getProjectDir(this.selectedProject);\n    \n    this.http.post(`${environment.apiUrl}/api/testing/stop`, {\n      project_dir: projectDir\n    }).subscribe(\n      (data: any) => {\n        this.testingInProgress = false;\n        this.isLoading = false;\n        this.notificationService.showSuccess('Testing stopped');\n      },\n      (error) => {\n        console.error('Error stopping tests:', error);\n        this.notificationService.showError('Failed to stop testing');\n      }\n    );\n  }\n}\n", "<div class=\"testing-container\">\n  <div class=\"testing-header\">\n    <h2>Autonomous Testing</h2>\n    <p>Run autonomous tests, fix errors, and validate functionality automatically.</p>\n  </div>\n\n  <div class=\"testing-content\">\n    <div class=\"testing-controls\">\n      <div class=\"form-group\">\n        <label for=\"project-select\">Select Project</label>\n        <select id=\"project-select\" [(ngModel)]=\"selectedProject\" class=\"form-control\">\n          <option *ngFor=\"let project of projects\" [value]=\"project.name\">{{ project.name }}</option>\n        </select>\n      </div>\n\n      <div class=\"testing-options\">\n        <h3>Testing Options</h3>\n        <div class=\"form-check\">\n          <input type=\"checkbox\" id=\"auto-fix\" [(ngModel)]=\"testConfig.autoFix\" class=\"form-check-input\">\n          <label for=\"auto-fix\" class=\"form-check-label\">Auto-fix errors</label>\n        </div>\n        <div class=\"form-check\">\n          <input type=\"checkbox\" id=\"auto-validate\" [(ngModel)]=\"testConfig.autoValidate\" class=\"form-check-input\">\n          <label for=\"auto-validate\" class=\"form-check-label\">Auto-validate in browser</label>\n        </div>\n        <div class=\"form-check\">\n          <input type=\"checkbox\" id=\"browser-validation\" [(ngModel)]=\"testConfig.browserValidation\" class=\"form-check-input\">\n          <label for=\"browser-validation\" class=\"form-check-label\">Browser validation</label>\n        </div>\n        <button (click)=\"updateTestConfig()\" class=\"btn btn-secondary\">Update Config</button>\n      </div>\n\n      <div class=\"testing-actions\">\n        <button (click)=\"runTests()\" [disabled]=\"isLoading || !selectedProject\" class=\"btn btn-primary\">\n          <i class=\"fas fa-vial\"></i> Run Tests\n        </button>\n        <button (click)=\"startContinuousTesting()\" [disabled]=\"isLoading || !selectedProject || continuousTestingActive\" class=\"btn btn-info\">\n          <i class=\"fas fa-sync-alt\"></i> Start Continuous Testing\n        </button>\n        <button (click)=\"stopTesting()\" [disabled]=\"!isLoading && !testingInProgress\" class=\"btn btn-danger\">\n          <i class=\"fas fa-stop\"></i> Stop Testing\n        </button>\n        <button (click)=\"openInVSCode()\" [disabled]=\"!selectedProject\" class=\"btn btn-secondary\">\n          <i class=\"fas fa-code\"></i> Open in VS Code\n        </button>\n        <button (click)=\"runDemo()\" [disabled]=\"isLoading\" class=\"btn btn-outline-primary\">\n          <i class=\"fas fa-play\"></i> Run Demo\n        </button>\n      </div>\n    </div>\n\n    <div class=\"testing-results\">\n      <div *ngIf=\"isLoading\" class=\"testing-loading\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"sr-only\">Loading...</span>\n        </div>\n        <p>Running tests... This may take a few moments.</p>\n      </div>\n\n      <div *ngIf=\"testingInProgress && !isLoading\" class=\"testing-progress\">\n        <div class=\"progress\">\n          <div class=\"progress-bar progress-bar-striped progress-bar-animated\" role=\"progressbar\" style=\"width: 100%\"></div>\n        </div>\n        <p>Tests in progress...</p>\n      </div>\n\n      <div *ngIf=\"testResults && !isLoading\" class=\"test-results-container\">\n        <h3>Test Results</h3>\n        \n        <div class=\"test-summary\" [ngClass]=\"{'test-success': testResults.success, 'test-failure': !testResults.success}\">\n          <div class=\"test-status\">\n            <i *ngIf=\"testResults.success\" class=\"fas fa-check-circle\"></i>\n            <i *ngIf=\"!testResults.success\" class=\"fas fa-times-circle\"></i>\n          </div>\n          <div class=\"test-info\">\n            <h4>{{ testResults.success ? 'Tests Passed' : 'Tests Failed' }}</h4>\n            <p>{{ testResults.message || 'Test execution completed' }}</p>\n          </div>\n        </div>\n\n        <div *ngIf=\"testResults.testing\" class=\"test-details\">\n          <div class=\"test-section\">\n            <h5>Test Details</h5>\n            <div class=\"test-stats\">\n              <div class=\"stat-item\">\n                <span class=\"stat-label\">Total Tests:</span>\n                <span class=\"stat-value\">{{ testResults.testing.total_tests || 0 }}</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-label\">Passed:</span>\n                <span class=\"stat-value\">{{ testResults.testing.passed_tests || 0 }}</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-label\">Failed:</span>\n                <span class=\"stat-value\">{{ testResults.testing.failed_tests || 0 }}</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-label\">Duration:</span>\n                <span class=\"stat-value\">{{ testResults.testing.duration || 0 }}s</span>\n              </div>\n            </div>\n          </div>\n\n          <div *ngIf=\"testResults.testing.errors && testResults.testing.errors.length > 0\" class=\"test-errors\">\n            <h5>Errors</h5>\n            <div *ngFor=\"let error of testResults.testing.errors\" class=\"error-item\">\n              <div class=\"error-header\">\n                <span class=\"error-file\">{{ error.file }}</span>\n                <span *ngIf=\"error.line\" class=\"error-line\">Line {{ error.line }}</span>\n              </div>\n              <div class=\"error-message\">{{ error.message }}</div>\n              <div *ngIf=\"error.fix\" class=\"error-fix\">\n                <span class=\"fix-label\">Auto-fix:</span>\n                <span class=\"fix-message\">{{ error.fix }}</span>\n              </div>\n            </div>\n          </div>\n\n          <div *ngIf=\"testResults.fixes && testResults.fixes.length > 0\" class=\"test-fixes\">\n            <h5>Applied Fixes</h5>\n            <div *ngFor=\"let fix of testResults.fixes\" class=\"fix-item\">\n              <div class=\"fix-header\">\n                <span class=\"fix-file\">{{ fix.file }}</span>\n                <span *ngIf=\"fix.line\" class=\"fix-line\">Line {{ fix.line }}</span>\n              </div>\n              <div class=\"fix-description\">{{ fix.description }}</div>\n            </div>\n          </div>\n        </div>\n\n        <div *ngIf=\"testResults.validation\" class=\"validation-results\">\n          <h5>Browser Validation</h5>\n          <div class=\"validation-status\" [ngClass]=\"{'validation-success': testResults.validation.success, 'validation-failure': !testResults.validation.success}\">\n            <i *ngIf=\"testResults.validation.success\" class=\"fas fa-check-circle\"></i>\n            <i *ngIf=\"!testResults.validation.success\" class=\"fas fa-times-circle\"></i>\n            <span>{{ testResults.validation.message }}</span>\n          </div>\n        </div>\n      </div>\n\n      <div *ngIf=\"!testResults && !isLoading && !testingInProgress\" class=\"no-results\">\n        <p>No test results available. Run tests to see results.</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,mCAAmC;;;;;;;;;ICSrDC,EAAA,CAAAC,cAAA,iBAAgE;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlDH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,IAAA,CAAsB;IAACN,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,iBAAA,CAAAH,UAAA,CAAAC,IAAA,CAAkB;;;;;IAyCtFN,EAAA,CAAAC,cAAA,cAA+C;IAErBD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,oDAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAGtDH,EAAA,CAAAC,cAAA,cAAsE;IAElED,EAAA,CAAAS,SAAA,cAAkH;IACpHT,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAQvBH,EAAA,CAAAS,SAAA,YAA+D;;;;;IAC/DT,EAAA,CAAAS,SAAA,YAAgE;;;;;IAoC5DT,EAAA,CAAAC,cAAA,eAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5BH,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAU,kBAAA,UAAAC,SAAA,CAAAC,IAAA,KAAqB;;;;;IAGnEZ,EAAA,CAAAC,cAAA,cAAyC;IACfD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtBH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAG,SAAA,CAAAE,GAAA,CAAe;;;;;IAR7Cb,EAAA,CAAAC,cAAA,cAAyE;IAE5CD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAc,UAAA,IAAAC,2DAAA,mBAAwE;IAC1Ef,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAc,UAAA,IAAAE,0DAAA,kBAGM;IACRhB,EAAA,CAAAG,YAAA,EAAM;;;;IARuBH,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAG,SAAA,CAAAM,IAAA,CAAgB;IAClCjB,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAI,UAAA,SAAAO,SAAA,CAAAC,IAAA,CAAgB;IAEEZ,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAQ,iBAAA,CAAAG,SAAA,CAAAO,OAAA,CAAmB;IACxClB,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,SAAAO,SAAA,CAAAE,GAAA,CAAe;;;;;IARzBb,EAAA,CAAAC,cAAA,cAAqG;IAC/FD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAc,UAAA,IAAAK,oDAAA,kBAUM;IACRnB,EAAA,CAAAG,YAAA,EAAM;;;;IAXmBH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,YAAAgB,OAAA,CAAAC,WAAA,CAAAC,OAAA,CAAAC,MAAA,CAA6B;;;;;IAkBhDvB,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAU,kBAAA,UAAAc,OAAA,CAAAZ,IAAA,KAAmB;;;;;IAH/DZ,EAAA,CAAAC,cAAA,cAA4D;IAEjCD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAc,UAAA,IAAAW,2DAAA,mBAAkE;IACpEzB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAH/BH,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAQ,iBAAA,CAAAgB,OAAA,CAAAP,IAAA,CAAc;IAC9BjB,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAoB,OAAA,CAAAZ,IAAA,CAAc;IAEMZ,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,iBAAA,CAAAgB,OAAA,CAAAE,WAAA,CAAqB;;;;;IAPtD1B,EAAA,CAAAC,cAAA,cAAkF;IAC5ED,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAc,UAAA,IAAAa,oDAAA,kBAMM;IACR3B,EAAA,CAAAG,YAAA,EAAM;;;;IAPiBH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,YAAAwB,OAAA,CAAAP,WAAA,CAAAQ,KAAA,CAAoB;;;;;IAxC7C7B,EAAA,CAAAC,cAAA,cAAsD;IAE9CD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,cAAwB;IAEKD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5EH,EAAA,CAAAC,cAAA,eAAuB;IACID,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7EH,EAAA,CAAAC,cAAA,eAAuB;IACID,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7EH,EAAA,CAAAC,cAAA,eAAuB;IACID,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAK9EH,EAAA,CAAAc,UAAA,KAAAgB,8CAAA,kBAaM;IAEN9B,EAAA,CAAAc,UAAA,KAAAiB,8CAAA,kBASM;IACR/B,EAAA,CAAAG,YAAA,EAAM;;;;IA1C2BH,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAQ,iBAAA,CAAAwB,MAAA,CAAAX,WAAA,CAAAC,OAAA,CAAAW,WAAA,MAA0C;IAI1CjC,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAQ,iBAAA,CAAAwB,MAAA,CAAAX,WAAA,CAAAC,OAAA,CAAAY,YAAA,MAA2C;IAI3ClC,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAQ,iBAAA,CAAAwB,MAAA,CAAAX,WAAA,CAAAC,OAAA,CAAAa,YAAA,MAA2C;IAI3CnC,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAU,kBAAA,KAAAsB,MAAA,CAAAX,WAAA,CAAAC,OAAA,CAAAc,QAAA,WAAwC;IAKjEpC,EAAA,CAAAO,SAAA,GAAyE;IAAzEP,EAAA,CAAAI,UAAA,SAAA4B,MAAA,CAAAX,WAAA,CAAAC,OAAA,CAAAC,MAAA,IAAAS,MAAA,CAAAX,WAAA,CAAAC,OAAA,CAAAC,MAAA,CAAAc,MAAA,KAAyE;IAezErC,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,SAAA4B,MAAA,CAAAX,WAAA,CAAAQ,KAAA,IAAAG,MAAA,CAAAX,WAAA,CAAAQ,KAAA,CAAAQ,MAAA,KAAuD;;;;;IAe3DrC,EAAA,CAAAS,SAAA,YAA0E;;;;;IAC1ET,EAAA,CAAAS,SAAA,YAA2E;;;;;;;;;;;IAJ/ET,EAAA,CAAAC,cAAA,cAA+D;IACzDD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,cAAyJ;IACvJD,EAAA,CAAAc,UAAA,IAAAwB,2CAAA,gBAA0E;IAC1EtC,EAAA,CAAAc,UAAA,IAAAyB,2CAAA,gBAA2E;IAC3EvC,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHpBH,EAAA,CAAAO,SAAA,GAAyH;IAAzHP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAwC,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAArB,WAAA,CAAAsB,UAAA,CAAAC,OAAA,GAAAF,MAAA,CAAArB,WAAA,CAAAsB,UAAA,CAAAC,OAAA,EAAyH;IAClJ5C,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAI,UAAA,SAAAsC,MAAA,CAAArB,WAAA,CAAAsB,UAAA,CAAAC,OAAA,CAAoC;IACpC5C,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,UAAAsC,MAAA,CAAArB,WAAA,CAAAsB,UAAA,CAAAC,OAAA,CAAqC;IACnC5C,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,iBAAA,CAAAkC,MAAA,CAAArB,WAAA,CAAAsB,UAAA,CAAAzB,OAAA,CAAoC;;;;;;;;;;;IArEhDlB,EAAA,CAAAC,cAAA,cAAsE;IAChED,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAErBH,EAAA,CAAAC,cAAA,cAAkH;IAE9GD,EAAA,CAAAc,UAAA,IAAA+B,oCAAA,gBAA+D;IAC/D7C,EAAA,CAAAc,UAAA,IAAAgC,oCAAA,gBAAgE;IAClE9C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IACjBD,EAAA,CAAAE,MAAA,GAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpEH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIlEH,EAAA,CAAAc,UAAA,KAAAiC,uCAAA,mBAgDM;IAEN/C,EAAA,CAAAc,UAAA,KAAAkC,uCAAA,kBAOM;IACRhD,EAAA,CAAAG,YAAA,EAAM;;;;IArEsBH,EAAA,CAAAO,SAAA,GAAuF;IAAvFP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAwC,eAAA,IAAAS,GAAA,EAAAC,MAAA,CAAA7B,WAAA,CAAAuB,OAAA,GAAAM,MAAA,CAAA7B,WAAA,CAAAuB,OAAA,EAAuF;IAEzG5C,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAA8C,MAAA,CAAA7B,WAAA,CAAAuB,OAAA,CAAyB;IACzB5C,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,UAAA8C,MAAA,CAAA7B,WAAA,CAAAuB,OAAA,CAA0B;IAG1B5C,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAQ,iBAAA,CAAA0C,MAAA,CAAA7B,WAAA,CAAAuB,OAAA,mCAA2D;IAC5D5C,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAQ,iBAAA,CAAA0C,MAAA,CAAA7B,WAAA,CAAAH,OAAA,+BAAuD;IAIxDlB,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAA8C,MAAA,CAAA7B,WAAA,CAAAC,OAAA,CAAyB;IAkDzBtB,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAA8C,MAAA,CAAA7B,WAAA,CAAAsB,UAAA,CAA4B;;;;;IAUpC3C,EAAA,CAAAC,cAAA,cAAiF;IAC5ED,EAAA,CAAAE,MAAA,2DAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADlInE,OAAM,MAAOgD,gBAAgB;EAa3BC,YACUC,IAAgB,EAChBC,cAA8B,EAC9BC,mBAAwC;IAFxC,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAf7B,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAApC,WAAW,GAAQ,IAAI;IACvB,KAAAqC,SAAS,GAAY,KAAK;IAC1B,KAAAC,UAAU,GAAQ;MAChBC,OAAO,EAAE,IAAI;MACbC,YAAY,EAAE,IAAI;MAClBC,iBAAiB,EAAE;KACpB;IACD,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,uBAAuB,GAAY,KAAK;EAMpC;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAD,YAAYA,CAAA;IACV,IAAI,CAACZ,cAAc,CAACc,WAAW,EAAE,CAACC,SAAS,CACxCC,IAAS,IAAI;MACZ,IAAI,CAACd,QAAQ,GAAGc,IAAI,CAACd,QAAQ;MAC7B,IAAI,IAAI,CAACA,QAAQ,CAACnB,MAAM,GAAG,CAAC,EAAE;QAC5B,IAAI,CAACoB,eAAe,GAAG,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC,CAAClD,IAAI;;IAEhD,CAAC,EACAiE,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,IAAI,CAAChB,mBAAmB,CAACkB,SAAS,CAAC,yBAAyB,CAAC;IAC/D,CAAC,CACF;EACH;EAEAN,cAAcA,CAAA;IACZ;IACA,IAAI,CAACd,IAAI,CAACqB,GAAG,CAAC,GAAG3E,WAAW,CAAC4E,MAAM,qBAAqB,CAAC,CAACN,SAAS,CAChEC,IAAS,IAAI;MACZ,IAAIA,IAAI,CAAChD,OAAO,EAAE;QAChB,IAAI,CAACqC,UAAU,GAAG;UAChBC,OAAO,EAAEU,IAAI,CAAChD,OAAO,CAACsD,QAAQ;UAC9Bf,YAAY,EAAES,IAAI,CAAChD,OAAO,CAACuD,aAAa;UACxCf,iBAAiB,EAAEQ,IAAI,CAAChD,OAAO,CAACwD;SACjC;;IAEL,CAAC,EACAP,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,CACF;EACH;EAEAQ,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACtB,eAAe,EAAE;MACzB,IAAI,CAACF,mBAAmB,CAACkB,SAAS,CAAC,yBAAyB,CAAC;MAC7D;;IAGF,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC1C,WAAW,GAAG,IAAI;IAEvB,MAAM2D,UAAU,GAAG,IAAI,CAAC1B,cAAc,CAAC2B,aAAa,CAAC,IAAI,CAACxB,eAAe,CAAC;IAE1E,IAAI,CAACJ,IAAI,CAAC6B,IAAI,CAAC,GAAGnF,WAAW,CAAC4E,MAAM,kBAAkB,EAAE;MACtDQ,WAAW,EAAEH,UAAU;MACvBJ,QAAQ,EAAE,IAAI,CAACjB,UAAU,CAACC,OAAO;MACjCiB,aAAa,EAAE,IAAI,CAAClB,UAAU,CAACE,YAAY;MAC3CuB,WAAW,EAAE,kCAAkC,IAAI,CAAC3B,eAAe;KACpE,CAAC,CAACY,SAAS,CACTC,IAAS,IAAI;MACZ,IAAI,CAACjD,WAAW,GAAGiD,IAAI,CAACe,OAAO;MAC/B,IAAI,CAAC3B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACK,iBAAiB,GAAG,KAAK;MAE9B,IAAIO,IAAI,CAAC1B,OAAO,EAAE;QAChB,IAAI,CAACW,mBAAmB,CAAC+B,WAAW,CAAC,8BAA8B,CAAC;OACrE,MAAM;QACL,IAAI,CAAC/B,mBAAmB,CAACgC,WAAW,CAAC,6BAA6B,CAAC;;IAEvE,CAAC,EACAhB,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,IAAI,CAACb,SAAS,GAAG,KAAK;MACtB,IAAI,CAACK,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACR,mBAAmB,CAACkB,SAAS,CAAC,qBAAqB,CAAC;IAC3D,CAAC,CACF;EACH;EAEAe,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC/B,eAAe,EAAE;MACzB,IAAI,CAACF,mBAAmB,CAACkB,SAAS,CAAC,yBAAyB,CAAC;MAC7D;;IAGF,MAAMO,UAAU,GAAG,IAAI,CAAC1B,cAAc,CAAC2B,aAAa,CAAC,IAAI,CAACxB,eAAe,CAAC;IAE1E,IAAI,CAACJ,IAAI,CAAC6B,IAAI,CAAC,GAAGnF,WAAW,CAAC4E,MAAM,yBAAyB,EAAE;MAC7DQ,WAAW,EAAEH,UAAU;MACvBJ,QAAQ,EAAE,IAAI,CAACjB,UAAU,CAACC,OAAO;MACjCiB,aAAa,EAAE,IAAI,CAAClB,UAAU,CAACE,YAAY;MAC3CuB,WAAW,EAAE,kCAAkC,IAAI,CAAC3B,eAAe;KACpE,CAAC,CAACY,SAAS,CACTC,IAAS,IAAI;MACZ,IAAI,CAACN,uBAAuB,GAAG,IAAI;MACnC,IAAI,CAACT,mBAAmB,CAAC+B,WAAW,CAAC,4BAA4B,CAAC;IACpE,CAAC,EACAf,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,IAAI,CAAChB,mBAAmB,CAACkB,SAAS,CAAC,oCAAoC,CAAC;IAC1E,CAAC,CACF;EACH;EAEAgB,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAChC,eAAe,EAAE;MACzB,IAAI,CAACF,mBAAmB,CAACkB,SAAS,CAAC,yBAAyB,CAAC;MAC7D;;IAGF,MAAMO,UAAU,GAAG,IAAI,CAAC1B,cAAc,CAAC2B,aAAa,CAAC,IAAI,CAACxB,eAAe,CAAC;IAE1E,IAAI,CAACJ,IAAI,CAAC6B,IAAI,CAAC,GAAGnF,WAAW,CAAC4E,MAAM,qBAAqB,EAAE;MACzDQ,WAAW,EAAEH,UAAU;MACvBU,eAAe,EAAE;KAClB,CAAC,CAACrB,SAAS,CACTC,IAAS,IAAI;MACZ,IAAI,CAACf,mBAAmB,CAAC+B,WAAW,CAAC,2BAA2B,CAAC;IACnE,CAAC,EACAf,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAAChB,mBAAmB,CAACkB,SAAS,CAAC,mCAAmC,CAAC;IACzE,CAAC,CACF;EACH;EAEAkB,OAAOA,CAAA;IACL,MAAMC,OAAO,GAAG,GAAG,IAAI,CAACtC,cAAc,CAACuC,UAAU,EAAE,OAAO;IAE1D,IAAI,CAACnC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC1C,WAAW,GAAG,IAAI;IAEvB,IAAI,CAACgC,IAAI,CAAC6B,IAAI,CAAC,GAAGnF,WAAW,CAAC4E,MAAM,mBAAmB,EAAE;MACvDQ,WAAW,EAAES;KACd,CAAC,CAACvB,SAAS,CACTC,IAAS,IAAI;MACZ,IAAI,CAACjD,WAAW,GAAGiD,IAAI,CAACwB,uBAAuB;MAC/C,IAAI,CAACpC,SAAS,GAAG,KAAK;MACtB,IAAI,CAACK,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACR,mBAAmB,CAAC+B,WAAW,CAAC,6BAA6B,CAAC;IACrE,CAAC,EACAf,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,CAACb,SAAS,GAAG,KAAK;MACtB,IAAI,CAACK,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACR,mBAAmB,CAACkB,SAAS,CAAC,oBAAoB,CAAC;IAC1D,CAAC,CACF;EACH;EAEAsB,gBAAgBA,CAAA;IACd,IAAI,CAAC1C,IAAI,CAAC2C,GAAG,CAAC,GAAGjG,WAAW,CAAC4E,MAAM,qBAAqB,EAAE;MACxDrD,OAAO,EAAE;QACPsD,QAAQ,EAAE,IAAI,CAACjB,UAAU,CAACC,OAAO;QACjCiB,aAAa,EAAE,IAAI,CAAClB,UAAU,CAACE,YAAY;QAC3CiB,kBAAkB,EAAE,IAAI,CAACnB,UAAU,CAACG;;KAEvC,CAAC,CAACO,SAAS,CACTC,IAAS,IAAI;MACZ,IAAI,CAACf,mBAAmB,CAAC+B,WAAW,CAAC,4BAA4B,CAAC;IACpE,CAAC,EACAf,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAI,CAAChB,mBAAmB,CAACkB,SAAS,CAAC,qCAAqC,CAAC;IAC3E,CAAC,CACF;EACH;EAEAwB,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACxC,eAAe,EAAE;MACzB;;IAGF,MAAMuB,UAAU,GAAG,IAAI,CAAC1B,cAAc,CAAC2B,aAAa,CAAC,IAAI,CAACxB,eAAe,CAAC;IAE1E,IAAI,CAACJ,IAAI,CAAC6B,IAAI,CAAC,GAAGnF,WAAW,CAAC4E,MAAM,mBAAmB,EAAE;MACvDQ,WAAW,EAAEH;KACd,CAAC,CAACX,SAAS,CACTC,IAAS,IAAI;MACZ,IAAI,CAACP,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACL,SAAS,GAAG,KAAK;MACtB,IAAI,CAACH,mBAAmB,CAAC+B,WAAW,CAAC,iBAAiB,CAAC;IACzD,CAAC,EACAf,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAAChB,mBAAmB,CAACkB,SAAS,CAAC,wBAAwB,CAAC;IAC9D,CAAC,CACF;EACH;;;uBA7MWtB,gBAAgB,EAAAnD,EAAA,CAAAkG,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAApG,EAAA,CAAAkG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtG,EAAA,CAAAkG,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAhBrD,gBAAgB;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX7B/G,EAAA,CAAAC,cAAA,aAA+B;UAEvBD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,kFAA2E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGpFH,EAAA,CAAAC,cAAA,aAA6B;UAGKD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClDH,EAAA,CAAAC,cAAA,iBAA+E;UAAnDD,EAAA,CAAAiH,UAAA,2BAAAC,2DAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAvD,eAAA,GAAA0D,MAAA;UAAA,EAA6B;UACvDnH,EAAA,CAAAc,UAAA,KAAAsG,mCAAA,oBAA2F;UAC7FpH,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,cAA6B;UACvBD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,cAAwB;UACeD,EAAA,CAAAiH,UAAA,2BAAAI,0DAAAF,MAAA;YAAA,OAAAH,GAAA,CAAArD,UAAA,CAAAC,OAAA,GAAAuD,MAAA;UAAA,EAAgC;UAArEnH,EAAA,CAAAG,YAAA,EAA+F;UAC/FH,EAAA,CAAAC,cAAA,iBAA+C;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAExEH,EAAA,CAAAC,cAAA,cAAwB;UACoBD,EAAA,CAAAiH,UAAA,2BAAAK,0DAAAH,MAAA;YAAA,OAAAH,GAAA,CAAArD,UAAA,CAAAE,YAAA,GAAAsD,MAAA;UAAA,EAAqC;UAA/EnH,EAAA,CAAAG,YAAA,EAAyG;UACzGH,EAAA,CAAAC,cAAA,iBAAoD;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEtFH,EAAA,CAAAC,cAAA,cAAwB;UACyBD,EAAA,CAAAiH,UAAA,2BAAAM,0DAAAJ,MAAA;YAAA,OAAAH,GAAA,CAAArD,UAAA,CAAAG,iBAAA,GAAAqD,MAAA;UAAA,EAA0C;UAAzFnH,EAAA,CAAAG,YAAA,EAAmH;UACnHH,EAAA,CAAAC,cAAA,iBAAyD;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAErFH,EAAA,CAAAC,cAAA,kBAA+D;UAAvDD,EAAA,CAAAiH,UAAA,mBAAAO,mDAAA;YAAA,OAASR,GAAA,CAAAjB,gBAAA,EAAkB;UAAA,EAAC;UAA2B/F,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGvFH,EAAA,CAAAC,cAAA,eAA6B;UACnBD,EAAA,CAAAiH,UAAA,mBAAAQ,mDAAA;YAAA,OAAST,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UAC1B/E,EAAA,CAAAS,SAAA,aAA2B;UAACT,EAAA,CAAAE,MAAA,mBAC9B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAsI;UAA9HD,EAAA,CAAAiH,UAAA,mBAAAS,mDAAA;YAAA,OAASV,GAAA,CAAAxB,sBAAA,EAAwB;UAAA,EAAC;UACxCxF,EAAA,CAAAS,SAAA,aAA+B;UAACT,EAAA,CAAAE,MAAA,kCAClC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqG;UAA7FD,EAAA,CAAAiH,UAAA,mBAAAU,mDAAA;YAAA,OAASX,GAAA,CAAAf,WAAA,EAAa;UAAA,EAAC;UAC7BjG,EAAA,CAAAS,SAAA,aAA2B;UAACT,EAAA,CAAAE,MAAA,sBAC9B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAyF;UAAjFD,EAAA,CAAAiH,UAAA,mBAAAW,mDAAA;YAAA,OAASZ,GAAA,CAAAvB,YAAA,EAAc;UAAA,EAAC;UAC9BzF,EAAA,CAAAS,SAAA,aAA2B;UAACT,EAAA,CAAAE,MAAA,yBAC9B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAmF;UAA3ED,EAAA,CAAAiH,UAAA,mBAAAY,mDAAA;YAAA,OAASb,GAAA,CAAArB,OAAA,EAAS;UAAA,EAAC;UACzB3F,EAAA,CAAAS,SAAA,aAA2B;UAACT,EAAA,CAAAE,MAAA,kBAC9B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAc,UAAA,KAAAgH,gCAAA,kBAKM;UAEN9H,EAAA,CAAAc,UAAA,KAAAiH,gCAAA,kBAKM;UAEN/H,EAAA,CAAAc,UAAA,KAAAkH,gCAAA,oBAwEM;UAENhI,EAAA,CAAAc,UAAA,KAAAmH,gCAAA,kBAEM;UACRjI,EAAA,CAAAG,YAAA,EAAM;;;UArI0BH,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAI,UAAA,YAAA4G,GAAA,CAAAvD,eAAA,CAA6B;UAC3BzD,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAA4G,GAAA,CAAAxD,QAAA,CAAW;UAOFxD,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAI,UAAA,YAAA4G,GAAA,CAAArD,UAAA,CAAAC,OAAA,CAAgC;UAI3B5D,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAI,UAAA,YAAA4G,GAAA,CAAArD,UAAA,CAAAE,YAAA,CAAqC;UAIhC7D,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAI,UAAA,YAAA4G,GAAA,CAAArD,UAAA,CAAAG,iBAAA,CAA0C;UAO9D9D,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAI,UAAA,aAAA4G,GAAA,CAAAtD,SAAA,KAAAsD,GAAA,CAAAvD,eAAA,CAA0C;UAG5BzD,EAAA,CAAAO,SAAA,GAAqE;UAArEP,EAAA,CAAAI,UAAA,aAAA4G,GAAA,CAAAtD,SAAA,KAAAsD,GAAA,CAAAvD,eAAA,IAAAuD,GAAA,CAAAhD,uBAAA,CAAqE;UAGhFhE,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAAI,UAAA,cAAA4G,GAAA,CAAAtD,SAAA,KAAAsD,GAAA,CAAAjD,iBAAA,CAA6C;UAG5C/D,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,cAAA4G,GAAA,CAAAvD,eAAA,CAA6B;UAGlCzD,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAI,UAAA,aAAA4G,GAAA,CAAAtD,SAAA,CAAsB;UAO9C1D,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAA4G,GAAA,CAAAtD,SAAA,CAAe;UAOf1D,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAI,UAAA,SAAA4G,GAAA,CAAAjD,iBAAA,KAAAiD,GAAA,CAAAtD,SAAA,CAAqC;UAOrC1D,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAI,UAAA,SAAA4G,GAAA,CAAA3F,WAAA,KAAA2F,GAAA,CAAAtD,SAAA,CAA+B;UA0E/B1D,EAAA,CAAAO,SAAA,GAAsD;UAAtDP,EAAA,CAAAI,UAAA,UAAA4G,GAAA,CAAA3F,WAAA,KAAA2F,GAAA,CAAAtD,SAAA,KAAAsD,GAAA,CAAAjD,iBAAA,CAAsD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}