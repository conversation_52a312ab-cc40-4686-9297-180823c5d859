{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { HttpClientModule } from '@angular/common/http';\nimport { RouterModule } from '@angular/router';\nimport { MonacoEditorModule } from 'ngx-monaco-editor-v2';\nimport { AppComponent } from './app.component';\nimport { ChatComponent } from './components/chat/chat.component';\nimport { CodeEditorComponent } from './components/code-editor/code-editor.component';\nimport { BrowserPreviewComponent } from './components/browser-preview/browser-preview.component';\nimport { ProjectListComponent } from './components/project-list/project-list.component';\nimport { ProjectDetailComponent } from './components/project-detail/project-detail.component';\nimport { FileExplorerComponent } from './components/file-explorer/file-explorer.component';\nimport { ConfigComponent } from './components/config/config.component';\nimport { HomeComponent } from './components/home/<USER>';\nimport { TestingComponent } from './components/testing/testing.component';\nimport { ReversePipe } from './components/chat/reverse.pipe';\n// ✅ Import your ChatGPTCopilotComponent here\nimport { ChatGPTCopilotComponent } from './components/chatgpt-copilot/chatgpt-copilot.component';\nimport { ApiService } from './services/api.service';\nimport { SocketService } from './services/socket.service';\nimport { SocketFactoryService } from './services/socket-factory.service';\nimport { ProjectService } from './services/project.service';\nimport { FileService } from './services/file.service';\nimport { AgentService } from './services/agent.service';\nimport { NotificationService } from './services/notification.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-monaco-editor-v2\";\nconst routes = [{\n  path: '',\n  component: HomeComponent\n}, {\n  path: 'projects',\n  component: ProjectListComponent\n}, {\n  path: 'projects/:name',\n  component: ProjectDetailComponent\n}, {\n  path: 'config',\n  component: ConfigComponent\n}, {\n  path: 'testing',\n  component: TestingComponent\n},\n// Add a route if needed for ChatGPTCopilotComponent\n// { path: 'copilot', component: ChatGPTCopilotComponent },\n{\n  path: '**',\n  redirectTo: ''\n}];\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [ApiService, SocketService, SocketFactoryService, ProjectService, FileService, AgentService, NotificationService],\n      imports: [BrowserModule, CommonModule, FormsModule, ReactiveFormsModule, HttpClientModule, RouterModule.forRoot(routes), MonacoEditorModule.forRoot()]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, ChatComponent, CodeEditorComponent, BrowserPreviewComponent, ProjectListComponent, ProjectDetailComponent, FileExplorerComponent, ConfigComponent, HomeComponent, TestingComponent, ReversePipe, ChatGPTCopilotComponent // ✅ Declared component\n    ],\n    imports: [BrowserModule, CommonModule, FormsModule, ReactiveFormsModule, HttpClientModule, i1.RouterModule, i2.MonacoEditorModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "ReactiveFormsModule", "CommonModule", "HttpClientModule", "RouterModule", "MonacoEditorModule", "AppComponent", "ChatComponent", "CodeEditorComponent", "BrowserPreviewComponent", "ProjectListComponent", "ProjectDetailComponent", "FileExplorerComponent", "ConfigComponent", "HomeComponent", "TestingComponent", "ReversePipe", "ChatGPTCopilotComponent", "ApiService", "SocketService", "SocketFactoryService", "ProjectService", "FileService", "AgentService", "NotificationService", "routes", "path", "component", "redirectTo", "AppModule", "bootstrap", "imports", "forRoot", "declarations", "i1", "i2"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { HttpClientModule } from '@angular/common/http';\nimport { RouterModule, Routes } from '@angular/router';\nimport { MonacoEditorModule } from 'ngx-monaco-editor-v2';\n\nimport { AppComponent } from './app.component';\nimport { ChatComponent } from './components/chat/chat.component';\nimport { CodeEditorComponent } from './components/code-editor/code-editor.component';\nimport { BrowserPreviewComponent } from './components/browser-preview/browser-preview.component';\nimport { ProjectListComponent } from './components/project-list/project-list.component';\nimport { ProjectDetailComponent } from './components/project-detail/project-detail.component';\nimport { FileExplorerComponent } from './components/file-explorer/file-explorer.component';\nimport { ConfigComponent } from './components/config/config.component';\nimport { HomeComponent } from './components/home/<USER>';\nimport { TestingComponent } from './components/testing/testing.component';\n\nimport { ReversePipe } from './components/chat/reverse.pipe';\n\n// ✅ Import your ChatGPTCopilotComponent here\nimport { ChatGPTCopilotComponent } from './components/chatgpt-copilot/chatgpt-copilot.component';\n\nimport { ApiService } from './services/api.service';\nimport { SocketService } from './services/socket.service';\nimport { SocketFactoryService } from './services/socket-factory.service';\nimport { ProjectService } from './services/project.service';\nimport { FileService } from './services/file.service';\nimport { AgentService } from './services/agent.service';\nimport { NotificationService } from './services/notification.service';\n\nconst routes: Routes = [\n  { path: '', component: HomeComponent },\n  { path: 'projects', component: ProjectListComponent },\n  { path: 'projects/:name', component: ProjectDetailComponent },\n  { path: 'config', component: ConfigComponent },\n  { path: 'testing', component: TestingComponent },\n  // Add a route if needed for ChatGPTCopilotComponent\n  // { path: 'copilot', component: ChatGPTCopilotComponent },\n\n  { path: '**', redirectTo: '' }\n];\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    ChatComponent,\n    CodeEditorComponent,\n    BrowserPreviewComponent,\n    ProjectListComponent,\n    ProjectDetailComponent,\n    FileExplorerComponent,\n    ConfigComponent,\n    HomeComponent,\n    TestingComponent,\n    ReversePipe,\n    ChatGPTCopilotComponent // ✅ Declared component\n  ],\n  imports: [\n    BrowserModule,\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n    RouterModule.forRoot(routes),\n    MonacoEditorModule.forRoot()\n  ],\n  providers: [\n    ApiService,\n    SocketService,\n    SocketFactoryService,\n    ProjectService,\n    FileService,\n    AgentService,\n    NotificationService\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,sBAAsB;AAEzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,gBAAgB,QAAQ,wCAAwC;AAEzE,SAASC,WAAW,QAAQ,gCAAgC;AAE5D;AACA,SAASC,uBAAuB,QAAQ,wDAAwD;AAEhG,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,mBAAmB,QAAQ,iCAAiC;;;;AAErE,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEb;AAAa,CAAE,EACtC;EAAEY,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEjB;AAAoB,CAAE,EACrD;EAAEgB,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEhB;AAAsB,CAAE,EAC7D;EAAEe,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEd;AAAe,CAAE,EAC9C;EAAEa,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEZ;AAAgB,CAAE;AAChD;AACA;AAEA;EAAEW,IAAI,EAAE,IAAI;EAAEE,UAAU,EAAE;AAAE,CAAE,CAC/B;AAqCD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRxB,YAAY;IAAA;EAAA;;;iBATb,CACTY,UAAU,EACVC,aAAa,EACbC,oBAAoB,EACpBC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,mBAAmB,CACpB;MAAAO,OAAA,GAhBChC,aAAa,EACbG,YAAY,EACZF,WAAW,EACXC,mBAAmB,EACnBE,gBAAgB,EAChBC,YAAY,CAAC4B,OAAO,CAACP,MAAM,CAAC,EAC5BpB,kBAAkB,CAAC2B,OAAO,EAAE;IAAA;EAAA;;;2EAanBH,SAAS;IAAAI,YAAA,GAjClB3B,YAAY,EACZC,aAAa,EACbC,mBAAmB,EACnBC,uBAAuB,EACvBC,oBAAoB,EACpBC,sBAAsB,EACtBC,qBAAqB,EACrBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,WAAW,EACXC,uBAAuB,CAAC;IAAA,C;cAGxBlB,aAAa,EACbG,YAAY,EACZF,WAAW,EACXC,mBAAmB,EACnBE,gBAAgB,EAAA+B,EAAA,CAAA9B,YAAA,EAAA+B,EAAA,CAAA9B,kBAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}