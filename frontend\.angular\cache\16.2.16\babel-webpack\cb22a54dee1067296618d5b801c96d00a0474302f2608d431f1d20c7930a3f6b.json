{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nfunction ChatGPTCopilotComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"img\", 8);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Processing...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"user-message\": a0,\n    \"assistant-message\": a1,\n    \"system-message\": a2\n  };\n};\nfunction ChatGPTCopilotComponent_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(2, _c0, message_r5.role === \"user\", message_r5.role === \"assistant\", message_r5.role === \"system\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(message_r5.content);\n  }\n}\nfunction ChatGPTCopilotComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, ChatGPTCopilotComponent_div_8_div_1_Template, 4, 6, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.messages);\n  }\n}\nfunction ChatGPTCopilotComponent_div_9_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Send\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatGPTCopilotComponent_div_9_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sending...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatGPTCopilotComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"textarea\", 14);\n    i0.ɵɵlistener(\"ngModelChange\", function ChatGPTCopilotComponent_div_9_Template_textarea_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.userInput = $event);\n    })(\"keydown.enter\", function ChatGPTCopilotComponent_div_9_Template_textarea_keydown_enter_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r10.sendMessage());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ChatGPTCopilotComponent_div_9_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.sendMessage());\n    });\n    i0.ɵɵtemplate(3, ChatGPTCopilotComponent_div_9_span_3_Template, 2, 0, \"span\", 16);\n    i0.ɵɵtemplate(4, ChatGPTCopilotComponent_div_9_span_4_Template, 2, 0, \"span\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.userInput);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading || !ctx_r2.userInput.trim());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ChatGPTCopilotComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"h3\");\n    i0.ɵɵtext(2, \"AI Chat Integration Not Configured\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Please configure Ollama or LM Studio in the Configuration page to enable AI chat integration.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"a\", 18);\n    i0.ɵɵtext(6, \"Go to Configuration\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ChatGPTCopilotComponent {\n  constructor(http) {\n    this.http = http;\n    this.isLoading = false;\n    this.messages = [];\n    this.userInput = '';\n    this.apiKey = '';\n    this.isConfigured = false;\n  }\n  ngOnInit() {\n    this.loadApiKey();\n    this.messages = [{\n      role: 'system',\n      content: 'You are a helpful assistant for software development tasks.'\n    }];\n  }\n  loadApiKey() {\n    this.isLoading = true;\n    this.http.get(`${environment.apiUrl}/config`).subscribe(data => {\n      this.isConfigured = !!(data.ollama_base_url || data.lm_studio_base_url);\n      this.isLoading = false;\n    }, error => {\n      console.error('Error loading configuration:', error);\n      this.isLoading = false;\n    });\n  }\n  sendMessage() {\n    if (!this.userInput.trim() || !this.isConfigured) return;\n    const userMessage = {\n      role: 'user',\n      content: this.userInput\n    };\n    this.messages.push(userMessage);\n    this.userInput = '';\n    this.isLoading = true;\n    this.http.post(`${environment.apiUrl}/chat`, {\n      messages: this.messages,\n      model_id: 'ollama/llama3'\n    }).subscribe(response => {\n      const assistantMessage = {\n        role: 'assistant',\n        content: response.message || 'Sorry, I could not generate a response.'\n      };\n      this.messages.push(assistantMessage);\n      this.isLoading = false;\n    }, error => {\n      console.error('Error sending message:', error);\n      this.messages.push({\n        role: 'assistant',\n        content: 'Sorry, there was an error processing your request. Please try again.'\n      });\n      this.isLoading = false;\n    });\n  }\n  static {\n    this.ɵfac = function ChatGPTCopilotComponent_Factory(t) {\n      return new (t || ChatGPTCopilotComponent)(i0.ɵɵdirectiveInject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatGPTCopilotComponent,\n      selectors: [[\"app-chatgpt-copilot\"]],\n      decls: 11,\n      vars: 4,\n      consts: [[1, \"chatgpt-container\"], [1, \"chatgpt-header\"], [1, \"chatgpt-content\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [\"class\", \"chat-messages\", 4, \"ngIf\"], [\"class\", \"chat-input\", 4, \"ngIf\"], [\"class\", \"not-configured\", 4, \"ngIf\"], [1, \"loading-spinner\"], [\"src\", \"assets/images/loading-spinner.svg\", \"alt\", \"Loading\"], [1, \"chat-messages\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [1, \"message-content\"], [1, \"chat-input\"], [\"placeholder\", \"Ask ChatGPT about your development task...\", 3, \"ngModel\", \"ngModelChange\", \"keydown.enter\"], [3, \"disabled\", \"click\"], [4, \"ngIf\"], [1, \"not-configured\"], [\"routerLink\", \"/config\", 1, \"config-link\"]],\n      template: function ChatGPTCopilotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"AI Chat Copilot\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"Use AI models as a copilot for your development tasks.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 2);\n          i0.ɵɵtemplate(7, ChatGPTCopilotComponent_div_7_Template, 4, 0, \"div\", 3);\n          i0.ɵɵtemplate(8, ChatGPTCopilotComponent_div_8_Template, 2, 1, \"div\", 4);\n          i0.ɵɵtemplate(9, ChatGPTCopilotComponent_div_9_Template, 5, 4, \"div\", 5);\n          i0.ɵɵtemplate(10, ChatGPTCopilotComponent_div_10_Template, 7, 0, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isConfigured);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isConfigured);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isConfigured);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.RouterLink],\n      styles: [\".chatgpt-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  padding: 20px;\\n}\\n\\n.chatgpt-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.chatgpt-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  color: #333;\\n}\\n.chatgpt-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.chatgpt-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  background-color: #f9f9f9;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n}\\n.loading-spinner[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  margin-bottom: 16px;\\n}\\n.loading-spinner[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.user-message[_ngcontent-%COMP%], .assistant-message[_ngcontent-%COMP%], .system-message[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  max-width: 80%;\\n}\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .assistant-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .system-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  white-space: pre-wrap;\\n}\\n\\n.user-message[_ngcontent-%COMP%] {\\n  align-self: flex-end;\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.assistant-message[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  background-color: #e9ecef;\\n  color: #333;\\n}\\n\\n.system-message[_ngcontent-%COMP%] {\\n  align-self: center;\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n  font-style: italic;\\n  font-size: 0.9em;\\n  border: 1px dashed #dee2e6;\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 16px;\\n  background-color: white;\\n  border-top: 1px solid #eee;\\n}\\n.chat-input[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 60px;\\n  padding: 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  resize: none;\\n  font-family: inherit;\\n  font-size: 14px;\\n}\\n.chat-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-left: 12px;\\n  padding: 0 20px;\\n  background-color: #007bff;\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  cursor: pointer;\\n}\\n.chat-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  background-color: #b3d7ff;\\n  cursor: not-allowed;\\n}\\n\\n.not-configured[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  text-align: center;\\n}\\n.not-configured[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  color: #dc3545;\\n}\\n.not-configured[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  color: #666;\\n}\\n.not-configured[_ngcontent-%COMP%]   .config-link[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  background-color: #007bff;\\n  color: white;\\n  text-decoration: none;\\n  border-radius: 4px;\\n}\\n.not-configured[_ngcontent-%COMP%]   .config-link[_ngcontent-%COMP%]:hover {\\n  background-color: #0069d9;\\n}\\n\\n.chatgpt-fallback[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  text-align: center;\\n}\\n.chatgpt-fallback[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  color: #333;\\n}\\n.chatgpt-fallback[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  color: #666;\\n}\\n.chatgpt-fallback[_ngcontent-%COMP%]   .chatgpt-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.chatgpt-fallback[_ngcontent-%COMP%]   .chatgpt-options[_ngcontent-%COMP%]   .chatgpt-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 16px;\\n  background-color: #f1f3f5;\\n  border-radius: 8px;\\n  text-decoration: none;\\n  color: #333;\\n}\\n.chatgpt-fallback[_ngcontent-%COMP%]   .chatgpt-options[_ngcontent-%COMP%]   .chatgpt-option[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n}\\n.chatgpt-fallback[_ngcontent-%COMP%]   .chatgpt-options[_ngcontent-%COMP%]   .chatgpt-option[_ngcontent-%COMP%]   .option-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-right: 12px;\\n}\\n.chatgpt-fallback[_ngcontent-%COMP%]   .chatgpt-options[_ngcontent-%COMP%]   .chatgpt-option[_ngcontent-%COMP%]   .option-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction3", "_c0", "message_r5", "role", "ɵɵadvance", "ɵɵtextInterpolate", "content", "ɵɵtemplate", "ChatGPTCopilotComponent_div_8_div_1_Template", "ctx_r1", "messages", "ɵɵlistener", "ChatGPTCopilotComponent_div_9_Template_textarea_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "userInput", "ChatGPTCopilotComponent_div_9_Template_textarea_keydown_enter_1_listener", "ctx_r10", "preventDefault", "sendMessage", "ChatGPTCopilotComponent_div_9_Template_button_click_2_listener", "ctx_r11", "ChatGPTCopilotComponent_div_9_span_3_Template", "ChatGPTCopilotComponent_div_9_span_4_Template", "ctx_r2", "isLoading", "trim", "ChatGPTCopilotComponent", "constructor", "http", "<PERSON><PERSON><PERSON><PERSON>", "isConfigured", "ngOnInit", "loadApiKey", "get", "apiUrl", "subscribe", "data", "ollama_base_url", "lm_studio_base_url", "error", "console", "userMessage", "push", "post", "model_id", "response", "assistant<PERSON><PERSON><PERSON>", "message", "ɵɵdirectiveInject", "i1", "HttpClient", "selectors", "decls", "vars", "consts", "template", "ChatGPTCopilotComponent_Template", "rf", "ctx", "ChatGPTCopilotComponent_div_7_Template", "ChatGPTCopilotComponent_div_8_Template", "ChatGPTCopilotComponent_div_9_Template", "ChatGPTCopilotComponent_div_10_Template"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\chatgpt-copilot\\chatgpt-copilot.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\chatgpt-copilot\\chatgpt-copilot.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { environment } from '../../../environments/environment';\n\ninterface ChatMessage {\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n}\n\n@Component({\n  selector: 'app-chatgpt-copilot',\n  templateUrl: './chatgpt-copilot.component.html',\n  styleUrls: ['./chatgpt-copilot.component.scss']\n})\nexport class ChatGPTCopilotComponent implements OnInit {\n  isLoading: boolean = false;\n  messages: ChatMessage[] = [];\n  userInput: string = '';\n  apiKey: string = '';\n  isConfigured: boolean = false;\n\n  constructor(private http: HttpClient) {}\n\n  ngOnInit(): void {\n    this.loadApiKey();\n    this.messages = [\n      { role: 'system', content: 'You are a helpful assistant for software development tasks.' }\n    ];\n  }\n\n  loadApiKey(): void {\n    this.isLoading = true;\n    this.http.get(`${environment.apiUrl}/config`).subscribe(\n      (data: any) => {\n        this.isConfigured = !!(data.ollama_base_url || data.lm_studio_base_url);\n        this.isLoading = false;\n      },\n      (error) => {\n        console.error('Error loading configuration:', error);\n        this.isLoading = false;\n      }\n    );\n  }\n\n  sendMessage(): void {\n    if (!this.userInput.trim() || !this.isConfigured) return;\n    \n    const userMessage: ChatMessage = { role: 'user', content: this.userInput };\n    this.messages.push(userMessage);\n    this.userInput = '';\n    this.isLoading = true;\n    \n    this.http.post(`${environment.apiUrl}/chat`, {\n      messages: this.messages,\n      model_id: 'ollama/llama3'\n    }).subscribe(\n      (response: any) => {\n        const assistantMessage: ChatMessage = { \n          role: 'assistant', \n          content: response.message || 'Sorry, I could not generate a response.'\n        };\n        this.messages.push(assistantMessage);\n        this.isLoading = false;\n      },\n      (error) => {\n        console.error('Error sending message:', error);\n        this.messages.push({ \n          role: 'assistant', \n          content: 'Sorry, there was an error processing your request. Please try again.'\n        });\n        this.isLoading = false;\n      }\n    );\n  }\n}\n", "<div class=\"chatgpt-container\">\n  <div class=\"chatgpt-header\">\n    <h2>AI Chat Copilot</h2>\n    <p>Use AI models as a copilot for your development tasks.</p>\n  </div>\n  \n  <div class=\"chatgpt-content\">\n    <div class=\"loading-spinner\" *ngIf=\"isLoading\">\n      <img src=\"assets/images/loading-spinner.svg\" alt=\"Loading\">\n      <p>Processing...</p>\n    </div>\n    \n    <div class=\"chat-messages\" *ngIf=\"isConfigured\">\n      <div *ngFor=\"let message of messages\" \n           [ngClass]=\"{'user-message': message.role === 'user', \n                      'assistant-message': message.role === 'assistant',\n                      'system-message': message.role === 'system'}\">\n        <div class=\"message-content\">\n          <p>{{ message.content }}</p>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"chat-input\" *ngIf=\"isConfigured\">\n      <textarea \n        [(ngModel)]=\"userInput\" \n        placeholder=\"Ask ChatGPT about your development task...\"\n        (keydown.enter)=\"$event.preventDefault(); sendMessage()\"></textarea>\n      <button (click)=\"sendMessage()\" [disabled]=\"isLoading || !userInput.trim()\">\n        <span *ngIf=\"!isLoading\">Send</span>\n        <span *ngIf=\"isLoading\">Sending...</span>\n      </button>\n    </div>\n    \n    <div class=\"not-configured\" *ngIf=\"!isConfigured\">\n      <h3>AI Chat Integration Not Configured</h3>\n      <p>Please configure Ollama or LM Studio in the Configuration page to enable AI chat integration.</p>\n      <a routerLink=\"/config\" class=\"config-link\">Go to Configuration</a>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,WAAW,QAAQ,mCAAmC;;;;;;;;ICK3DC,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,aAA2D;IAC3DF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;;;;;;;IAIpBJ,EAAA,CAAAC,cAAA,cAG8D;IAEvDD,EAAA,CAAAG,MAAA,GAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAJ3BJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,UAAA,CAAAC,IAAA,aAAAD,UAAA,CAAAC,IAAA,kBAAAD,UAAA,CAAAC,IAAA,eAEwD;IAEtDT,EAAA,CAAAU,SAAA,GAAqB;IAArBV,EAAA,CAAAW,iBAAA,CAAAH,UAAA,CAAAI,OAAA,CAAqB;;;;;IAN9BZ,EAAA,CAAAC,cAAA,aAAgD;IAC9CD,EAAA,CAAAa,UAAA,IAAAC,4CAAA,kBAOM;IACRd,EAAA,CAAAI,YAAA,EAAM;;;;IARqBJ,EAAA,CAAAU,SAAA,GAAW;IAAXV,EAAA,CAAAK,UAAA,YAAAU,MAAA,CAAAC,QAAA,CAAW;;;;;IAgBlChB,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACpCJ,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAP7CJ,EAAA,CAAAC,cAAA,cAA6C;IAEzCD,EAAA,CAAAiB,UAAA,2BAAAC,yEAAAC,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAF,MAAA,CAAAG,SAAA,GAAAN,MAAA;IAAA,EAAuB,2BAAAO,yEAAAP,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAM,OAAA,GAAA3B,EAAA,CAAAuB,aAAA;MAENJ,MAAA,CAAAS,cAAA,EAAuB;MAAA,OAAE5B,EAAA,CAAAwB,WAAA,CAAAG,OAAA,CAAAE,WAAA,EAAa;IAAA,EAFhC;IAEkC7B,EAAA,CAAAI,YAAA,EAAW;IACtEJ,EAAA,CAAAC,cAAA,iBAA4E;IAApED,EAAA,CAAAiB,UAAA,mBAAAa,+DAAA;MAAA9B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAU,OAAA,GAAA/B,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAO,OAAA,CAAAF,WAAA,EAAa;IAAA,EAAC;IAC7B7B,EAAA,CAAAa,UAAA,IAAAmB,6CAAA,mBAAoC;IACpChC,EAAA,CAAAa,UAAA,IAAAoB,6CAAA,mBAAyC;IAC3CjC,EAAA,CAAAI,YAAA,EAAS;;;;IANPJ,EAAA,CAAAU,SAAA,GAAuB;IAAvBV,EAAA,CAAAK,UAAA,YAAA6B,MAAA,CAAAT,SAAA,CAAuB;IAGOzB,EAAA,CAAAU,SAAA,GAA2C;IAA3CV,EAAA,CAAAK,UAAA,aAAA6B,MAAA,CAAAC,SAAA,KAAAD,MAAA,CAAAT,SAAA,CAAAW,IAAA,GAA2C;IAClEpC,EAAA,CAAAU,SAAA,GAAgB;IAAhBV,EAAA,CAAAK,UAAA,UAAA6B,MAAA,CAAAC,SAAA,CAAgB;IAChBnC,EAAA,CAAAU,SAAA,GAAe;IAAfV,EAAA,CAAAK,UAAA,SAAA6B,MAAA,CAAAC,SAAA,CAAe;;;;;IAI1BnC,EAAA,CAAAC,cAAA,cAAkD;IAC5CD,EAAA,CAAAG,MAAA,yCAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3CJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,oGAA6F;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACpGJ,EAAA,CAAAC,cAAA,YAA4C;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;ADvBzE,OAAM,MAAOiC,uBAAuB;EAOlCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IANxB,KAAAJ,SAAS,GAAY,KAAK;IAC1B,KAAAnB,QAAQ,GAAkB,EAAE;IAC5B,KAAAS,SAAS,GAAW,EAAE;IACtB,KAAAe,MAAM,GAAW,EAAE;IACnB,KAAAC,YAAY,GAAY,KAAK;EAEU;EAEvCC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAAC3B,QAAQ,GAAG,CACd;MAAEP,IAAI,EAAE,QAAQ;MAAEG,OAAO,EAAE;IAA6D,CAAE,CAC3F;EACH;EAEA+B,UAAUA,CAAA;IACR,IAAI,CAACR,SAAS,GAAG,IAAI;IACrB,IAAI,CAACI,IAAI,CAACK,GAAG,CAAC,GAAG7C,WAAW,CAAC8C,MAAM,SAAS,CAAC,CAACC,SAAS,CACpDC,IAAS,IAAI;MACZ,IAAI,CAACN,YAAY,GAAG,CAAC,EAAEM,IAAI,CAACC,eAAe,IAAID,IAAI,CAACE,kBAAkB,CAAC;MACvE,IAAI,CAACd,SAAS,GAAG,KAAK;IACxB,CAAC,EACAe,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,IAAI,CAACf,SAAS,GAAG,KAAK;IACxB,CAAC,CACF;EACH;EAEAN,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACW,IAAI,EAAE,IAAI,CAAC,IAAI,CAACK,YAAY,EAAE;IAElD,MAAMW,WAAW,GAAgB;MAAE3C,IAAI,EAAE,MAAM;MAAEG,OAAO,EAAE,IAAI,CAACa;IAAS,CAAE;IAC1E,IAAI,CAACT,QAAQ,CAACqC,IAAI,CAACD,WAAW,CAAC;IAC/B,IAAI,CAAC3B,SAAS,GAAG,EAAE;IACnB,IAAI,CAACU,SAAS,GAAG,IAAI;IAErB,IAAI,CAACI,IAAI,CAACe,IAAI,CAAC,GAAGvD,WAAW,CAAC8C,MAAM,OAAO,EAAE;MAC3C7B,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBuC,QAAQ,EAAE;KACX,CAAC,CAACT,SAAS,CACTU,QAAa,IAAI;MAChB,MAAMC,gBAAgB,GAAgB;QACpChD,IAAI,EAAE,WAAW;QACjBG,OAAO,EAAE4C,QAAQ,CAACE,OAAO,IAAI;OAC9B;MACD,IAAI,CAAC1C,QAAQ,CAACqC,IAAI,CAACI,gBAAgB,CAAC;MACpC,IAAI,CAACtB,SAAS,GAAG,KAAK;IACxB,CAAC,EACAe,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,IAAI,CAAClC,QAAQ,CAACqC,IAAI,CAAC;QACjB5C,IAAI,EAAE,WAAW;QACjBG,OAAO,EAAE;OACV,CAAC;MACF,IAAI,CAACuB,SAAS,GAAG,KAAK;IACxB,CAAC,CACF;EACH;;;uBA3DWE,uBAAuB,EAAArC,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAvBxB,uBAAuB;MAAAyB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdpCpE,EAAA,CAAAC,cAAA,aAA+B;UAEvBD,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxBJ,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAG,MAAA,6DAAsD;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAG/DJ,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAa,UAAA,IAAAyD,sCAAA,iBAGM;UAENtE,EAAA,CAAAa,UAAA,IAAA0D,sCAAA,iBASM;UAENvE,EAAA,CAAAa,UAAA,IAAA2D,sCAAA,iBASM;UAENxE,EAAA,CAAAa,UAAA,KAAA4D,uCAAA,iBAIM;UACRzE,EAAA,CAAAI,YAAA,EAAM;;;UAhC0BJ,EAAA,CAAAU,SAAA,GAAe;UAAfV,EAAA,CAAAK,UAAA,SAAAgE,GAAA,CAAAlC,SAAA,CAAe;UAKjBnC,EAAA,CAAAU,SAAA,GAAkB;UAAlBV,EAAA,CAAAK,UAAA,SAAAgE,GAAA,CAAA5B,YAAA,CAAkB;UAWrBzC,EAAA,CAAAU,SAAA,GAAkB;UAAlBV,EAAA,CAAAK,UAAA,SAAAgE,GAAA,CAAA5B,YAAA,CAAkB;UAWdzC,EAAA,CAAAU,SAAA,GAAmB;UAAnBV,EAAA,CAAAK,UAAA,UAAAgE,GAAA,CAAA5B,YAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}