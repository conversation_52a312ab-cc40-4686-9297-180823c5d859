{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let NotificationService = /*#__PURE__*/(() => {\n  class NotificationService {\n    constructor() {}\n    showSuccess(message) {\n      this.showNotification(message, 'success');\n    }\n    showError(message) {\n      this.showNotification(message, 'error');\n    }\n    showWarning(message) {\n      this.showNotification(message, 'warning');\n    }\n    showInfo(message) {\n      this.showNotification(message, 'info');\n    }\n    showNotification(message, type) {\n      let container = document.getElementById('notification-container');\n      if (!container) {\n        container = document.createElement('div');\n        container.id = 'notification-container';\n        container.style.position = 'fixed';\n        container.style.top = '20px';\n        container.style.right = '20px';\n        container.style.zIndex = '9999';\n        document.body.appendChild(container);\n      }\n      const notification = document.createElement('div');\n      notification.className = `notification notification-${type}`;\n      notification.style.backgroundColor = this.getBackgroundColor(type);\n      notification.style.color = '#fff';\n      notification.style.padding = '12px 20px';\n      notification.style.borderRadius = '4px';\n      notification.style.marginBottom = '10px';\n      notification.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';\n      notification.style.minWidth = '250px';\n      notification.style.maxWidth = '350px';\n      notification.style.opacity = '0';\n      notification.style.transform = 'translateX(50px)';\n      notification.style.transition = 'opacity 0.3s, transform 0.3s';\n      const messageText = document.createElement('div');\n      messageText.textContent = message;\n      notification.appendChild(messageText);\n      const closeButton = document.createElement('button');\n      closeButton.textContent = '×';\n      closeButton.style.background = 'none';\n      closeButton.style.border = 'none';\n      closeButton.style.color = '#fff';\n      closeButton.style.fontSize = '20px';\n      closeButton.style.fontWeight = 'bold';\n      closeButton.style.cursor = 'pointer';\n      closeButton.style.position = 'absolute';\n      closeButton.style.top = '5px';\n      closeButton.style.right = '10px';\n      closeButton.style.padding = '0';\n      closeButton.style.lineHeight = '20px';\n      closeButton.onclick = () => this.removeNotification(notification);\n      notification.appendChild(closeButton);\n      container.appendChild(notification);\n      setTimeout(() => {\n        notification.style.opacity = '1';\n        notification.style.transform = 'translateX(0)';\n      }, 10);\n      setTimeout(() => {\n        this.removeNotification(notification);\n      }, 5000);\n    }\n    removeNotification(notification) {\n      notification.style.opacity = '0';\n      notification.style.transform = 'translateX(50px)';\n      setTimeout(() => {\n        if (notification.parentNode) {\n          notification.parentNode.removeChild(notification);\n        }\n      }, 300);\n    }\n    getBackgroundColor(type) {\n      switch (type) {\n        case 'success':\n          return '#28a745';\n        case 'error':\n          return '#dc3545';\n        case 'warning':\n          return '#ffc107';\n        case 'info':\n          return '#17a2b8';\n        default:\n          return '#6c757d';\n      }\n    }\n    static {\n      this.ɵfac = function NotificationService_Factory(t) {\n        return new (t || NotificationService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: NotificationService,\n        factory: NotificationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NotificationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}