{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n  if (!cssMapping) {\n    return content;\n  }\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(JSON.stringify(cssMapping));\n    return [content, \"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64, \" */\")].join(\"\\n\");\n  }\n  return [content].join(\"\\n\");\n};", "map": {"version": 3, "names": ["module", "exports", "item", "content", "cssMapping", "btoa", "base64", "JSON", "stringify", "concat", "join"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/css-loader/dist/runtime/sourceMaps.js"], "sourcesContent": ["\n\"use strict\";\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n\n  if (!cssMapping) {\n    return content;\n  }\n\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(JSON.stringify(cssMapping));\n    return [content, \"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64, \" */\")].join(\"\\n\");\n  }\n\n  return [content].join(\"\\n\");\n};\n"], "mappings": "AACA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;EAC/B,IAAIC,OAAO,GAAGD,IAAI,CAAC,CAAC,CAAC;EACrB,IAAIE,UAAU,GAAGF,IAAI,CAAC,CAAC,CAAC;EAExB,IAAI,CAACE,UAAU,EAAE;IACf,OAAOD,OAAO;EAChB;EAEA,IAAI,OAAOE,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAIC,MAAM,GAAGD,IAAI,CAACE,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAC,CAAC;IAC7C,OAAO,CAACD,OAAO,EAAE,kEAAkE,CAACM,MAAM,CAACH,MAAM,EAAE,KAAK,CAAC,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC;EACvH;EAEA,OAAO,CAACP,OAAO,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}