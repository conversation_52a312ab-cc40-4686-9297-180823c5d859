{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const ArgumentOutOfRangeError = createErrorClass(_super => function ArgumentOutOfRangeErrorImpl() {\n  _super(this);\n  this.name = 'ArgumentOutOfRangeError';\n  this.message = 'argument out of range';\n});\n//# sourceMappingURL=ArgumentOutOfRangeError.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}