{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { HomeComponent } from './components/home/<USER>';\nimport { ProjectListComponent } from './components/project-list/project-list.component';\nimport { ProjectDetailComponent } from './components/project-detail/project-detail.component';\nimport { ConfigComponent } from './components/config/config.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HomeComponent\n}, {\n  path: 'projects',\n  component: ProjectListComponent\n}, {\n  path: 'projects/:name',\n  component: ProjectDetailComponent\n}, {\n  path: 'config',\n  component: ConfigComponent\n}, {\n  path: '**',\n  redirectTo: ''\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "HomeComponent", "ProjectListComponent", "ProjectDetailComponent", "ConfigComponent", "routes", "path", "component", "redirectTo", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { HomeComponent } from './components/home/<USER>';\nimport { ProjectListComponent } from './components/project-list/project-list.component';\nimport { ProjectDetailComponent } from './components/project-detail/project-detail.component';\nimport { ConfigComponent } from './components/config/config.component';\n\nconst routes: Routes = [\n  { path: '', component: HomeComponent },\n  { path: 'projects', component: ProjectListComponent },\n  { path: 'projects/:name', component: ProjectDetailComponent },\n  { path: 'config', component: ConfigComponent },\n  { path: '**', redirectTo: '' }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,eAAe,QAAQ,sCAAsC;;;AAEtE,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEN;AAAa,CAAE,EACtC;EAAEK,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEL;AAAoB,CAAE,EACrD;EAAEI,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEJ;AAAsB,CAAE,EAC7D;EAAEG,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEH;AAAe,CAAE,EAC9C;EAAEE,IAAI,EAAE,IAAI;EAAEE,UAAU,EAAE;AAAE,CAAE,CAC/B;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBT,YAAY,CAACU,OAAO,CAACL,MAAM,CAAC,EAC5BL,YAAY;IAAA;EAAA;;;2EAEXS,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAZ,YAAA;IAAAa,OAAA,GAFjBb,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}