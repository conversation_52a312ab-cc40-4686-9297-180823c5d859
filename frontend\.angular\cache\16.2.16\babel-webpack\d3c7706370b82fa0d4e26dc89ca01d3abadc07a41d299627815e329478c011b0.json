{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ApiService = /*#__PURE__*/(() => {\n  class ApiService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.apiUrl;\n      console.log('[ApiService] Initialized with base URL:', this.apiUrl);\n    }\n    /** Models */\n    getModels() {\n      console.log('[ApiService] GET models');\n      return this.http.get(`${this.apiUrl}/models`);\n    }\n    testModelConnection(modelId) {\n      console.log('[ApiService] POST test model with id:', modelId);\n      return this.http.post(`${this.apiUrl}/models/test`, {\n        model_id: modelId\n      });\n    }\n    /** Projects */\n    getProjects() {\n      console.log('[ApiService] GET projects');\n      return this.http.get(`${this.apiUrl}/projects`);\n    }\n    getProject(name) {\n      console.log('[ApiService] GET project:', name);\n      return this.http.get(`${this.apiUrl}/projects/${encodeURIComponent(name)}`);\n    }\n    createProject(name, description) {\n      console.log('[ApiService] POST create project:', {\n        name,\n        description\n      });\n      return this.http.post(`${this.apiUrl}/projects`, {\n        name,\n        description\n      });\n    }\n    deleteProject(name) {\n      console.log('[ApiService] DELETE project:', name);\n      return this.http.delete(`${this.apiUrl}/projects/${encodeURIComponent(name)}`);\n    }\n    deleteAllProjects() {\n      console.log('[ApiService] DELETE all projects');\n      return this.http.delete(`${this.apiUrl}/projects`);\n    }\n    /** Files */\n    getProjectFiles(projectName) {\n      console.log('[ApiService] GET files for project:', projectName);\n      return this.http.get(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/files`);\n    }\n    getFileContent(projectName, filePath) {\n      console.log('[ApiService] GET file content:', projectName, filePath);\n      return this.http.get(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/files/${encodeURIComponent(filePath)}`);\n    }\n    updateFileContent(projectName, filePath, content) {\n      console.log('[ApiService] PUT update file:', projectName, filePath);\n      return this.http.put(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/files/${encodeURIComponent(filePath)}`, {\n        content\n      });\n    }\n    deleteFile(projectName, filePath) {\n      console.log('[ApiService] DELETE file:', projectName, filePath);\n      return this.http.delete(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/files/${encodeURIComponent(filePath)}`);\n    }\n    openProjectInExplorer(projectName) {\n      console.log('[ApiService] POST open project in explorer:', projectName);\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/open-explorer`, {});\n    }\n    /**\n     * Upload file to project\n     * @param projectName Project name\n     * @param formData FormData containing the file to upload\n     * @returns Observable with upload result\n     */\n    uploadFile(projectName, formData) {\n      console.log('[ApiService] POST upload file to project:', projectName);\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/files/upload`, formData);\n    }\n    /**\n     * Reset project (delete all files)\n     * @param projectName Project name\n     * @returns Observable with reset result\n     */\n    resetProject(projectName) {\n      console.log('[ApiService] POST reset project:', projectName);\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/reset`, {});\n    }\n    /** Messages */\n    getProjectMessages(projectName) {\n      console.log('[ApiService] GET messages for project:', projectName);\n      return this.http.get(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/messages`);\n    }\n    deleteProjectMessages(projectName) {\n      console.log('[ApiService] DELETE messages for project:', projectName);\n      return this.http.delete(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/messages`);\n    }\n    sendMessage(projectName, message, modelId, localLlmModel, streamingEnabled = true) {\n      console.log('[ApiService] POST send message:', {\n        projectName,\n        message,\n        modelId,\n        localLlmModel,\n        streamingEnabled\n      });\n      const payload = {\n        project_name: projectName,\n        message,\n        model_id: modelId,\n        streaming_enabled: streamingEnabled\n      };\n      if (localLlmModel) {\n        payload.local_llm_model_id = localLlmModel;\n      }\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/messages`, payload);\n    }\n    /**\n     * Add a reaction to a message\n     * @param projectName Project name\n     * @param messageId Message ID\n     * @param reaction Reaction type (like, dislike, etc.)\n     * @returns Observable with reaction result\n     */\n    addMessageReaction(projectName, messageId, reaction) {\n      console.log('[ApiService] POST message reaction:', {\n        projectName,\n        messageId,\n        reaction\n      });\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/messages/${encodeURIComponent(messageId)}/reaction`, {\n        reaction\n      });\n    }\n    /**\n     * Get context memory for a project\n     * @param projectName Project name\n     * @param limit Maximum number of memory entries to return\n     * @returns Observable with context memory\n     */\n    getContextMemory(projectName, limit = 5) {\n      console.log('[ApiService] GET context memory for project:', projectName, 'limit:', limit);\n      return this.http.get(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/context-memory?limit=${limit}`);\n    }\n    /**\n     * Reset context memory for a project\n     * @param projectName Project name\n     * @returns Observable with reset result\n     */\n    resetContextMemory(projectName) {\n      console.log('[ApiService] POST reset context memory for project:', projectName);\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/context-memory/reset`, {});\n    }\n    /** Configuration */\n    getConfig() {\n      console.log('[ApiService] GET config');\n      return this.http.get(`${this.apiUrl}/config`);\n    }\n    updateConfig(config) {\n      console.log('[ApiService] PUT update config:', config);\n      return this.http.put(`${this.apiUrl}/config`, config);\n    }\n    exportProjectChat(name) {\n      console.log('[ApiService] POST export project chat:', name);\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(name)}/export-chat`, {});\n    }\n    /** Feedback System */\n    submitFeedback(feedbackData) {\n      console.log('[ApiService] POST feedback:', feedbackData);\n      return this.http.post(`${this.apiUrl}/feedback`, feedbackData);\n    }\n    getFeedbackSummary(projectName) {\n      let params = {};\n      if (projectName) {\n        params['project_name'] = projectName;\n      }\n      console.log('[ApiService] GET feedback summary, params:', params);\n      return this.http.get(`${this.apiUrl}/feedback/summary`, {\n        params\n      });\n    }\n    getUserPreferences() {\n      console.log('[ApiService] GET user preferences');\n      return this.http.get(`${this.apiUrl}/feedback/preferences`);\n    }\n    getModelRecommendation(taskType) {\n      console.log('[ApiService] GET model recommendation for task type:', taskType);\n      return this.http.get(`${this.apiUrl}/feedback/model_recommendation`, {\n        params: {\n          task_type: taskType\n        }\n      });\n    }\n    /** Autonomous Agent API */\n    /**\n     * Execute a planned task in the project\n     * @param projectName Project name\n     * @param task Task to execute\n     * @param modelId Model ID to use for execution\n     * @returns Observable with execution result\n     */\n    executeTask(projectName, task, modelId) {\n      console.log('[ApiService] POST execute task:', task);\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/tasks`, {\n        task,\n        model_id: modelId\n      });\n    }\n    /**\n     * Execute a command in the project\n     * @param projectName Project name\n     * @param command Command to execute\n     * @returns Observable with command output\n     */\n    executeCommand(projectName, command) {\n      console.log('[ApiService] POST execute command:', command);\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/execute`, {\n        command\n      });\n    }\n    /**\n     * Modify a file through the agent API\n     * @param projectName Project name\n     * @param filePath File path to modify\n     * @param content New content for the file\n     * @returns Observable with modification results\n     */\n    modifyFile(projectName, filePath, content) {\n      console.log('[ApiService] PUT modify file:', filePath);\n      return this.http.put(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/modify`, {\n        path: filePath,\n        content\n      });\n    }\n    /**\n     * Save project chat messages to persist history and expanded state\n     * @param projectName Project name\n     * @param messages Array of messages to save\n     * @param chatExpanded Boolean indicating if chat is expanded\n     * @returns Observable with save result\n     */\n    saveProjectMessages(projectName, messages, chatExpanded = false) {\n      console.log('[ApiService] POST save project messages:', messages.length, 'chatExpanded:', chatExpanded);\n      return this.http.post(`${this.apiUrl}/projects/${encodeURIComponent(projectName)}/messages/save`, {\n        messages,\n        chatExpanded\n      });\n    }\n    static {\n      this.ɵfac = function ApiService_Factory(t) {\n        return new (t || ApiService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ApiService,\n        factory: ApiService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ApiService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}