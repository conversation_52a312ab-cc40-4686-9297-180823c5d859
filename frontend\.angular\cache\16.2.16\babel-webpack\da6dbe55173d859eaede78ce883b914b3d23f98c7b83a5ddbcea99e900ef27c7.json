{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/project.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction ProjectListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"div\", 12);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading projects...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"p\");\n    i0.ɵɵtext(2, \"No projects found. Create a new project to get started.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_div_14_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const project_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openProject(project_r3.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"h3\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 17)(5, \"span\", 18);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 18);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"span\", 20);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 21);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 22)(15, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_div_14_Template_button_click_15_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const project_r3 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.deleteProject(project_r3.name, $event));\n    });\n    i0.ɵɵtext(16, \" Delete \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r3 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(project_r3.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Created: \", ctx_r2.formatDate(project_r3.created_at), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Updated: \", ctx_r2.formatDate(project_r3.updated_at), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", (project_r3.files == null ? null : project_r3.files.length) || 0, \" files\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", (project_r3.messages == null ? null : project_r3.messages.length) || 0, \" messages\");\n  }\n}\nexport class ProjectListComponent {\n  constructor(projectService, router) {\n    this.projectService = projectService;\n    this.router = router;\n    this.projects = [];\n    this.loading = false;\n    this.newProjectName = '';\n  }\n  ngOnInit() {\n    this.loadProjects();\n  }\n  loadProjects() {\n    this.loading = true;\n    this.projectService.getProjects().subscribe(response => {\n      this.projects = response.projects || [];\n      this.loading = false;\n    }, error => {\n      console.error('Error loading projects:', error);\n      this.loading = false;\n    });\n  }\n  createProject() {\n    if (!this.newProjectName.trim()) return;\n    this.loading = true;\n    this.projectService.createProject(this.newProjectName).subscribe(response => {\n      this.newProjectName = '';\n      this.loadProjects();\n      this.router.navigate(['/projects', response.project.name]);\n    }, error => {\n      console.error('Error creating project:', error);\n      this.loading = false;\n    });\n  }\n  deleteProject(projectName, event) {\n    event.stopPropagation();\n    if (!confirm(`Are you sure you want to delete project \"${projectName}\"?`)) {\n      return;\n    }\n    this.loading = true;\n    this.projectService.deleteProject(projectName).subscribe(() => {\n      this.loadProjects();\n    }, error => {\n      console.error('Error deleting project:', error);\n      this.loading = false;\n    });\n  }\n  openProject(projectName) {\n    this.router.navigate(['/projects', projectName]);\n  }\n  formatDate(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  deleteAllProjects() {\n    if (!confirm('Are you sure you want to delete ALL projects? This action cannot be undone.')) {\n      return;\n    }\n    this.loading = true;\n    this.projectService.deleteAllProjects().subscribe(() => {\n      this.loadProjects();\n    }, error => {\n      console.error('Error deleting all projects:', error);\n      this.loading = false;\n    });\n  }\n  static {\n    this.ɵfac = function ProjectListComponent_Factory(t) {\n      return new (t || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.ProjectService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectListComponent,\n      selectors: [[\"app-project-list\"]],\n      decls: 15,\n      vars: 7,\n      consts: [[1, \"projects-container\"], [1, \"projects-header\"], [1, \"create-project\"], [\"type\", \"text\", \"placeholder\", \"New project name\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keyup.enter\"], [3, \"disabled\", \"click\"], [2, \"margin-left\", \"8px\", \"background\", \"#e53935\", \"color\", \"white\", 3, \"disabled\", \"click\"], [1, \"projects-content\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"projects-list\"], [\"class\", \"project-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"empty-state\"], [1, \"project-card\", 3, \"click\"], [1, \"project-info\"], [1, \"project-name\"], [1, \"project-meta\"], [1, \"project-date\"], [1, \"project-stats\"], [1, \"project-files\"], [1, \"project-messages\"], [1, \"project-actions\"], [1, \"delete-btn\", 3, \"click\"]],\n      template: function ProjectListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Projects\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"input\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function ProjectListComponent_Template_input_ngModelChange_5_listener($event) {\n            return ctx.newProjectName = $event;\n          })(\"keyup.enter\", function ProjectListComponent_Template_input_keyup_enter_5_listener() {\n            return ctx.createProject();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ProjectListComponent_Template_button_click_6_listener() {\n            return ctx.createProject();\n          });\n          i0.ɵɵtext(7, \" Create Project \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function ProjectListComponent_Template_button_click_8_listener() {\n            return ctx.deleteAllProjects();\n          });\n          i0.ɵɵtext(9, \" Delete All \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 6);\n          i0.ɵɵtemplate(11, ProjectListComponent_div_11_Template, 4, 0, \"div\", 7);\n          i0.ɵɵtemplate(12, ProjectListComponent_div_12_Template, 3, 0, \"div\", 8);\n          i0.ɵɵelementStart(13, \"div\", 9);\n          i0.ɵɵtemplate(14, ProjectListComponent_div_14_Template, 17, 5, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngModel\", ctx.newProjectName)(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.newProjectName.trim() || ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.projects.length === 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.projects.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.projects);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel],\n      styles: [\".projects-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  padding: 20px;\\n}\\n\\n.projects-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.projects-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 500;\\n}\\n.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid #ccc;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  min-width: 200px;\\n}\\n.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled {\\n  background-color: #f5f5f5;\\n  cursor: not-allowed;\\n}\\n.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  background-color: #2196f3;\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: #1976d2;\\n}\\n.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  background-color: #bbdefb;\\n  cursor: not-allowed;\\n}\\n\\n.projects-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.projects-content[_ngcontent-%COMP%]   .loading-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin: 32px 0;\\n}\\n.projects-content[_ngcontent-%COMP%]   .loading-indicator[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: 3px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 50%;\\n  border-top-color: #3498db;\\n  animation: _ngcontent-%COMP%_spin 1s ease-in-out infinite;\\n  margin-bottom: 8px;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.projects-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 200px;\\n  color: #888;\\n  font-style: italic;\\n}\\n\\n.projects-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 16px;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  padding: 16px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  transition: box-shadow 0.2s, transform 0.2s;\\n  cursor: pointer;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-2px);\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-name[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 18px;\\n  font-weight: 500;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 12px;\\n  font-size: 12px;\\n  color: #666;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-meta[_ngcontent-%COMP%]   .project-date[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  font-size: 14px;\\n  color: #444;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-files[_ngcontent-%COMP%], .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-messages[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-files[_ngcontent-%COMP%]::before, .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-messages[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 16px;\\n  height: 16px;\\n  margin-right: 4px;\\n  background-color: #2196f3;\\n  mask-size: cover;\\n  -webkit-mask-size: cover;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-files[_ngcontent-%COMP%]::before {\\n  mask-image: url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z\\\"/></svg>');\\n  -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z\\\"/></svg>');\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-messages[_ngcontent-%COMP%]::before {\\n  mask-image: url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10z\\\"/></svg>');\\n  -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10z\\\"/></svg>');\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  margin-top: 16px;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  background-color: #f44336;\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #d32f2f;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProjectListComponent_div_14_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r5", "project_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "openProject", "name", "ProjectListComponent_div_14_Template_button_click_15_listener", "$event", "ctx_r6", "deleteProject", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "ctx_r2", "formatDate", "created_at", "updated_at", "files", "length", "messages", "ProjectListComponent", "constructor", "projectService", "router", "projects", "loading", "newProjectName", "ngOnInit", "loadProjects", "getProjects", "subscribe", "response", "error", "console", "createProject", "trim", "navigate", "project", "projectName", "event", "stopPropagation", "confirm", "dateString", "date", "Date", "toLocaleString", "deleteAllProjects", "ɵɵdirectiveInject", "i1", "ProjectService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "ProjectListComponent_Template", "rf", "ctx", "ProjectListComponent_Template_input_ngModelChange_5_listener", "ProjectListComponent_Template_input_keyup_enter_5_listener", "ProjectListComponent_Template_button_click_6_listener", "ProjectListComponent_Template_button_click_8_listener", "ɵɵtemplate", "ProjectListComponent_div_11_Template", "ProjectListComponent_div_12_Template", "ProjectListComponent_div_14_Template", "ɵɵproperty"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\project-list\\project-list.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\project-list\\project-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { ProjectService } from '../../services/project.service';\n\n@Component({\n  selector: 'app-project-list',\n  templateUrl: './project-list.component.html',\n  styleUrls: ['./project-list.component.scss']\n})\nexport class ProjectListComponent implements OnInit {\n  projects: any[] = [];\n  loading: boolean = false;\n  newProjectName: string = '';\n  \n  constructor(\n    private projectService: ProjectService,\n    private router: Router\n  ) { }\n  \n  ngOnInit(): void {\n    this.loadProjects();\n  }\n  \n  loadProjects(): void {\n    this.loading = true;\n    \n    this.projectService.getProjects().subscribe(\n      (response) => {\n        this.projects = response.projects || [];\n        this.loading = false;\n      },\n      (error) => {\n        console.error('Error loading projects:', error);\n        this.loading = false;\n      }\n    );\n  }\n  \n  createProject(): void {\n    if (!this.newProjectName.trim()) return;\n    \n    this.loading = true;\n    \n    this.projectService.createProject(this.newProjectName).subscribe(\n      (response) => {\n        this.newProjectName = '';\n        this.loadProjects();\n        this.router.navigate(['/projects', response.project.name]);\n      },\n      (error) => {\n        console.error('Error creating project:', error);\n        this.loading = false;\n      }\n    );\n  }\n  \n  deleteProject(projectName: string, event: Event): void {\n    event.stopPropagation();\n    \n    if (!confirm(`Are you sure you want to delete project \"${projectName}\"?`)) {\n      return;\n    }\n    \n    this.loading = true;\n    \n    this.projectService.deleteProject(projectName).subscribe(\n      () => {\n        this.loadProjects();\n      },\n      (error) => {\n        console.error('Error deleting project:', error);\n        this.loading = false;\n      }\n    );\n  }\n  \n  openProject(projectName: string): void {\n    this.router.navigate(['/projects', projectName]);\n  }\n  \n  formatDate(dateString: string): string {\n    if (!dateString) return '';\n    \n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  \n  deleteAllProjects(): void {\n    if (!confirm('Are you sure you want to delete ALL projects? This action cannot be undone.')) {\n      return;\n    }\n    this.loading = true;\n    this.projectService.deleteAllProjects().subscribe(\n      () => {\n        this.loadProjects();\n      },\n      (error) => {\n        console.error('Error deleting all projects:', error);\n        this.loading = false;\n      }\n    );\n  }\n}\n", "<div class=\"projects-container\">\n  <div class=\"projects-header\">\n    <h2>Projects</h2>\n    <div class=\"create-project\">\n      <input \n        type=\"text\" \n        [(ngModel)]=\"newProjectName\" \n        placeholder=\"New project name\" \n        [disabled]=\"loading\"\n        (keyup.enter)=\"createProject()\"\n      />\n      <button (click)=\"createProject()\" [disabled]=\"!newProjectName.trim() || loading\">\n        Create Project\n      </button>\n      <button (click)=\"deleteAllProjects()\" [disabled]=\"loading || projects.length === 0\" style=\"margin-left: 8px; background: #e53935; color: white;\">\n        Delete All\n      </button>\n    </div>\n  </div>\n  \n  <div class=\"projects-content\">\n    <div *ngIf=\"loading\" class=\"loading-indicator\">\n      <div class=\"spinner\"></div>\n      <p>Loading projects...</p>\n    </div>\n    \n    <div *ngIf=\"!loading && projects.length === 0\" class=\"empty-state\">\n      <p>No projects found. Create a new project to get started.</p>\n    </div>\n    \n    <div class=\"projects-list\">\n      <div \n        *ngFor=\"let project of projects\" \n        class=\"project-card\" \n        (click)=\"openProject(project.name)\"\n      >\n        <div class=\"project-info\">\n          <h3 class=\"project-name\">{{ project.name }}</h3>\n          <div class=\"project-meta\">\n            <span class=\"project-date\">Created: {{ formatDate(project.created_at) }}</span>\n            <span class=\"project-date\">Updated: {{ formatDate(project.updated_at) }}</span>\n          </div>\n          <div class=\"project-stats\">\n            <span class=\"project-files\">{{ project.files?.length || 0 }} files</span>\n            <span class=\"project-messages\">{{ project.messages?.length || 0 }} messages</span>\n          </div>\n        </div>\n        <div class=\"project-actions\">\n          <button class=\"delete-btn\" (click)=\"deleteProject(project.name, $event)\">\n            Delete\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;ICqBIA,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,SAAA,cAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAG5BJ,EAAA,CAAAC,cAAA,cAAmE;IAC9DD,EAAA,CAAAG,MAAA,8DAAuD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAI9DJ,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAC,0DAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAL,UAAA,CAAAM,IAAA,CAAyB;IAAA,EAAC;IAEnChB,EAAA,CAAAC,cAAA,cAA0B;IACCD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChDJ,EAAA,CAAAC,cAAA,cAA0B;IACGD,EAAA,CAAAG,MAAA,GAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC/EJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEjFJ,EAAA,CAAAC,cAAA,cAA2B;IACGD,EAAA,CAAAG,MAAA,IAAsC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzEJ,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,IAA4C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGtFJ,EAAA,CAAAC,cAAA,eAA6B;IACAD,EAAA,CAAAK,UAAA,mBAAAY,8DAAAC,MAAA;MAAA,MAAAX,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAQ,MAAA,GAAAnB,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAK,MAAA,CAAAC,aAAA,CAAAV,UAAA,CAAAM,IAAA,EAAAE,MAAA,CAAmC;IAAA,EAAC;IACtElB,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAbgBJ,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAsB,iBAAA,CAAAZ,UAAA,CAAAM,IAAA,CAAkB;IAEdhB,EAAA,CAAAqB,SAAA,GAA6C;IAA7CrB,EAAA,CAAAuB,kBAAA,cAAAC,MAAA,CAAAC,UAAA,CAAAf,UAAA,CAAAgB,UAAA,MAA6C;IAC7C1B,EAAA,CAAAqB,SAAA,GAA6C;IAA7CrB,EAAA,CAAAuB,kBAAA,cAAAC,MAAA,CAAAC,UAAA,CAAAf,UAAA,CAAAiB,UAAA,MAA6C;IAG5C3B,EAAA,CAAAqB,SAAA,GAAsC;IAAtCrB,EAAA,CAAAuB,kBAAA,MAAAb,UAAA,CAAAkB,KAAA,kBAAAlB,UAAA,CAAAkB,KAAA,CAAAC,MAAA,iBAAsC;IACnC7B,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAAuB,kBAAA,MAAAb,UAAA,CAAAoB,QAAA,kBAAApB,UAAA,CAAAoB,QAAA,CAAAD,MAAA,oBAA4C;;;ADnCvF,OAAM,MAAOE,oBAAoB;EAK/BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,cAAc,GAAW,EAAE;EAKvB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACH,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACH,cAAc,CAACO,WAAW,EAAE,CAACC,SAAS,CACxCC,QAAQ,IAAI;MACX,IAAI,CAACP,QAAQ,GAAGO,QAAQ,CAACP,QAAQ,IAAI,EAAE;MACvC,IAAI,CAACC,OAAO,GAAG,KAAK;IACtB,CAAC,EACAO,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,IAAI,CAACP,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEAS,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACR,cAAc,CAACS,IAAI,EAAE,EAAE;IAEjC,IAAI,CAACV,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACH,cAAc,CAACY,aAAa,CAAC,IAAI,CAACR,cAAc,CAAC,CAACI,SAAS,CAC7DC,QAAQ,IAAI;MACX,IAAI,CAACL,cAAc,GAAG,EAAE;MACxB,IAAI,CAACE,YAAY,EAAE;MACnB,IAAI,CAACL,MAAM,CAACa,QAAQ,CAAC,CAAC,WAAW,EAAEL,QAAQ,CAACM,OAAO,CAAChC,IAAI,CAAC,CAAC;IAC5D,CAAC,EACA2B,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,IAAI,CAACP,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEAhB,aAAaA,CAAC6B,WAAmB,EAAEC,KAAY;IAC7CA,KAAK,CAACC,eAAe,EAAE;IAEvB,IAAI,CAACC,OAAO,CAAC,4CAA4CH,WAAW,IAAI,CAAC,EAAE;MACzE;;IAGF,IAAI,CAACb,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACH,cAAc,CAACb,aAAa,CAAC6B,WAAW,CAAC,CAACR,SAAS,CACtD,MAAK;MACH,IAAI,CAACF,YAAY,EAAE;IACrB,CAAC,EACAI,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,IAAI,CAACP,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEArB,WAAWA,CAACkC,WAAmB;IAC7B,IAAI,CAACf,MAAM,CAACa,QAAQ,CAAC,CAAC,WAAW,EAAEE,WAAW,CAAC,CAAC;EAClD;EAEAxB,UAAUA,CAAC4B,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAE1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,cAAc,EAAE;EAC9B;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACL,OAAO,CAAC,6EAA6E,CAAC,EAAE;MAC3F;;IAEF,IAAI,CAAChB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACH,cAAc,CAACwB,iBAAiB,EAAE,CAAChB,SAAS,CAC/C,MAAK;MACH,IAAI,CAACF,YAAY,EAAE;IACrB,CAAC,EACAI,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,IAAI,CAACP,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;;;uBA5FWL,oBAAoB,EAAA/B,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5D,EAAA,CAAA0D,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApB/B,oBAAoB;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTjCrE,EAAA,CAAAC,cAAA,aAAgC;UAExBD,EAAA,CAAAG,MAAA,eAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjBJ,EAAA,CAAAC,cAAA,aAA4B;UAGxBD,EAAA,CAAAK,UAAA,2BAAAkE,6DAAArD,MAAA;YAAA,OAAAoD,GAAA,CAAAjC,cAAA,GAAAnB,MAAA;UAAA,EAA4B,yBAAAsD,2DAAA;YAAA,OAGbF,GAAA,CAAAzB,aAAA,EAAe;UAAA,EAHF;UAF9B7C,EAAA,CAAAI,YAAA,EAME;UACFJ,EAAA,CAAAC,cAAA,gBAAiF;UAAzED,EAAA,CAAAK,UAAA,mBAAAoE,sDAAA;YAAA,OAASH,GAAA,CAAAzB,aAAA,EAAe;UAAA,EAAC;UAC/B7C,EAAA,CAAAG,MAAA,uBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,gBAAiJ;UAAzID,EAAA,CAAAK,UAAA,mBAAAqE,sDAAA;YAAA,OAASJ,GAAA,CAAAb,iBAAA,EAAmB;UAAA,EAAC;UACnCzD,EAAA,CAAAG,MAAA,mBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAIbJ,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAA2E,UAAA,KAAAC,oCAAA,iBAGM;UAEN5E,EAAA,CAAA2E,UAAA,KAAAE,oCAAA,iBAEM;UAEN7E,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAA2E,UAAA,KAAAG,oCAAA,mBAqBM;UACR9E,EAAA,CAAAI,YAAA,EAAM;;;UA/CFJ,EAAA,CAAAqB,SAAA,GAA4B;UAA5BrB,EAAA,CAAA+E,UAAA,YAAAT,GAAA,CAAAjC,cAAA,CAA4B,aAAAiC,GAAA,CAAAlC,OAAA;UAKIpC,EAAA,CAAAqB,SAAA,GAA8C;UAA9CrB,EAAA,CAAA+E,UAAA,cAAAT,GAAA,CAAAjC,cAAA,CAAAS,IAAA,MAAAwB,GAAA,CAAAlC,OAAA,CAA8C;UAG1CpC,EAAA,CAAAqB,SAAA,GAA6C;UAA7CrB,EAAA,CAAA+E,UAAA,aAAAT,GAAA,CAAAlC,OAAA,IAAAkC,GAAA,CAAAnC,QAAA,CAAAN,MAAA,OAA6C;UAO/E7B,EAAA,CAAAqB,SAAA,GAAa;UAAbrB,EAAA,CAAA+E,UAAA,SAAAT,GAAA,CAAAlC,OAAA,CAAa;UAKbpC,EAAA,CAAAqB,SAAA,GAAuC;UAAvCrB,EAAA,CAAA+E,UAAA,UAAAT,GAAA,CAAAlC,OAAA,IAAAkC,GAAA,CAAAnC,QAAA,CAAAN,MAAA,OAAuC;UAMrB7B,EAAA,CAAAqB,SAAA,GAAW;UAAXrB,EAAA,CAAA+E,UAAA,YAAAT,GAAA,CAAAnC,QAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}