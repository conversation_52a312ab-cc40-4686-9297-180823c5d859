{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/socket.service\";\nimport * as i3 from \"../../services/project.service\";\nimport * as i4 from \"../../services/agent.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"./reverse.pipe\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r13.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(model_r13.name);\n  }\n}\nfunction ChatComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r14.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(model_r14.name);\n  }\n}\nfunction ChatComponent_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(model_r15.name);\n  }\n}\nfunction ChatComponent_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const llm_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", llm_r16.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(llm_r16.name);\n  }\n}\nfunction ChatComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"p\");\n    i0.ɵɵtext(2, \"No messages yet. Start a conversation with the AI agent.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_div_46_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"U\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"AI\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"S\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83C\\uDF10\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCBB\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u26A0\\uFE0F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDC64 You\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDD16 AI Agent\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u2699\\uFE0F System\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDD0D Browser\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 OpenAI\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 LM Studio\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCBB Local LLM\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCBB Terminal\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 OpenAI\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83E\\uDDE0 LM Studio\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u26A0\\uFE0F Error\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCCB Plan\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDCE3 Notification\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(message_r17.metadata.modelId);\n  }\n}\nfunction ChatComponent_div_46_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵelement(1, \"div\", 65)(2, \"div\", 65)(3, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"span\", 67);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Execution time: \", message_r17.metadata.executionTime, \"ms\");\n  }\n}\nfunction ChatComponent_div_46_span_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_46_span_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1, a2, a3, a4, a5, a6, a7) {\n  return {\n    \"user-message\": a0,\n    \"agent-message\": a1,\n    \"system-message\": a2,\n    \"browser-message\": a3,\n    \"openai-message\": a4,\n    \"llm-message\": a5,\n    \"error-message\": a6,\n    \"streaming\": a7\n  };\n};\nconst _c2 = function (a0, a1, a2, a3, a4, a5, a6) {\n  return {\n    \"user-avatar\": a0,\n    \"ai-avatar\": a1,\n    \"system-avatar\": a2,\n    \"browser-avatar\": a3,\n    \"openai-avatar\": a4,\n    \"llm-avatar\": a5,\n    \"error-avatar\": a6\n  };\n};\nfunction ChatComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵtemplate(2, ChatComponent_div_46_span_2_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(3, ChatComponent_div_46_span_3_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(4, ChatComponent_div_46_span_4_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(5, ChatComponent_div_46_span_5_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(6, ChatComponent_div_46_span_6_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(7, ChatComponent_div_46_span_7_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(8, ChatComponent_div_46_span_8_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(9, ChatComponent_div_46_span_9_Template, 2, 0, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 52)(11, \"div\", 53)(12, \"span\", 54);\n    i0.ɵɵtemplate(13, ChatComponent_div_46_span_13_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(14, ChatComponent_div_46_span_14_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(15, ChatComponent_div_46_span_15_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(16, ChatComponent_div_46_span_16_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(17, ChatComponent_div_46_span_17_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(18, ChatComponent_div_46_span_18_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(19, ChatComponent_div_46_span_19_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(20, ChatComponent_div_46_span_20_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(21, ChatComponent_div_46_span_21_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(22, ChatComponent_div_46_span_22_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(23, ChatComponent_div_46_span_23_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(24, ChatComponent_div_46_span_24_Template, 2, 0, \"span\", 51);\n    i0.ɵɵtemplate(25, ChatComponent_div_46_span_25_Template, 2, 0, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 55);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, ChatComponent_div_46_span_29_Template, 2, 1, \"span\", 56);\n    i0.ɵɵtemplate(30, ChatComponent_div_46_span_30_Template, 4, 0, \"span\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"div\", 58);\n    i0.ɵɵtemplate(32, ChatComponent_div_46_div_32_Template, 3, 1, \"div\", 59);\n    i0.ɵɵelementStart(33, \"div\", 60)(34, \"div\", 61)(35, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_46_Template_button_click_35_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r48);\n      const message_r17 = restoredCtx.$implicit;\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.addReaction(message_r17.id, \"like\"));\n    });\n    i0.ɵɵtext(36, \" \\uD83D\\uDC4D \");\n    i0.ɵɵtemplate(37, ChatComponent_div_46_span_37_Template, 2, 0, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_46_Template_button_click_38_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r48);\n      const message_r17 = restoredCtx.$implicit;\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.addReaction(message_r17.id, \"dislike\"));\n    });\n    i0.ɵɵtext(39, \" \\uD83D\\uDC4E \");\n    i0.ɵɵtemplate(40, ChatComponent_div_46_span_40_Template, 2, 0, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_46_Template_button_click_41_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r48);\n      const message_r17 = restoredCtx.$implicit;\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.addReaction(message_r17.id, \"love\"));\n    });\n    i0.ɵɵtext(42, \" \\u2764\\uFE0F \");\n    i0.ɵɵtemplate(43, ChatComponent_div_46_span_43_Template, 2, 0, \"span\", 51);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const message_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction8(40, _c1, message_r17.sender === \"user\", message_r17.sender === \"agent\", message_r17.sender === \"system\", message_r17.messageType === \"browser\", message_r17.messageType === \"openai\", message_r17.messageType === \"local_llm\", message_r17.messageType === \"error\", message_r17.sender === \"agent\" && !message_r17.isComplete));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction7(49, _c2, message_r17.sender === \"user\", message_r17.sender === \"agent\", message_r17.sender === \"system\", message_r17.messageType === \"browser\", message_r17.messageType === \"openai\", message_r17.messageType === \"local_llm\", message_r17.messageType === \"error\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.sender === \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.sender === \"agent\" && !message_r17.messageType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.sender === \"system\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"browser\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"local_llm\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"error\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"system_notification\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", message_r17.sender === \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.sender === \"agent\" && !message_r17.messageType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.sender === \"system\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"browser\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"lm_studio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"local_llm\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"terminal\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"llm_openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"llm_lm_studio\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"error\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"plan\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.messageType === \"system_notification\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(28, 37, message_r17.timestamp, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r17.metadata == null ? null : message_r17.metadata.modelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.sender === \"agent\" && !message_r17.isComplete);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", message_r17.content, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r17.metadata && message_r17.metadata.executionTime);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", message_r17.reactions && message_r17.reactions.includes(\"like\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r17.reactions && message_r17.reactions.includes(\"like\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", message_r17.reactions && message_r17.reactions.includes(\"dislike\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r17.reactions && message_r17.reactions.includes(\"dislike\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", message_r17.reactions && message_r17.reactions.includes(\"love\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r17.reactions && message_r17.reactions.includes(\"love\"));\n  }\n}\nfunction ChatComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelement(1, \"div\", 65)(2, \"div\", 65)(3, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"div\", 70);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"AI Agent is thinking...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"h4\");\n    i0.ɵɵtext(3, \"Agent Thinking Process\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_50_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.toggleAgentThinking());\n    });\n    i0.ɵɵtext(5, \"Close\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\", 74);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r9.agentThinkingContent);\n  }\n}\nfunction ChatComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"span\", 70);\n    i0.ɵɵelementStart(2, \"span\", 76);\n    i0.ɵɵtext(3, \"The AI assistant is working on a complex, long-running task. This may take several minutes. Please do not close this window.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ChatComponent_div_52_div_31_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵtext(1, \"GPT\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_52_div_31_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1, \"Local LLM\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_52_div_31_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵtext(1, \"HOT\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_52_div_31_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵtext(1, \"Web Search Results\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_52_div_31_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵtext(1, \"File Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_52_div_31_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"span\");\n    i0.ɵɵtext(2, \"Was this helpful?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 110)(4, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_52_div_31_div_21_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const i_r55 = i0.ɵɵnextContext().index;\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r63.setSubtaskFeedback(i_r55, \"up\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_52_div_31_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const i_r55 = i0.ɵɵnextContext().index;\n      const ctx_r66 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r66.setSubtaskFeedback(i_r55, \"down\"));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const subtask_r54 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"selected\", subtask_r54.feedback === \"up\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"selected\", subtask_r54.feedback === \"down\");\n  }\n}\nfunction ChatComponent_div_52_div_31_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_52_div_31_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const i_r55 = i0.ɵɵnextContext().index;\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.retrySubtask(i_r55));\n    });\n    i0.ɵɵtext(1, \" Retry \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChatComponent_div_52_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89)(2, \"div\", 90);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 91);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ChatComponent_div_52_div_31_div_6_Template, 2, 0, \"div\", 92);\n    i0.ɵɵtemplate(7, ChatComponent_div_52_div_31_div_7_Template, 2, 0, \"div\", 93);\n    i0.ɵɵtemplate(8, ChatComponent_div_52_div_31_div_8_Template, 2, 0, \"div\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 95)(10, \"div\", 96);\n    i0.ɵɵtext(11, \"\\u25B6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 97)(15, \"div\", 98);\n    i0.ɵɵtext(16, \"Output:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 99);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ChatComponent_div_52_div_31_div_19_Template, 2, 0, \"div\", 100);\n    i0.ɵɵtemplate(20, ChatComponent_div_52_div_31_div_20_Template, 2, 0, \"div\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, ChatComponent_div_52_div_31_div_21_Template, 6, 4, \"div\", 102);\n    i0.ɵɵtemplate(22, ChatComponent_div_52_div_31_button_22_Template, 2, 0, \"button\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subtask_r54 = ctx.$implicit;\n    const i_r55 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r55 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(subtask_r54.subtask.type || \"COMMAND\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r54.model_type === \"openai\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r54.model_type === \"local\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r54.web_research_used);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(subtask_r54.subtask.description || \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"error\", subtask_r54.error);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", subtask_r54.result || \"Processing...\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r54.web_results);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r54.file_diff);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r54.completed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", subtask_r54.error);\n  }\n}\nfunction ChatComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78)(2, \"h4\");\n    i0.ɵɵtext(3, \"Autonomous Agent Workflow\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 79)(5, \"div\", 80)(6, \"span\");\n    i0.ɵɵtext(7, \"\\uD83D\\uDD0D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\");\n    i0.ɵɵtext(9, \"Planning\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"div\", 81);\n    i0.ɵɵelementStart(11, \"div\", 82)(12, \"span\");\n    i0.ɵɵtext(13, \"\\uD83C\\uDFA8\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\");\n    i0.ɵɵtext(15, \"Design\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(16, \"div\", 81);\n    i0.ɵɵelementStart(17, \"div\", 83)(18, \"span\");\n    i0.ɵɵtext(19, \"\\uD83D\\uDEE0\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\");\n    i0.ɵɵtext(21, \"Implementation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(22, \"div\", 81);\n    i0.ɵɵelementStart(23, \"div\", 84)(24, \"span\");\n    i0.ɵɵtext(25, \"\\uD83E\\uDDEA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\");\n    i0.ɵɵtext(27, \"Testing\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 85);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 86);\n    i0.ɵɵtemplate(31, ChatComponent_div_52_div_31_Template, 23, 13, \"div\", 87);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r11.currentStage >= 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r11.currentStage >= 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r11.currentStage >= 2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r11.currentStage >= 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r11.currentStage >= 3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r11.currentStage >= 3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r11.currentStage >= 4);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r11.completedSubtasks, \"/\", ctx_r11.subtasks.length, \" steps completed \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.subtasks);\n  }\n}\nfunction ChatComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112);\n    i0.ɵɵtext(1, \"Saving messages...\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    \"expanded\": a0\n  };\n};\nconst _c4 = function (a0) {\n  return {\n    \"active\": a0\n  };\n};\nconst _c5 = function (a0, a1) {\n  return {\n    \"fa-chevron-up\": a0,\n    \"fa-chevron-down\": a1\n  };\n};\nexport class ChatComponent {\n  constructor(fb, socketService, projectService, agentService) {\n    this.fb = fb;\n    this.socketService = socketService;\n    this.projectService = projectService;\n    this.agentService = agentService;\n    this.projectName = '';\n    this.messagesLoading = false;\n    this.messagesSaving = false;\n    this.messageEvent = new EventEmitter();\n    this.chatExpandChange = new EventEmitter();\n    this.messages = [];\n    this.loading = false;\n    this.models = [];\n    this.selectedModel = 'deepseek/deepseek-coder';\n    this.localLlmModels = [{\n      id: 'mistral-nemo-instruct-2407',\n      name: 'Mistral Nemo Instruct 2407'\n    }\n    // Add more local LLM models here if needed\n    ];\n\n    this.selectedLocalLlmModel = 'mistral-nemo-instruct-2407';\n    this.isChatExpanded = false;\n    this.subtasks = [];\n    this.autonomousMode = false;\n    this.agentTyping = false;\n    this.agentThinkingContent = '';\n    this.showAgentThinking = false;\n    this.streamingEnabled = true;\n    this.showApiPayloads = true;\n    this.apiRequests = [];\n    this.apiResponses = [];\n  }\n  ngOnInit() {\n    console.log('[ChatComponent] ngOnInit called');\n    this.initForm();\n    this.loadMessages();\n    this.loadModels();\n    this.setupSocketListeners();\n  }\n  ngAfterViewChecked() {\n    this.scrollToBottom();\n  }\n  scrollToBottom() {\n    try {\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\n        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\n      }\n    } catch (err) {}\n  }\n  initForm() {\n    console.log('[ChatComponent] Initializing form');\n    this.messageForm = this.fb.group({\n      message: ['', Validators.required]\n    });\n  }\n  loadMessages() {\n    console.log('[ChatComponent] loadMessages called for project:', this.projectName);\n    if (!this.projectName) {\n      console.warn('[ChatComponent] No projectName found. Skipping loadMessages.');\n      return;\n    }\n    if (!this.messagesLoading) {\n      this.loading = true;\n    }\n    this.projectService.getProjectMessages(this.projectName).subscribe(response => {\n      console.log('[ChatComponent] Project messages loaded:', response);\n      this.messages = response.messages || [];\n      this.loading = false;\n    }, error => {\n      console.error('[ChatComponent] ❌ Error loading messages:', error);\n      this.loading = false;\n    });\n  }\n  loadModels() {\n    console.log('[ChatComponent] loadModels called');\n    this.agentService.getModels().subscribe(response => {\n      console.log('[ChatComponent] Models loaded:', response);\n      this.models = response.models || [];\n    }, error => {\n      console.error('[ChatComponent] ❌ Error loading models:', error);\n    });\n  }\n  /**\n   * Gets models filtered by provider\n   */\n  getModelsByProvider(provider) {\n    return this.models.filter(model => model.id.startsWith(`${provider}/`));\n  }\n  /**\n   * Gets models that don't belong to specified providers\n   */\n  getOtherModels() {\n    const knownProviders = ['openai', 'deepseek'];\n    return this.models.filter(model => !knownProviders.some(provider => model.id.startsWith(`${provider}/`)));\n  }\n  setupSocketListeners() {\n    console.log('[ChatComponent] Setting up socket listeners');\n    this.socketService.on('agent_message').subscribe(data => {\n      console.log('[ChatComponent] 🔁 Received socket \"agent_message\":', data);\n      if (data.project_name === this.projectName) {\n        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\n        // Determine the message type from incoming data\n        const messageType = data.message_type || 'agent';\n        this.messages.push({\n          id: data.message_id || `msg-${Date.now()}`,\n          sender: 'agent',\n          content: data.message,\n          timestamp: new Date(),\n          isAgentWorkingPlaceholder: false,\n          messageType: messageType,\n          reactions: []\n        });\n        this.loading = false;\n        console.log('[ChatComponent] Message added to chat from agent');\n      }\n    });\n    this.socketService.on('agent_typing').subscribe(data => {\n      console.log('[ChatComponent] 🔁 Received socket \"agent_typing\":', data);\n      if (data.project_name === this.projectName) {\n        this.agentTyping = data.is_typing;\n        // If not typing anymore, remove any placeholder messages\n        if (!data.is_typing) {\n          this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\n        }\n      }\n    });\n    this.socketService.on('agent_stream_token').subscribe(data => {\n      console.log('[ChatComponent] 🔁 Received socket \"agent_stream_token\":', data);\n      if (data.project_name === this.projectName) {\n        // Find the last agent message or create a new one if none exists\n        let lastAgentMessage = this.messages.find(m => m.sender === 'agent' && !m.isComplete);\n        if (!lastAgentMessage) {\n          lastAgentMessage = {\n            id: `stream-${Date.now()}`,\n            sender: 'agent',\n            content: '',\n            timestamp: new Date(),\n            isComplete: false,\n            reactions: []\n          };\n          this.messages.push(lastAgentMessage);\n        }\n        // Append the token to the message content\n        lastAgentMessage.content += data.token;\n        this.scrollToBottom();\n      }\n    });\n    this.socketService.on('agent_complete').subscribe(data => {\n      console.log('[ChatComponent] ✅ Received socket \"agent_complete\":', data);\n      if (data.project_name === this.projectName) {\n        this.loading = false;\n        this.agentTyping = false;\n        // Mark all agent messages as complete\n        this.messages.forEach(message => {\n          if (message.sender === 'agent') {\n            message.isComplete = true;\n          }\n        });\n        // Remove any placeholder messages\n        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\n        console.log('[ChatComponent] Loading state cleared after agent_complete');\n      }\n    });\n    this.socketService.on('agent_thinking').subscribe(data => {\n      console.log('[ChatComponent] 🧠 Received socket \"agent_thinking\":', data);\n      if (data.project_name === this.projectName && this.showAgentThinking) {\n        // Display the agent's thought process in a special UI element if debugging is enabled\n        this.agentThinkingContent = data.thinking;\n      }\n    });\n    this.socketService.on('message_reaction').subscribe(data => {\n      console.log('[ChatComponent] 👍 Received socket \"message_reaction\":', data);\n      if (data.project_name === this.projectName) {\n        // Find the message and add the reaction\n        const messageToUpdate = this.messages.find(m => m.id === data.message_id);\n        if (messageToUpdate) {\n          if (!messageToUpdate.reactions) {\n            messageToUpdate.reactions = [];\n          }\n          if (!messageToUpdate.reactions.includes(data.reaction)) {\n            messageToUpdate.reactions.push(data.reaction);\n          }\n        }\n      }\n    });\n  }\n  sendMessage() {\n    if (this.messageForm.invalid) {\n      return;\n    }\n    const messageContent = this.messageForm.get('message')?.value;\n    if (!messageContent || !this.projectName) {\n      return;\n    }\n    // Add user message to the chat\n    const userMessageId = `msg-${Date.now()}`;\n    this.messages.push({\n      id: userMessageId,\n      sender: 'user',\n      content: messageContent,\n      timestamp: new Date(),\n      reactions: []\n    });\n    // Create request payload\n    const requestPayload = {\n      project_name: this.projectName,\n      message: messageContent,\n      model_id: this.selectedModel,\n      local_llm_model_id: this.selectedLocalLlmModel,\n      streaming_enabled: this.streamingEnabled\n    };\n    // Store request\n    const requestEntry = {\n      timestamp: new Date(),\n      type: 'request',\n      endpoint: `/projects/${this.projectName}/messages`,\n      payload: requestPayload\n    };\n    this.apiRequests.push(requestEntry);\n    // If API payloads are visible, add to messages\n    if (this.showApiPayloads) {\n      this.messages.push({\n        id: `api-req-${Date.now()}`,\n        sender: 'system',\n        messageType: 'api_request',\n        content: `<strong>API Request:</strong><br><pre>${JSON.stringify(requestPayload, null, 2)}</pre>`,\n        timestamp: new Date(),\n        reactions: []\n      });\n    }\n    // Reset the form\n    this.messageForm.reset();\n    // Show loading indicator\n    this.loading = true;\n    // Send to API\n    if (this.streamingEnabled) {\n      // For streaming, we handle via sockets\n      this.socketService.sendMessage(this.projectName, messageContent, this.selectedModel);\n    } else {\n      // For non-streaming, we make a direct API call\n      this.agentService.sendMessage(this.projectName, messageContent, this.selectedModel, this.selectedLocalLlmModel, false).subscribe(response => {\n        // Store response\n        const responseEntry = {\n          timestamp: new Date(),\n          type: 'response',\n          endpoint: `/projects/${this.projectName}/messages`,\n          payload: response\n        };\n        this.apiResponses.push(responseEntry);\n        // If API payloads are visible, add to messages\n        if (this.showApiPayloads) {\n          this.messages.push({\n            id: `api-res-${Date.now()}`,\n            sender: 'system',\n            messageType: 'api_response',\n            content: `<strong>API Response:</strong><br><pre>${JSON.stringify(response, null, 2)}</pre>`,\n            timestamp: new Date(),\n            reactions: []\n          });\n        }\n        this.loading = false;\n      }, error => {\n        console.error('[ChatComponent] ❌ Error sending message:', error);\n        // Store error response\n        const errorEntry = {\n          timestamp: new Date(),\n          type: 'error',\n          endpoint: `/projects/${this.projectName}/messages`,\n          payload: error\n        };\n        this.apiResponses.push(errorEntry);\n        // Add error message\n        this.messages.push({\n          id: `error-${Date.now()}`,\n          sender: 'system',\n          messageType: 'error',\n          content: `<strong>API Error:</strong><br><pre>${JSON.stringify(error, null, 2)}</pre>`,\n          timestamp: new Date(),\n          reactions: []\n        });\n        this.loading = false;\n      });\n    }\n  }\n  onModelChange(modelId) {\n    console.log('[ChatComponent] Model changed to:', modelId);\n    this.selectedModel = modelId;\n  }\n  deleteMessage(index) {\n    this.messages.splice(index, 1);\n  }\n  clearChat() {\n    if (!this.projectName) return;\n    // Clear messages in UI\n    this.messages = [];\n    // Call backend API to delete chat history\n    this.projectService.deleteProjectMessages(this.projectName).subscribe(() => {\n      console.log('[ChatComponent] Chat history deleted on backend');\n    }, error => {\n      console.error('[ChatComponent] Error deleting chat history:', error);\n    });\n  }\n  toggleChatExpand() {\n    this.isChatExpanded = !this.isChatExpanded;\n    this.chatExpandChange.emit(this.isChatExpanded);\n  }\n  onEnter(event) {\n    if (event.shiftKey) {\n      return; // allow newline\n    }\n\n    event.preventDefault();\n    this.sendMessage();\n  }\n  exportChat() {\n    if (!this.projectName) return;\n    this.projectService.exportProjectChat(this.projectName).subscribe(response => {\n      alert('Chat exported: ' + (response?.file_path || 'Success'));\n    }, error => {\n      alert('Failed to export chat: ' + (error?.error?.detail || error));\n    });\n  }\n  exportDynamicChat() {\n    if (!this.messages.length) return;\n    // Format chat as text\n    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\n    const blob = new Blob([chatText], {\n      type: 'text/plain'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n    const filename = `${this.projectName || 'chat'}-dynamic-${timestamp}.txt`;\n    a.href = url;\n    a.download = filename;\n    document.body.appendChild(a);\n    a.click();\n    setTimeout(() => {\n      document.body.removeChild(a);\n      window.URL.revokeObjectURL(url);\n    }, 0);\n  }\n  copyChat() {\n    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\n    navigator.clipboard.writeText(chatText).then(() => alert('Chat copied to clipboard!'), () => alert('Failed to copy chat to clipboard.'));\n  }\n  onLocalLlmModelChange(modelId) {\n    this.selectedLocalLlmModel = modelId;\n    // You can add logic here to notify the backend or update the session if needed\n    console.log('[ChatComponent] Local LLM model changed to:', modelId);\n  }\n  get completedSubtasks() {\n    if (!this.subtasks) return 0;\n    return this.subtasks.filter(subtask => !subtask.error).length;\n  }\n  retrySubtask(index) {\n    // Simulate retry: clear error and result, set to loading, then re-run (in real app, call backend)\n    const subtask = this.subtasks[index];\n    subtask.error = null;\n    subtask.result = 'Retrying...';\n    // Simulate async retry (replace with real backend call)\n    setTimeout(() => {\n      subtask.result = 'Retried result (simulated)';\n      subtask.error = null;\n    }, 1500);\n  }\n  setSubtaskFeedback(index, feedback) {\n    this.subtasks[index].feedback = feedback;\n  }\n  get longTaskInProgress() {\n    if (this.loading) return true;\n    if (this.subtasks && this.subtasks.length > 0) {\n      return this.subtasks.some(s => !s.result && !s.error);\n    }\n    return false;\n  }\n  toggleAutonomousMode() {\n    this.autonomousMode = !this.autonomousMode;\n  }\n  /**\n   * Formats a unified diff string as HTML with basic syntax highlighting for added, removed, and context lines.\n   * @param diff The unified diff string\n   * @returns HTML string\n   */\n  formatFileDiff(diff) {\n    if (!diff) return '';\n    // Escape HTML\n    const escape = s => s.replace(/[&<>]/g, c => ({\n      '&': '&amp;',\n      '<': '&lt;',\n      '>': '&gt;'\n    })[c] || c);\n    return '<pre>' + diff.split('\\n').map(line => {\n      if (line.startsWith('+') && !line.startsWith('+++')) {\n        return `<span class='diff-added'>${escape(line)}</span>`;\n      } else if (line.startsWith('-') && !line.startsWith('---')) {\n        return `<span class='diff-removed'>${escape(line)}</span>`;\n      } else if (line.startsWith('@@')) {\n        return `<span class='diff-hunk'>${escape(line)}</span>`;\n      } else {\n        return escape(line);\n      }\n    }).join('\\n') + '</pre>';\n  }\n  /**\n   * Returns the current workflow stage for the progress indicator in the chat UI.\n   * 1 = Planning, 2 = Design, 3 = Implementation, 4 = Testing\n   */\n  get currentStage() {\n    if (!this.subtasks || this.subtasks.length === 0) return 0;\n    // If all subtasks are completed, return 4 (Testing)\n    if (this.subtasks.every(s => s.completed || s.result || s.error)) return 4;\n    // Otherwise, estimate stage based on subtask type or index\n    // (You can refine this logic as needed)\n    let stage = 1;\n    for (const subtask of this.subtasks) {\n      if (subtask.subtask?.type === 'design') stage = Math.max(stage, 2);else if (subtask.subtask?.type === 'implementation') stage = Math.max(stage, 3);else if (subtask.subtask?.type === 'testing') stage = Math.max(stage, 4);\n    }\n    return stage;\n  }\n  addReaction(messageId, reaction) {\n    console.log(`[ChatComponent] Adding reaction ${reaction} to message ${messageId}`);\n    // Optimistically update UI\n    const messageToUpdate = this.messages.find(m => m.id === messageId);\n    if (messageToUpdate) {\n      if (!messageToUpdate.reactions) {\n        messageToUpdate.reactions = [];\n      }\n      if (!messageToUpdate.reactions.includes(reaction)) {\n        messageToUpdate.reactions.push(reaction);\n      }\n    }\n    // Send to backend\n    this.agentService.addMessageReaction(this.projectName, messageId, reaction).subscribe(response => {\n      console.log('[ChatComponent] ✅ Reaction added successfully:', response);\n    }, error => {\n      console.error('[ChatComponent] ❌ Error adding reaction:', error);\n      // Remove the reaction if it failed\n      if (messageToUpdate && messageToUpdate.reactions) {\n        messageToUpdate.reactions = messageToUpdate.reactions.filter(r => r !== reaction);\n      }\n    });\n  }\n  resetContextMemory() {\n    if (!this.projectName) return;\n    this.agentService.resetContextMemory(this.projectName).subscribe(response => {\n      console.log('[ChatComponent] ✅ Context memory reset successfully:', response);\n      alert('Conversation memory has been reset.');\n    }, error => {\n      console.error('[ChatComponent] ❌ Error resetting context memory:', error);\n      alert('Failed to reset conversation memory.');\n    });\n  }\n  toggleAgentThinking() {\n    this.showAgentThinking = !this.showAgentThinking;\n  }\n  toggleApiPayloads() {\n    this.showApiPayloads = !this.showApiPayloads;\n    console.log('[ChatComponent] API payload visibility toggled:', this.showApiPayloads);\n  }\n  static {\n    this.ɵfac = function ChatComponent_Factory(t) {\n      return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SocketService), i0.ɵɵdirectiveInject(i3.ProjectService), i0.ɵɵdirectiveInject(i4.AgentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatComponent,\n      selectors: [[\"app-chat\"]],\n      viewQuery: function ChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n        }\n      },\n      inputs: {\n        projectName: \"projectName\",\n        messagesLoading: \"messagesLoading\",\n        messagesSaving: \"messagesSaving\"\n      },\n      outputs: {\n        messageEvent: \"messageEvent\",\n        chatExpandChange: \"chatExpandChange\"\n      },\n      decls: 65,\n      vars: 42,\n      consts: [[1, \"chat-container\", 3, \"ngClass\"], [1, \"chat-header\"], [1, \"agent-title\"], [1, \"agent-icon\"], [1, \"agent-status\", 3, \"ngClass\"], [1, \"model-controls\"], [1, \"model-selector\", \"cloud-model\"], [\"for\", \"modelSelect\"], [1, \"fa\", \"fa-cloud\"], [\"id\", \"modelSelect\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"label\", \"OpenAI\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"DeepSeek\"], [1, \"model-selector\", \"local-model\"], [\"for\", \"localLlmSelect\"], [1, \"fa\", \"fa-desktop\"], [\"id\", \"localLlmSelect\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"streaming-toggle\"], [\"for\", \"streamingToggle\"], [\"type\", \"checkbox\", \"id\", \"streamingToggle\", 3, \"ngModel\", \"ngModelChange\"], [1, \"chat-actions\"], [\"title\", \"Toggle between autonomous and assisted mode\", 1, \"mode-toggle-btn\", 3, \"ngClass\", \"click\"], [1, \"fa\", \"fa-robot\"], [\"title\", \"Reset conversation memory\", 1, \"memory-btn\", 3, \"click\"], [1, \"fa\", \"fa-brain\"], [\"title\", \"Toggle API payloads visibility\", 1, \"api-toggle-btn\", 3, \"ngClass\", \"click\"], [1, \"fa\", \"fa-exchange-alt\"], [\"title\", \"Clear chat history\", 1, \"clear-chat-btn\", 3, \"click\"], [1, \"fa\", \"fa-trash\"], [1, \"expand-chat-btn\", 3, \"click\"], [1, \"fa\", 3, \"ngClass\"], [1, \"messages-container\"], [\"messagesContainer\", \"\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"agent-thinking-panel\", 4, \"ngIf\"], [\"class\", \"long-task-banner\", 4, \"ngIf\"], [\"class\", \"autonomous-workflow\", 4, \"ngIf\"], [1, \"message-form\", 3, \"formGroup\", \"ngSubmit\"], [\"formControlName\", \"message\", \"placeholder\", \"Type your message here...\", \"rows\", \"3\", 3, \"disabled\", \"keydown.enter\"], [\"type\", \"submit\", 3, \"disabled\"], [\"type\", \"button\", \"title\", \"Export chat history\", 1, \"export-chat-btn\", 3, \"disabled\", \"click\"], [\"type\", \"button\", \"title\", \"Export chat as file (dynamic, no storage)\", 1, \"export-chat-btn\", 3, \"disabled\", \"click\"], [\"type\", \"button\", \"title\", \"Copy entire chat to clipboard\", 1, \"copy-chat-btn\", 3, \"disabled\", \"click\"], [\"class\", \"saving-indicator\", 4, \"ngIf\"], [3, \"value\"], [1, \"empty-state\"], [1, \"message\", 3, \"ngClass\"], [1, \"avatar\", 3, \"ngClass\"], [4, \"ngIf\"], [1, \"bubble\"], [1, \"message-header\"], [1, \"sender\"], [1, \"timestamp\"], [\"class\", \"message-type\", 4, \"ngIf\"], [\"class\", \"streaming-indicator\", 4, \"ngIf\"], [1, \"message-content\", 3, \"innerHTML\"], [\"class\", \"message-metadata\", 4, \"ngIf\"], [1, \"message-reactions\"], [1, \"reaction-buttons\"], [1, \"reaction-button\", 3, \"click\"], [1, \"message-type\"], [1, \"streaming-indicator\"], [1, \"typing-dot\"], [1, \"message-metadata\"], [1, \"execution-time\"], [1, \"typing-indicator\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"agent-thinking-panel\"], [1, \"thinking-header\"], [3, \"click\"], [1, \"thinking-content\"], [1, \"long-task-banner\"], [1, \"banner-text\"], [1, \"autonomous-workflow\"], [1, \"workflow-header\"], [1, \"progress-stages\"], [1, \"stage-icon\", \"planning\"], [1, \"stage-connector\"], [1, \"stage-icon\", \"design\"], [1, \"stage-icon\", \"implementation\"], [1, \"stage-icon\", \"testing\"], [1, \"progress-counter\"], [1, \"subtasks-grid\"], [\"class\", \"subtask-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"subtask-card\"], [1, \"subtask-header\"], [1, \"subtask-number\"], [1, \"subtask-type\"], [\"class\", \"model-label\", 4, \"ngIf\"], [\"class\", \"model-label local\", 4, \"ngIf\"], [\"class\", \"hot-label\", 4, \"ngIf\"], [1, \"task-description\"], [1, \"bullet\"], [1, \"output-area\"], [1, \"output-label\"], [1, \"output-content\"], [\"class\", \"web-results\", 4, \"ngIf\"], [\"class\", \"file-changes\", 4, \"ngIf\"], [\"class\", \"feedback-row\", 4, \"ngIf\"], [\"class\", \"retry-button\", 3, \"click\", 4, \"ngIf\"], [1, \"model-label\"], [1, \"model-label\", \"local\"], [1, \"hot-label\"], [1, \"web-results\"], [1, \"file-changes\"], [1, \"feedback-row\"], [1, \"feedback-options\"], [1, \"retry-button\", 3, \"click\"], [1, \"saving-indicator\"]],\n      template: function ChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n          i0.ɵɵtext(4, \"\\uD83E\\uDD16\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"h2\");\n          i0.ɵɵtext(6, \"Autonomous AI Agent\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 4);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 5)(10, \"div\", 6)(11, \"label\", 7);\n          i0.ɵɵelement(12, \"i\", 8);\n          i0.ɵɵtext(13, \" Cloud LLM:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"select\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_Template_select_ngModelChange_14_listener($event) {\n            return ctx.selectedModel = $event;\n          })(\"change\", function ChatComponent_Template_select_change_14_listener() {\n            return ctx.onModelChange(ctx.selectedModel);\n          });\n          i0.ɵɵelementStart(15, \"optgroup\", 10);\n          i0.ɵɵtemplate(16, ChatComponent_option_16_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"optgroup\", 12);\n          i0.ɵɵtemplate(18, ChatComponent_option_18_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, ChatComponent_option_19_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 13)(21, \"label\", 14);\n          i0.ɵɵelement(22, \"i\", 15);\n          i0.ɵɵtext(23, \" Local LLM:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"select\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_Template_select_ngModelChange_24_listener($event) {\n            return ctx.selectedLocalLlmModel = $event;\n          })(\"change\", function ChatComponent_Template_select_change_24_listener() {\n            return ctx.onLocalLlmModelChange(ctx.selectedLocalLlmModel);\n          });\n          i0.ɵɵtemplate(25, ChatComponent_option_25_Template, 2, 2, \"option\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 17)(27, \"label\", 18)(28, \"input\", 19);\n          i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_Template_input_ngModelChange_28_listener($event) {\n            return ctx.streamingEnabled = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(29, \" Streaming \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 20)(31, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_31_listener() {\n            return ctx.toggleAutonomousMode();\n          });\n          i0.ɵɵelement(32, \"i\", 22);\n          i0.ɵɵelementStart(33, \"span\");\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_35_listener() {\n            return ctx.resetContextMemory();\n          });\n          i0.ɵɵelement(36, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_37_listener() {\n            return ctx.toggleApiPayloads();\n          });\n          i0.ɵɵelement(38, \"i\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_39_listener() {\n            return ctx.clearChat();\n          });\n          i0.ɵɵelement(40, \"i\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_41_listener() {\n            return ctx.toggleChatExpand();\n          });\n          i0.ɵɵelement(42, \"i\", 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"div\", 31, 32);\n          i0.ɵɵtemplate(45, ChatComponent_div_45_Template, 3, 0, \"div\", 33);\n          i0.ɵɵtemplate(46, ChatComponent_div_46_Template, 44, 57, \"div\", 34);\n          i0.ɵɵpipe(47, \"reverse\");\n          i0.ɵɵtemplate(48, ChatComponent_div_48_Template, 4, 0, \"div\", 35);\n          i0.ɵɵtemplate(49, ChatComponent_div_49_Template, 4, 0, \"div\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(50, ChatComponent_div_50_Template, 8, 1, \"div\", 37);\n          i0.ɵɵtemplate(51, ChatComponent_div_51_Template, 4, 0, \"div\", 38);\n          i0.ɵɵtemplate(52, ChatComponent_div_52_Template, 32, 17, \"div\", 39);\n          i0.ɵɵelementStart(53, \"form\", 40);\n          i0.ɵɵlistener(\"ngSubmit\", function ChatComponent_Template_form_ngSubmit_53_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(54, \"textarea\", 41);\n          i0.ɵɵlistener(\"keydown.enter\", function ChatComponent_Template_textarea_keydown_enter_54_listener($event) {\n            return ctx.onEnter($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"button\", 42)(56, \"span\");\n          i0.ɵɵtext(57, \"Send\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_58_listener() {\n            return ctx.exportChat();\n          });\n          i0.ɵɵtext(59, \"Export Chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_60_listener() {\n            return ctx.exportDynamicChat();\n          });\n          i0.ɵɵtext(61, \"Export Dynamic Chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"button\", 45);\n          i0.ɵɵlistener(\"click\", function ChatComponent_Template_button_click_62_listener() {\n            return ctx.copyChat();\n          });\n          i0.ɵɵtext(63, \"Copy Chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(64, ChatComponent_div_64_Template, 2, 0, \"div\", 46);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(31, _c3, ctx.isChatExpanded));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c4, ctx.loading || ctx.longTaskInProgress || ctx.agentTyping));\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading || ctx.longTaskInProgress ? \"Working\" : ctx.agentTyping ? \"Typing...\" : \"Ready\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedModel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getModelsByProvider(\"openai\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getModelsByProvider(\"deepseek\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getOtherModels());\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedLocalLlmModel);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.localLlmModels);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.streamingEnabled);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(35, _c4, ctx.autonomousMode));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.autonomousMode ? \"Autonomous\" : \"Assisted\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(37, _c4, ctx.showApiPayloads));\n          i0.ɵɵadvance(4);\n          i0.ɵɵattribute(\"aria-label\", ctx.isChatExpanded ? \"Collapse Chat\" : \"Expand Chat\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(39, _c5, ctx.isChatExpanded, !ctx.isChatExpanded));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.messages.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(47, 29, ctx.messages));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.agentTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.loading || ctx.messagesLoading) && !ctx.agentTyping);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAgentThinking && ctx.agentThinkingContent);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.longTaskInProgress);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.subtasks && ctx.subtasks.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.messagesLoading || ctx.messagesSaving);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.messageForm.invalid || ctx.loading || ctx.messagesLoading || ctx.messagesSaving);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.messages.length === 0 || ctx.loading || ctx.messagesSaving);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.messages.length === 0 || ctx.loading || ctx.messagesSaving);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.messages.length === 0 || ctx.loading || ctx.messagesSaving);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.messagesSaving);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i5.DatePipe, i6.ReversePipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.progress-stages[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin: 20px 0;\\n  padding: 10px 20px;\\n  background: #f0f5ff;\\n  border-radius: 16px;\\n  position: relative;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  position: relative;\\n  z-index: 2;\\n  padding: 12px;\\n  border-radius: 50%;\\n  background: white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\\n  opacity: 0.7;\\n  transition: all 0.3s ease;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 12px rgba(79, 140, 255, 0.25);\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.active[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%] {\\n  background: #4f8cff;\\n  color: white;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.active[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2a5298;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.completed[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.completed[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%] {\\n  background: #22c55e;\\n  color: white;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage.completed[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%] {\\n  color: #22c55e;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n  transition: all 0.3s ease;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 500;\\n  color: #666;\\n  white-space: nowrap;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-connector[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  height: 4px;\\n  background: #e0e0e0;\\n  margin: 0 -10px;\\n  position: relative;\\n  z-index: 1;\\n  transition: background 0.3s ease;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-connector.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #4f8cff, #22c55e);\\n}\\n\\n.progress-bar-container[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 12px;\\n  background: #f0f0f0;\\n  border-radius: 6px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]   .progress-bar-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(to right, #4f8cff, #22c55e);\\n  border-radius: 6px;\\n  transition: width 0.5s ease;\\n  position: relative;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]   .progress-bar-fill.animated[_ngcontent-%COMP%] {\\n  background-size: 30px 30px;\\n  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);\\n  animation: _ngcontent-%COMP%_animate-stripes 1s linear infinite;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-bg[_ngcontent-%COMP%]   .progress-bar-fill[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 8px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar-label[_ngcontent-%COMP%] {\\n  display: block;\\n  text-align: center;\\n  margin-top: 6px;\\n  font-size: 14px;\\n  color: #555;\\n}\\n\\n@keyframes _ngcontent-%COMP%_animate-stripes {\\n  0% {\\n    background-position: 0 0;\\n  }\\n  100% {\\n    background-position: 30px 0;\\n  }\\n}\\n.subtasks-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n  gap: 20px;\\n  margin-top: 20px;\\n  overflow-y: auto;\\n  padding: 0 20px 20px;\\n  max-height: 600px;\\n}\\n\\n.subtask-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 10px;\\n  border: 1px solid #eaeaea;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n}\\n.subtask-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-2px);\\n}\\n.subtask-card.subtask-error[_ngcontent-%COMP%] {\\n  border-color: #ffbaba;\\n  background-color: #fff8f8;\\n}\\n.subtask-card.subtask-error[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%] {\\n  background: #fff0f0;\\n  border-bottom-color: #ffbaba;\\n}\\n.subtask-card.subtask-error[_ngcontent-%COMP%]   .subtask-index[_ngcontent-%COMP%] {\\n  background: #ff5252;\\n}\\n.subtask-card.subtask-error[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ff5252;\\n}\\n.subtask-card.subtask-completed[_ngcontent-%COMP%] {\\n  border-color: #d0ead0;\\n}\\n.subtask-card.subtask-completed[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%] {\\n  background: #f0fff0;\\n  border-bottom-color: #d0ead0;\\n}\\n.subtask-card.subtask-completed[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #22c55e;\\n}\\n.subtask-card.subtask-active[_ngcontent-%COMP%] {\\n  border-color: #b3d7ff;\\n  box-shadow: 0 0 0 3px rgba(79, 140, 255, 0.2);\\n}\\n.subtask-card.subtask-active[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%] {\\n  background: #f0f7ff;\\n  border-bottom-color: #b3d7ff;\\n}\\n.subtask-card.subtask-active[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #4f8cff;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 16px 20px;\\n  background: #fafbff;\\n  border-bottom: 1px solid #eaeaea;\\n  position: relative;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-top-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  width: 100%;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-index[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 28px;\\n  height: 28px;\\n  background: #f0f5ff;\\n  border-radius: 50%;\\n  font-size: 13px;\\n  font-weight: bold;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  padding: 3px 8px;\\n  border-radius: 6px;\\n  background: #e0e0e0;\\n  color: #333;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  font-weight: 600;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.file[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  color: #1565c0;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.command[_ngcontent-%COMP%] {\\n  background: #e8f5e9;\\n  color: #2e7d32;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.browser_test[_ngcontent-%COMP%] {\\n  background: #fffde7;\\n  color: #f57f17;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-type.folder[_ngcontent-%COMP%] {\\n  background: #ede7f6;\\n  color: #4527a0;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-right-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-check-circle[_ngcontent-%COMP%] {\\n  color: #22c55e;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-exclamation-circle[_ngcontent-%COMP%] {\\n  color: #e53935;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-sync[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-metadata[_ngcontent-%COMP%]   .subtask-status-icon[_ngcontent-%COMP%]   .fa-circle-notch[_ngcontent-%COMP%] {\\n  color: #4f8cff;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-description[_ngcontent-%COMP%] {\\n  margin-bottom: 14px;\\n  font-size: 14px;\\n  color: #333;\\n  line-height: 1.4;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-description[_ngcontent-%COMP%]   .fa-arrow-right[_ngcontent-%COMP%] {\\n  color: #4f8cff;\\n  margin-top: 2px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .subtask-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  justify-content: space-between;\\n  flex-wrap: wrap;\\n  border-top: 1px solid #f0f0f0;\\n  padding-top: 10px;\\n  margin-top: 6px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .model-badge[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .web-research-badge[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .model-badge.openai[_ngcontent-%COMP%] {\\n  background: #dcfce7;\\n  color: #166534;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .model-badge.local[_ngcontent-%COMP%] {\\n  background: #e0f2fe;\\n  color: #075985;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .web-research-badge[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .web-research-badge[_ngcontent-%COMP%]:before {\\n  content: \\\"\\uD83C\\uDF10\\\";\\n  font-size: 12px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  padding: 4px 10px;\\n  background: #f44336;\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  transition: all 0.2s;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #d32f2f;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-left: auto;\\n  align-items: center;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .feedback-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin-right: 4px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e0e0e0;\\n  cursor: pointer;\\n  color: #888;\\n  padding: 6px 10px;\\n  border-radius: 6px;\\n  transition: all 0.2s;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn[_ngcontent-%COMP%]:hover {\\n  background: #f5f5f5;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn.selected[_ngcontent-%COMP%]:first-child {\\n  color: white;\\n  background-color: #22c55e;\\n  border-color: #22c55e;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-header[_ngcontent-%COMP%]   .feedback-btns[_ngcontent-%COMP%]   .thumb-btn.selected[_ngcontent-%COMP%]:last-child {\\n  color: white;\\n  background-color: #e53935;\\n  border-color: #e53935;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  font-weight: 500;\\n  color: #333;\\n  display: block;\\n  transition: background 0.2s ease;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]:hover {\\n  background: #f9f9f9;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]::-webkit-details-marker {\\n  display: none;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u25B6\\\";\\n  font-size: 10px;\\n  margin-right: 8px;\\n  display: inline-block;\\n  transition: transform 0.2s ease;\\n}\\ndetails[open][_ngcontent-%COMP%]   .subtask-card[_ngcontent-%COMP%]   .subtask-summary[_ngcontent-%COMP%]::before {\\n  transform: rotate(90deg);\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-top: 1px solid #f0f0f0;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #444;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   .copy-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #4f8cff;\\n  cursor: pointer;\\n  font-size: 14px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   .copy-btn[_ngcontent-%COMP%]:hover {\\n  color: #2a5298;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-output[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  font-family: monospace;\\n  font-size: 13px;\\n  line-height: 1.4;\\n  border-radius: 6px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n  margin: 0;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .result-output[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n  color: #333;\\n  border: 1px solid #eaeaea;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  background: #fff5f5;\\n  color: #c53030;\\n  border: 1px solid #feb2b2;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .subtask-result[_ngcontent-%COMP%]   .no-output[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  color: #666;\\n  font-style: italic;\\n  background: #f9f9f9;\\n  border-radius: 6px;\\n  text-align: center;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  background: #f9f9f9;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n  font-size: 13px;\\n  font-weight: 500;\\n  color: #444;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%]::-webkit-details-marker, .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   summary[_ngcontent-%COMP%]::-webkit-details-marker {\\n  display: none;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   .web-results-content[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   .web-results-content[_ngcontent-%COMP%], .subtask-card[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 12px;\\n  border-radius: 6px;\\n  font-family: monospace;\\n  font-size: 12px;\\n  line-height: 1.4;\\n  max-height: 150px;\\n  overflow-y: auto;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]   .diff-added[_ngcontent-%COMP%] {\\n  color: #22c55e;\\n  background-color: #f0fff4;\\n  display: block;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]   .diff-removed[_ngcontent-%COMP%] {\\n  color: #e53e3e;\\n  background-color: #fff5f5;\\n  display: block;\\n}\\n.subtask-card[_ngcontent-%COMP%]   .file-diff[_ngcontent-%COMP%]   .diff-hunk[_ngcontent-%COMP%] {\\n  color: #805ad5;\\n  background-color: #f8f0fc;\\n  display: block;\\n  padding: 2px 0;\\n  margin: 8px 0 4px 0;\\n}\\n\\n\\n\\n.feedback-thanks[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  font-size: 12px;\\n  color: #22c55e;\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n  padding: 4px 10px;\\n  border-radius: 4px;\\n  background-color: rgba(34, 197, 94, 0.1);\\n}\\n\\n\\n\\n.feedback-btns[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-left: auto;\\n}\\n\\n.feedback-label[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  color: #555;\\n  margin-right: 5px;\\n}\\n\\n\\n\\n.thumb-btn[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n  border: 1px solid #e0e0e0;\\n  padding: 6px 10px;\\n  border-radius: 6px;\\n  transition: all 0.2s;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n.thumb-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.thumb-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n}\\n.thumb-btn.selected[_ngcontent-%COMP%]:first-child {\\n  color: white;\\n  background-color: #22c55e;\\n  border-color: #22c55e;\\n}\\n.thumb-btn.selected[_ngcontent-%COMP%]:last-child {\\n  color: white;\\n  background-color: #e53935;\\n  border-color: #e53935;\\n}\\n\\n\\n\\n\\n\\n.autonomous-workflow[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding: 15px;\\n  background: #f9fafc;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n\\n.workflow-header[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n.workflow-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n\\n\\n.progress-stages[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 15px;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  opacity: 0.5;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #e8f0fe;\\n  border-radius: 50%;\\n  margin-bottom: 5px;\\n  font-size: 16px;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: #4285f4;\\n  color: white;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-icon.active[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-connector[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  height: 2px;\\n  background: #e0e0e0;\\n  margin: 0 5px;\\n}\\n.progress-stages[_ngcontent-%COMP%]   .stage-connector.active[_ngcontent-%COMP%] {\\n  background: #4285f4;\\n}\\n\\n.progress-counter[_ngcontent-%COMP%] {\\n  text-align: right;\\n  font-size: 13px;\\n  color: #666;\\n  margin-bottom: 15px;\\n}\\n\\n\\n\\n.subtasks-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));\\n  gap: 15px;\\n  padding-bottom: 10px;\\n}\\n\\n.subtask-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n  border: 1px solid #eaeaea;\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.subtask-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\\n}\\n.subtask-card.running[_ngcontent-%COMP%] {\\n  border-color: #4285f4;\\n  box-shadow: 0 0 0 1px rgba(66, 133, 244, 0.2);\\n}\\n.subtask-card.completed[_ngcontent-%COMP%] {\\n  border-color: #34a853;\\n  box-shadow: 0 0 0 1px rgba(52, 168, 83, 0.2);\\n}\\n.subtask-card.error[_ngcontent-%COMP%] {\\n  border-color: #ea4335;\\n  box-shadow: 0 0 0 1px rgba(234, 67, 53, 0.2);\\n}\\n\\n\\n\\n.subtask-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 10px;\\n  background: #f4f8fc;\\n  border-bottom: 1px solid #e1e7ed;\\n  height: 40px;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .subtask-number[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  background: #4285f4;\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 500;\\n  margin-right: 10px;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .subtask-type[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n  font-size: 11px;\\n  background: #e8f0fe;\\n  color: #1967d2;\\n  padding: 3px 8px;\\n  border-radius: 3px;\\n  letter-spacing: 0.5px;\\n  font-weight: 500;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .model-label[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  font-size: 11px;\\n  padding: 2px 6px;\\n  border-radius: 3px;\\n  background: #1a73e8;\\n  color: white;\\n  font-weight: 500;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .model-label.local[_ngcontent-%COMP%] {\\n  background: #9334e6;\\n}\\n.subtask-header[_ngcontent-%COMP%]   .hot-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  padding: 2px 6px;\\n  border-radius: 3px;\\n  background: #ea4335;\\n  color: white;\\n  font-weight: 500;\\n  margin-left: 5px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n\\n\\n.task-description[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 10px;\\n  border-bottom: 1px solid #e8f0fe;\\n  min-height: 40px;\\n}\\n.task-description[_ngcontent-%COMP%]   .bullet[_ngcontent-%COMP%] {\\n  color: #4285f4;\\n  font-size: 11px;\\n  margin-top: 2px;\\n  width: 12px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.task-description[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:last-child {\\n  font-size: 13px;\\n  line-height: 1.5;\\n  color: #202124;\\n  font-weight: 400;\\n  flex: 1;\\n}\\n\\n\\n\\n.output-area[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  flex-grow: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.output-area[_ngcontent-%COMP%]   .output-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 13px;\\n  margin-bottom: 6px;\\n  color: #202124;\\n}\\n.output-area[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  padding: 8px 10px;\\n  background: #f8f9fa;\\n  border-radius: 4px;\\n  font-family: \\\"Roboto Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.5;\\n  color: #3c4043;\\n  max-height: 120px;\\n  min-height: 30px;\\n  overflow-y: auto;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n  border: 1px solid #e8eaed;\\n  margin-bottom: 8px;\\n}\\n.output-area[_ngcontent-%COMP%]   .output-content.error[_ngcontent-%COMP%] {\\n  background: #fce8e6;\\n  color: #c5221f;\\n  border-color: #f6bbb8;\\n}\\n.output-area[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%], .output-area[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #1a73e8;\\n  margin-top: 0px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  height: 24px;\\n}\\n.output-area[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]:before, .output-area[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]:before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 6px;\\n  height: 6px;\\n  background: #1a73e8;\\n  border-radius: 50%;\\n  margin-right: 5px;\\n}\\n.output-area[_ngcontent-%COMP%]   .web-results[_ngcontent-%COMP%]:hover, .output-area[_ngcontent-%COMP%]   .file-changes[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.feedback-row[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  border-top: 1px solid #e8f0fe;\\n  background: #f8f9fa;\\n  height: 36px;\\n}\\n.feedback-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #5f6368;\\n  margin-left: 3px;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 3px;\\n  border: 1px solid #e0e0e0;\\n  background: white;\\n  cursor: pointer;\\n  position: relative;\\n  padding: 0;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 1px;\\n  background: #4caf50;\\n  opacity: 0;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child.selected:after {\\n  opacity: 1;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 1px;\\n  background: #f44336;\\n  opacity: 0;\\n}\\n.feedback-row[_ngcontent-%COMP%]   .feedback-options[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child.selected:after {\\n  opacity: 1;\\n}\\n\\n\\n\\n.retry-button[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 10px 10px 10px auto;\\n  padding: 4px 10px;\\n  background: #ea4335;\\n  color: white;\\n  border: none;\\n  border-radius: 3px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);\\n}\\n.retry-button[_ngcontent-%COMP%]:hover {\\n  background: #d32f2f;\\n  box-shadow: 0 2px 4px rgba(60, 64, 67, 0.3);\\n}\\n\\n\\n\\n\\n\\n.browser-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e3f2fd, #bbdefb) !important;\\n  border: 1px solid #90caf9 !important;\\n  border-radius: 12px !important;\\n  color: #0d47a1 !important;\\n}\\n\\n.browser-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2196f3, #64b5f6) !important;\\n  color: white !important;\\n}\\n\\n\\n\\n.openai-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f3e5f5, #e1bee7) !important;\\n  border: 1px solid #ce93d8 !important;\\n  border-radius: 12px !important;\\n  color: #4a148c !important;\\n}\\n\\n.openai-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9c27b0, #ba68c8) !important;\\n  color: white !important;\\n}\\n\\n\\n\\n.llm-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e9, #c8e6c9) !important;\\n  border: 1px solid #a5d6a7 !important;\\n  border-radius: 12px !important;\\n  color: #1b5e20 !important;\\n}\\n\\n.llm-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50, #81c784) !important;\\n  color: white !important;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffebee, #ffcdd2) !important;\\n  border: 1px solid #ef9a9a !important;\\n  border-radius: 12px !important;\\n  color: #b71c1c !important;\\n}\\n\\n.error-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f44336, #e57373) !important;\\n  color: white !important;\\n}\\n\\n\\n\\n.system-notification[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff8e1, #ffe0b2) !important;\\n  border: 1px solid #ffcc80 !important;\\n  border-radius: 12px !important;\\n  color: #e65100 !important;\\n}\\n\\n\\n\\n.message-metadata[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 12px;\\n  color: rgba(0, 0, 0, 0.6);\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n\\n.execution-time[_ngcontent-%COMP%] {\\n  padding: 2px 6px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 4px;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.message-type[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  padding: 2px 6px;\\n  background-color: rgba(0, 0, 0, 0.05);\\n  border-radius: 4px;\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.user-message[_ngcontent-%COMP%]   .message-metadata[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .execution-time[_ngcontent-%COMP%], .user-message[_ngcontent-%COMP%]   .message-type[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  background: linear-gradient(135deg, #fafdff 80%, #e3f0ff 100%);\\n  border-radius: 18px;\\n  box-shadow: 0 6px 32px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n  margin: 0 auto;\\n  transition: box-shadow 0.3s;\\n}\\n\\n.chat-container[_ngcontent-%COMP%]:focus-within, .chat-container[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);\\n}\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 5px 16px;\\n  background: #3378d1;\\n  color: white;\\n  border-top-left-radius: 8px;\\n  border-top-right-radius: 8px;\\n  height: 46px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  animation: _ngcontent-%COMP%_fadeInDown 0.5s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.agent-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.agent-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 22px;\\n  font-weight: 700;\\n  letter-spacing: 0.5px;\\n  white-space: nowrap;\\n}\\n.agent-title[_ngcontent-%COMP%]   .agent-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-right: 4px;\\n}\\n.agent-title[_ngcontent-%COMP%]   .agent-status[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  background-color: rgba(255, 255, 255, 0.2);\\n  color: #fff;\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.agent-title[_ngcontent-%COMP%]   .agent-status.active[_ngcontent-%COMP%] {\\n  background-color: #22c55e;\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);\\n  }\\n}\\n.model-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  align-items: center;\\n  flex-wrap: wrap;\\n}\\n\\n.model-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  background: rgba(255, 255, 255, 0.18);\\n  padding: 7px 14px;\\n  border-radius: 8px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);\\n  transition: background 0.2s;\\n}\\n.model-selector[_ngcontent-%COMP%]   .autonomous-workflow[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding: 20px;\\n  background: linear-gradient(to bottom, #f5f7fa, #ffffff);\\n  border-radius: 12px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-out;\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #2a5298;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #4f8cff;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   .workflow-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   .workflow-action-btn[_ngcontent-%COMP%] {\\n  background-color: #f0f5ff;\\n  border: 1px solid #d0e1ff;\\n  border-radius: 6px;\\n  padding: 6px 10px;\\n  color: #4f8cff;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.model-selector[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%]   .workflow-action-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #e0edff;\\n  transform: translateY(-1px);\\n}\\n\\n.model-selector[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 15px;\\n  font-weight: 500;\\n  color: #2d3a4a;\\n}\\n\\n.model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  padding: 6px 12px 6px 32px;\\n  border-radius: 6px;\\n  border: 1.5px solid #b3d7ff;\\n  background: url('data:image/svg+xml;utf8,<svg fill=\\\"%234f8cff\\\" height=\\\"16\\\" viewBox=\\\"0 0 24 24\\\" width=\\\"16\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\"><path d=\\\"M7 10l5 5 5-5z\\\"/></svg>') no-repeat 8px center/18px 18px, #fff;\\n  color: #333;\\n  font-size: 15px;\\n  transition: border 0.2s;\\n  appearance: none;\\n}\\n\\n.model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  border: 1.5px solid #4f8cff;\\n  outline: none;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #e57373 60%, #ffb199 100%);\\n  border: none;\\n  color: #fff;\\n  padding: 7px 18px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 15px;\\n  font-weight: 600;\\n  transition: background 0.2s, color 0.2s, box-shadow 0.2s;\\n  margin-left: 10px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDDD1\\\"; \\n\\n  font-size: 1.1em;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #ffb199 60%, #e57373 100%);\\n  color: #fffde7;\\n  box-shadow: 0 2px 8px rgba(229, 115, 115, 0.18);\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.mode-toggle-btn[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  border-radius: 16px;\\n  background-color: rgba(255, 255, 255, 0.15);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  color: white;\\n  font-size: 13px;\\n  font-weight: 600;\\n  transition: all 0.2s ease;\\n  min-width: 120px;\\n  text-align: center;\\n  white-space: nowrap;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.mode-toggle-btn[_ngcontent-%COMP%]   .fa-robot[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.mode-toggle-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.25);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\\n}\\n.mode-toggle-btn.active[_ngcontent-%COMP%] {\\n  background-color: #22c55e;\\n  border-color: #16a34a;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  padding: 6px 10px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  margin: 0 5px;\\n}\\n\\n.clear-chat-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 60, 60, 0.8);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\\n}\\n\\n.expand-chat-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.15);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  color: white;\\n  font-size: 14px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n  margin-left: 5px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.expand-chat-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.25);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  padding: 0 32px 0 32px;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column-reverse;\\n  gap: 18px;\\n  background: transparent;\\n  scrollbar-width: thin;\\n  scrollbar-color: #b3d7ff #fafdff;\\n  animation: _ngcontent-%COMP%_fadeIn 0.6s;\\n  max-height: 300px;\\n  min-height: 80px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #b3d7ff;\\n  border-radius: 4px;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #fafdff;\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: 12px;\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_slideIn 0.4s forwards;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.avatar[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #4f8cff 60%, #a0c4ff 100%);\\n  color: #fff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  font-size: 18px;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);\\n  flex-shrink: 0;\\n  margin-bottom: 2px;\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffb199 60%, #e57373 100%);\\n}\\n\\n.system-message[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107 60%, #ff9800 100%);\\n}\\n\\n.bubble[_ngcontent-%COMP%] {\\n  padding: 14px 20px;\\n  border-radius: 16px;\\n  max-width: 420px;\\n  min-width: 60px;\\n  word-break: break-word;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  font-size: 16px;\\n  line-height: 1.6;\\n  position: relative;\\n  transition: background 0.2s;\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #ffb199 60%, #e57373 100%);\\n  color: #fff;\\n  border-bottom-right-radius: 4px;\\n  align-self: flex-end;\\n}\\n\\n.agent-message[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%] {\\n  background: #fafdff;\\n  color: #2d3a4a;\\n  border-bottom-left-radius: 4px;\\n  align-self: flex-start;\\n}\\n\\n.message-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 4px;\\n  font-size: 13px;\\n  opacity: 0.8;\\n}\\n\\n.sender[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n\\n.timestamp[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  line-height: 1.5;\\n  font-size: 15px;\\n}\\n\\n.message-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 14px;\\n  padding: 12px 32px 16px 32px;\\n  background: #fff;\\n  border-top: 1px solid #e0e0e0;\\n  border-bottom-left-radius: 8px;\\n  border-bottom-right-radius: 8px;\\n  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.03);\\n  align-items: flex-end;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  resize: none;\\n  font-family: inherit;\\n  height: 40px;\\n  font-size: 14px;\\n  background: white;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\\n  border: 1.5px solid #4f8cff;\\n  outline: none;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  background: #e0e0e0;\\n  color: #555;\\n  border: none;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: background 0.2s;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:not(:disabled):hover {\\n  background: #d0d0d0;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 180px;\\n  color: #888;\\n  text-align: center;\\n  font-size: 17px;\\n  opacity: 0.8;\\n}\\n\\n.loading-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 20px;\\n  color: #777;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  border: 3px solid rgba(0, 0, 0, 0.1);\\n  border-top: 3px solid #4f8cff;\\n  border-radius: 50%;\\n  width: 28px;\\n  height: 28px;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 900px) {\\n  .chat-header[_ngcontent-%COMP%], .message-form[_ngcontent-%COMP%], .messages-container[_ngcontent-%COMP%] {\\n    padding-left: 12px;\\n    padding-right: 12px;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    max-height: 180px;\\n    min-height: 60px;\\n  }\\n}\\n@media (max-width: 600px) {\\n  .chat-header[_ngcontent-%COMP%], .message-form[_ngcontent-%COMP%], .messages-container[_ngcontent-%COMP%] {\\n    padding-left: 2px;\\n    padding-right: 2px;\\n  }\\n  .chat-header[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding-top: 10px;\\n    padding-bottom: 10px;\\n  }\\n  .message-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 10px 16px;\\n    font-size: 15px;\\n  }\\n}\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  background: #ffffff;\\n  border-radius: 8px;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  min-height: 46px;\\n  overflow: visible;\\n}\\n\\n\\n\\n.message-form[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  gap: 10px;\\n  padding: 10px;\\n  background: white;\\n  position: sticky;\\n  bottom: 0;\\n  z-index: 1000;\\n  align-items: center;\\n  width: 100%;\\n  flex-wrap: wrap;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%]:not(.expanded)   .messages-container[_ngcontent-%COMP%], .chat-container[_ngcontent-%COMP%]:not(.expanded)   .autonomous-workflow[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n\\n\\n.file-changes-container[_ngcontent-%COMP%] {\\n  display: none !important;\\n}\\n\\n\\n\\n.chat-container.expanded[_ngcontent-%COMP%] {\\n  height: auto;\\n  max-height: 600px;\\n  \\n\\n}\\n\\n\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-height: 150px;\\n  overflow-y: auto;\\n  transition: all 0.3s;\\n}\\n\\n.chat-container.expanded[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n}\\n\\n\\n\\n.chat-container.expanded[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%] {\\n  border-radius: 10px 10px 0 0;\\n  position: sticky;\\n  top: 0;\\n  z-index: 10;\\n}\\n\\n\\n\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  flex-wrap: wrap;\\n  flex: 1;\\n  margin: 0 20px;\\n  \\n\\n}\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: rgba(255, 255, 255, 0.15);\\n  padding: 4px 12px;\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  margin-right: 10px;\\n}\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 600;\\n  white-space: nowrap;\\n}\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: none;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-size: 13px;\\n  min-width: 140px;\\n}\\n.chat-container.expanded[_ngcontent-%COMP%]   .model-controls[_ngcontent-%COMP%]   .model-selector[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n  background: #fff;\\n  color: #333;\\n}\\n\\n\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n\\n\\n.expand-chat-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4f8cff 80%, #a0c4ff 100%);\\n  color: white;\\n  opacity: 1 !important;\\n  visibility: visible !important;\\n}\\n\\n\\n\\n.file-changes-wrapper[_ngcontent-%COMP%] {\\n  margin: 10px 0;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n  width: 100%;\\n}\\n\\n.file-changes[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  width: 100%;\\n}\\n\\n.file-changes-header[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  background: #f1f3f4;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.file-diff[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  font-family: \\\"Roboto Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.5;\\n  white-space: pre-wrap;\\n  overflow-x: auto;\\n  max-height: 300px;\\n  overflow-y: auto;\\n  background: #f8f9fa;\\n  color: #333;\\n  border: 1px solid #eee;\\n  margin: 0;\\n}\\n\\n\\n\\n.diff-added[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  background-color: #e6ffec;\\n  display: block;\\n}\\n\\n.diff-removed[_ngcontent-%COMP%] {\\n  color: #d73a49;\\n  background-color: #ffeef0;\\n  display: block;\\n}\\n\\n.diff-hunk[_ngcontent-%COMP%] {\\n  color: #0366d6;\\n  background-color: #f1f8ff;\\n  display: block;\\n  font-weight: bold;\\n}\\n\\n\\n\\n.chat-container.expanded[_ngcontent-%COMP%] {\\n  max-height: none;\\n  height: auto;\\n  position: relative;\\n}\\n\\n.chat-container.expanded[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%], .chat-container.expanded[_ngcontent-%COMP%]   .autonomous-workflow[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  position: relative !important;\\n  z-index: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);\\n  min-height: 150px; \\n\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between; \\n\\n}\\n\\n.expand-chat-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4f8cff 80%, #a0c4ff 100%);\\n  color: #fff;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 18px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  margin-left: 10px;\\n  padding: 6px 14px;\\n  transition: background 0.2s, box-shadow 0.2s;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.expand-chat-btn[_ngcontent-%COMP%]:hover {\\n  background: #4f8cff;\\n  box-shadow: 0 2px 8px rgba(79, 140, 255, 0.18);\\n}\\n\\n.export-chat-btn[_ngcontent-%COMP%], .copy-chat-btn[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  padding: 6px 14px;\\n  background: #1976d2;\\n  color: #fff;\\n  border: none;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  transition: background 0.2s;\\n}\\n\\n.export-chat-btn[_ngcontent-%COMP%]:disabled, .copy-chat-btn[_ngcontent-%COMP%]:disabled {\\n  background: #bdbdbd;\\n  cursor: not-allowed;\\n}\\n\\n.export-chat-btn[_ngcontent-%COMP%]:hover:not(:disabled), .copy-chat-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #1565c0;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 10px 20px;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background-color: #4f8cff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing-animation 1.4s infinite ease-in-out;\\n  opacity: 0.7;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing-animation {\\n  0%, 100% {\\n    transform: scale(0.7);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: scale(1.2);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.message.streaming[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  border-right: 2px solid #4f8cff;\\n  animation: _ngcontent-%COMP%_cursor-blink 1s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_cursor-blink {\\n  0%, 100% {\\n    border-color: transparent;\\n  }\\n  50% {\\n    border-color: #4f8cff;\\n  }\\n}\\n\\n\\n.message-reactions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 5px 0;\\n}\\n\\n.reaction-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 6px;\\n}\\n\\n.reaction-button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 16px;\\n  padding: 4px 8px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n.reaction-button[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  transform: translateY(-1px);\\n}\\n.reaction-button.active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-color: #bbdefb;\\n  color: #1976d2;\\n}\\n\\n\\n\\n.agent-thinking-panel[_ngcontent-%COMP%] {\\n  margin: 10px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.thinking-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 15px;\\n  background-color: #f5f5f5;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.thinking-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.thinking-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #666;\\n  cursor: pointer;\\n  font-size: 14px;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n}\\n.thinking-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: #e0e0e0;\\n}\\n\\n.thinking-content[_ngcontent-%COMP%] {\\n  padding: 10px 15px;\\n  background-color: #fafafa;\\n  font-family: monospace;\\n  font-size: 14px;\\n  line-height: 1.5;\\n  overflow-x: auto;\\n  margin: 0;\\n  white-space: pre-wrap;\\n}\\n\\n\\n\\n.memory-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4f8cff 0%, #2979ff 100%);\\n  border: none;\\n  color: #fff;\\n  padding: 7px 12px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 15px;\\n  font-weight: 600;\\n  transition: all 0.2s;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.memory-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #2979ff 0%, #1565c0 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(41, 121, 255, 0.2);\\n}\\n.memory-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n\\n\\n.streaming-toggle[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.18);\\n  padding: 5px 10px;\\n  border-radius: 8px;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n}\\n.streaming-toggle[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n.streaming-toggle[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  accent-color: #22c55e;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.saving-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -20px;\\n  left: 10px;\\n  font-size: 12px;\\n  color: #888;\\n  background-color: rgba(255, 255, 255, 0.7);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0.6;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "model_r13", "id", "ɵɵadvance", "ɵɵtextInterpolate", "name", "model_r14", "model_r15", "llm_r16", "message_r17", "metadata", "modelId", "ɵɵelement", "ɵɵtextInterpolate1", "executionTime", "ɵɵtemplate", "ChatComponent_div_46_span_2_Template", "ChatComponent_div_46_span_3_Template", "ChatComponent_div_46_span_4_Template", "ChatComponent_div_46_span_5_Template", "ChatComponent_div_46_span_6_Template", "ChatComponent_div_46_span_7_Template", "ChatComponent_div_46_span_8_Template", "ChatComponent_div_46_span_9_Template", "ChatComponent_div_46_span_13_Template", "ChatComponent_div_46_span_14_Template", "ChatComponent_div_46_span_15_Template", "ChatComponent_div_46_span_16_Template", "ChatComponent_div_46_span_17_Template", "ChatComponent_div_46_span_18_Template", "ChatComponent_div_46_span_19_Template", "ChatComponent_div_46_span_20_Template", "ChatComponent_div_46_span_21_Template", "ChatComponent_div_46_span_22_Template", "ChatComponent_div_46_span_23_Template", "ChatComponent_div_46_span_24_Template", "ChatComponent_div_46_span_25_Template", "ChatComponent_div_46_span_29_Template", "ChatComponent_div_46_span_30_Template", "ChatComponent_div_46_div_32_Template", "ɵɵlistener", "ChatComponent_div_46_Template_button_click_35_listener", "restoredCtx", "ɵɵrestoreView", "_r48", "$implicit", "ctx_r47", "ɵɵnextContext", "ɵɵresetView", "addReaction", "ChatComponent_div_46_span_37_Template", "ChatComponent_div_46_Template_button_click_38_listener", "ctx_r49", "ChatComponent_div_46_span_40_Template", "ChatComponent_div_46_Template_button_click_41_listener", "ctx_r50", "ChatComponent_div_46_span_43_Template", "ɵɵpureFunction8", "_c1", "sender", "messageType", "isComplete", "ɵɵpureFunction7", "_c2", "ɵɵpipeBind2", "timestamp", "content", "ɵɵsanitizeHtml", "ɵɵclassProp", "reactions", "includes", "ChatComponent_div_50_Template_button_click_4_listener", "_r52", "ctx_r51", "toggleAgentThinking", "ctx_r9", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ChatComponent_div_52_div_31_div_21_Template_button_click_4_listener", "_r65", "i_r55", "index", "ctx_r63", "setSubtaskFeedback", "ChatComponent_div_52_div_31_div_21_Template_button_click_5_listener", "ctx_r66", "subtask_r54", "feedback", "ChatComponent_div_52_div_31_button_22_Template_button_click_0_listener", "_r71", "ctx_r69", "retrySubtask", "ChatComponent_div_52_div_31_div_6_Template", "ChatComponent_div_52_div_31_div_7_Template", "ChatComponent_div_52_div_31_div_8_Template", "ChatComponent_div_52_div_31_div_19_Template", "ChatComponent_div_52_div_31_div_20_Template", "ChatComponent_div_52_div_31_div_21_Template", "ChatComponent_div_52_div_31_button_22_Template", "subtask", "type", "model_type", "web_research_used", "description", "error", "result", "web_results", "file_diff", "completed", "ChatComponent_div_52_div_31_Template", "ctx_r11", "currentStage", "ɵɵtextInterpolate2", "completedSubtasks", "subtasks", "length", "ChatComponent", "constructor", "fb", "socketService", "projectService", "agentService", "projectName", "messagesLoading", "messagesSaving", "messageEvent", "chatExpandChange", "messages", "loading", "models", "selected<PERSON><PERSON>l", "localLlmModels", "selectedLocalLlmModel", "isChatExpanded", "autonomousMode", "agentTyping", "showAgentThinking", "streamingEnabled", "showApiPayloads", "apiRequests", "apiResponses", "ngOnInit", "console", "log", "initForm", "loadMessages", "loadModels", "setupSocketListeners", "ngAfterViewChecked", "scrollToBottom", "messagesContainer", "nativeElement", "scrollTop", "scrollHeight", "err", "messageForm", "group", "message", "required", "warn", "getProjectMessages", "subscribe", "response", "getModels", "getModelsByProvider", "provider", "filter", "model", "startsWith", "getOtherModels", "knownProviders", "some", "on", "data", "project_name", "m", "isAgentWorkingPlaceholder", "message_type", "push", "message_id", "Date", "now", "is_typing", "lastAgentMessage", "find", "token", "for<PERSON>ach", "thinking", "messageToUpdate", "reaction", "sendMessage", "invalid", "messageContent", "get", "value", "userMessageId", "requestPayload", "model_id", "local_llm_model_id", "streaming_enabled", "requestEntry", "endpoint", "payload", "JSON", "stringify", "reset", "responseEntry", "errorEntry", "onModelChange", "deleteMessage", "splice", "clearChat", "deleteProjectMessages", "toggleChatExpand", "emit", "onEnter", "event", "shift<PERSON>ey", "preventDefault", "exportChat", "exportProjectChat", "alert", "file_path", "detail", "exportDynamicChat", "chatText", "map", "join", "blob", "Blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "toISOString", "replace", "filename", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "copyChat", "navigator", "clipboard", "writeText", "then", "onLocalLlmModelChange", "longTaskInProgress", "s", "toggleAutonomousMode", "formatFileDiff", "diff", "escape", "c", "split", "line", "every", "stage", "Math", "max", "messageId", "addMessageReaction", "r", "resetContextMemory", "toggleApiPayloads", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "SocketService", "i3", "ProjectService", "i4", "AgentService", "selectors", "viewQuery", "ChatComponent_Query", "rf", "ctx", "ChatComponent_Template_select_ngModelChange_14_listener", "$event", "ChatComponent_Template_select_change_14_listener", "ChatComponent_option_16_Template", "ChatComponent_option_18_Template", "ChatComponent_option_19_Template", "ChatComponent_Template_select_ngModelChange_24_listener", "ChatComponent_Template_select_change_24_listener", "ChatComponent_option_25_Template", "ChatComponent_Template_input_ngModelChange_28_listener", "ChatComponent_Template_button_click_31_listener", "ChatComponent_Template_button_click_35_listener", "ChatComponent_Template_button_click_37_listener", "ChatComponent_Template_button_click_39_listener", "ChatComponent_Template_button_click_41_listener", "ChatComponent_div_45_Template", "ChatComponent_div_46_Template", "ChatComponent_div_48_Template", "ChatComponent_div_49_Template", "ChatComponent_div_50_Template", "ChatComponent_div_51_Template", "ChatComponent_div_52_Template", "ChatComponent_Template_form_ngSubmit_53_listener", "ChatComponent_Template_textarea_keydown_enter_54_listener", "ChatComponent_Template_button_click_58_listener", "ChatComponent_Template_button_click_60_listener", "ChatComponent_Template_button_click_62_listener", "ChatComponent_div_64_Template", "ɵɵpureFunction1", "_c3", "_c4", "ɵɵattribute", "ɵɵpureFunction2", "_c5", "ɵɵpipeBind1"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\chat\\chat.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\chat\\chat.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { SocketService } from '../../services/socket.service';\r\nimport { ProjectService } from '../../services/project.service';\r\nimport { AgentService } from '../../services/agent.service';\r\n\r\ninterface MessageData {\r\n  project_name: string;\r\n  message: string;\r\n  message_id?: string;\r\n  message_type?: string;\r\n}\r\n\r\ninterface CompleteData {\r\n  project_name: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-chat',\r\n  templateUrl: './chat.component.html',\r\n  styleUrls: ['./chat.component.scss']\r\n})\r\nexport class ChatComponent implements OnInit, AfterViewChecked {\r\n  @Input() projectName: string = '';\r\n  @Input() messagesLoading: boolean = false;\r\n  @Input() messagesSaving: boolean = false;\r\n  @Output() messageEvent = new EventEmitter<any>();\r\n  @Output() chatExpandChange = new EventEmitter<boolean>();\r\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\r\n\r\n  messageForm!: FormGroup;\r\n  messages: any[] = [];\r\n  loading = false;\r\n  models: any[] = [];\r\n  selectedModel: string = 'deepseek/deepseek-coder';\r\n  localLlmModels: any[] = [\r\n    { id: 'mistral-nemo-instruct-2407', name: 'Mistral Nemo Instruct 2407' },\r\n    // Add more local LLM models here if needed\r\n  ];\r\n  selectedLocalLlmModel: string = 'mistral-nemo-instruct-2407';\r\n  isChatExpanded: boolean = false;\r\n  subtasks: any[] = [];\r\n  autonomousMode: boolean = false;\r\n  agentTyping: boolean = false;\r\n  agentThinkingContent: string = '';\r\n  showAgentThinking: boolean = false;\r\n  streamingEnabled: boolean = true;\r\n  showApiPayloads: boolean = true;\r\n  apiRequests: any[] = [];\r\n  apiResponses: any[] = [];\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private socketService: SocketService,\r\n    private projectService: ProjectService,\r\n    private agentService: AgentService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    console.log('[ChatComponent] ngOnInit called');\r\n    this.initForm();\r\n    this.loadMessages();\r\n    this.loadModels();\r\n    this.setupSocketListeners();\r\n  }\r\n\r\n  ngAfterViewChecked(): void {\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    try {\r\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\r\n        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\r\n      }\r\n    } catch (err) {}\r\n  }\r\n\r\n  initForm(): void {\r\n    console.log('[ChatComponent] Initializing form');\r\n    this.messageForm = this.fb.group({\r\n      message: ['', Validators.required]\r\n    });\r\n  }\r\n\r\n  loadMessages(): void {\r\n    console.log('[ChatComponent] loadMessages called for project:', this.projectName);\r\n    if (!this.projectName) {\r\n      console.warn('[ChatComponent] No projectName found. Skipping loadMessages.');\r\n      return;\r\n    }\r\n\r\n    if (!this.messagesLoading) {\r\n      this.loading = true;\r\n    }\r\n\r\n    this.projectService.getProjectMessages(this.projectName).subscribe(\r\n      (response: any) => {\r\n        console.log('[ChatComponent] Project messages loaded:', response);\r\n        this.messages = response.messages || [];\r\n        this.loading = false;\r\n      },\r\n      (error: any) => {\r\n        console.error('[ChatComponent] ❌ Error loading messages:', error);\r\n        this.loading = false;\r\n      }\r\n    );\r\n  }\r\n\r\n  loadModels(): void {\r\n    console.log('[ChatComponent] loadModels called');\r\n    this.agentService.getModels().subscribe(\r\n      (response: any) => {\r\n        console.log('[ChatComponent] Models loaded:', response);\r\n        this.models = response.models || [];\r\n      },\r\n      (error: any) => {\r\n        console.error('[ChatComponent] ❌ Error loading models:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets models filtered by provider\r\n   */\r\n  getModelsByProvider(provider: string): any[] {\r\n    return this.models.filter(model => model.id.startsWith(`${provider}/`));\r\n  }\r\n\r\n  /**\r\n   * Gets models that don't belong to specified providers\r\n   */\r\n  getOtherModels(): any[] {\r\n    const knownProviders = ['openai', 'deepseek'];\r\n    return this.models.filter(model => \r\n      !knownProviders.some(provider => model.id.startsWith(`${provider}/`))\r\n    );\r\n  }\r\n\r\n  setupSocketListeners(): void {\r\n    console.log('[ChatComponent] Setting up socket listeners');\r\n\r\n    this.socketService.on('agent_message').subscribe((data: MessageData) => {\r\n      console.log('[ChatComponent] 🔁 Received socket \"agent_message\":', data);\r\n\r\n      if (data.project_name === this.projectName) {\r\n        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\r\n        \r\n        // Determine the message type from incoming data\r\n        const messageType = data.message_type || 'agent';\r\n        \r\n        this.messages.push({\r\n          id: data.message_id || `msg-${Date.now()}`,\r\n          sender: 'agent',\r\n          content: data.message,\r\n          timestamp: new Date(),\r\n          isAgentWorkingPlaceholder: false,\r\n          messageType: messageType,\r\n          reactions: []\r\n        });\r\n        this.loading = false;\r\n        console.log('[ChatComponent] Message added to chat from agent');\r\n      }\r\n    });\r\n\r\n    this.socketService.on('agent_typing').subscribe((data: any) => {\r\n      console.log('[ChatComponent] 🔁 Received socket \"agent_typing\":', data);\r\n\r\n      if (data.project_name === this.projectName) {\r\n        this.agentTyping = data.is_typing;\r\n        // If not typing anymore, remove any placeholder messages\r\n        if (!data.is_typing) {\r\n          this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\r\n        }\r\n      }\r\n    });\r\n\r\n    this.socketService.on('agent_stream_token').subscribe((data: any) => {\r\n      console.log('[ChatComponent] 🔁 Received socket \"agent_stream_token\":', data);\r\n\r\n      if (data.project_name === this.projectName) {\r\n        // Find the last agent message or create a new one if none exists\r\n        let lastAgentMessage = this.messages.find(m => m.sender === 'agent' && !m.isComplete);\r\n        \r\n        if (!lastAgentMessage) {\r\n          lastAgentMessage = {\r\n            id: `stream-${Date.now()}`,\r\n            sender: 'agent',\r\n            content: '',\r\n            timestamp: new Date(),\r\n            isComplete: false,\r\n            reactions: []\r\n          };\r\n          this.messages.push(lastAgentMessage);\r\n        }\r\n        \r\n        // Append the token to the message content\r\n        lastAgentMessage.content += data.token;\r\n        this.scrollToBottom();\r\n      }\r\n    });\r\n\r\n    this.socketService.on('agent_complete').subscribe((data: CompleteData) => {\r\n      console.log('[ChatComponent] ✅ Received socket \"agent_complete\":', data);\r\n\r\n      if (data.project_name === this.projectName) {\r\n        this.loading = false;\r\n        this.agentTyping = false;\r\n        \r\n        // Mark all agent messages as complete\r\n        this.messages.forEach(message => {\r\n          if (message.sender === 'agent') {\r\n            message.isComplete = true;\r\n          }\r\n        });\r\n        \r\n        // Remove any placeholder messages\r\n        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);\r\n        console.log('[ChatComponent] Loading state cleared after agent_complete');\r\n      }\r\n    });\r\n    \r\n    this.socketService.on('agent_thinking').subscribe((data: any) => {\r\n      console.log('[ChatComponent] 🧠 Received socket \"agent_thinking\":', data);\r\n      \r\n      if (data.project_name === this.projectName && this.showAgentThinking) {\r\n        // Display the agent's thought process in a special UI element if debugging is enabled\r\n        this.agentThinkingContent = data.thinking;\r\n      }\r\n    });\r\n    \r\n    this.socketService.on('message_reaction').subscribe((data: any) => {\r\n      console.log('[ChatComponent] 👍 Received socket \"message_reaction\":', data);\r\n      \r\n      if (data.project_name === this.projectName) {\r\n        // Find the message and add the reaction\r\n        const messageToUpdate = this.messages.find(m => m.id === data.message_id);\r\n        if (messageToUpdate) {\r\n          if (!messageToUpdate.reactions) {\r\n            messageToUpdate.reactions = [];\r\n          }\r\n          if (!messageToUpdate.reactions.includes(data.reaction)) {\r\n            messageToUpdate.reactions.push(data.reaction);\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  sendMessage(): void {\r\n    if (this.messageForm.invalid) {\r\n      return;\r\n    }\r\n    \r\n    const messageContent = this.messageForm.get('message')?.value;\r\n    if (!messageContent || !this.projectName) {\r\n      return;\r\n    }\r\n    \r\n    // Add user message to the chat\r\n    const userMessageId = `msg-${Date.now()}`;\r\n    this.messages.push({\r\n      id: userMessageId,\r\n      sender: 'user',\r\n      content: messageContent,\r\n      timestamp: new Date(),\r\n      reactions: []\r\n    });\r\n    \r\n    // Create request payload\r\n    const requestPayload = {\r\n      project_name: this.projectName,\r\n      message: messageContent,\r\n      model_id: this.selectedModel,\r\n      local_llm_model_id: this.selectedLocalLlmModel,\r\n      streaming_enabled: this.streamingEnabled\r\n    };\r\n    \r\n    // Store request\r\n    const requestEntry = {\r\n      timestamp: new Date(),\r\n      type: 'request',\r\n      endpoint: `/projects/${this.projectName}/messages`,\r\n      payload: requestPayload\r\n    };\r\n    this.apiRequests.push(requestEntry);\r\n    \r\n    // If API payloads are visible, add to messages\r\n    if (this.showApiPayloads) {\r\n      this.messages.push({\r\n        id: `api-req-${Date.now()}`,\r\n        sender: 'system',\r\n        messageType: 'api_request',\r\n        content: `<strong>API Request:</strong><br><pre>${JSON.stringify(requestPayload, null, 2)}</pre>`,\r\n        timestamp: new Date(),\r\n        reactions: []\r\n      });\r\n    }\r\n    \r\n    // Reset the form\r\n    this.messageForm.reset();\r\n    \r\n    // Show loading indicator\r\n    this.loading = true;\r\n    \r\n    // Send to API\r\n    if (this.streamingEnabled) {\r\n      // For streaming, we handle via sockets\r\n      this.socketService.sendMessage(this.projectName, messageContent, this.selectedModel);\r\n    } else {\r\n      // For non-streaming, we make a direct API call\r\n      this.agentService.sendMessage(\r\n        this.projectName, \r\n        messageContent, \r\n        this.selectedModel,\r\n        this.selectedLocalLlmModel,\r\n        false\r\n      ).subscribe(\r\n        (response: any) => {\r\n          // Store response\r\n          const responseEntry = {\r\n            timestamp: new Date(),\r\n            type: 'response',\r\n            endpoint: `/projects/${this.projectName}/messages`,\r\n            payload: response\r\n          };\r\n          this.apiResponses.push(responseEntry);\r\n          \r\n          // If API payloads are visible, add to messages\r\n          if (this.showApiPayloads) {\r\n            this.messages.push({\r\n              id: `api-res-${Date.now()}`,\r\n              sender: 'system',\r\n              messageType: 'api_response',\r\n              content: `<strong>API Response:</strong><br><pre>${JSON.stringify(response, null, 2)}</pre>`,\r\n              timestamp: new Date(),\r\n              reactions: []\r\n            });\r\n          }\r\n          \r\n          this.loading = false;\r\n        },\r\n        (error: any) => {\r\n          console.error('[ChatComponent] ❌ Error sending message:', error);\r\n          \r\n          // Store error response\r\n          const errorEntry = {\r\n            timestamp: new Date(),\r\n            type: 'error',\r\n            endpoint: `/projects/${this.projectName}/messages`,\r\n            payload: error\r\n          };\r\n          this.apiResponses.push(errorEntry);\r\n          \r\n          // Add error message\r\n          this.messages.push({\r\n            id: `error-${Date.now()}`,\r\n            sender: 'system',\r\n            messageType: 'error',\r\n            content: `<strong>API Error:</strong><br><pre>${JSON.stringify(error, null, 2)}</pre>`,\r\n            timestamp: new Date(),\r\n            reactions: []\r\n          });\r\n          \r\n          this.loading = false;\r\n        }\r\n      );\r\n    }\r\n  }\r\n\r\n  onModelChange(modelId: string): void {\r\n    console.log('[ChatComponent] Model changed to:', modelId);\r\n    this.selectedModel = modelId;\r\n  }\r\n\r\n  deleteMessage(index: number): void {\r\n    this.messages.splice(index, 1);\r\n  }\r\n\r\n  clearChat(): void {\r\n    if (!this.projectName) return;\r\n    // Clear messages in UI\r\n    this.messages = [];\r\n    // Call backend API to delete chat history\r\n    this.projectService.deleteProjectMessages(this.projectName).subscribe(\r\n      () => {\r\n        console.log('[ChatComponent] Chat history deleted on backend');\r\n      },\r\n      (error) => {\r\n        console.error('[ChatComponent] Error deleting chat history:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n  toggleChatExpand(): void {\r\n    this.isChatExpanded = !this.isChatExpanded;\r\n    this.chatExpandChange.emit(this.isChatExpanded);\r\n  }\r\n\r\n  onEnter(event: KeyboardEvent): void {\r\n    if (event.shiftKey) {\r\n      return; // allow newline\r\n    }\r\n    event.preventDefault();\r\n    this.sendMessage();\r\n  }\r\n\r\n  exportChat(): void {\r\n    if (!this.projectName) return;\r\n    this.projectService.exportProjectChat(this.projectName).subscribe(\r\n      (response: any) => {\r\n        alert('Chat exported: ' + (response?.file_path || 'Success'));\r\n      },\r\n      (error: any) => {\r\n        alert('Failed to export chat: ' + (error?.error?.detail || error));\r\n      }\r\n    );\r\n  }\r\n\r\n  exportDynamicChat(): void {\r\n    if (!this.messages.length) return;\r\n    // Format chat as text\r\n    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\r\n    const blob = new Blob([chatText], { type: 'text/plain' });\r\n    const url = window.URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\r\n    const filename = `${this.projectName || 'chat'}-dynamic-${timestamp}.txt`;\r\n    a.href = url;\r\n    a.download = filename;\r\n    document.body.appendChild(a);\r\n    a.click();\r\n    setTimeout(() => {\r\n      document.body.removeChild(a);\r\n      window.URL.revokeObjectURL(url);\r\n    }, 0);\r\n  }\r\n\r\n  copyChat(): void {\r\n    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\\n');\r\n    navigator.clipboard.writeText(chatText).then(\r\n      () => alert('Chat copied to clipboard!'),\r\n      () => alert('Failed to copy chat to clipboard.')\r\n    );\r\n  }\r\n\r\n  onLocalLlmModelChange(modelId: string): void {\r\n    this.selectedLocalLlmModel = modelId;\r\n    // You can add logic here to notify the backend or update the session if needed\r\n    console.log('[ChatComponent] Local LLM model changed to:', modelId);\r\n  }\r\n\r\n  get completedSubtasks(): number {\r\n    if (!this.subtasks) return 0;\r\n    return this.subtasks.filter(subtask => !subtask.error).length;\r\n  }\r\n\r\n  retrySubtask(index: number): void {\r\n    // Simulate retry: clear error and result, set to loading, then re-run (in real app, call backend)\r\n    const subtask = this.subtasks[index];\r\n    subtask.error = null;\r\n    subtask.result = 'Retrying...';\r\n    // Simulate async retry (replace with real backend call)\r\n    setTimeout(() => {\r\n      subtask.result = 'Retried result (simulated)';\r\n      subtask.error = null;\r\n    }, 1500);\r\n  }\r\n\r\n  setSubtaskFeedback(index: number, feedback: 'up' | 'down'): void {\r\n    this.subtasks[index].feedback = feedback;\r\n  }\r\n\r\n  get longTaskInProgress(): boolean {\r\n    if (this.loading) return true;\r\n    if (this.subtasks && this.subtasks.length > 0) {\r\n      return this.subtasks.some(s => !s.result && !s.error);\r\n    }\r\n    return false;\r\n  }\r\n\r\n  toggleAutonomousMode(): void {\r\n    this.autonomousMode = !this.autonomousMode;\r\n  }\r\n\r\n  /**\r\n   * Formats a unified diff string as HTML with basic syntax highlighting for added, removed, and context lines.\r\n   * @param diff The unified diff string\r\n   * @returns HTML string\r\n   */\r\n  formatFileDiff(diff: string): string {\r\n    if (!diff) return '';\r\n    // Escape HTML\r\n    const escape = (s: string) => s.replace(/[&<>]/g, c => ({'&':'&amp;','<':'&lt;','>':'&gt;'}[c]||c));\r\n    return '<pre>' + diff.split('\\n').map(line => {\r\n      if (line.startsWith('+') && !line.startsWith('+++')) {\r\n        return `<span class='diff-added'>${escape(line)}</span>`;\r\n      } else if (line.startsWith('-') && !line.startsWith('---')) {\r\n        return `<span class='diff-removed'>${escape(line)}</span>`;\r\n      } else if (line.startsWith('@@')) {\r\n        return `<span class='diff-hunk'>${escape(line)}</span>`;\r\n      } else {\r\n        return escape(line);\r\n      }\r\n    }).join('\\n') + '</pre>';\r\n  }\r\n\r\n  /**\r\n   * Returns the current workflow stage for the progress indicator in the chat UI.\r\n   * 1 = Planning, 2 = Design, 3 = Implementation, 4 = Testing\r\n   */\r\n  get currentStage(): number {\r\n    if (!this.subtasks || this.subtasks.length === 0) return 0;\r\n    // If all subtasks are completed, return 4 (Testing)\r\n    if (this.subtasks.every(s => s.completed || s.result || s.error)) return 4;\r\n    // Otherwise, estimate stage based on subtask type or index\r\n    // (You can refine this logic as needed)\r\n    let stage = 1;\r\n    for (const subtask of this.subtasks) {\r\n      if (subtask.subtask?.type === 'design') stage = Math.max(stage, 2);\r\n      else if (subtask.subtask?.type === 'implementation') stage = Math.max(stage, 3);\r\n      else if (subtask.subtask?.type === 'testing') stage = Math.max(stage, 4);\r\n    }\r\n    return stage;\r\n  }\r\n\r\n  addReaction(messageId: string, reaction: string): void {\r\n    console.log(`[ChatComponent] Adding reaction ${reaction} to message ${messageId}`);\r\n    \r\n    // Optimistically update UI\r\n    const messageToUpdate = this.messages.find(m => m.id === messageId);\r\n    if (messageToUpdate) {\r\n      if (!messageToUpdate.reactions) {\r\n        messageToUpdate.reactions = [];\r\n      }\r\n      if (!messageToUpdate.reactions.includes(reaction)) {\r\n        messageToUpdate.reactions.push(reaction);\r\n      }\r\n    }\r\n    \r\n    // Send to backend\r\n    this.agentService.addMessageReaction(this.projectName, messageId, reaction).subscribe(\r\n      (response) => {\r\n        console.log('[ChatComponent] ✅ Reaction added successfully:', response);\r\n      },\r\n      (error) => {\r\n        console.error('[ChatComponent] ❌ Error adding reaction:', error);\r\n        // Remove the reaction if it failed\r\n        if (messageToUpdate && messageToUpdate.reactions) {\r\n          messageToUpdate.reactions = messageToUpdate.reactions.filter((r: string) => r !== reaction);\r\n        }\r\n      }\r\n    );\r\n  }\r\n  \r\n  resetContextMemory(): void {\r\n    if (!this.projectName) return;\r\n    \r\n    this.agentService.resetContextMemory(this.projectName).subscribe(\r\n      (response) => {\r\n        console.log('[ChatComponent] ✅ Context memory reset successfully:', response);\r\n        alert('Conversation memory has been reset.');\r\n      },\r\n      (error) => {\r\n        console.error('[ChatComponent] ❌ Error resetting context memory:', error);\r\n        alert('Failed to reset conversation memory.');\r\n      }\r\n    );\r\n  }\r\n  \r\n  toggleAgentThinking(): void {\r\n    this.showAgentThinking = !this.showAgentThinking;\r\n  }\r\n\r\n  toggleApiPayloads(): void {\r\n    this.showApiPayloads = !this.showApiPayloads;\r\n    console.log('[ChatComponent] API payload visibility toggled:', this.showApiPayloads);\r\n  }\r\n} ", "<div class=\"chat-container\" [ngClass]=\"{'expanded': isChatExpanded}\">\n  <div class=\"chat-header\">\n    <div class=\"agent-title\">\n      <span class=\"agent-icon\">🤖</span>\n      <h2>Autonomous AI Agent</h2>\n      <span class=\"agent-status\" [ngClass]=\"{'active': loading || longTaskInProgress || agentTyping}\">\n        {{loading || longTaskInProgress ? 'Working' : agentTyping ? 'Typing...' : 'Ready'}}\n      </span>\n    </div>\n    <div class=\"model-controls\">\n      <div class=\"model-selector cloud-model\">\n        <label for=\"modelSelect\"><i class=\"fa fa-cloud\"></i> Cloud LLM:</label>\n        <select id=\"modelSelect\" [(ngModel)]=\"selectedModel\" (change)=\"onModelChange(selectedModel)\">\n          <optgroup label=\"OpenAI\">\n            <option *ngFor=\"let model of getModelsByProvider('openai')\" [value]=\"model.id\">{{ model.name }}</option>\n          </optgroup>\n          <optgroup label=\"DeepSeek\">\n            <option *ngFor=\"let model of getModelsByProvider('deepseek')\" [value]=\"model.id\">{{ model.name }}</option>\n          </optgroup>\n          <option *ngFor=\"let model of getOtherModels()\" [value]=\"model.id\">{{ model.name }}</option>\n        </select>\n      </div>\n      <div class=\"model-selector local-model\">\n        <label for=\"localLlmSelect\"><i class=\"fa fa-desktop\"></i> Local LLM:</label>\n        <select id=\"localLlmSelect\" [(ngModel)]=\"selectedLocalLlmModel\" (change)=\"onLocalLlmModelChange(selectedLocalLlmModel)\">\n          <option *ngFor=\"let llm of localLlmModels\" [value]=\"llm.id\">{{ llm.name }}</option>\n        </select>\n      </div>\n      <div class=\"streaming-toggle\">\n        <label for=\"streamingToggle\">\n          <input type=\"checkbox\" id=\"streamingToggle\" [(ngModel)]=\"streamingEnabled\"> \n          Streaming\n        </label>\n      </div>\n    </div>\n    <div class=\"chat-actions\">\n      <button class=\"mode-toggle-btn\" [ngClass]=\"{'active': autonomousMode}\" (click)=\"toggleAutonomousMode()\" title=\"Toggle between autonomous and assisted mode\">\n        <i class=\"fa fa-robot\"></i>\n        <span>{{autonomousMode ? 'Autonomous' : 'Assisted'}}</span>\n      </button>\n      <button class=\"memory-btn\" (click)=\"resetContextMemory()\" title=\"Reset conversation memory\">\n        <i class=\"fa fa-brain\"></i>\n      </button>\n      <button class=\"api-toggle-btn\" (click)=\"toggleApiPayloads()\" [ngClass]=\"{'active': showApiPayloads}\" title=\"Toggle API payloads visibility\">\n        <i class=\"fa fa-exchange-alt\"></i>\n      </button>\n      <button class=\"clear-chat-btn\" (click)=\"clearChat()\" title=\"Clear chat history\">\n        <i class=\"fa fa-trash\"></i>\n      </button>\n      <button class=\"expand-chat-btn\" (click)=\"toggleChatExpand()\" [attr.aria-label]=\"isChatExpanded ? 'Collapse Chat' : 'Expand Chat'\">\n        <i class=\"fa\" [ngClass]=\"{'fa-chevron-up': isChatExpanded, 'fa-chevron-down': !isChatExpanded}\"></i>\n      </button>\n    </div>\n  </div>\n  \n  <div class=\"messages-container\" #messagesContainer>\n    <div *ngIf=\"messages.length === 0\" class=\"empty-state\">\n      <p>No messages yet. Start a conversation with the AI agent.</p>\n    </div>\n    \n    <div *ngFor=\"let message of messages | reverse\" class=\"message\" [ngClass]=\"{\n      'user-message': message.sender === 'user', \n      'agent-message': message.sender === 'agent', \n      'system-message': message.sender === 'system',\n      'browser-message': message.messageType === 'browser',\n      'openai-message': message.messageType === 'openai',\n      'llm-message': message.messageType === 'local_llm',\n      'error-message': message.messageType === 'error',\n      'streaming': message.sender === 'agent' && !message.isComplete\n      }\">\n      <div class=\"avatar\" [ngClass]=\"{\n        'user-avatar': message.sender === 'user', \n        'ai-avatar': message.sender === 'agent', \n        'system-avatar': message.sender === 'system',\n        'browser-avatar': message.messageType === 'browser',\n        'openai-avatar': message.messageType === 'openai',\n        'llm-avatar': message.messageType === 'local_llm',\n        'error-avatar': message.messageType === 'error'\n        }\">\n        <span *ngIf=\"message.sender === 'user'\">U</span>\n        <span *ngIf=\"message.sender === 'agent' && !message.messageType\">AI</span>\n        <span *ngIf=\"message.sender === 'system'\">S</span>\n        <span *ngIf=\"message.messageType === 'browser'\">🌐</span>\n        <span *ngIf=\"message.messageType === 'openai'\">🧠</span>\n        <span *ngIf=\"message.messageType === 'local_llm'\">💻</span>\n        <span *ngIf=\"message.messageType === 'error'\">⚠️</span>\n        <span *ngIf=\"message.messageType === 'system_notification'\">📣</span>\n      </div>\n      <div class=\"bubble\">\n        <div class=\"message-header\">\n          <span class=\"sender\">\n            <span *ngIf=\"message.sender === 'user'\">👤 You</span>\n            <span *ngIf=\"message.sender === 'agent' && !message.messageType\">🤖 AI Agent</span>\n            <span *ngIf=\"message.sender === 'system'\">⚙️ System</span>\n            <span *ngIf=\"message.messageType === 'browser'\">🔍 Browser</span>\n            <span *ngIf=\"message.messageType === 'openai'\">🧠 OpenAI</span>\n            <span *ngIf=\"message.messageType === 'lm_studio'\">🧠 LM Studio</span>\n            <span *ngIf=\"message.messageType === 'local_llm'\">💻 Local LLM</span>\n            <span *ngIf=\"message.messageType === 'terminal'\">💻 Terminal</span>\n            <span *ngIf=\"message.messageType === 'llm_openai'\">🧠 OpenAI</span>\n            <span *ngIf=\"message.messageType === 'llm_lm_studio'\">🧠 LM Studio</span>\n            <span *ngIf=\"message.messageType === 'error'\">⚠️ Error</span>\n            <span *ngIf=\"message.messageType === 'plan'\">📋 Plan</span>\n            <span *ngIf=\"message.messageType === 'system_notification'\">📣 Notification</span>\n          </span>\n          <span class=\"timestamp\">{{ message.timestamp | date:'short' }}</span>\n          <span class=\"message-type\" *ngIf=\"message.metadata?.modelId\">{{ message.metadata.modelId }}</span>\n          \n          <!-- Streaming indicator for incomplete messages -->\n          <span class=\"streaming-indicator\" *ngIf=\"message.sender === 'agent' && !message.isComplete\">\n            <div class=\"typing-dot\"></div>\n            <div class=\"typing-dot\"></div>\n            <div class=\"typing-dot\"></div>\n          </span>\n        </div>\n        <div class=\"message-content\" [innerHTML]=\"message.content\"></div>\n        <div class=\"message-metadata\" *ngIf=\"message.metadata && message.metadata.executionTime\">\n          <span class=\"execution-time\">Execution time: {{ message.metadata.executionTime }}ms</span>\n        </div>\n        \n        <!-- Message reactions -->\n        <div class=\"message-reactions\">\n          <div class=\"reaction-buttons\">\n            <button class=\"reaction-button\" (click)=\"addReaction(message.id, 'like')\" \n                    [class.active]=\"message.reactions && message.reactions.includes('like')\">\n              👍 <span *ngIf=\"message.reactions && message.reactions.includes('like')\">1</span>\n            </button>\n            <button class=\"reaction-button\" (click)=\"addReaction(message.id, 'dislike')\"\n                    [class.active]=\"message.reactions && message.reactions.includes('dislike')\">\n              👎 <span *ngIf=\"message.reactions && message.reactions.includes('dislike')\">1</span>\n            </button>\n            <button class=\"reaction-button\" (click)=\"addReaction(message.id, 'love')\"\n                    [class.active]=\"message.reactions && message.reactions.includes('love')\">\n              ❤️ <span *ngIf=\"message.reactions && message.reactions.includes('love')\">1</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- Typing indicator outside of messages -->\n    <div *ngIf=\"agentTyping\" class=\"typing-indicator\">\n      <div class=\"typing-dot\"></div>\n      <div class=\"typing-dot\"></div>\n      <div class=\"typing-dot\"></div>\n    </div>\n    \n    <div *ngIf=\"(loading || messagesLoading) && !agentTyping\" class=\"loading-indicator\">\n      <div class=\"spinner\"></div>\n      <p>AI Agent is thinking...</p>\n    </div>\n  </div>\n  \n  <!-- Debug/Developer Tools - Agent Thinking Display -->\n  <div *ngIf=\"showAgentThinking && agentThinkingContent\" class=\"agent-thinking-panel\">\n    <div class=\"thinking-header\">\n      <h4>Agent Thinking Process</h4>\n      <button (click)=\"toggleAgentThinking()\">Close</button>\n    </div>\n    <pre class=\"thinking-content\">{{agentThinkingContent}}</pre>\n  </div>\n  \n  <!-- Long-running task banner -->\n  <div *ngIf=\"longTaskInProgress\" class=\"long-task-banner\">\n    <span class=\"spinner\"></span>\n    <span class=\"banner-text\">The AI assistant is working on a complex, long-running task. This may take several minutes. Please do not close this window.</span>\n  </div>\n  \n  <!-- Autonomous Agent Workflow Visualization -->\n  <div *ngIf=\"subtasks && subtasks.length > 0\" class=\"autonomous-workflow\">\n    <div class=\"workflow-header\">\n      <h4>Autonomous Agent Workflow</h4>\n    </div>\n    \n    <!-- Progress Stages as in Screenshot -->\n    <div class=\"progress-stages\">\n      <div class=\"stage-icon planning\" [class.active]=\"currentStage >= 1\">\n        <span>🔍</span>\n        <div>Planning</div>\n      </div>\n      <div class=\"stage-connector\" [class.active]=\"currentStage >= 1\"></div>\n      <div class=\"stage-icon design\" [class.active]=\"currentStage >= 2\">\n        <span>🎨</span>\n        <div>Design</div>\n      </div>\n      <div class=\"stage-connector\" [class.active]=\"currentStage >= 2\"></div>\n      <div class=\"stage-icon implementation\" [class.active]=\"currentStage >= 3\">\n        <span>🛠️</span>\n        <div>Implementation</div>\n      </div>\n      <div class=\"stage-connector\" [class.active]=\"currentStage >= 3\"></div>\n      <div class=\"stage-icon testing\" [class.active]=\"currentStage >= 4\">\n        <span>🧪</span>\n        <div>Testing</div>\n      </div>\n    </div>\n    \n    <!-- Simple Progress Counter -->\n    <div class=\"progress-counter\">\n      {{completedSubtasks}}/{{subtasks.length}} steps completed\n    </div>\n    \n    <!-- Subtask Cards Grid (Exactly as in Screenshots) -->\n    <div class=\"subtasks-grid\">\n      <div *ngFor=\"let subtask of subtasks; let i = index\" class=\"subtask-card\">\n        <!-- Card Header -->\n        <div class=\"subtask-header\">\n          <div class=\"subtask-number\">{{ i + 1 }}</div>\n          <div class=\"subtask-type\">{{ subtask.subtask.type || 'COMMAND' }}</div>\n          <div class=\"model-label\" *ngIf=\"subtask.model_type === 'openai'\">GPT</div>\n          <div class=\"model-label local\" *ngIf=\"subtask.model_type === 'local'\">Local LLM</div>\n          <div class=\"hot-label\" *ngIf=\"subtask.web_research_used\">HOT</div>\n        </div>\n        \n        <!-- Task Description -->\n        <div class=\"task-description\">\n          <div class=\"bullet\">▶</div>\n          <div>{{ subtask.subtask.description || '' }}</div>\n        </div>\n        \n        <!-- Output Area -->\n        <div class=\"output-area\">\n          <div class=\"output-label\">Output:</div>\n          <div class=\"output-content\" [class.error]=\"subtask.error\">\n            {{ subtask.result || 'Processing...' }}\n          </div>\n          \n          <!-- Additional outputs if available -->\n          <div class=\"web-results\" *ngIf=\"subtask.web_results\">Web Search Results</div>\n          <div class=\"file-changes\" *ngIf=\"subtask.file_diff\">File Changes</div>\n        </div>\n        \n        <!-- Simple Feedback UI -->\n        <div class=\"feedback-row\" *ngIf=\"subtask.completed\">\n          <span>Was this helpful?</span>\n          <div class=\"feedback-options\">\n            <button [class.selected]=\"subtask.feedback === 'up'\" (click)=\"setSubtaskFeedback(i, 'up')\"></button>\n            <button [class.selected]=\"subtask.feedback === 'down'\" (click)=\"setSubtaskFeedback(i, 'down')\"></button>\n          </div>\n        </div>\n        \n        <!-- Retry Button -->\n        <button *ngIf=\"subtask.error\" class=\"retry-button\" (click)=\"retrySubtask(i)\">\n          Retry\n        </button>\n      </div>\n    </div>\n  </div>\n  \n  <!-- We've completely hidden the file changes section to avoid display issues -->\n  <!-- If you need to see file changes, you can re-enable this section later -->\n  \n  <form [formGroup]=\"messageForm\" (ngSubmit)=\"sendMessage()\" class=\"message-form\">\n    <textarea \n      formControlName=\"message\" \n      placeholder=\"Type your message here...\" \n      [disabled]=\"loading || messagesLoading || messagesSaving\"\n      rows=\"3\"\n      (keydown.enter)=\"onEnter($any($event))\"\n    ></textarea>\n    <button type=\"submit\" [disabled]=\"messageForm.invalid || loading || messagesLoading || messagesSaving\">\n      <span>Send</span>\n    </button>\n    <button class=\"export-chat-btn\" type=\"button\" (click)=\"exportChat()\" [disabled]=\"messages.length === 0 || loading || messagesSaving\" title=\"Export chat history\">Export Chat</button>\n    <button class=\"export-chat-btn\" type=\"button\" (click)=\"exportDynamicChat()\" [disabled]=\"messages.length === 0 || loading || messagesSaving\" title=\"Export chat as file (dynamic, no storage)\">Export Dynamic Chat</button>\n    <button class=\"copy-chat-btn\" type=\"button\" (click)=\"copyChat()\" [disabled]=\"messages.length === 0 || loading || messagesSaving\" title=\"Copy entire chat to clipboard\">Copy Chat</button>\n    <div *ngIf=\"messagesSaving\" class=\"saving-indicator\">Saving messages...</div>\n  </form>\n</div>\n"], "mappings": "AAAA,SAA2CA,YAAY,QAAiD,eAAe;AACvH,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;ICavDC,EAAA,CAAAC,cAAA,iBAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5CH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,EAAA,CAAkB;IAACN,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,SAAA,CAAAI,IAAA,CAAgB;;;;;IAG/FT,EAAA,CAAAC,cAAA,iBAAiF;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5CH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAJ,EAAA,CAAkB;IAACN,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAE,SAAA,CAAAD,IAAA,CAAgB;;;;;IAEnGT,EAAA,CAAAC,cAAA,iBAAkE;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5CH,EAAA,CAAAI,UAAA,UAAAO,SAAA,CAAAL,EAAA,CAAkB;IAACN,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAG,SAAA,CAAAF,IAAA,CAAgB;;;;;IAMlFT,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAxCH,EAAA,CAAAI,UAAA,UAAAQ,OAAA,CAAAN,EAAA,CAAgB;IAACN,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAQ,iBAAA,CAAAI,OAAA,CAAAH,IAAA,CAAc;;;;;IA+BhFT,EAAA,CAAAC,cAAA,cAAuD;IAClDD,EAAA,CAAAE,MAAA,+DAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAsB7DH,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChDH,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC1EH,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClDH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxDH,EAAA,CAAAC,cAAA,WAAkD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC3DH,EAAA,CAAAC,cAAA,WAA8C;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACvDH,EAAA,CAAAC,cAAA,WAA4D;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAKjEH,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,uBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrDH,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAE,MAAA,4BAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACnFH,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC1DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2BAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACjEH,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC/DH,EAAA,CAAAC,cAAA,WAAkD;IAAAD,EAAA,CAAAE,MAAA,6BAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrEH,EAAA,CAAAC,cAAA,WAAkD;IAAAD,EAAA,CAAAE,MAAA,6BAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACrEH,EAAA,CAAAC,cAAA,WAAiD;IAAAD,EAAA,CAAAE,MAAA,4BAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACnEH,EAAA,CAAAC,cAAA,WAAmD;IAAAD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACnEH,EAAA,CAAAC,cAAA,WAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzEH,EAAA,CAAAC,cAAA,WAA8C;IAAAD,EAAA,CAAAE,MAAA,yBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7DH,EAAA,CAAAC,cAAA,WAA6C;IAAAD,EAAA,CAAAE,MAAA,wBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC3DH,EAAA,CAAAC,cAAA,WAA4D;IAAAD,EAAA,CAAAE,MAAA,gCAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGpFH,EAAA,CAAAC,cAAA,eAA6D;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAArCH,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAQ,iBAAA,CAAAK,WAAA,CAAAC,QAAA,CAAAC,OAAA,CAA8B;;;;;IAG3Ff,EAAA,CAAAC,cAAA,eAA4F;IAC1FD,EAAA,CAAAgB,SAAA,cAA8B;IAGhChB,EAAA,CAAAG,YAAA,EAAO;;;;;IAGTH,EAAA,CAAAC,cAAA,cAAyF;IAC1DD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7DH,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAiB,kBAAA,qBAAAJ,WAAA,CAAAC,QAAA,CAAAI,aAAA,OAAsD;;;;;IAQ5ElB,EAAA,CAAAC,cAAA,WAAsE;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI9EH,EAAA,CAAAC,cAAA,WAAyE;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAIjFH,EAAA,CAAAC,cAAA,WAAsE;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAzE3FH,EAAA,CAAAC,cAAA,cASK;IAUDD,EAAA,CAAAmB,UAAA,IAAAC,oCAAA,mBAAgD;IAChDpB,EAAA,CAAAmB,UAAA,IAAAE,oCAAA,mBAA0E;IAC1ErB,EAAA,CAAAmB,UAAA,IAAAG,oCAAA,mBAAkD;IAClDtB,EAAA,CAAAmB,UAAA,IAAAI,oCAAA,mBAAyD;IACzDvB,EAAA,CAAAmB,UAAA,IAAAK,oCAAA,mBAAwD;IACxDxB,EAAA,CAAAmB,UAAA,IAAAM,oCAAA,mBAA2D;IAC3DzB,EAAA,CAAAmB,UAAA,IAAAO,oCAAA,mBAAuD;IACvD1B,EAAA,CAAAmB,UAAA,IAAAQ,oCAAA,mBAAqE;IACvE3B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAoB;IAGdD,EAAA,CAAAmB,UAAA,KAAAS,qCAAA,mBAAqD;IACrD5B,EAAA,CAAAmB,UAAA,KAAAU,qCAAA,mBAAmF;IACnF7B,EAAA,CAAAmB,UAAA,KAAAW,qCAAA,mBAA0D;IAC1D9B,EAAA,CAAAmB,UAAA,KAAAY,qCAAA,mBAAiE;IACjE/B,EAAA,CAAAmB,UAAA,KAAAa,qCAAA,mBAA+D;IAC/DhC,EAAA,CAAAmB,UAAA,KAAAc,qCAAA,mBAAqE;IACrEjC,EAAA,CAAAmB,UAAA,KAAAe,qCAAA,mBAAqE;IACrElC,EAAA,CAAAmB,UAAA,KAAAgB,qCAAA,mBAAmE;IACnEnC,EAAA,CAAAmB,UAAA,KAAAiB,qCAAA,mBAAmE;IACnEpC,EAAA,CAAAmB,UAAA,KAAAkB,qCAAA,mBAAyE;IACzErC,EAAA,CAAAmB,UAAA,KAAAmB,qCAAA,mBAA6D;IAC7DtC,EAAA,CAAAmB,UAAA,KAAAoB,qCAAA,mBAA2D;IAC3DvC,EAAA,CAAAmB,UAAA,KAAAqB,qCAAA,mBAAkF;IACpFxC,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAsC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAmB,UAAA,KAAAsB,qCAAA,mBAAkG;IAGlGzC,EAAA,CAAAmB,UAAA,KAAAuB,qCAAA,mBAIO;IACT1C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAgB,SAAA,eAAiE;IACjEhB,EAAA,CAAAmB,UAAA,KAAAwB,oCAAA,kBAEM;IAGN3C,EAAA,CAAAC,cAAA,eAA+B;IAEKD,EAAA,CAAA4C,UAAA,mBAAAC,uDAAA;MAAA,MAAAC,WAAA,GAAA9C,EAAA,CAAA+C,aAAA,CAAAC,IAAA;MAAA,MAAAnC,WAAA,GAAAiC,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAmD,aAAA;MAAA,OAASnD,EAAA,CAAAoD,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAxC,WAAA,CAAAP,EAAA,EAAwB,MAAM,CAAC;IAAA,EAAC;IAEvEN,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAAmB,UAAA,KAAAmC,qCAAA,mBAA8E;IACnFtD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACoF;IADpDD,EAAA,CAAA4C,UAAA,mBAAAW,uDAAA;MAAA,MAAAT,WAAA,GAAA9C,EAAA,CAAA+C,aAAA,CAAAC,IAAA;MAAA,MAAAnC,WAAA,GAAAiC,WAAA,CAAAG,SAAA;MAAA,MAAAO,OAAA,GAAAxD,EAAA,CAAAmD,aAAA;MAAA,OAASnD,EAAA,CAAAoD,WAAA,CAAAI,OAAA,CAAAH,WAAA,CAAAxC,WAAA,CAAAP,EAAA,EAAwB,SAAS,CAAC;IAAA,EAAC;IAE1EN,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAAmB,UAAA,KAAAsC,qCAAA,mBAAiF;IACtFzD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACiF;IADjDD,EAAA,CAAA4C,UAAA,mBAAAc,uDAAA;MAAA,MAAAZ,WAAA,GAAA9C,EAAA,CAAA+C,aAAA,CAAAC,IAAA;MAAA,MAAAnC,WAAA,GAAAiC,WAAA,CAAAG,SAAA;MAAA,MAAAU,OAAA,GAAA3D,EAAA,CAAAmD,aAAA;MAAA,OAASnD,EAAA,CAAAoD,WAAA,CAAAO,OAAA,CAAAN,WAAA,CAAAxC,WAAA,CAAAP,EAAA,EAAwB,MAAM,CAAC;IAAA,EAAC;IAEvEN,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAAmB,UAAA,KAAAyC,qCAAA,mBAA8E;IACnF5D,EAAA,CAAAG,YAAA,EAAS;;;;IA1E+CH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA6D,eAAA,KAAAC,GAAA,EAAAjD,WAAA,CAAAkD,MAAA,aAAAlD,WAAA,CAAAkD,MAAA,cAAAlD,WAAA,CAAAkD,MAAA,eAAAlD,WAAA,CAAAmD,WAAA,gBAAAnD,WAAA,CAAAmD,WAAA,eAAAnD,WAAA,CAAAmD,WAAA,kBAAAnD,WAAA,CAAAmD,WAAA,cAAAnD,WAAA,CAAAkD,MAAA,iBAAAlD,WAAA,CAAAoD,UAAA,EAS5D;IACkBjE,EAAA,CAAAO,SAAA,GAQhB;IARgBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAkE,eAAA,KAAAC,GAAA,EAAAtD,WAAA,CAAAkD,MAAA,aAAAlD,WAAA,CAAAkD,MAAA,cAAAlD,WAAA,CAAAkD,MAAA,eAAAlD,WAAA,CAAAmD,WAAA,gBAAAnD,WAAA,CAAAmD,WAAA,eAAAnD,WAAA,CAAAmD,WAAA,kBAAAnD,WAAA,CAAAmD,WAAA,cAQhB;IACKhE,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAkD,MAAA,YAA+B;IAC/B/D,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAkD,MAAA,iBAAAlD,WAAA,CAAAmD,WAAA,CAAwD;IACxDhE,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAkD,MAAA,cAAiC;IACjC/D,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,eAAuC;IACvChE,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,cAAsC;IACtChE,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,iBAAyC;IACzChE,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,aAAqC;IACrChE,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,2BAAmD;IAK/ChE,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAkD,MAAA,YAA+B;IAC/B/D,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAkD,MAAA,iBAAAlD,WAAA,CAAAmD,WAAA,CAAwD;IACxDhE,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAkD,MAAA,cAAiC;IACjC/D,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,eAAuC;IACvChE,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,cAAsC;IACtChE,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,iBAAyC;IACzChE,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,iBAAyC;IACzChE,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,gBAAwC;IACxChE,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,kBAA0C;IAC1ChE,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,qBAA6C;IAC7ChE,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,aAAqC;IACrChE,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,YAAoC;IACpChE,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAmD,WAAA,2BAAmD;IAEpChE,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAoE,WAAA,SAAAvD,WAAA,CAAAwD,SAAA,WAAsC;IAClCrE,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAC,QAAA,kBAAAD,WAAA,CAAAC,QAAA,CAAAC,OAAA,CAA+B;IAGxBf,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAkD,MAAA,iBAAAlD,WAAA,CAAAoD,UAAA,CAAuD;IAM/DjE,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,cAAAS,WAAA,CAAAyD,OAAA,EAAAtE,EAAA,CAAAuE,cAAA,CAA6B;IAC3BvE,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAAC,QAAA,IAAAD,WAAA,CAAAC,QAAA,CAAAI,aAAA,CAAwD;IAQ3ElB,EAAA,CAAAO,SAAA,GAAwE;IAAxEP,EAAA,CAAAwE,WAAA,WAAA3D,WAAA,CAAA4D,SAAA,IAAA5D,WAAA,CAAA4D,SAAA,CAAAC,QAAA,SAAwE;IACpE1E,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAA4D,SAAA,IAAA5D,WAAA,CAAA4D,SAAA,CAAAC,QAAA,SAA6D;IAGjE1E,EAAA,CAAAO,SAAA,GAA2E;IAA3EP,EAAA,CAAAwE,WAAA,WAAA3D,WAAA,CAAA4D,SAAA,IAAA5D,WAAA,CAAA4D,SAAA,CAAAC,QAAA,YAA2E;IACvE1E,EAAA,CAAAO,SAAA,GAAgE;IAAhEP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAA4D,SAAA,IAAA5D,WAAA,CAAA4D,SAAA,CAAAC,QAAA,YAAgE;IAGpE1E,EAAA,CAAAO,SAAA,GAAwE;IAAxEP,EAAA,CAAAwE,WAAA,WAAA3D,WAAA,CAAA4D,SAAA,IAAA5D,WAAA,CAAA4D,SAAA,CAAAC,QAAA,SAAwE;IACpE1E,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAI,UAAA,SAAAS,WAAA,CAAA4D,SAAA,IAAA5D,WAAA,CAAA4D,SAAA,CAAAC,QAAA,SAA6D;;;;;IAQjF1E,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAgB,SAAA,cAA8B;IAGhChB,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAgB,SAAA,cAA2B;IAC3BhB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAKlCH,EAAA,CAAAC,cAAA,cAAoF;IAE5ED,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,iBAAwC;IAAhCD,EAAA,CAAA4C,UAAA,mBAAA+B,sDAAA;MAAA3E,EAAA,CAAA+C,aAAA,CAAA6B,IAAA;MAAA,MAAAC,OAAA,GAAA7E,EAAA,CAAAmD,aAAA;MAAA,OAASnD,EAAA,CAAAoD,WAAA,CAAAyB,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAAC9E,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAExDH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA9BH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAAuE,MAAA,CAAAC,oBAAA,CAAwB;;;;;IAIxDhF,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAgB,SAAA,eAA6B;IAC7BhB,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mIAA4H;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA4CvJH,EAAA,CAAAC,cAAA,eAAiE;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1EH,EAAA,CAAAC,cAAA,eAAsE;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACrFH,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAiBlEH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC7EH,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAIxEH,EAAA,CAAAC,cAAA,eAAoD;IAC5CD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9BH,EAAA,CAAAC,cAAA,eAA8B;IACyBD,EAAA,CAAA4C,UAAA,mBAAAqC,oEAAA;MAAAjF,EAAA,CAAA+C,aAAA,CAAAmC,IAAA;MAAA,MAAAC,KAAA,GAAAnF,EAAA,CAAAmD,aAAA,GAAAiC,KAAA;MAAA,MAAAC,OAAA,GAAArF,EAAA,CAAAmD,aAAA;MAAA,OAASnD,EAAA,CAAAoD,WAAA,CAAAiC,OAAA,CAAAC,kBAAA,CAAAH,KAAA,EAAsB,IAAI,CAAC;IAAA,EAAC;IAACnF,EAAA,CAAAG,YAAA,EAAS;IACpGH,EAAA,CAAAC,cAAA,iBAA+F;IAAxCD,EAAA,CAAA4C,UAAA,mBAAA2C,oEAAA;MAAAvF,EAAA,CAAA+C,aAAA,CAAAmC,IAAA;MAAA,MAAAC,KAAA,GAAAnF,EAAA,CAAAmD,aAAA,GAAAiC,KAAA;MAAA,MAAAI,OAAA,GAAAxF,EAAA,CAAAmD,aAAA;MAAA,OAASnD,EAAA,CAAAoD,WAAA,CAAAoC,OAAA,CAAAF,kBAAA,CAAAH,KAAA,EAAsB,MAAM,CAAC;IAAA,EAAC;IAACnF,EAAA,CAAAG,YAAA,EAAS;;;;IADhGH,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAwE,WAAA,aAAAiB,WAAA,CAAAC,QAAA,UAA4C;IAC5C1F,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAwE,WAAA,aAAAiB,WAAA,CAAAC,QAAA,YAA8C;;;;;;IAK1D1F,EAAA,CAAAC,cAAA,kBAA6E;IAA1BD,EAAA,CAAA4C,UAAA,mBAAA+C,uEAAA;MAAA3F,EAAA,CAAA+C,aAAA,CAAA6C,IAAA;MAAA,MAAAT,KAAA,GAAAnF,EAAA,CAAAmD,aAAA,GAAAiC,KAAA;MAAA,MAAAS,OAAA,GAAA7F,EAAA,CAAAmD,aAAA;MAAA,OAASnD,EAAA,CAAAoD,WAAA,CAAAyC,OAAA,CAAAC,YAAA,CAAAX,KAAA,CAAe;IAAA,EAAC;IAC1EnF,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAxCXH,EAAA,CAAAC,cAAA,cAA0E;IAG1CD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7CH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvEH,EAAA,CAAAmB,UAAA,IAAA4E,0CAAA,kBAA0E;IAC1E/F,EAAA,CAAAmB,UAAA,IAAA6E,0CAAA,kBAAqF;IACrFhG,EAAA,CAAAmB,UAAA,IAAA8E,0CAAA,kBAAkE;IACpEjG,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA8B;IACRD,EAAA,CAAAE,MAAA,cAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3BH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIpDH,EAAA,CAAAC,cAAA,eAAyB;IACGD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvCH,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAmB,UAAA,KAAA+E,2CAAA,mBAA6E;IAC7ElG,EAAA,CAAAmB,UAAA,KAAAgF,2CAAA,mBAAsE;IACxEnG,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAmB,UAAA,KAAAiF,2CAAA,mBAMM;IAGNpG,EAAA,CAAAmB,UAAA,KAAAkF,8CAAA,sBAES;IACXrG,EAAA,CAAAG,YAAA,EAAM;;;;;IAtC0BH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAQ,iBAAA,CAAA2E,KAAA,KAAW;IACbnF,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,iBAAA,CAAAiF,WAAA,CAAAa,OAAA,CAAAC,IAAA,cAAuC;IACvCvG,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAqF,WAAA,CAAAe,UAAA,cAAqC;IAC/BxG,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAI,UAAA,SAAAqF,WAAA,CAAAe,UAAA,aAAoC;IAC5CxG,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAqF,WAAA,CAAAgB,iBAAA,CAA+B;IAMlDzG,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,iBAAA,CAAAiF,WAAA,CAAAa,OAAA,CAAAI,WAAA,OAAuC;IAMhB1G,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAwE,WAAA,UAAAiB,WAAA,CAAAkB,KAAA,CAA6B;IACvD3G,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAwE,WAAA,CAAAmB,MAAA,yBACF;IAG0B5G,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAqF,WAAA,CAAAoB,WAAA,CAAyB;IACxB7G,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAqF,WAAA,CAAAqB,SAAA,CAAuB;IAIzB9G,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAqF,WAAA,CAAAsB,SAAA,CAAuB;IASzC/G,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAqF,WAAA,CAAAkB,KAAA,CAAmB;;;;;IAzElC3G,EAAA,CAAAC,cAAA,cAAyE;IAEjED,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIpCH,EAAA,CAAAC,cAAA,cAA6B;IAEnBD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACfH,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAErBH,EAAA,CAAAgB,SAAA,eAAsE;IACtEhB,EAAA,CAAAC,cAAA,eAAkE;IAC1DD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACfH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEnBH,EAAA,CAAAgB,SAAA,eAAsE;IACtEhB,EAAA,CAAAC,cAAA,eAA0E;IAClED,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChBH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE3BH,EAAA,CAAAgB,SAAA,eAAsE;IACtEhB,EAAA,CAAAC,cAAA,eAAmE;IAC3DD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACfH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAKtBH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAmB,UAAA,KAAA6F,oCAAA,oBAyCM;IACRhH,EAAA,CAAAG,YAAA,EAAM;;;;IAtE6BH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwE,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAItClH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwE,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAChClH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwE,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAIpClH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwE,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IACxBlH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwE,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAI5ClH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwE,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAC/BlH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwE,WAAA,WAAAyC,OAAA,CAAAC,YAAA,MAAkC;IAQlElH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAmH,kBAAA,MAAAF,OAAA,CAAAG,iBAAA,OAAAH,OAAA,CAAAI,QAAA,CAAAC,MAAA,sBACF;IAI2BtH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAI,UAAA,YAAA6G,OAAA,CAAAI,QAAA,CAAa;;;;;IA8DxCrH,EAAA,CAAAC,cAAA,eAAqD;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;;;;;;;;;ADpPjF,OAAM,MAAOoH,aAAa;EA6BxBC,YACUC,EAAe,EACfC,aAA4B,EAC5BC,cAA8B,EAC9BC,YAA0B;IAH1B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IAhCb,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,cAAc,GAAY,KAAK;IAC9B,KAAAC,YAAY,GAAG,IAAIlI,YAAY,EAAO;IACtC,KAAAmI,gBAAgB,GAAG,IAAInI,YAAY,EAAW;IAIxD,KAAAoI,QAAQ,GAAU,EAAE;IACpB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,aAAa,GAAW,yBAAyB;IACjD,KAAAC,cAAc,GAAU,CACtB;MAAEhI,EAAE,EAAE,4BAA4B;MAAEG,IAAI,EAAE;IAA4B;IACtE;IAAA,CACD;;IACD,KAAA8H,qBAAqB,GAAW,4BAA4B;IAC5D,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAnB,QAAQ,GAAU,EAAE;IACpB,KAAAoB,cAAc,GAAY,KAAK;IAC/B,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA1D,oBAAoB,GAAW,EAAE;IACjC,KAAA2D,iBAAiB,GAAY,KAAK;IAClC,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,eAAe,GAAY,IAAI;IAC/B,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,YAAY,GAAU,EAAE;EAOrB;EAEHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAA,cAAcA,CAAA;IACZ,IAAI;MACF,IAAI,IAAI,CAACC,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACC,aAAa,EAAE;QAClE,IAAI,CAACD,iBAAiB,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACF,iBAAiB,CAACC,aAAa,CAACE,YAAY;;KAErG,CAAC,OAAOC,GAAG,EAAE;EAChB;EAEAV,QAAQA,CAAA;IACNF,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI,CAACY,WAAW,GAAG,IAAI,CAACrC,EAAE,CAACsC,KAAK,CAAC;MAC/BC,OAAO,EAAE,CAAC,EAAE,EAAEjK,UAAU,CAACkK,QAAQ;KAClC,CAAC;EACJ;EAEAb,YAAYA,CAAA;IACVH,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE,IAAI,CAACrB,WAAW,CAAC;IACjF,IAAI,CAAC,IAAI,CAACA,WAAW,EAAE;MACrBoB,OAAO,CAACiB,IAAI,CAAC,8DAA8D,CAAC;MAC5E;;IAGF,IAAI,CAAC,IAAI,CAACpC,eAAe,EAAE;MACzB,IAAI,CAACK,OAAO,GAAG,IAAI;;IAGrB,IAAI,CAACR,cAAc,CAACwC,kBAAkB,CAAC,IAAI,CAACtC,WAAW,CAAC,CAACuC,SAAS,CAC/DC,QAAa,IAAI;MAChBpB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmB,QAAQ,CAAC;MACjE,IAAI,CAACnC,QAAQ,GAAGmC,QAAQ,CAACnC,QAAQ,IAAI,EAAE;MACvC,IAAI,CAACC,OAAO,GAAG,KAAK;IACtB,CAAC,EACAxB,KAAU,IAAI;MACbsC,OAAO,CAACtC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,IAAI,CAACwB,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEAkB,UAAUA,CAAA;IACRJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI,CAACtB,YAAY,CAAC0C,SAAS,EAAE,CAACF,SAAS,CACpCC,QAAa,IAAI;MAChBpB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmB,QAAQ,CAAC;MACvD,IAAI,CAACjC,MAAM,GAAGiC,QAAQ,CAACjC,MAAM,IAAI,EAAE;IACrC,CAAC,EACAzB,KAAU,IAAI;MACbsC,OAAO,CAACtC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE,CAAC,CACF;EACH;EAEA;;;EAGA4D,mBAAmBA,CAACC,QAAgB;IAClC,OAAO,IAAI,CAACpC,MAAM,CAACqC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACpK,EAAE,CAACqK,UAAU,CAAC,GAAGH,QAAQ,GAAG,CAAC,CAAC;EACzE;EAEA;;;EAGAI,cAAcA,CAAA;IACZ,MAAMC,cAAc,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;IAC7C,OAAO,IAAI,CAACzC,MAAM,CAACqC,MAAM,CAACC,KAAK,IAC7B,CAACG,cAAc,CAACC,IAAI,CAACN,QAAQ,IAAIE,KAAK,CAACpK,EAAE,CAACqK,UAAU,CAAC,GAAGH,QAAQ,GAAG,CAAC,CAAC,CACtE;EACH;EAEAlB,oBAAoBA,CAAA;IAClBL,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAE1D,IAAI,CAACxB,aAAa,CAACqD,EAAE,CAAC,eAAe,CAAC,CAACX,SAAS,CAAEY,IAAiB,IAAI;MACrE/B,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE8B,IAAI,CAAC;MAExE,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAACpD,WAAW,EAAE;QAC1C,IAAI,CAACK,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACuC,MAAM,CAACS,CAAC,IAAI,CAACA,CAAC,CAACC,yBAAyB,CAAC;QAEvE;QACA,MAAMnH,WAAW,GAAGgH,IAAI,CAACI,YAAY,IAAI,OAAO;QAEhD,IAAI,CAAClD,QAAQ,CAACmD,IAAI,CAAC;UACjB/K,EAAE,EAAE0K,IAAI,CAACM,UAAU,IAAI,OAAOC,IAAI,CAACC,GAAG,EAAE,EAAE;UAC1CzH,MAAM,EAAE,OAAO;UACfO,OAAO,EAAE0G,IAAI,CAAChB,OAAO;UACrB3F,SAAS,EAAE,IAAIkH,IAAI,EAAE;UACrBJ,yBAAyB,EAAE,KAAK;UAChCnH,WAAW,EAAEA,WAAW;UACxBS,SAAS,EAAE;SACZ,CAAC;QACF,IAAI,CAAC0D,OAAO,GAAG,KAAK;QACpBc,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;IAEnE,CAAC,CAAC;IAEF,IAAI,CAACxB,aAAa,CAACqD,EAAE,CAAC,cAAc,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAC5D/B,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE8B,IAAI,CAAC;MAEvE,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAACpD,WAAW,EAAE;QAC1C,IAAI,CAACa,WAAW,GAAGsC,IAAI,CAACS,SAAS;QACjC;QACA,IAAI,CAACT,IAAI,CAACS,SAAS,EAAE;UACnB,IAAI,CAACvD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACuC,MAAM,CAACS,CAAC,IAAI,CAACA,CAAC,CAACC,yBAAyB,CAAC;;;IAG7E,CAAC,CAAC;IAEF,IAAI,CAACzD,aAAa,CAACqD,EAAE,CAAC,oBAAoB,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAClE/B,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE8B,IAAI,CAAC;MAE7E,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAACpD,WAAW,EAAE;QAC1C;QACA,IAAI6D,gBAAgB,GAAG,IAAI,CAACxD,QAAQ,CAACyD,IAAI,CAACT,CAAC,IAAIA,CAAC,CAACnH,MAAM,KAAK,OAAO,IAAI,CAACmH,CAAC,CAACjH,UAAU,CAAC;QAErF,IAAI,CAACyH,gBAAgB,EAAE;UACrBA,gBAAgB,GAAG;YACjBpL,EAAE,EAAE,UAAUiL,IAAI,CAACC,GAAG,EAAE,EAAE;YAC1BzH,MAAM,EAAE,OAAO;YACfO,OAAO,EAAE,EAAE;YACXD,SAAS,EAAE,IAAIkH,IAAI,EAAE;YACrBtH,UAAU,EAAE,KAAK;YACjBQ,SAAS,EAAE;WACZ;UACD,IAAI,CAACyD,QAAQ,CAACmD,IAAI,CAACK,gBAAgB,CAAC;;QAGtC;QACAA,gBAAgB,CAACpH,OAAO,IAAI0G,IAAI,CAACY,KAAK;QACtC,IAAI,CAACpC,cAAc,EAAE;;IAEzB,CAAC,CAAC;IAEF,IAAI,CAAC9B,aAAa,CAACqD,EAAE,CAAC,gBAAgB,CAAC,CAACX,SAAS,CAAEY,IAAkB,IAAI;MACvE/B,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE8B,IAAI,CAAC;MAExE,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAACpD,WAAW,EAAE;QAC1C,IAAI,CAACM,OAAO,GAAG,KAAK;QACpB,IAAI,CAACO,WAAW,GAAG,KAAK;QAExB;QACA,IAAI,CAACR,QAAQ,CAAC2D,OAAO,CAAC7B,OAAO,IAAG;UAC9B,IAAIA,OAAO,CAACjG,MAAM,KAAK,OAAO,EAAE;YAC9BiG,OAAO,CAAC/F,UAAU,GAAG,IAAI;;QAE7B,CAAC,CAAC;QAEF;QACA,IAAI,CAACiE,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACuC,MAAM,CAACS,CAAC,IAAI,CAACA,CAAC,CAACC,yBAAyB,CAAC;QACvElC,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;;IAE7E,CAAC,CAAC;IAEF,IAAI,CAACxB,aAAa,CAACqD,EAAE,CAAC,gBAAgB,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAC9D/B,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE8B,IAAI,CAAC;MAEzE,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAACpD,WAAW,IAAI,IAAI,CAACc,iBAAiB,EAAE;QACpE;QACA,IAAI,CAAC3D,oBAAoB,GAAGgG,IAAI,CAACc,QAAQ;;IAE7C,CAAC,CAAC;IAEF,IAAI,CAACpE,aAAa,CAACqD,EAAE,CAAC,kBAAkB,CAAC,CAACX,SAAS,CAAEY,IAAS,IAAI;MAChE/B,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE8B,IAAI,CAAC;MAE3E,IAAIA,IAAI,CAACC,YAAY,KAAK,IAAI,CAACpD,WAAW,EAAE;QAC1C;QACA,MAAMkE,eAAe,GAAG,IAAI,CAAC7D,QAAQ,CAACyD,IAAI,CAACT,CAAC,IAAIA,CAAC,CAAC5K,EAAE,KAAK0K,IAAI,CAACM,UAAU,CAAC;QACzE,IAAIS,eAAe,EAAE;UACnB,IAAI,CAACA,eAAe,CAACtH,SAAS,EAAE;YAC9BsH,eAAe,CAACtH,SAAS,GAAG,EAAE;;UAEhC,IAAI,CAACsH,eAAe,CAACtH,SAAS,CAACC,QAAQ,CAACsG,IAAI,CAACgB,QAAQ,CAAC,EAAE;YACtDD,eAAe,CAACtH,SAAS,CAAC4G,IAAI,CAACL,IAAI,CAACgB,QAAQ,CAAC;;;;IAIrD,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACnC,WAAW,CAACoC,OAAO,EAAE;MAC5B;;IAGF,MAAMC,cAAc,GAAG,IAAI,CAACrC,WAAW,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK;IAC7D,IAAI,CAACF,cAAc,IAAI,CAAC,IAAI,CAACtE,WAAW,EAAE;MACxC;;IAGF;IACA,MAAMyE,aAAa,GAAG,OAAOf,IAAI,CAACC,GAAG,EAAE,EAAE;IACzC,IAAI,CAACtD,QAAQ,CAACmD,IAAI,CAAC;MACjB/K,EAAE,EAAEgM,aAAa;MACjBvI,MAAM,EAAE,MAAM;MACdO,OAAO,EAAE6H,cAAc;MACvB9H,SAAS,EAAE,IAAIkH,IAAI,EAAE;MACrB9G,SAAS,EAAE;KACZ,CAAC;IAEF;IACA,MAAM8H,cAAc,GAAG;MACrBtB,YAAY,EAAE,IAAI,CAACpD,WAAW;MAC9BmC,OAAO,EAAEmC,cAAc;MACvBK,QAAQ,EAAE,IAAI,CAACnE,aAAa;MAC5BoE,kBAAkB,EAAE,IAAI,CAAClE,qBAAqB;MAC9CmE,iBAAiB,EAAE,IAAI,CAAC9D;KACzB;IAED;IACA,MAAM+D,YAAY,GAAG;MACnBtI,SAAS,EAAE,IAAIkH,IAAI,EAAE;MACrBhF,IAAI,EAAE,SAAS;MACfqG,QAAQ,EAAE,aAAa,IAAI,CAAC/E,WAAW,WAAW;MAClDgF,OAAO,EAAEN;KACV;IACD,IAAI,CAACzD,WAAW,CAACuC,IAAI,CAACsB,YAAY,CAAC;IAEnC;IACA,IAAI,IAAI,CAAC9D,eAAe,EAAE;MACxB,IAAI,CAACX,QAAQ,CAACmD,IAAI,CAAC;QACjB/K,EAAE,EAAE,WAAWiL,IAAI,CAACC,GAAG,EAAE,EAAE;QAC3BzH,MAAM,EAAE,QAAQ;QAChBC,WAAW,EAAE,aAAa;QAC1BM,OAAO,EAAE,yCAAyCwI,IAAI,CAACC,SAAS,CAACR,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ;QACjGlI,SAAS,EAAE,IAAIkH,IAAI,EAAE;QACrB9G,SAAS,EAAE;OACZ,CAAC;;IAGJ;IACA,IAAI,CAACqF,WAAW,CAACkD,KAAK,EAAE;IAExB;IACA,IAAI,CAAC7E,OAAO,GAAG,IAAI;IAEnB;IACA,IAAI,IAAI,CAACS,gBAAgB,EAAE;MACzB;MACA,IAAI,CAAClB,aAAa,CAACuE,WAAW,CAAC,IAAI,CAACpE,WAAW,EAAEsE,cAAc,EAAE,IAAI,CAAC9D,aAAa,CAAC;KACrF,MAAM;MACL;MACA,IAAI,CAACT,YAAY,CAACqE,WAAW,CAC3B,IAAI,CAACpE,WAAW,EAChBsE,cAAc,EACd,IAAI,CAAC9D,aAAa,EAClB,IAAI,CAACE,qBAAqB,EAC1B,KAAK,CACN,CAAC6B,SAAS,CACRC,QAAa,IAAI;QAChB;QACA,MAAM4C,aAAa,GAAG;UACpB5I,SAAS,EAAE,IAAIkH,IAAI,EAAE;UACrBhF,IAAI,EAAE,UAAU;UAChBqG,QAAQ,EAAE,aAAa,IAAI,CAAC/E,WAAW,WAAW;UAClDgF,OAAO,EAAExC;SACV;QACD,IAAI,CAACtB,YAAY,CAACsC,IAAI,CAAC4B,aAAa,CAAC;QAErC;QACA,IAAI,IAAI,CAACpE,eAAe,EAAE;UACxB,IAAI,CAACX,QAAQ,CAACmD,IAAI,CAAC;YACjB/K,EAAE,EAAE,WAAWiL,IAAI,CAACC,GAAG,EAAE,EAAE;YAC3BzH,MAAM,EAAE,QAAQ;YAChBC,WAAW,EAAE,cAAc;YAC3BM,OAAO,EAAE,0CAA0CwI,IAAI,CAACC,SAAS,CAAC1C,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ;YAC5FhG,SAAS,EAAE,IAAIkH,IAAI,EAAE;YACrB9G,SAAS,EAAE;WACZ,CAAC;;QAGJ,IAAI,CAAC0D,OAAO,GAAG,KAAK;MACtB,CAAC,EACAxB,KAAU,IAAI;QACbsC,OAAO,CAACtC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAEhE;QACA,MAAMuG,UAAU,GAAG;UACjB7I,SAAS,EAAE,IAAIkH,IAAI,EAAE;UACrBhF,IAAI,EAAE,OAAO;UACbqG,QAAQ,EAAE,aAAa,IAAI,CAAC/E,WAAW,WAAW;UAClDgF,OAAO,EAAElG;SACV;QACD,IAAI,CAACoC,YAAY,CAACsC,IAAI,CAAC6B,UAAU,CAAC;QAElC;QACA,IAAI,CAAChF,QAAQ,CAACmD,IAAI,CAAC;UACjB/K,EAAE,EAAE,SAASiL,IAAI,CAACC,GAAG,EAAE,EAAE;UACzBzH,MAAM,EAAE,QAAQ;UAChBC,WAAW,EAAE,OAAO;UACpBM,OAAO,EAAE,uCAAuCwI,IAAI,CAACC,SAAS,CAACpG,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ;UACtFtC,SAAS,EAAE,IAAIkH,IAAI,EAAE;UACrB9G,SAAS,EAAE;SACZ,CAAC;QAEF,IAAI,CAAC0D,OAAO,GAAG,KAAK;MACtB,CAAC,CACF;;EAEL;EAEAgF,aAAaA,CAACpM,OAAe;IAC3BkI,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEnI,OAAO,CAAC;IACzD,IAAI,CAACsH,aAAa,GAAGtH,OAAO;EAC9B;EAEAqM,aAAaA,CAAChI,KAAa;IACzB,IAAI,CAAC8C,QAAQ,CAACmF,MAAM,CAACjI,KAAK,EAAE,CAAC,CAAC;EAChC;EAEAkI,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACzF,WAAW,EAAE;IACvB;IACA,IAAI,CAACK,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,CAACP,cAAc,CAAC4F,qBAAqB,CAAC,IAAI,CAAC1F,WAAW,CAAC,CAACuC,SAAS,CACnE,MAAK;MACHnB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAChE,CAAC,EACAvC,KAAK,IAAI;MACRsC,OAAO,CAACtC,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;IACtE,CAAC,CACF;EACH;EAEA6G,gBAAgBA,CAAA;IACd,IAAI,CAAChF,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,CAACP,gBAAgB,CAACwF,IAAI,CAAC,IAAI,CAACjF,cAAc,CAAC;EACjD;EAEAkF,OAAOA,CAACC,KAAoB;IAC1B,IAAIA,KAAK,CAACC,QAAQ,EAAE;MAClB,OAAO,CAAC;;;IAEVD,KAAK,CAACE,cAAc,EAAE;IACtB,IAAI,CAAC5B,WAAW,EAAE;EACpB;EAEA6B,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACjG,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,CAACoG,iBAAiB,CAAC,IAAI,CAAClG,WAAW,CAAC,CAACuC,SAAS,CAC9DC,QAAa,IAAI;MAChB2D,KAAK,CAAC,iBAAiB,IAAI3D,QAAQ,EAAE4D,SAAS,IAAI,SAAS,CAAC,CAAC;IAC/D,CAAC,EACAtH,KAAU,IAAI;MACbqH,KAAK,CAAC,yBAAyB,IAAIrH,KAAK,EAAEA,KAAK,EAAEuH,MAAM,IAAIvH,KAAK,CAAC,CAAC;IACpE,CAAC,CACF;EACH;EAEAwH,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACjG,QAAQ,CAACZ,MAAM,EAAE;IAC3B;IACA,MAAM8G,QAAQ,GAAG,IAAI,CAAClG,QAAQ,CAACmG,GAAG,CAACnD,CAAC,IAAI,IAAIA,CAAC,CAACnH,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,KAAKmH,CAAC,CAAC5G,OAAO,EAAE,CAAC,CAACgK,IAAI,CAAC,IAAI,CAAC;IAC1G,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,QAAQ,CAAC,EAAE;MAAE7H,IAAI,EAAE;IAAY,CAAE,CAAC;IACzD,MAAMkI,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC5C,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrC,MAAM1K,SAAS,GAAG,IAAIkH,IAAI,EAAE,CAACyD,WAAW,EAAE,CAACC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;IAChE,MAAMC,QAAQ,GAAG,GAAG,IAAI,CAACrH,WAAW,IAAI,MAAM,YAAYxD,SAAS,MAAM;IACzEwK,CAAC,CAACM,IAAI,GAAGV,GAAG;IACZI,CAAC,CAACO,QAAQ,GAAGF,QAAQ;IACrBJ,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,CAAC,CAAC;IAC5BA,CAAC,CAACU,KAAK,EAAE;IACTC,UAAU,CAAC,MAAK;MACdV,QAAQ,CAACO,IAAI,CAACI,WAAW,CAACZ,CAAC,CAAC;MAC5BH,MAAM,CAACC,GAAG,CAACe,eAAe,CAACjB,GAAG,CAAC;IACjC,CAAC,EAAE,CAAC,CAAC;EACP;EAEAkB,QAAQA,CAAA;IACN,MAAMvB,QAAQ,GAAG,IAAI,CAAClG,QAAQ,CAACmG,GAAG,CAACnD,CAAC,IAAI,IAAIA,CAAC,CAACnH,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,KAAKmH,CAAC,CAAC5G,OAAO,EAAE,CAAC,CAACgK,IAAI,CAAC,IAAI,CAAC;IAC1GsB,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1B,QAAQ,CAAC,CAAC2B,IAAI,CAC1C,MAAM/B,KAAK,CAAC,2BAA2B,CAAC,EACxC,MAAMA,KAAK,CAAC,mCAAmC,CAAC,CACjD;EACH;EAEAgC,qBAAqBA,CAACjP,OAAe;IACnC,IAAI,CAACwH,qBAAqB,GAAGxH,OAAO;IACpC;IACAkI,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEnI,OAAO,CAAC;EACrE;EAEA,IAAIqG,iBAAiBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE,OAAO,CAAC;IAC5B,OAAO,IAAI,CAACA,QAAQ,CAACoD,MAAM,CAACnE,OAAO,IAAI,CAACA,OAAO,CAACK,KAAK,CAAC,CAACW,MAAM;EAC/D;EAEAxB,YAAYA,CAACV,KAAa;IACxB;IACA,MAAMkB,OAAO,GAAG,IAAI,CAACe,QAAQ,CAACjC,KAAK,CAAC;IACpCkB,OAAO,CAACK,KAAK,GAAG,IAAI;IACpBL,OAAO,CAACM,MAAM,GAAG,aAAa;IAC9B;IACA4I,UAAU,CAAC,MAAK;MACdlJ,OAAO,CAACM,MAAM,GAAG,4BAA4B;MAC7CN,OAAO,CAACK,KAAK,GAAG,IAAI;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;EAEArB,kBAAkBA,CAACF,KAAa,EAAEM,QAAuB;IACvD,IAAI,CAAC2B,QAAQ,CAACjC,KAAK,CAAC,CAACM,QAAQ,GAAGA,QAAQ;EAC1C;EAEA,IAAIuK,kBAAkBA,CAAA;IACpB,IAAI,IAAI,CAAC9H,OAAO,EAAE,OAAO,IAAI;IAC7B,IAAI,IAAI,CAACd,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MAC7C,OAAO,IAAI,CAACD,QAAQ,CAACyD,IAAI,CAACoF,CAAC,IAAI,CAACA,CAAC,CAACtJ,MAAM,IAAI,CAACsJ,CAAC,CAACvJ,KAAK,CAAC;;IAEvD,OAAO,KAAK;EACd;EAEAwJ,oBAAoBA,CAAA;IAClB,IAAI,CAAC1H,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA;;;;;EAKA2H,cAAcA,CAACC,IAAY;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB;IACA,MAAMC,MAAM,GAAIJ,CAAS,IAAKA,CAAC,CAACjB,OAAO,CAAC,QAAQ,EAAEsB,CAAC,IAAK;MAAC,GAAG,EAAC,OAAO;MAAC,GAAG,EAAC,MAAM;MAAC,GAAG,EAAC;IAAM,CAAC,EAACA,CAAC,CAAC,IAAEA,CAAE,CAAC;IACnG,OAAO,OAAO,GAAGF,IAAI,CAACG,KAAK,CAAC,IAAI,CAAC,CAACnC,GAAG,CAACoC,IAAI,IAAG;MAC3C,IAAIA,IAAI,CAAC9F,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC8F,IAAI,CAAC9F,UAAU,CAAC,KAAK,CAAC,EAAE;QACnD,OAAO,4BAA4B2F,MAAM,CAACG,IAAI,CAAC,SAAS;OACzD,MAAM,IAAIA,IAAI,CAAC9F,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC8F,IAAI,CAAC9F,UAAU,CAAC,KAAK,CAAC,EAAE;QAC1D,OAAO,8BAA8B2F,MAAM,CAACG,IAAI,CAAC,SAAS;OAC3D,MAAM,IAAIA,IAAI,CAAC9F,UAAU,CAAC,IAAI,CAAC,EAAE;QAChC,OAAO,2BAA2B2F,MAAM,CAACG,IAAI,CAAC,SAAS;OACxD,MAAM;QACL,OAAOH,MAAM,CAACG,IAAI,CAAC;;IAEvB,CAAC,CAAC,CAACnC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ;EAC1B;EAEA;;;;EAIA,IAAIpH,YAAYA,CAAA;IACd,IAAI,CAAC,IAAI,CAACG,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC1D;IACA,IAAI,IAAI,CAACD,QAAQ,CAACqJ,KAAK,CAACR,CAAC,IAAIA,CAAC,CAACnJ,SAAS,IAAImJ,CAAC,CAACtJ,MAAM,IAAIsJ,CAAC,CAACvJ,KAAK,CAAC,EAAE,OAAO,CAAC;IAC1E;IACA;IACA,IAAIgK,KAAK,GAAG,CAAC;IACb,KAAK,MAAMrK,OAAO,IAAI,IAAI,CAACe,QAAQ,EAAE;MACnC,IAAIf,OAAO,CAACA,OAAO,EAAEC,IAAI,KAAK,QAAQ,EAAEoK,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC,KAC9D,IAAIrK,OAAO,CAACA,OAAO,EAAEC,IAAI,KAAK,gBAAgB,EAAEoK,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC,KAC3E,IAAIrK,OAAO,CAACA,OAAO,EAAEC,IAAI,KAAK,SAAS,EAAEoK,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACF,KAAK,EAAE,CAAC,CAAC;;IAE1E,OAAOA,KAAK;EACd;EAEAtN,WAAWA,CAACyN,SAAiB,EAAE9E,QAAgB;IAC7C/C,OAAO,CAACC,GAAG,CAAC,mCAAmC8C,QAAQ,eAAe8E,SAAS,EAAE,CAAC;IAElF;IACA,MAAM/E,eAAe,GAAG,IAAI,CAAC7D,QAAQ,CAACyD,IAAI,CAACT,CAAC,IAAIA,CAAC,CAAC5K,EAAE,KAAKwQ,SAAS,CAAC;IACnE,IAAI/E,eAAe,EAAE;MACnB,IAAI,CAACA,eAAe,CAACtH,SAAS,EAAE;QAC9BsH,eAAe,CAACtH,SAAS,GAAG,EAAE;;MAEhC,IAAI,CAACsH,eAAe,CAACtH,SAAS,CAACC,QAAQ,CAACsH,QAAQ,CAAC,EAAE;QACjDD,eAAe,CAACtH,SAAS,CAAC4G,IAAI,CAACW,QAAQ,CAAC;;;IAI5C;IACA,IAAI,CAACpE,YAAY,CAACmJ,kBAAkB,CAAC,IAAI,CAAClJ,WAAW,EAAEiJ,SAAS,EAAE9E,QAAQ,CAAC,CAAC5B,SAAS,CAClFC,QAAQ,IAAI;MACXpB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEmB,QAAQ,CAAC;IACzE,CAAC,EACA1D,KAAK,IAAI;MACRsC,OAAO,CAACtC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE;MACA,IAAIoF,eAAe,IAAIA,eAAe,CAACtH,SAAS,EAAE;QAChDsH,eAAe,CAACtH,SAAS,GAAGsH,eAAe,CAACtH,SAAS,CAACgG,MAAM,CAAEuG,CAAS,IAAKA,CAAC,KAAKhF,QAAQ,CAAC;;IAE/F,CAAC,CACF;EACH;EAEAiF,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACpJ,WAAW,EAAE;IAEvB,IAAI,CAACD,YAAY,CAACqJ,kBAAkB,CAAC,IAAI,CAACpJ,WAAW,CAAC,CAACuC,SAAS,CAC7DC,QAAQ,IAAI;MACXpB,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEmB,QAAQ,CAAC;MAC7E2D,KAAK,CAAC,qCAAqC,CAAC;IAC9C,CAAC,EACArH,KAAK,IAAI;MACRsC,OAAO,CAACtC,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzEqH,KAAK,CAAC,sCAAsC,CAAC;IAC/C,CAAC,CACF;EACH;EAEAlJ,mBAAmBA,CAAA;IACjB,IAAI,CAAC6D,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAEAuI,iBAAiBA,CAAA;IACf,IAAI,CAACrI,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5CI,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE,IAAI,CAACL,eAAe,CAAC;EACtF;;;uBA3iBWtB,aAAa,EAAAvH,EAAA,CAAAmR,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArR,EAAA,CAAAmR,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAvR,EAAA,CAAAmR,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAzR,EAAA,CAAAmR,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAbpK,aAAa;MAAAqK,SAAA;MAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;UCtB1B/R,EAAA,CAAAC,cAAA,aAAqE;UAGtCD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,cAAgG;UAC9FD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAETH,EAAA,CAAAC,cAAA,aAA4B;UAECD,EAAA,CAAAgB,SAAA,YAA2B;UAAChB,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvEH,EAAA,CAAAC,cAAA,iBAA6F;UAApED,EAAA,CAAA4C,UAAA,2BAAAqP,wDAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA3J,aAAA,GAAA6J,MAAA;UAAA,EAA2B,oBAAAC,iDAAA;YAAA,OAAWH,GAAA,CAAA7E,aAAA,CAAA6E,GAAA,CAAA3J,aAAA,CAA4B;UAAA,EAAvC;UAClDrI,EAAA,CAAAC,cAAA,oBAAyB;UACvBD,EAAA,CAAAmB,UAAA,KAAAiR,gCAAA,qBAAwG;UAC1GpS,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAC,cAAA,oBAA2B;UACzBD,EAAA,CAAAmB,UAAA,KAAAkR,gCAAA,qBAA0G;UAC5GrS,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAmB,UAAA,KAAAmR,gCAAA,qBAA2F;UAC7FtS,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAC,cAAA,eAAwC;UACVD,EAAA,CAAAgB,SAAA,aAA6B;UAAChB,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5EH,EAAA,CAAAC,cAAA,kBAAwH;UAA5FD,EAAA,CAAA4C,UAAA,2BAAA2P,wDAAAL,MAAA;YAAA,OAAAF,GAAA,CAAAzJ,qBAAA,GAAA2J,MAAA;UAAA,EAAmC,oBAAAM,iDAAA;YAAA,OAAWR,GAAA,CAAAhC,qBAAA,CAAAgC,GAAA,CAAAzJ,qBAAA,CAA4C;UAAA,EAAvD;UAC7DvI,EAAA,CAAAmB,UAAA,KAAAsR,gCAAA,qBAAmF;UACrFzS,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAC,cAAA,eAA8B;UAEkBD,EAAA,CAAA4C,UAAA,2BAAA8P,uDAAAR,MAAA;YAAA,OAAAF,GAAA,CAAApJ,gBAAA,GAAAsJ,MAAA;UAAA,EAA8B;UAA1ElS,EAAA,CAAAG,YAAA,EAA2E;UAC3EH,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGZH,EAAA,CAAAC,cAAA,eAA0B;UAC+CD,EAAA,CAAA4C,UAAA,mBAAA+P,gDAAA;YAAA,OAASX,GAAA,CAAA7B,oBAAA,EAAsB;UAAA,EAAC;UACrGnQ,EAAA,CAAAgB,SAAA,aAA2B;UAC3BhB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA8C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7DH,EAAA,CAAAC,cAAA,kBAA4F;UAAjED,EAAA,CAAA4C,UAAA,mBAAAgQ,gDAAA;YAAA,OAASZ,GAAA,CAAAf,kBAAA,EAAoB;UAAA,EAAC;UACvDjR,EAAA,CAAAgB,SAAA,aAA2B;UAC7BhB,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA4I;UAA7GD,EAAA,CAAA4C,UAAA,mBAAAiQ,gDAAA;YAAA,OAASb,GAAA,CAAAd,iBAAA,EAAmB;UAAA,EAAC;UAC1DlR,EAAA,CAAAgB,SAAA,aAAkC;UACpChB,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAgF;UAAjDD,EAAA,CAAA4C,UAAA,mBAAAkQ,gDAAA;YAAA,OAASd,GAAA,CAAA1E,SAAA,EAAW;UAAA,EAAC;UAClDtN,EAAA,CAAAgB,SAAA,aAA2B;UAC7BhB,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAkI;UAAlGD,EAAA,CAAA4C,UAAA,mBAAAmQ,gDAAA;YAAA,OAASf,GAAA,CAAAxE,gBAAA,EAAkB;UAAA,EAAC;UAC1DxN,EAAA,CAAAgB,SAAA,aAAoG;UACtGhB,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,mBAAmD;UACjDD,EAAA,CAAAmB,UAAA,KAAA6R,6BAAA,kBAEM;UAENhT,EAAA,CAAAmB,UAAA,KAAA8R,6BAAA,oBA8EM;;UAGNjT,EAAA,CAAAmB,UAAA,KAAA+R,6BAAA,kBAIM;UAENlT,EAAA,CAAAmB,UAAA,KAAAgS,6BAAA,kBAGM;UACRnT,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAmB,UAAA,KAAAiS,6BAAA,kBAMM;UAGNpT,EAAA,CAAAmB,UAAA,KAAAkS,6BAAA,kBAGM;UAGNrT,EAAA,CAAAmB,UAAA,KAAAmS,6BAAA,oBA8EM;UAKNtT,EAAA,CAAAC,cAAA,gBAAgF;UAAhDD,EAAA,CAAA4C,UAAA,sBAAA2Q,iDAAA;YAAA,OAAYvB,GAAA,CAAA/F,WAAA,EAAa;UAAA,EAAC;UACxDjM,EAAA,CAAAC,cAAA,oBAMC;UADCD,EAAA,CAAA4C,UAAA,2BAAA4Q,0DAAAtB,MAAA;YAAA,OAAiBF,GAAA,CAAAtE,OAAA,CAAAwE,MAAA,CAAqB;UAAA,EAAC;UACxClS,EAAA,CAAAG,YAAA,EAAW;UACZH,EAAA,CAAAC,cAAA,kBAAuG;UAC/FD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEnBH,EAAA,CAAAC,cAAA,kBAAiK;UAAnHD,EAAA,CAAA4C,UAAA,mBAAA6Q,gDAAA;YAAA,OAASzB,GAAA,CAAAlE,UAAA,EAAY;UAAA,EAAC;UAA6F9N,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrLH,EAAA,CAAAC,cAAA,kBAA8L;UAAhJD,EAAA,CAAA4C,UAAA,mBAAA8Q,gDAAA;YAAA,OAAS1B,GAAA,CAAA7D,iBAAA,EAAmB;UAAA,EAAC;UAAmHnO,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1NH,EAAA,CAAAC,cAAA,kBAAuK;UAA3HD,EAAA,CAAA4C,UAAA,mBAAA+Q,gDAAA;YAAA,OAAS3B,GAAA,CAAArC,QAAA,EAAU;UAAA,EAAC;UAAuG3P,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzLH,EAAA,CAAAmB,UAAA,KAAAyS,6BAAA,kBAA6E;UAC/E5T,EAAA,CAAAG,YAAA,EAAO;;;UA3QmBH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA6T,eAAA,KAAAC,GAAA,EAAA9B,GAAA,CAAAxJ,cAAA,EAAwC;UAKnCxI,EAAA,CAAAO,SAAA,GAAoE;UAApEP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA6T,eAAA,KAAAE,GAAA,EAAA/B,GAAA,CAAA7J,OAAA,IAAA6J,GAAA,CAAA/B,kBAAA,IAAA+B,GAAA,CAAAtJ,WAAA,EAAoE;UAC7F1I,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAiB,kBAAA,MAAA+Q,GAAA,CAAA7J,OAAA,IAAA6J,GAAA,CAAA/B,kBAAA,eAAA+B,GAAA,CAAAtJ,WAAA,8BACF;UAK2B1I,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAI,UAAA,YAAA4R,GAAA,CAAA3J,aAAA,CAA2B;UAEtBrI,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAI,UAAA,YAAA4R,GAAA,CAAAzH,mBAAA,WAAgC;UAGhCvK,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,YAAA4R,GAAA,CAAAzH,mBAAA,aAAkC;UAEpCvK,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAA4R,GAAA,CAAApH,cAAA,GAAmB;UAKnB5K,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAI,UAAA,YAAA4R,GAAA,CAAAzJ,qBAAA,CAAmC;UACrCvI,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAA4R,GAAA,CAAA1J,cAAA,CAAiB;UAKGtI,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAI,UAAA,YAAA4R,GAAA,CAAApJ,gBAAA,CAA8B;UAM9C5I,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA6T,eAAA,KAAAE,GAAA,EAAA/B,GAAA,CAAAvJ,cAAA,EAAsC;UAE9DzI,EAAA,CAAAO,SAAA,GAA8C;UAA9CP,EAAA,CAAAQ,iBAAA,CAAAwR,GAAA,CAAAvJ,cAAA,6BAA8C;UAKOzI,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA6T,eAAA,KAAAE,GAAA,EAAA/B,GAAA,CAAAnJ,eAAA,EAAuC;UAMvC7I,EAAA,CAAAO,SAAA,GAAoE;UAApEP,EAAA,CAAAgU,WAAA,eAAAhC,GAAA,CAAAxJ,cAAA,mCAAoE;UACjHxI,EAAA,CAAAO,SAAA,GAAiF;UAAjFP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAiU,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAAxJ,cAAA,GAAAwJ,GAAA,CAAAxJ,cAAA,EAAiF;UAM7FxI,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAI,UAAA,SAAA4R,GAAA,CAAA9J,QAAA,CAAAZ,MAAA,OAA2B;UAIRtH,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAmU,WAAA,SAAAnC,GAAA,CAAA9J,QAAA,EAAqB;UAiFxClI,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,SAAA4R,GAAA,CAAAtJ,WAAA,CAAiB;UAMjB1I,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAI,UAAA,UAAA4R,GAAA,CAAA7J,OAAA,IAAA6J,GAAA,CAAAlK,eAAA,MAAAkK,GAAA,CAAAtJ,WAAA,CAAkD;UAOpD1I,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAI,UAAA,SAAA4R,GAAA,CAAArJ,iBAAA,IAAAqJ,GAAA,CAAAhN,oBAAA,CAA+C;UAS/ChF,EAAA,CAAAO,SAAA,GAAwB;UAAxBP,EAAA,CAAAI,UAAA,SAAA4R,GAAA,CAAA/B,kBAAA,CAAwB;UAMxBjQ,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAI,UAAA,SAAA4R,GAAA,CAAA3K,QAAA,IAAA2K,GAAA,CAAA3K,QAAA,CAAAC,MAAA,KAAqC;UAmFrCtH,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAI,UAAA,cAAA4R,GAAA,CAAAlI,WAAA,CAAyB;UAI3B9J,EAAA,CAAAO,SAAA,GAAyD;UAAzDP,EAAA,CAAAI,UAAA,aAAA4R,GAAA,CAAA7J,OAAA,IAAA6J,GAAA,CAAAlK,eAAA,IAAAkK,GAAA,CAAAjK,cAAA,CAAyD;UAIrC/H,EAAA,CAAAO,SAAA,GAAgF;UAAhFP,EAAA,CAAAI,UAAA,aAAA4R,GAAA,CAAAlI,WAAA,CAAAoC,OAAA,IAAA8F,GAAA,CAAA7J,OAAA,IAAA6J,GAAA,CAAAlK,eAAA,IAAAkK,GAAA,CAAAjK,cAAA,CAAgF;UAGjC/H,EAAA,CAAAO,SAAA,GAA+D;UAA/DP,EAAA,CAAAI,UAAA,aAAA4R,GAAA,CAAA9J,QAAA,CAAAZ,MAAA,UAAA0K,GAAA,CAAA7J,OAAA,IAAA6J,GAAA,CAAAjK,cAAA,CAA+D;UACxD/H,EAAA,CAAAO,SAAA,GAA+D;UAA/DP,EAAA,CAAAI,UAAA,aAAA4R,GAAA,CAAA9J,QAAA,CAAAZ,MAAA,UAAA0K,GAAA,CAAA7J,OAAA,IAAA6J,GAAA,CAAAjK,cAAA,CAA+D;UAC1E/H,EAAA,CAAAO,SAAA,GAA+D;UAA/DP,EAAA,CAAAI,UAAA,aAAA4R,GAAA,CAAA9J,QAAA,CAAAZ,MAAA,UAAA0K,GAAA,CAAA7J,OAAA,IAAA6J,GAAA,CAAAjK,cAAA,CAA+D;UAC1H/H,EAAA,CAAAO,SAAA,GAAoB;UAApBP,EAAA,CAAAI,UAAA,SAAA4R,GAAA,CAAAjK,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}