{"ast": null, "code": "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { defaultBinaryType } from \"./transports/websocket-constructor.js\";\nexport class Socket extends Emitter {\n  /**\n   * Socket constructor.\n   *\n   * @param {String|Object} uri - uri or options\n   * @param {Object} opts - options\n   */\n  constructor(uri, opts = {}) {\n    super();\n    this.binaryType = defaultBinaryType;\n    this.writeBuffer = [];\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = null;\n    }\n    if (uri) {\n      uri = parse(uri);\n      opts.hostname = uri.host;\n      opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n      opts.port = uri.port;\n      if (uri.query) opts.query = uri.query;\n    } else if (opts.host) {\n      opts.hostname = parse(opts.host).host;\n    }\n    installTimerFunctions(this, opts);\n    this.secure = null != opts.secure ? opts.secure : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n    if (opts.hostname && !opts.port) {\n      // if no port is specified manually, use the protocol default\n      opts.port = this.secure ? \"443\" : \"80\";\n    }\n    this.hostname = opts.hostname || (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n    this.port = opts.port || (typeof location !== \"undefined\" && location.port ? location.port : this.secure ? \"443\" : \"80\");\n    this.transports = opts.transports || [\"polling\", \"websocket\", \"webtransport\"];\n    this.writeBuffer = [];\n    this.prevBufferLen = 0;\n    this.opts = Object.assign({\n      path: \"/engine.io\",\n      agent: false,\n      withCredentials: false,\n      upgrade: true,\n      timestampParam: \"t\",\n      rememberUpgrade: false,\n      addTrailingSlash: true,\n      rejectUnauthorized: true,\n      perMessageDeflate: {\n        threshold: 1024\n      },\n      transportOptions: {},\n      closeOnBeforeunload: false\n    }, opts);\n    this.opts.path = this.opts.path.replace(/\\/$/, \"\") + (this.opts.addTrailingSlash ? \"/\" : \"\");\n    if (typeof this.opts.query === \"string\") {\n      this.opts.query = decode(this.opts.query);\n    }\n    // set on handshake\n    this.id = null;\n    this.upgrades = null;\n    this.pingInterval = null;\n    this.pingTimeout = null;\n    // set on heartbeat\n    this.pingTimeoutTimer = null;\n    if (typeof addEventListener === \"function\") {\n      if (this.opts.closeOnBeforeunload) {\n        // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n        // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n        // closed/reloaded)\n        this.beforeunloadEventListener = () => {\n          if (this.transport) {\n            // silently close the transport\n            this.transport.removeAllListeners();\n            this.transport.close();\n          }\n        };\n        addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n      }\n      if (this.hostname !== \"localhost\") {\n        this.offlineEventListener = () => {\n          this.onClose(\"transport close\", {\n            description: \"network connection lost\"\n          });\n        };\n        addEventListener(\"offline\", this.offlineEventListener, false);\n      }\n    }\n    this.open();\n  }\n  /**\n   * Creates transport of the given type.\n   *\n   * @param {String} name - transport name\n   * @return {Transport}\n   * @private\n   */\n  createTransport(name) {\n    const query = Object.assign({}, this.opts.query);\n    // append engine.io protocol identifier\n    query.EIO = protocol;\n    // transport name\n    query.transport = name;\n    // session id if we already have one\n    if (this.id) query.sid = this.id;\n    const opts = Object.assign({}, this.opts, {\n      query,\n      socket: this,\n      hostname: this.hostname,\n      secure: this.secure,\n      port: this.port\n    }, this.opts.transportOptions[name]);\n    return new transports[name](opts);\n  }\n  /**\n   * Initializes transport to use and starts probe.\n   *\n   * @private\n   */\n  open() {\n    let transport;\n    if (this.opts.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf(\"websocket\") !== -1) {\n      transport = \"websocket\";\n    } else if (0 === this.transports.length) {\n      // Emit error on next tick so it can be listened to\n      this.setTimeoutFn(() => {\n        this.emitReserved(\"error\", \"No transports available\");\n      }, 0);\n      return;\n    } else {\n      transport = this.transports[0];\n    }\n    this.readyState = \"opening\";\n    // Retry with the next transport if the transport is disabled (jsonp: false)\n    try {\n      transport = this.createTransport(transport);\n    } catch (e) {\n      this.transports.shift();\n      this.open();\n      return;\n    }\n    transport.open();\n    this.setTransport(transport);\n  }\n  /**\n   * Sets the current transport. Disables the existing one (if any).\n   *\n   * @private\n   */\n  setTransport(transport) {\n    if (this.transport) {\n      this.transport.removeAllListeners();\n    }\n    // set up transport\n    this.transport = transport;\n    // set up transport listeners\n    transport.on(\"drain\", this.onDrain.bind(this)).on(\"packet\", this.onPacket.bind(this)).on(\"error\", this.onError.bind(this)).on(\"close\", reason => this.onClose(\"transport close\", reason));\n  }\n  /**\n   * Probes a transport.\n   *\n   * @param {String} name - transport name\n   * @private\n   */\n  probe(name) {\n    let transport = this.createTransport(name);\n    let failed = false;\n    Socket.priorWebsocketSuccess = false;\n    const onTransportOpen = () => {\n      if (failed) return;\n      transport.send([{\n        type: \"ping\",\n        data: \"probe\"\n      }]);\n      transport.once(\"packet\", msg => {\n        if (failed) return;\n        if (\"pong\" === msg.type && \"probe\" === msg.data) {\n          this.upgrading = true;\n          this.emitReserved(\"upgrading\", transport);\n          if (!transport) return;\n          Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n          this.transport.pause(() => {\n            if (failed) return;\n            if (\"closed\" === this.readyState) return;\n            cleanup();\n            this.setTransport(transport);\n            transport.send([{\n              type: \"upgrade\"\n            }]);\n            this.emitReserved(\"upgrade\", transport);\n            transport = null;\n            this.upgrading = false;\n            this.flush();\n          });\n        } else {\n          const err = new Error(\"probe error\");\n          // @ts-ignore\n          err.transport = transport.name;\n          this.emitReserved(\"upgradeError\", err);\n        }\n      });\n    };\n    function freezeTransport() {\n      if (failed) return;\n      // Any callback called by transport should be ignored since now\n      failed = true;\n      cleanup();\n      transport.close();\n      transport = null;\n    }\n    // Handle any error that happens while probing\n    const onerror = err => {\n      const error = new Error(\"probe error: \" + err);\n      // @ts-ignore\n      error.transport = transport.name;\n      freezeTransport();\n      this.emitReserved(\"upgradeError\", error);\n    };\n    function onTransportClose() {\n      onerror(\"transport closed\");\n    }\n    // When the socket is closed while we're probing\n    function onclose() {\n      onerror(\"socket closed\");\n    }\n    // When the socket is upgraded while we're probing\n    function onupgrade(to) {\n      if (transport && to.name !== transport.name) {\n        freezeTransport();\n      }\n    }\n    // Remove all listeners on the transport and on self\n    const cleanup = () => {\n      transport.removeListener(\"open\", onTransportOpen);\n      transport.removeListener(\"error\", onerror);\n      transport.removeListener(\"close\", onTransportClose);\n      this.off(\"close\", onclose);\n      this.off(\"upgrading\", onupgrade);\n    };\n    transport.once(\"open\", onTransportOpen);\n    transport.once(\"error\", onerror);\n    transport.once(\"close\", onTransportClose);\n    this.once(\"close\", onclose);\n    this.once(\"upgrading\", onupgrade);\n    if (this.upgrades.indexOf(\"webtransport\") !== -1 && name !== \"webtransport\") {\n      // favor WebTransport\n      this.setTimeoutFn(() => {\n        if (!failed) {\n          transport.open();\n        }\n      }, 200);\n    } else {\n      transport.open();\n    }\n  }\n  /**\n   * Called when connection is deemed open.\n   *\n   * @private\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n    this.emitReserved(\"open\");\n    this.flush();\n    // we check for `readyState` in case an `open`\n    // listener already closed the socket\n    if (\"open\" === this.readyState && this.opts.upgrade) {\n      let i = 0;\n      const l = this.upgrades.length;\n      for (; i < l; i++) {\n        this.probe(this.upgrades[i]);\n      }\n    }\n  }\n  /**\n   * Handles a packet.\n   *\n   * @private\n   */\n  onPacket(packet) {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n      this.emitReserved(\"packet\", packet);\n      // Socket is live - any packet counts\n      this.emitReserved(\"heartbeat\");\n      this.resetPingTimeout();\n      switch (packet.type) {\n        case \"open\":\n          this.onHandshake(JSON.parse(packet.data));\n          break;\n        case \"ping\":\n          this.sendPacket(\"pong\");\n          this.emitReserved(\"ping\");\n          this.emitReserved(\"pong\");\n          break;\n        case \"error\":\n          const err = new Error(\"server error\");\n          // @ts-ignore\n          err.code = packet.data;\n          this.onError(err);\n          break;\n        case \"message\":\n          this.emitReserved(\"data\", packet.data);\n          this.emitReserved(\"message\", packet.data);\n          break;\n      }\n    } else {}\n  }\n  /**\n   * Called upon handshake completion.\n   *\n   * @param {Object} data - handshake obj\n   * @private\n   */\n  onHandshake(data) {\n    this.emitReserved(\"handshake\", data);\n    this.id = data.sid;\n    this.transport.query.sid = data.sid;\n    this.upgrades = this.filterUpgrades(data.upgrades);\n    this.pingInterval = data.pingInterval;\n    this.pingTimeout = data.pingTimeout;\n    this.maxPayload = data.maxPayload;\n    this.onOpen();\n    // In case open handler closes socket\n    if (\"closed\" === this.readyState) return;\n    this.resetPingTimeout();\n  }\n  /**\n   * Sets and resets ping timeout timer based on server pings.\n   *\n   * @private\n   */\n  resetPingTimeout() {\n    this.clearTimeoutFn(this.pingTimeoutTimer);\n    this.pingTimeoutTimer = this.setTimeoutFn(() => {\n      this.onClose(\"ping timeout\");\n    }, this.pingInterval + this.pingTimeout);\n    if (this.opts.autoUnref) {\n      this.pingTimeoutTimer.unref();\n    }\n  }\n  /**\n   * Called on `drain` event\n   *\n   * @private\n   */\n  onDrain() {\n    this.writeBuffer.splice(0, this.prevBufferLen);\n    // setting prevBufferLen = 0 is very important\n    // for example, when upgrading, upgrade packet is sent over,\n    // and a nonzero prevBufferLen could cause problems on `drain`\n    this.prevBufferLen = 0;\n    if (0 === this.writeBuffer.length) {\n      this.emitReserved(\"drain\");\n    } else {\n      this.flush();\n    }\n  }\n  /**\n   * Flush write buffers.\n   *\n   * @private\n   */\n  flush() {\n    if (\"closed\" !== this.readyState && this.transport.writable && !this.upgrading && this.writeBuffer.length) {\n      const packets = this.getWritablePackets();\n      this.transport.send(packets);\n      // keep track of current length of writeBuffer\n      // splice writeBuffer and callbackBuffer on `drain`\n      this.prevBufferLen = packets.length;\n      this.emitReserved(\"flush\");\n    }\n  }\n  /**\n   * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n   * long-polling)\n   *\n   * @private\n   */\n  getWritablePackets() {\n    const shouldCheckPayloadSize = this.maxPayload && this.transport.name === \"polling\" && this.writeBuffer.length > 1;\n    if (!shouldCheckPayloadSize) {\n      return this.writeBuffer;\n    }\n    let payloadSize = 1; // first packet type\n    for (let i = 0; i < this.writeBuffer.length; i++) {\n      const data = this.writeBuffer[i].data;\n      if (data) {\n        payloadSize += byteLength(data);\n      }\n      if (i > 0 && payloadSize > this.maxPayload) {\n        return this.writeBuffer.slice(0, i);\n      }\n      payloadSize += 2; // separator + packet type\n    }\n\n    return this.writeBuffer;\n  }\n  /**\n   * Sends a message.\n   *\n   * @param {String} msg - message.\n   * @param {Object} options.\n   * @param {Function} callback function.\n   * @return {Socket} for chaining.\n   */\n  write(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n  send(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n  /**\n   * Sends a packet.\n   *\n   * @param {String} type: packet type.\n   * @param {String} data.\n   * @param {Object} options.\n   * @param {Function} fn - callback function.\n   * @private\n   */\n  sendPacket(type, data, options, fn) {\n    if (\"function\" === typeof data) {\n      fn = data;\n      data = undefined;\n    }\n    if (\"function\" === typeof options) {\n      fn = options;\n      options = null;\n    }\n    if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n      return;\n    }\n    options = options || {};\n    options.compress = false !== options.compress;\n    const packet = {\n      type: type,\n      data: data,\n      options: options\n    };\n    this.emitReserved(\"packetCreate\", packet);\n    this.writeBuffer.push(packet);\n    if (fn) this.once(\"flush\", fn);\n    this.flush();\n  }\n  /**\n   * Closes the connection.\n   */\n  close() {\n    const close = () => {\n      this.onClose(\"forced close\");\n      this.transport.close();\n    };\n    const cleanupAndClose = () => {\n      this.off(\"upgrade\", cleanupAndClose);\n      this.off(\"upgradeError\", cleanupAndClose);\n      close();\n    };\n    const waitForUpgrade = () => {\n      // wait for upgrade to finish since we can't send packets while pausing a transport\n      this.once(\"upgrade\", cleanupAndClose);\n      this.once(\"upgradeError\", cleanupAndClose);\n    };\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.readyState = \"closing\";\n      if (this.writeBuffer.length) {\n        this.once(\"drain\", () => {\n          if (this.upgrading) {\n            waitForUpgrade();\n          } else {\n            close();\n          }\n        });\n      } else if (this.upgrading) {\n        waitForUpgrade();\n      } else {\n        close();\n      }\n    }\n    return this;\n  }\n  /**\n   * Called upon transport error\n   *\n   * @private\n   */\n  onError(err) {\n    Socket.priorWebsocketSuccess = false;\n    this.emitReserved(\"error\", err);\n    this.onClose(\"transport error\", err);\n  }\n  /**\n   * Called upon transport close.\n   *\n   * @private\n   */\n  onClose(reason, description) {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n      // clear timers\n      this.clearTimeoutFn(this.pingTimeoutTimer);\n      // stop event from firing again for transport\n      this.transport.removeAllListeners(\"close\");\n      // ensure transport won't stay open\n      this.transport.close();\n      // ignore further transport communication\n      this.transport.removeAllListeners();\n      if (typeof removeEventListener === \"function\") {\n        removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n        removeEventListener(\"offline\", this.offlineEventListener, false);\n      }\n      // set ready state\n      this.readyState = \"closed\";\n      // clear session id\n      this.id = null;\n      // emit close event\n      this.emitReserved(\"close\", reason, description);\n      // clean buffers after, so users can still\n      // grab the buffers on `close` event\n      this.writeBuffer = [];\n      this.prevBufferLen = 0;\n    }\n  }\n  /**\n   * Filters upgrades, returning only those matching client transports.\n   *\n   * @param {Array} upgrades - server upgrades\n   * @private\n   */\n  filterUpgrades(upgrades) {\n    const filteredUpgrades = [];\n    let i = 0;\n    const j = upgrades.length;\n    for (; i < j; i++) {\n      if (~this.transports.indexOf(upgrades[i])) filteredUpgrades.push(upgrades[i]);\n    }\n    return filteredUpgrades;\n  }\n}\nSocket.protocol = protocol;", "map": {"version": 3, "names": ["transports", "installTimerFunctions", "byteLength", "decode", "parse", "Emitter", "protocol", "defaultBinaryType", "Socket", "constructor", "uri", "opts", "binaryType", "writeBuffer", "hostname", "host", "secure", "port", "query", "location", "prevBufferLen", "Object", "assign", "path", "agent", "withCredentials", "upgrade", "timestampParam", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "closeOnBeforeunload", "replace", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "addEventListener", "beforeunloadEventListener", "transport", "removeAllListeners", "close", "offlineEventListener", "onClose", "description", "open", "createTransport", "name", "EIO", "sid", "socket", "priorWebsocketSuccess", "indexOf", "length", "setTimeoutFn", "emit<PERSON><PERSON><PERSON><PERSON>", "readyState", "e", "shift", "setTransport", "on", "onDrain", "bind", "onPacket", "onError", "reason", "probe", "failed", "onTransportOpen", "send", "type", "data", "once", "msg", "upgrading", "pause", "cleanup", "flush", "err", "Error", "freezeTransport", "onerror", "error", "onTransportClose", "onclose", "onupgrade", "to", "removeListener", "off", "onOpen", "i", "l", "packet", "resetPingTimeout", "onHandshake", "JSON", "sendPacket", "code", "filterUpgrades", "maxPayload", "clearTimeoutFn", "autoUnref", "unref", "splice", "writable", "packets", "getWritablePackets", "shouldCheckPayloadSize", "payloadSize", "slice", "write", "options", "fn", "undefined", "compress", "push", "cleanupAndClose", "waitForUpgrade", "removeEventListener", "filteredUpgrades", "j"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/engine.io-client/build/esm/socket.js"], "sourcesContent": ["import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { defaultBinaryType } from \"./transports/websocket-constructor.js\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\n            \"polling\",\n            \"websocket\",\n            \"webtransport\",\n        ];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this.upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            this.resetPingTimeout();\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,WAAW;AAC7D,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,OAAO,MAAMC,MAAM,SAASH,OAAO,CAAC;EAChC;AACJ;AACA;AACA;AACA;AACA;EACII,WAAWA,CAACC,GAAG,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IACxB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,UAAU,GAAGL,iBAAiB;IACnC,IAAI,CAACM,WAAW,GAAG,EAAE;IACrB,IAAIH,GAAG,IAAI,QAAQ,KAAK,OAAOA,GAAG,EAAE;MAChCC,IAAI,GAAGD,GAAG;MACVA,GAAG,GAAG,IAAI;IACd;IACA,IAAIA,GAAG,EAAE;MACLA,GAAG,GAAGN,KAAK,CAACM,GAAG,CAAC;MAChBC,IAAI,CAACG,QAAQ,GAAGJ,GAAG,CAACK,IAAI;MACxBJ,IAAI,CAACK,MAAM,GAAGN,GAAG,CAACJ,QAAQ,KAAK,OAAO,IAAII,GAAG,CAACJ,QAAQ,KAAK,KAAK;MAChEK,IAAI,CAACM,IAAI,GAAGP,GAAG,CAACO,IAAI;MACpB,IAAIP,GAAG,CAACQ,KAAK,EACTP,IAAI,CAACO,KAAK,GAAGR,GAAG,CAACQ,KAAK;IAC9B,CAAC,MACI,IAAIP,IAAI,CAACI,IAAI,EAAE;MAChBJ,IAAI,CAACG,QAAQ,GAAGV,KAAK,CAACO,IAAI,CAACI,IAAI,CAAC,CAACA,IAAI;IACzC;IACAd,qBAAqB,CAAC,IAAI,EAAEU,IAAI,CAAC;IACjC,IAAI,CAACK,MAAM,GACP,IAAI,IAAIL,IAAI,CAACK,MAAM,GACbL,IAAI,CAACK,MAAM,GACX,OAAOG,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAKA,QAAQ,CAACb,QAAQ;IAC3E,IAAIK,IAAI,CAACG,QAAQ,IAAI,CAACH,IAAI,CAACM,IAAI,EAAE;MAC7B;MACAN,IAAI,CAACM,IAAI,GAAG,IAAI,CAACD,MAAM,GAAG,KAAK,GAAG,IAAI;IAC1C;IACA,IAAI,CAACF,QAAQ,GACTH,IAAI,CAACG,QAAQ,KACR,OAAOK,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACL,QAAQ,GAAG,WAAW,CAAC;IAC3E,IAAI,CAACG,IAAI,GACLN,IAAI,CAACM,IAAI,KACJ,OAAOE,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACF,IAAI,GAC3CE,QAAQ,CAACF,IAAI,GACb,IAAI,CAACD,MAAM,GACP,KAAK,GACL,IAAI,CAAC;IACvB,IAAI,CAAChB,UAAU,GAAGW,IAAI,CAACX,UAAU,IAAI,CACjC,SAAS,EACT,WAAW,EACX,cAAc,CACjB;IACD,IAAI,CAACa,WAAW,GAAG,EAAE;IACrB,IAAI,CAACO,aAAa,GAAG,CAAC;IACtB,IAAI,CAACT,IAAI,GAAGU,MAAM,CAACC,MAAM,CAAC;MACtBC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,KAAK;MACZC,eAAe,EAAE,KAAK;MACtBC,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE,GAAG;MACnBC,eAAe,EAAE,KAAK;MACtBC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,iBAAiB,EAAE;QACfC,SAAS,EAAE;MACf,CAAC;MACDC,gBAAgB,EAAE,CAAC,CAAC;MACpBC,mBAAmB,EAAE;IACzB,CAAC,EAAEvB,IAAI,CAAC;IACR,IAAI,CAACA,IAAI,CAACY,IAAI,GACV,IAAI,CAACZ,IAAI,CAACY,IAAI,CAACY,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAC5B,IAAI,CAACxB,IAAI,CAACkB,gBAAgB,GAAG,GAAG,GAAG,EAAE,CAAC;IAC/C,IAAI,OAAO,IAAI,CAAClB,IAAI,CAACO,KAAK,KAAK,QAAQ,EAAE;MACrC,IAAI,CAACP,IAAI,CAACO,KAAK,GAAGf,MAAM,CAAC,IAAI,CAACQ,IAAI,CAACO,KAAK,CAAC;IAC7C;IACA;IACA,IAAI,CAACkB,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;MACxC,IAAI,IAAI,CAAC9B,IAAI,CAACuB,mBAAmB,EAAE;QAC/B;QACA;QACA;QACA,IAAI,CAACQ,yBAAyB,GAAG,MAAM;UACnC,IAAI,IAAI,CAACC,SAAS,EAAE;YAChB;YACA,IAAI,CAACA,SAAS,CAACC,kBAAkB,CAAC,CAAC;YACnC,IAAI,CAACD,SAAS,CAACE,KAAK,CAAC,CAAC;UAC1B;QACJ,CAAC;QACDJ,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACC,yBAAyB,EAAE,KAAK,CAAC;MAC3E;MACA,IAAI,IAAI,CAAC5B,QAAQ,KAAK,WAAW,EAAE;QAC/B,IAAI,CAACgC,oBAAoB,GAAG,MAAM;UAC9B,IAAI,CAACC,OAAO,CAAC,iBAAiB,EAAE;YAC5BC,WAAW,EAAE;UACjB,CAAC,CAAC;QACN,CAAC;QACDP,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACK,oBAAoB,EAAE,KAAK,CAAC;MACjE;IACJ;IACA,IAAI,CAACG,IAAI,CAAC,CAAC;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,IAAI,EAAE;IAClB,MAAMjC,KAAK,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACX,IAAI,CAACO,KAAK,CAAC;IAChD;IACAA,KAAK,CAACkC,GAAG,GAAG9C,QAAQ;IACpB;IACAY,KAAK,CAACyB,SAAS,GAAGQ,IAAI;IACtB;IACA,IAAI,IAAI,CAACf,EAAE,EACPlB,KAAK,CAACmC,GAAG,GAAG,IAAI,CAACjB,EAAE;IACvB,MAAMzB,IAAI,GAAGU,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACX,IAAI,EAAE;MACtCO,KAAK;MACLoC,MAAM,EAAE,IAAI;MACZxC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBE,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,IAAI,EAAE,IAAI,CAACA;IACf,CAAC,EAAE,IAAI,CAACN,IAAI,CAACsB,gBAAgB,CAACkB,IAAI,CAAC,CAAC;IACpC,OAAO,IAAInD,UAAU,CAACmD,IAAI,CAAC,CAACxC,IAAI,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIsC,IAAIA,CAAA,EAAG;IACH,IAAIN,SAAS;IACb,IAAI,IAAI,CAAChC,IAAI,CAACiB,eAAe,IACzBpB,MAAM,CAAC+C,qBAAqB,IAC5B,IAAI,CAACvD,UAAU,CAACwD,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7Cb,SAAS,GAAG,WAAW;IAC3B,CAAC,MACI,IAAI,CAAC,KAAK,IAAI,CAAC3C,UAAU,CAACyD,MAAM,EAAE;MACnC;MACA,IAAI,CAACC,YAAY,CAAC,MAAM;QACpB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE,yBAAyB,CAAC;MACzD,CAAC,EAAE,CAAC,CAAC;MACL;IACJ,CAAC,MACI;MACDhB,SAAS,GAAG,IAAI,CAAC3C,UAAU,CAAC,CAAC,CAAC;IAClC;IACA,IAAI,CAAC4D,UAAU,GAAG,SAAS;IAC3B;IACA,IAAI;MACAjB,SAAS,GAAG,IAAI,CAACO,eAAe,CAACP,SAAS,CAAC;IAC/C,CAAC,CACD,OAAOkB,CAAC,EAAE;MACN,IAAI,CAAC7D,UAAU,CAAC8D,KAAK,CAAC,CAAC;MACvB,IAAI,CAACb,IAAI,CAAC,CAAC;MACX;IACJ;IACAN,SAAS,CAACM,IAAI,CAAC,CAAC;IAChB,IAAI,CAACc,YAAY,CAACpB,SAAS,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACIoB,YAAYA,CAACpB,SAAS,EAAE;IACpB,IAAI,IAAI,CAACA,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACC,kBAAkB,CAAC,CAAC;IACvC;IACA;IACA,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B;IACAA,SAAS,CACJqB,EAAE,CAAC,OAAO,EAAE,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CACpCF,EAAE,CAAC,QAAQ,EAAE,IAAI,CAACG,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC,CACtCF,EAAE,CAAC,OAAO,EAAE,IAAI,CAACI,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC,CACpCF,EAAE,CAAC,OAAO,EAAGK,MAAM,IAAK,IAAI,CAACtB,OAAO,CAAC,iBAAiB,EAAEsB,MAAM,CAAC,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACnB,IAAI,EAAE;IACR,IAAIR,SAAS,GAAG,IAAI,CAACO,eAAe,CAACC,IAAI,CAAC;IAC1C,IAAIoB,MAAM,GAAG,KAAK;IAClB/D,MAAM,CAAC+C,qBAAqB,GAAG,KAAK;IACpC,MAAMiB,eAAe,GAAGA,CAAA,KAAM;MAC1B,IAAID,MAAM,EACN;MACJ5B,SAAS,CAAC8B,IAAI,CAAC,CAAC;QAAEC,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAQ,CAAC,CAAC,CAAC;MACjDhC,SAAS,CAACiC,IAAI,CAAC,QAAQ,EAAGC,GAAG,IAAK;QAC9B,IAAIN,MAAM,EACN;QACJ,IAAI,MAAM,KAAKM,GAAG,CAACH,IAAI,IAAI,OAAO,KAAKG,GAAG,CAACF,IAAI,EAAE;UAC7C,IAAI,CAACG,SAAS,GAAG,IAAI;UACrB,IAAI,CAACnB,YAAY,CAAC,WAAW,EAAEhB,SAAS,CAAC;UACzC,IAAI,CAACA,SAAS,EACV;UACJnC,MAAM,CAAC+C,qBAAqB,GAAG,WAAW,KAAKZ,SAAS,CAACQ,IAAI;UAC7D,IAAI,CAACR,SAAS,CAACoC,KAAK,CAAC,MAAM;YACvB,IAAIR,MAAM,EACN;YACJ,IAAI,QAAQ,KAAK,IAAI,CAACX,UAAU,EAC5B;YACJoB,OAAO,CAAC,CAAC;YACT,IAAI,CAACjB,YAAY,CAACpB,SAAS,CAAC;YAC5BA,SAAS,CAAC8B,IAAI,CAAC,CAAC;cAAEC,IAAI,EAAE;YAAU,CAAC,CAAC,CAAC;YACrC,IAAI,CAACf,YAAY,CAAC,SAAS,EAAEhB,SAAS,CAAC;YACvCA,SAAS,GAAG,IAAI;YAChB,IAAI,CAACmC,SAAS,GAAG,KAAK;YACtB,IAAI,CAACG,KAAK,CAAC,CAAC;UAChB,CAAC,CAAC;QACN,CAAC,MACI;UACD,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,aAAa,CAAC;UACpC;UACAD,GAAG,CAACvC,SAAS,GAAGA,SAAS,CAACQ,IAAI;UAC9B,IAAI,CAACQ,YAAY,CAAC,cAAc,EAAEuB,GAAG,CAAC;QAC1C;MACJ,CAAC,CAAC;IACN,CAAC;IACD,SAASE,eAAeA,CAAA,EAAG;MACvB,IAAIb,MAAM,EACN;MACJ;MACAA,MAAM,GAAG,IAAI;MACbS,OAAO,CAAC,CAAC;MACTrC,SAAS,CAACE,KAAK,CAAC,CAAC;MACjBF,SAAS,GAAG,IAAI;IACpB;IACA;IACA,MAAM0C,OAAO,GAAIH,GAAG,IAAK;MACrB,MAAMI,KAAK,GAAG,IAAIH,KAAK,CAAC,eAAe,GAAGD,GAAG,CAAC;MAC9C;MACAI,KAAK,CAAC3C,SAAS,GAAGA,SAAS,CAACQ,IAAI;MAChCiC,eAAe,CAAC,CAAC;MACjB,IAAI,CAACzB,YAAY,CAAC,cAAc,EAAE2B,KAAK,CAAC;IAC5C,CAAC;IACD,SAASC,gBAAgBA,CAAA,EAAG;MACxBF,OAAO,CAAC,kBAAkB,CAAC;IAC/B;IACA;IACA,SAASG,OAAOA,CAAA,EAAG;MACfH,OAAO,CAAC,eAAe,CAAC;IAC5B;IACA;IACA,SAASI,SAASA,CAACC,EAAE,EAAE;MACnB,IAAI/C,SAAS,IAAI+C,EAAE,CAACvC,IAAI,KAAKR,SAAS,CAACQ,IAAI,EAAE;QACzCiC,eAAe,CAAC,CAAC;MACrB;IACJ;IACA;IACA,MAAMJ,OAAO,GAAGA,CAAA,KAAM;MAClBrC,SAAS,CAACgD,cAAc,CAAC,MAAM,EAAEnB,eAAe,CAAC;MACjD7B,SAAS,CAACgD,cAAc,CAAC,OAAO,EAAEN,OAAO,CAAC;MAC1C1C,SAAS,CAACgD,cAAc,CAAC,OAAO,EAAEJ,gBAAgB,CAAC;MACnD,IAAI,CAACK,GAAG,CAAC,OAAO,EAAEJ,OAAO,CAAC;MAC1B,IAAI,CAACI,GAAG,CAAC,WAAW,EAAEH,SAAS,CAAC;IACpC,CAAC;IACD9C,SAAS,CAACiC,IAAI,CAAC,MAAM,EAAEJ,eAAe,CAAC;IACvC7B,SAAS,CAACiC,IAAI,CAAC,OAAO,EAAES,OAAO,CAAC;IAChC1C,SAAS,CAACiC,IAAI,CAAC,OAAO,EAAEW,gBAAgB,CAAC;IACzC,IAAI,CAACX,IAAI,CAAC,OAAO,EAAEY,OAAO,CAAC;IAC3B,IAAI,CAACZ,IAAI,CAAC,WAAW,EAAEa,SAAS,CAAC;IACjC,IAAI,IAAI,CAACpD,QAAQ,CAACmB,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAC5CL,IAAI,KAAK,cAAc,EAAE;MACzB;MACA,IAAI,CAACO,YAAY,CAAC,MAAM;QACpB,IAAI,CAACa,MAAM,EAAE;UACT5B,SAAS,CAACM,IAAI,CAAC,CAAC;QACpB;MACJ,CAAC,EAAE,GAAG,CAAC;IACX,CAAC,MACI;MACDN,SAAS,CAACM,IAAI,CAAC,CAAC;IACpB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI4C,MAAMA,CAAA,EAAG;IACL,IAAI,CAACjC,UAAU,GAAG,MAAM;IACxBpD,MAAM,CAAC+C,qBAAqB,GAAG,WAAW,KAAK,IAAI,CAACZ,SAAS,CAACQ,IAAI;IAClE,IAAI,CAACQ,YAAY,CAAC,MAAM,CAAC;IACzB,IAAI,CAACsB,KAAK,CAAC,CAAC;IACZ;IACA;IACA,IAAI,MAAM,KAAK,IAAI,CAACrB,UAAU,IAAI,IAAI,CAACjD,IAAI,CAACe,OAAO,EAAE;MACjD,IAAIoE,CAAC,GAAG,CAAC;MACT,MAAMC,CAAC,GAAG,IAAI,CAAC1D,QAAQ,CAACoB,MAAM;MAC9B,OAAOqC,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QACf,IAAI,CAACxB,KAAK,CAAC,IAAI,CAACjC,QAAQ,CAACyD,CAAC,CAAC,CAAC;MAChC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI3B,QAAQA,CAAC6B,MAAM,EAAE;IACb,IAAI,SAAS,KAAK,IAAI,CAACpC,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;MAC/B,IAAI,CAACD,YAAY,CAAC,QAAQ,EAAEqC,MAAM,CAAC;MACnC;MACA,IAAI,CAACrC,YAAY,CAAC,WAAW,CAAC;MAC9B,IAAI,CAACsC,gBAAgB,CAAC,CAAC;MACvB,QAAQD,MAAM,CAACtB,IAAI;QACf,KAAK,MAAM;UACP,IAAI,CAACwB,WAAW,CAACC,IAAI,CAAC/F,KAAK,CAAC4F,MAAM,CAACrB,IAAI,CAAC,CAAC;UACzC;QACJ,KAAK,MAAM;UACP,IAAI,CAACyB,UAAU,CAAC,MAAM,CAAC;UACvB,IAAI,CAACzC,YAAY,CAAC,MAAM,CAAC;UACzB,IAAI,CAACA,YAAY,CAAC,MAAM,CAAC;UACzB;QACJ,KAAK,OAAO;UACR,MAAMuB,GAAG,GAAG,IAAIC,KAAK,CAAC,cAAc,CAAC;UACrC;UACAD,GAAG,CAACmB,IAAI,GAAGL,MAAM,CAACrB,IAAI;UACtB,IAAI,CAACP,OAAO,CAACc,GAAG,CAAC;UACjB;QACJ,KAAK,SAAS;UACV,IAAI,CAACvB,YAAY,CAAC,MAAM,EAAEqC,MAAM,CAACrB,IAAI,CAAC;UACtC,IAAI,CAAChB,YAAY,CAAC,SAAS,EAAEqC,MAAM,CAACrB,IAAI,CAAC;UACzC;MACR;IACJ,CAAC,MACI,CACL;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuB,WAAWA,CAACvB,IAAI,EAAE;IACd,IAAI,CAAChB,YAAY,CAAC,WAAW,EAAEgB,IAAI,CAAC;IACpC,IAAI,CAACvC,EAAE,GAAGuC,IAAI,CAACtB,GAAG;IAClB,IAAI,CAACV,SAAS,CAACzB,KAAK,CAACmC,GAAG,GAAGsB,IAAI,CAACtB,GAAG;IACnC,IAAI,CAAChB,QAAQ,GAAG,IAAI,CAACiE,cAAc,CAAC3B,IAAI,CAACtC,QAAQ,CAAC;IAClD,IAAI,CAACC,YAAY,GAAGqC,IAAI,CAACrC,YAAY;IACrC,IAAI,CAACC,WAAW,GAAGoC,IAAI,CAACpC,WAAW;IACnC,IAAI,CAACgE,UAAU,GAAG5B,IAAI,CAAC4B,UAAU;IACjC,IAAI,CAACV,MAAM,CAAC,CAAC;IACb;IACA,IAAI,QAAQ,KAAK,IAAI,CAACjC,UAAU,EAC5B;IACJ,IAAI,CAACqC,gBAAgB,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACIA,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACO,cAAc,CAAC,IAAI,CAAChE,gBAAgB,CAAC;IAC1C,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACkB,YAAY,CAAC,MAAM;MAC5C,IAAI,CAACX,OAAO,CAAC,cAAc,CAAC;IAChC,CAAC,EAAE,IAAI,CAACT,YAAY,GAAG,IAAI,CAACC,WAAW,CAAC;IACxC,IAAI,IAAI,CAAC5B,IAAI,CAAC8F,SAAS,EAAE;MACrB,IAAI,CAACjE,gBAAgB,CAACkE,KAAK,CAAC,CAAC;IACjC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIzC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACpD,WAAW,CAAC8F,MAAM,CAAC,CAAC,EAAE,IAAI,CAACvF,aAAa,CAAC;IAC9C;IACA;IACA;IACA,IAAI,CAACA,aAAa,GAAG,CAAC;IACtB,IAAI,CAAC,KAAK,IAAI,CAACP,WAAW,CAAC4C,MAAM,EAAE;MAC/B,IAAI,CAACE,YAAY,CAAC,OAAO,CAAC;IAC9B,CAAC,MACI;MACD,IAAI,CAACsB,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIA,KAAKA,CAAA,EAAG;IACJ,IAAI,QAAQ,KAAK,IAAI,CAACrB,UAAU,IAC5B,IAAI,CAACjB,SAAS,CAACiE,QAAQ,IACvB,CAAC,IAAI,CAAC9B,SAAS,IACf,IAAI,CAACjE,WAAW,CAAC4C,MAAM,EAAE;MACzB,MAAMoD,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzC,IAAI,CAACnE,SAAS,CAAC8B,IAAI,CAACoC,OAAO,CAAC;MAC5B;MACA;MACA,IAAI,CAACzF,aAAa,GAAGyF,OAAO,CAACpD,MAAM;MACnC,IAAI,CAACE,YAAY,CAAC,OAAO,CAAC;IAC9B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACImD,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,sBAAsB,GAAG,IAAI,CAACR,UAAU,IAC1C,IAAI,CAAC5D,SAAS,CAACQ,IAAI,KAAK,SAAS,IACjC,IAAI,CAACtC,WAAW,CAAC4C,MAAM,GAAG,CAAC;IAC/B,IAAI,CAACsD,sBAAsB,EAAE;MACzB,OAAO,IAAI,CAAClG,WAAW;IAC3B;IACA,IAAImG,WAAW,GAAG,CAAC,CAAC,CAAC;IACrB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjF,WAAW,CAAC4C,MAAM,EAAEqC,CAAC,EAAE,EAAE;MAC9C,MAAMnB,IAAI,GAAG,IAAI,CAAC9D,WAAW,CAACiF,CAAC,CAAC,CAACnB,IAAI;MACrC,IAAIA,IAAI,EAAE;QACNqC,WAAW,IAAI9G,UAAU,CAACyE,IAAI,CAAC;MACnC;MACA,IAAImB,CAAC,GAAG,CAAC,IAAIkB,WAAW,GAAG,IAAI,CAACT,UAAU,EAAE;QACxC,OAAO,IAAI,CAAC1F,WAAW,CAACoG,KAAK,CAAC,CAAC,EAAEnB,CAAC,CAAC;MACvC;MACAkB,WAAW,IAAI,CAAC,CAAC,CAAC;IACtB;;IACA,OAAO,IAAI,CAACnG,WAAW;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIqG,KAAKA,CAACrC,GAAG,EAAEsC,OAAO,EAAEC,EAAE,EAAE;IACpB,IAAI,CAAChB,UAAU,CAAC,SAAS,EAAEvB,GAAG,EAAEsC,OAAO,EAAEC,EAAE,CAAC;IAC5C,OAAO,IAAI;EACf;EACA3C,IAAIA,CAACI,GAAG,EAAEsC,OAAO,EAAEC,EAAE,EAAE;IACnB,IAAI,CAAChB,UAAU,CAAC,SAAS,EAAEvB,GAAG,EAAEsC,OAAO,EAAEC,EAAE,CAAC;IAC5C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIhB,UAAUA,CAAC1B,IAAI,EAAEC,IAAI,EAAEwC,OAAO,EAAEC,EAAE,EAAE;IAChC,IAAI,UAAU,KAAK,OAAOzC,IAAI,EAAE;MAC5ByC,EAAE,GAAGzC,IAAI;MACTA,IAAI,GAAG0C,SAAS;IACpB;IACA,IAAI,UAAU,KAAK,OAAOF,OAAO,EAAE;MAC/BC,EAAE,GAAGD,OAAO;MACZA,OAAO,GAAG,IAAI;IAClB;IACA,IAAI,SAAS,KAAK,IAAI,CAACvD,UAAU,IAAI,QAAQ,KAAK,IAAI,CAACA,UAAU,EAAE;MAC/D;IACJ;IACAuD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvBA,OAAO,CAACG,QAAQ,GAAG,KAAK,KAAKH,OAAO,CAACG,QAAQ;IAC7C,MAAMtB,MAAM,GAAG;MACXtB,IAAI,EAAEA,IAAI;MACVC,IAAI,EAAEA,IAAI;MACVwC,OAAO,EAAEA;IACb,CAAC;IACD,IAAI,CAACxD,YAAY,CAAC,cAAc,EAAEqC,MAAM,CAAC;IACzC,IAAI,CAACnF,WAAW,CAAC0G,IAAI,CAACvB,MAAM,CAAC;IAC7B,IAAIoB,EAAE,EACF,IAAI,CAACxC,IAAI,CAAC,OAAO,EAAEwC,EAAE,CAAC;IAC1B,IAAI,CAACnC,KAAK,CAAC,CAAC;EAChB;EACA;AACJ;AACA;EACIpC,KAAKA,CAAA,EAAG;IACJ,MAAMA,KAAK,GAAGA,CAAA,KAAM;MAChB,IAAI,CAACE,OAAO,CAAC,cAAc,CAAC;MAC5B,IAAI,CAACJ,SAAS,CAACE,KAAK,CAAC,CAAC;IAC1B,CAAC;IACD,MAAM2E,eAAe,GAAGA,CAAA,KAAM;MAC1B,IAAI,CAAC5B,GAAG,CAAC,SAAS,EAAE4B,eAAe,CAAC;MACpC,IAAI,CAAC5B,GAAG,CAAC,cAAc,EAAE4B,eAAe,CAAC;MACzC3E,KAAK,CAAC,CAAC;IACX,CAAC;IACD,MAAM4E,cAAc,GAAGA,CAAA,KAAM;MACzB;MACA,IAAI,CAAC7C,IAAI,CAAC,SAAS,EAAE4C,eAAe,CAAC;MACrC,IAAI,CAAC5C,IAAI,CAAC,cAAc,EAAE4C,eAAe,CAAC;IAC9C,CAAC;IACD,IAAI,SAAS,KAAK,IAAI,CAAC5D,UAAU,IAAI,MAAM,KAAK,IAAI,CAACA,UAAU,EAAE;MAC7D,IAAI,CAACA,UAAU,GAAG,SAAS;MAC3B,IAAI,IAAI,CAAC/C,WAAW,CAAC4C,MAAM,EAAE;QACzB,IAAI,CAACmB,IAAI,CAAC,OAAO,EAAE,MAAM;UACrB,IAAI,IAAI,CAACE,SAAS,EAAE;YAChB2C,cAAc,CAAC,CAAC;UACpB,CAAC,MACI;YACD5E,KAAK,CAAC,CAAC;UACX;QACJ,CAAC,CAAC;MACN,CAAC,MACI,IAAI,IAAI,CAACiC,SAAS,EAAE;QACrB2C,cAAc,CAAC,CAAC;MACpB,CAAC,MACI;QACD5E,KAAK,CAAC,CAAC;MACX;IACJ;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIuB,OAAOA,CAACc,GAAG,EAAE;IACT1E,MAAM,CAAC+C,qBAAqB,GAAG,KAAK;IACpC,IAAI,CAACI,YAAY,CAAC,OAAO,EAAEuB,GAAG,CAAC;IAC/B,IAAI,CAACnC,OAAO,CAAC,iBAAiB,EAAEmC,GAAG,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACInC,OAAOA,CAACsB,MAAM,EAAErB,WAAW,EAAE;IACzB,IAAI,SAAS,KAAK,IAAI,CAACY,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;MAC/B;MACA,IAAI,CAAC4C,cAAc,CAAC,IAAI,CAAChE,gBAAgB,CAAC;MAC1C;MACA,IAAI,CAACG,SAAS,CAACC,kBAAkB,CAAC,OAAO,CAAC;MAC1C;MACA,IAAI,CAACD,SAAS,CAACE,KAAK,CAAC,CAAC;MACtB;MACA,IAAI,CAACF,SAAS,CAACC,kBAAkB,CAAC,CAAC;MACnC,IAAI,OAAO8E,mBAAmB,KAAK,UAAU,EAAE;QAC3CA,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAChF,yBAAyB,EAAE,KAAK,CAAC;QAC1EgF,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC5E,oBAAoB,EAAE,KAAK,CAAC;MACpE;MACA;MACA,IAAI,CAACc,UAAU,GAAG,QAAQ;MAC1B;MACA,IAAI,CAACxB,EAAE,GAAG,IAAI;MACd;MACA,IAAI,CAACuB,YAAY,CAAC,OAAO,EAAEU,MAAM,EAAErB,WAAW,CAAC;MAC/C;MACA;MACA,IAAI,CAACnC,WAAW,GAAG,EAAE;MACrB,IAAI,CAACO,aAAa,GAAG,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkF,cAAcA,CAACjE,QAAQ,EAAE;IACrB,MAAMsF,gBAAgB,GAAG,EAAE;IAC3B,IAAI7B,CAAC,GAAG,CAAC;IACT,MAAM8B,CAAC,GAAGvF,QAAQ,CAACoB,MAAM;IACzB,OAAOqC,CAAC,GAAG8B,CAAC,EAAE9B,CAAC,EAAE,EAAE;MACf,IAAI,CAAC,IAAI,CAAC9F,UAAU,CAACwD,OAAO,CAACnB,QAAQ,CAACyD,CAAC,CAAC,CAAC,EACrC6B,gBAAgB,CAACJ,IAAI,CAAClF,QAAQ,CAACyD,CAAC,CAAC,CAAC;IAC1C;IACA,OAAO6B,gBAAgB;EAC3B;AACJ;AACAnH,MAAM,CAACF,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}