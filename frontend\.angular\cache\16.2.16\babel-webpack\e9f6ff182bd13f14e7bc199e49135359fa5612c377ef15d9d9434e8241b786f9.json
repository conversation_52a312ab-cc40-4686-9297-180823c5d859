{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class NotificationService {\n  constructor() {}\n  showSuccess(message) {\n    this.showNotification(message, 'success');\n  }\n  showError(message) {\n    this.showNotification(message, 'error');\n  }\n  showWarning(message) {\n    this.showNotification(message, 'warning');\n  }\n  showInfo(message) {\n    this.showNotification(message, 'info');\n  }\n  showNotification(message, type) {\n    let container = document.getElementById('notification-container');\n    if (!container) {\n      container = document.createElement('div');\n      container.id = 'notification-container';\n      container.style.position = 'fixed';\n      container.style.top = '20px';\n      container.style.right = '20px';\n      container.style.zIndex = '9999';\n      document.body.appendChild(container);\n    }\n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.style.backgroundColor = this.getBackgroundColor(type);\n    notification.style.color = '#fff';\n    notification.style.padding = '12px 20px';\n    notification.style.borderRadius = '4px';\n    notification.style.marginBottom = '10px';\n    notification.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';\n    notification.style.minWidth = '250px';\n    notification.style.maxWidth = '350px';\n    notification.style.opacity = '0';\n    notification.style.transform = 'translateX(50px)';\n    notification.style.transition = 'opacity 0.3s, transform 0.3s';\n    const messageText = document.createElement('div');\n    messageText.textContent = message;\n    notification.appendChild(messageText);\n    const closeButton = document.createElement('button');\n    closeButton.textContent = '×';\n    closeButton.style.background = 'none';\n    closeButton.style.border = 'none';\n    closeButton.style.color = '#fff';\n    closeButton.style.fontSize = '20px';\n    closeButton.style.fontWeight = 'bold';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '5px';\n    closeButton.style.right = '10px';\n    closeButton.style.padding = '0';\n    closeButton.style.lineHeight = '20px';\n    closeButton.onclick = () => this.removeNotification(notification);\n    notification.appendChild(closeButton);\n    container.appendChild(notification);\n    setTimeout(() => {\n      notification.style.opacity = '1';\n      notification.style.transform = 'translateX(0)';\n    }, 10);\n    setTimeout(() => {\n      this.removeNotification(notification);\n    }, 5000);\n  }\n  removeNotification(notification) {\n    notification.style.opacity = '0';\n    notification.style.transform = 'translateX(50px)';\n    setTimeout(() => {\n      if (notification.parentNode) {\n        notification.parentNode.removeChild(notification);\n      }\n    }, 300);\n  }\n  getBackgroundColor(type) {\n    switch (type) {\n      case 'success':\n        return '#28a745';\n      case 'error':\n        return '#dc3545';\n      case 'warning':\n        return '#ffc107';\n      case 'info':\n        return '#17a2b8';\n      default:\n        return '#6c757d';\n    }\n  }\n  static {\n    this.ɵfac = function NotificationService_Factory(t) {\n      return new (t || NotificationService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: NotificationService,\n      factory: NotificationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["NotificationService", "constructor", "showSuccess", "message", "showNotification", "showError", "showWarning", "showInfo", "type", "container", "document", "getElementById", "createElement", "id", "style", "position", "top", "right", "zIndex", "body", "append<PERSON><PERSON><PERSON>", "notification", "className", "backgroundColor", "getBackgroundColor", "color", "padding", "borderRadius", "marginBottom", "boxShadow", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "opacity", "transform", "transition", "messageText", "textContent", "closeButton", "background", "border", "fontSize", "fontWeight", "cursor", "lineHeight", "onclick", "removeNotification", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "factory", "ɵfac", "providedIn"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\services\\notification.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class NotificationService {\n  constructor() { }\n\n  showSuccess(message: string): void {\n    this.showNotification(message, 'success');\n  }\n\n  showError(message: string): void {\n    this.showNotification(message, 'error');\n  }\n\n  showWarning(message: string): void {\n    this.showNotification(message, 'warning');\n  }\n\n  showInfo(message: string): void {\n    this.showNotification(message, 'info');\n  }\n\n  private showNotification(message: string, type: string): void {\n    let container = document.getElementById('notification-container');\n    \n    if (!container) {\n      container = document.createElement('div');\n      container.id = 'notification-container';\n      container.style.position = 'fixed';\n      container.style.top = '20px';\n      container.style.right = '20px';\n      container.style.zIndex = '9999';\n      document.body.appendChild(container);\n    }\n    \n    const notification = document.createElement('div');\n    notification.className = `notification notification-${type}`;\n    notification.style.backgroundColor = this.getBackgroundColor(type);\n    notification.style.color = '#fff';\n    notification.style.padding = '12px 20px';\n    notification.style.borderRadius = '4px';\n    notification.style.marginBottom = '10px';\n    notification.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';\n    notification.style.minWidth = '250px';\n    notification.style.maxWidth = '350px';\n    notification.style.opacity = '0';\n    notification.style.transform = 'translateX(50px)';\n    notification.style.transition = 'opacity 0.3s, transform 0.3s';\n    \n    const messageText = document.createElement('div');\n    messageText.textContent = message;\n    notification.appendChild(messageText);\n    \n    const closeButton = document.createElement('button');\n    closeButton.textContent = '×';\n    closeButton.style.background = 'none';\n    closeButton.style.border = 'none';\n    closeButton.style.color = '#fff';\n    closeButton.style.fontSize = '20px';\n    closeButton.style.fontWeight = 'bold';\n    closeButton.style.cursor = 'pointer';\n    closeButton.style.position = 'absolute';\n    closeButton.style.top = '5px';\n    closeButton.style.right = '10px';\n    closeButton.style.padding = '0';\n    closeButton.style.lineHeight = '20px';\n    closeButton.onclick = () => this.removeNotification(notification);\n    notification.appendChild(closeButton);\n    \n    container.appendChild(notification);\n    \n    setTimeout(() => {\n      notification.style.opacity = '1';\n      notification.style.transform = 'translateX(0)';\n    }, 10);\n    \n    setTimeout(() => {\n      this.removeNotification(notification);\n    }, 5000);\n  }\n  \n  private removeNotification(notification: HTMLElement): void {\n    notification.style.opacity = '0';\n    notification.style.transform = 'translateX(50px)';\n    \n    setTimeout(() => {\n      if (notification.parentNode) {\n        notification.parentNode.removeChild(notification);\n      }\n    }, 300);\n  }\n  \n  private getBackgroundColor(type: string): string {\n    switch (type) {\n      case 'success':\n        return '#28a745';\n      case 'error':\n        return '#dc3545';\n      case 'warning':\n        return '#ffc107';\n      case 'info':\n        return '#17a2b8';\n      default:\n        return '#6c757d';\n    }\n  }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,mBAAmB;EAC9BC,YAAA,GAAgB;EAEhBC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACC,gBAAgB,CAACD,OAAO,EAAE,SAAS,CAAC;EAC3C;EAEAE,SAASA,CAACF,OAAe;IACvB,IAAI,CAACC,gBAAgB,CAACD,OAAO,EAAE,OAAO,CAAC;EACzC;EAEAG,WAAWA,CAACH,OAAe;IACzB,IAAI,CAACC,gBAAgB,CAACD,OAAO,EAAE,SAAS,CAAC;EAC3C;EAEAI,QAAQA,CAACJ,OAAe;IACtB,IAAI,CAACC,gBAAgB,CAACD,OAAO,EAAE,MAAM,CAAC;EACxC;EAEQC,gBAAgBA,CAACD,OAAe,EAAEK,IAAY;IACpD,IAAIC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;IAEjE,IAAI,CAACF,SAAS,EAAE;MACdA,SAAS,GAAGC,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACzCH,SAAS,CAACI,EAAE,GAAG,wBAAwB;MACvCJ,SAAS,CAACK,KAAK,CAACC,QAAQ,GAAG,OAAO;MAClCN,SAAS,CAACK,KAAK,CAACE,GAAG,GAAG,MAAM;MAC5BP,SAAS,CAACK,KAAK,CAACG,KAAK,GAAG,MAAM;MAC9BR,SAAS,CAACK,KAAK,CAACI,MAAM,GAAG,MAAM;MAC/BR,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACX,SAAS,CAAC;;IAGtC,MAAMY,YAAY,GAAGX,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;IAClDS,YAAY,CAACC,SAAS,GAAG,6BAA6Bd,IAAI,EAAE;IAC5Da,YAAY,CAACP,KAAK,CAACS,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAChB,IAAI,CAAC;IAClEa,YAAY,CAACP,KAAK,CAACW,KAAK,GAAG,MAAM;IACjCJ,YAAY,CAACP,KAAK,CAACY,OAAO,GAAG,WAAW;IACxCL,YAAY,CAACP,KAAK,CAACa,YAAY,GAAG,KAAK;IACvCN,YAAY,CAACP,KAAK,CAACc,YAAY,GAAG,MAAM;IACxCP,YAAY,CAACP,KAAK,CAACe,SAAS,GAAG,2BAA2B;IAC1DR,YAAY,CAACP,KAAK,CAACgB,QAAQ,GAAG,OAAO;IACrCT,YAAY,CAACP,KAAK,CAACiB,QAAQ,GAAG,OAAO;IACrCV,YAAY,CAACP,KAAK,CAACkB,OAAO,GAAG,GAAG;IAChCX,YAAY,CAACP,KAAK,CAACmB,SAAS,GAAG,kBAAkB;IACjDZ,YAAY,CAACP,KAAK,CAACoB,UAAU,GAAG,8BAA8B;IAE9D,MAAMC,WAAW,GAAGzB,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;IACjDuB,WAAW,CAACC,WAAW,GAAGjC,OAAO;IACjCkB,YAAY,CAACD,WAAW,CAACe,WAAW,CAAC;IAErC,MAAME,WAAW,GAAG3B,QAAQ,CAACE,aAAa,CAAC,QAAQ,CAAC;IACpDyB,WAAW,CAACD,WAAW,GAAG,GAAG;IAC7BC,WAAW,CAACvB,KAAK,CAACwB,UAAU,GAAG,MAAM;IACrCD,WAAW,CAACvB,KAAK,CAACyB,MAAM,GAAG,MAAM;IACjCF,WAAW,CAACvB,KAAK,CAACW,KAAK,GAAG,MAAM;IAChCY,WAAW,CAACvB,KAAK,CAAC0B,QAAQ,GAAG,MAAM;IACnCH,WAAW,CAACvB,KAAK,CAAC2B,UAAU,GAAG,MAAM;IACrCJ,WAAW,CAACvB,KAAK,CAAC4B,MAAM,GAAG,SAAS;IACpCL,WAAW,CAACvB,KAAK,CAACC,QAAQ,GAAG,UAAU;IACvCsB,WAAW,CAACvB,KAAK,CAACE,GAAG,GAAG,KAAK;IAC7BqB,WAAW,CAACvB,KAAK,CAACG,KAAK,GAAG,MAAM;IAChCoB,WAAW,CAACvB,KAAK,CAACY,OAAO,GAAG,GAAG;IAC/BW,WAAW,CAACvB,KAAK,CAAC6B,UAAU,GAAG,MAAM;IACrCN,WAAW,CAACO,OAAO,GAAG,MAAM,IAAI,CAACC,kBAAkB,CAACxB,YAAY,CAAC;IACjEA,YAAY,CAACD,WAAW,CAACiB,WAAW,CAAC;IAErC5B,SAAS,CAACW,WAAW,CAACC,YAAY,CAAC;IAEnCyB,UAAU,CAAC,MAAK;MACdzB,YAAY,CAACP,KAAK,CAACkB,OAAO,GAAG,GAAG;MAChCX,YAAY,CAACP,KAAK,CAACmB,SAAS,GAAG,eAAe;IAChD,CAAC,EAAE,EAAE,CAAC;IAENa,UAAU,CAAC,MAAK;MACd,IAAI,CAACD,kBAAkB,CAACxB,YAAY,CAAC;IACvC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQwB,kBAAkBA,CAACxB,YAAyB;IAClDA,YAAY,CAACP,KAAK,CAACkB,OAAO,GAAG,GAAG;IAChCX,YAAY,CAACP,KAAK,CAACmB,SAAS,GAAG,kBAAkB;IAEjDa,UAAU,CAAC,MAAK;MACd,IAAIzB,YAAY,CAAC0B,UAAU,EAAE;QAC3B1B,YAAY,CAAC0B,UAAU,CAACC,WAAW,CAAC3B,YAAY,CAAC;;IAErD,CAAC,EAAE,GAAG,CAAC;EACT;EAEQG,kBAAkBA,CAAChB,IAAY;IACrC,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,MAAM;QACT,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;;EAEtB;;;uBAtGWR,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAiD,OAAA,EAAnBjD,mBAAmB,CAAAkD,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}