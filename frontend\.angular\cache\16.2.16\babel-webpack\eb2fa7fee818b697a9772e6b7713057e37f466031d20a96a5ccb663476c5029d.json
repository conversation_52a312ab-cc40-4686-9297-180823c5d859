{"ast": null, "code": "// export const environment = {\n//   production: false,\n//   apiUrl: 'http://localhost:5001/api',\n//   wsUrl: 'ws://localhost:5001/ws'\n// };\nexport const environment = {\n  production: false,\n  apiUrl: 'http://localhost:5000',\n  wsUrl: 'ws://localhost:5000/ws'\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "wsUrl"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\environments\\environment.ts"], "sourcesContent": ["// export const environment = {\n//   production: false,\n//   apiUrl: 'http://localhost:5001/api',\n//   wsUrl: 'ws://localhost:5001/ws'\n// };\n\nexport const environment = {\n  production: false,\n  apiUrl:  'http://localhost:5000',   // ← changed from 5001 to 5000\n  wsUrl:   'ws://localhost:5000/ws'\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAG,uBAAuB;EAChCC,KAAK,EAAI;CACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}