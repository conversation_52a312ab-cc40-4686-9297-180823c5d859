{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/project.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction HomeComponent_div_64_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_64_div_4_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const project_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.openProject(project_r3.name));\n    });\n    i0.ɵɵelementStart(1, \"h3\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 26)(4, \"span\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r3 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r3.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Updated: \", ctx_r2.formatDate(project_r3.updated_at), \"\");\n  }\n}\nfunction HomeComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"h2\");\n    i0.ɵɵtext(2, \"Recent Projects\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtemplate(4, HomeComponent_div_64_div_4_Template, 6, 2, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22)(6, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_64_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToProjects());\n    });\n    i0.ɵɵtext(7, \" View All Projects \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.recentProjects);\n  }\n}\nfunction HomeComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"img\", 29);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading recent projects...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class HomeComponent {\n  constructor(projectService, router) {\n    this.projectService = projectService;\n    this.router = router;\n    this.recentProjects = [];\n    this.loading = false;\n  }\n  ngOnInit() {\n    this.loadRecentProjects();\n  }\n  loadRecentProjects() {\n    this.loading = true;\n    this.projectService.getProjects().subscribe(response => {\n      this.recentProjects = (response.projects || []).sort((a, b) => {\n        return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();\n      }).slice(0, 5);\n      this.loading = false;\n    }, error => {\n      console.error('Error loading recent projects:', error);\n      this.loading = false;\n    });\n  }\n  navigateToProjects() {\n    this.router.navigate(['/projects']);\n  }\n  navigateToConfig() {\n    this.router.navigate(['/config']);\n  }\n  openProject(projectName) {\n    this.router.navigate(['/projects', projectName]);\n  }\n  navigateToFeature(feature) {\n    switch (feature) {\n      case 'ai-development':\n        this.router.navigate(['/projects']);\n        break;\n      case 'documentation':\n        this.router.navigate(['/projects']);\n        break;\n      case 'feedback':\n        this.router.navigate(['/projects']);\n        break;\n      case 'integrations':\n        this.router.navigate(['/config']);\n        break;\n      case 'vscode':\n        this.router.navigate(['/testing']);\n        break;\n      case 'testing':\n        this.router.navigate(['/testing']);\n        break;\n      default:\n        this.router.navigate(['/']);\n        break;\n    }\n  }\n  formatDate(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ProjectService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 66,\n      vars: 2,\n      consts: [[1, \"home-container\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-description\"], [1, \"hero-actions\"], [1, \"primary-btn\", 3, \"click\"], [1, \"secondary-btn\", 3, \"click\"], [1, \"features-section\"], [1, \"features-grid\"], [1, \"feature-card\", \"clickable\", 3, \"click\"], [1, \"feature-icon\", \"ai-icon\"], [1, \"material-icons\"], [1, \"feature-icon\", \"doc-icon\"], [1, \"feature-icon\", \"feedback-icon\"], [1, \"feature-icon\", \"integration-icon\"], [1, \"feature-icon\", \"vscode-icon\"], [1, \"feature-icon\", \"testing-icon\"], [\"class\", \"recent-projects-section\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [1, \"recent-projects-section\"], [1, \"projects-list\"], [\"class\", \"project-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all\"], [1, \"text-btn\", 3, \"click\"], [1, \"project-card\", 3, \"click\"], [1, \"project-name\"], [1, \"project-meta\"], [1, \"project-date\"], [1, \"loading-indicator\"], [\"src\", \"assets/images/loading-spinner.svg\", \"alt\", \"Loading\", 1, \"spinner-image\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4, \"Autonomous AI Software Development Agent\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 3);\n          i0.ɵɵtext(6, \" Create complete projects with AI assistance - including source code, configuration files, and documentation. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_8_listener() {\n            return ctx.navigateToProjects();\n          });\n          i0.ɵɵtext(9, \" Start a New Project \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_10_listener() {\n            return ctx.navigateToConfig();\n          });\n          i0.ɵɵtext(11, \" Configure AI Models \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 7)(13, \"h2\");\n          i0.ɵɵtext(14, \"Key Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 8)(16, \"div\", 9);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_16_listener() {\n            return ctx.navigateToFeature(\"ai-development\");\n          });\n          i0.ɵɵelementStart(17, \"div\", 10)(18, \"i\", 11);\n          i0.ɵɵtext(19, \"smart_toy\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"h3\");\n          i0.ɵɵtext(21, \"AI-Powered Development\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"Leverage external AI models to generate complete projects based on your specifications.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 9);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_24_listener() {\n            return ctx.navigateToFeature(\"documentation\");\n          });\n          i0.ɵɵelementStart(25, \"div\", 12)(26, \"i\", 11);\n          i0.ɵɵtext(27, \"description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"h3\");\n          i0.ɵɵtext(29, \"Complete Documentation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"p\");\n          i0.ɵɵtext(31, \"Automatically generate comprehensive documentation for your projects.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 9);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_32_listener() {\n            return ctx.navigateToFeature(\"feedback\");\n          });\n          i0.ɵɵelementStart(33, \"div\", 13)(34, \"i\", 11);\n          i0.ɵɵtext(35, \"sync\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"h3\");\n          i0.ɵɵtext(37, \"Real-Time Feedback\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\");\n          i0.ɵɵtext(39, \"See your changes in real-time with integrated code editor and browser preview.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 9);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_40_listener() {\n            return ctx.navigateToFeature(\"integrations\");\n          });\n          i0.ɵɵelementStart(41, \"div\", 14)(42, \"i\", 11);\n          i0.ɵɵtext(43, \"extension\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"h3\");\n          i0.ɵɵtext(45, \"Multiple AI Integrations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"p\");\n          i0.ɵɵtext(47, \"Connect to OpenAI, Ollama, or LM Studio using your own API keys.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 9);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_48_listener() {\n            return ctx.navigateToFeature(\"vscode\");\n          });\n          i0.ɵɵelementStart(49, \"div\", 15)(50, \"i\", 11);\n          i0.ɵɵtext(51, \"code\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"h3\");\n          i0.ɵɵtext(53, \"VS Code Integration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"p\");\n          i0.ɵɵtext(55, \"Seamlessly integrated with Visual Studio Code for a complete development experience.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"div\", 9);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_div_click_56_listener() {\n            return ctx.navigateToFeature(\"testing\");\n          });\n          i0.ɵɵelementStart(57, \"div\", 16)(58, \"i\", 11);\n          i0.ɵɵtext(59, \"check_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"h3\");\n          i0.ɵɵtext(61, \"Autonomous Testing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"p\");\n          i0.ɵɵtext(63, \"Automatically test, fix errors, and validate functionality without manual intervention.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(64, HomeComponent_div_64_Template, 8, 1, \"div\", 17);\n          i0.ɵɵtemplate(65, HomeComponent_div_65_Template, 4, 0, \"div\", 18);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(64);\n          i0.ɵɵproperty(\"ngIf\", ctx.recentProjects.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 80px 20px;\\n  background: url(\\\"/assets/images/hero-bg.svg\\\") no-repeat center center;\\n  background-size: cover;\\n  border-radius: 8px;\\n  margin-bottom: 40px;\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 800px;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  font-weight: 700;\\n  margin-bottom: 16px;\\n  color: #2196f3;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-description[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: #555;\\n  margin-bottom: 32px;\\n  line-height: 1.5;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 16px;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  font-size: 16px;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button.primary-btn[_ngcontent-%COMP%] {\\n  background-color: #2196f3;\\n  color: white;\\n  border: none;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button.primary-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1976d2;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button.secondary-btn[_ngcontent-%COMP%] {\\n  background-color: white;\\n  color: #2196f3;\\n  border: 1px solid #2196f3;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%]   button.secondary-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #e3f2fd;\\n}\\n\\n.features-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.features-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin-bottom: 24px;\\n  text-align: center;\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n  gap: 24px;\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 8px;\\n  padding: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.2s, box-shadow 0.2s;\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card.clickable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  position: relative;\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card.clickable[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card.clickable[_ngcontent-%COMP%]:hover:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 12px;\\n  right: 12px;\\n  width: 24px;\\n  height: 24px;\\n  background-image: url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"24\\\" height=\\\"24\\\" viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"%232196f3\\\" stroke-width=\\\"2\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\"><path d=\\\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\\\"></path><polyline points=\\\"15 3 21 3 21 9\\\"></polyline><line x1=\\\"10\\\" y1=\\\"14\\\" x2=\\\"21\\\" y2=\\\"3\\\"></line></svg>');\\n  background-repeat: no-repeat;\\n  opacity: 0.7;\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 20px;\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: white;\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.ai-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.doc-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f83600 0%, #f9d423 100%);\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.feedback-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00c6fb 0%, #005bea 100%);\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.integration-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.vscode-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007acc 0%, #0097e6 100%);\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .feature-icon.testing-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin-bottom: 12px;\\n  color: #333;\\n}\\n.features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  line-height: 1.5;\\n}\\n\\n.recent-projects-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.recent-projects-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin-bottom: 24px;\\n  text-align: center;\\n}\\n.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 24px;\\n}\\n.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  cursor: pointer;\\n  transition: box-shadow 0.2s, transform 0.2s;\\n}\\n.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-2px);\\n}\\n.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-name[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 18px;\\n  font-weight: 500;\\n}\\n.recent-projects-section[_ngcontent-%COMP%]   .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-meta[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.recent-projects-section[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.recent-projects-section[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]   .text-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #2196f3;\\n  font-weight: 500;\\n  cursor: pointer;\\n  font-size: 16px;\\n}\\n.recent-projects-section[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]   .text-btn[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.loading-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin: 32px 0;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: 3px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 50%;\\n  border-top-color: #3498db;\\n  animation: _ngcontent-%COMP%_spin 1s ease-in-out infinite;\\n  margin-bottom: 8px;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%] {\\n    padding: 40px 16px;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-description[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .features-section[_ngcontent-%COMP%]   .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "HomeComponent_div_64_div_4_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r5", "project_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "openProject", "name", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "ctx_r2", "formatDate", "updated_at", "ɵɵtemplate", "HomeComponent_div_64_div_4_Template", "HomeComponent_div_64_Template_button_click_6_listener", "_r7", "ctx_r6", "navigateToProjects", "ɵɵproperty", "ctx_r0", "recentProjects", "ɵɵelement", "HomeComponent", "constructor", "projectService", "router", "loading", "ngOnInit", "loadRecentProjects", "getProjects", "subscribe", "response", "projects", "sort", "a", "b", "Date", "getTime", "slice", "error", "console", "navigate", "navigateToConfig", "projectName", "navigateToFeature", "feature", "dateString", "date", "toLocaleString", "ɵɵdirectiveInject", "i1", "ProjectService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_Template_button_click_8_listener", "HomeComponent_Template_button_click_10_listener", "HomeComponent_Template_div_click_16_listener", "HomeComponent_Template_div_click_24_listener", "HomeComponent_Template_div_click_32_listener", "HomeComponent_Template_div_click_40_listener", "HomeComponent_Template_div_click_48_listener", "HomeComponent_Template_div_click_56_listener", "HomeComponent_div_64_Template", "HomeComponent_div_65_Template", "length"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\home\\home.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\components\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { ProjectService } from '../../services/project.service';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  recentProjects: any[] = [];\n  loading: boolean = false;\n  \n  constructor(\n    private projectService: ProjectService,\n    private router: Router\n  ) { }\n  \n  ngOnInit(): void {\n    this.loadRecentProjects();\n  }\n  \n  loadRecentProjects(): void {\n    this.loading = true;\n    \n    this.projectService.getProjects().subscribe(\n      (response) => {\n        this.recentProjects = (response.projects || [])\n          .sort((a: any, b: any) => {\n            return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();\n          })\n          .slice(0, 5);\n        \n        this.loading = false;\n      },\n      (error) => {\n        console.error('Error loading recent projects:', error);\n        this.loading = false;\n      }\n    );\n  }\n  \n  navigateToProjects(): void {\n    this.router.navigate(['/projects']);\n  }\n  \n  navigateToConfig(): void {\n    this.router.navigate(['/config']);\n  }\n  \n  openProject(projectName: string): void {\n    this.router.navigate(['/projects', projectName]);\n  }\n  \n  navigateToFeature(feature: string): void {\n    switch (feature) {\n      case 'ai-development':\n        this.router.navigate(['/projects']);\n        break;\n      case 'documentation':\n        this.router.navigate(['/projects']);\n        break;\n      case 'feedback':\n        this.router.navigate(['/projects']);\n        break;\n      case 'integrations':\n        this.router.navigate(['/config']);\n        break;\n      case 'vscode':\n        this.router.navigate(['/testing']);\n        break;\n      case 'testing':\n        this.router.navigate(['/testing']);\n        break;\n      default:\n        this.router.navigate(['/']);\n        break;\n    }\n  }\n  \n  formatDate(dateString: string): string {\n    if (!dateString) return '';\n    \n    const date = new Date(dateString);\n    return date.toLocaleString();\n  }\n}\n", "<div class=\"home-container\">\n  <div class=\"hero-section\">\n    <div class=\"hero-content\">\n      <h1>Autonomous AI Software Development Agent</h1>\n      <p class=\"hero-description\">\n        Create complete projects with AI assistance - including source code, configuration files, and documentation.\n      </p>\n      <div class=\"hero-actions\">\n        <button class=\"primary-btn\" (click)=\"navigateToProjects()\">\n          Start a New Project\n        </button>\n        <button class=\"secondary-btn\" (click)=\"navigateToConfig()\">\n          Configure AI Models\n        </button>\n      </div>\n    </div>\n  </div>\n  \n  <div class=\"features-section\">\n    <h2>Key Features</h2>\n    \n    <div class=\"features-grid\">\n      <div class=\"feature-card clickable\" (click)=\"navigateToFeature('ai-development')\">\n        <div class=\"feature-icon ai-icon\">\n          <i class=\"material-icons\">smart_toy</i>\n        </div>\n        <h3>AI-Powered Development</h3>\n        <p>Leverage external AI models to generate complete projects based on your specifications.</p>\n      </div>\n      \n      <div class=\"feature-card clickable\" (click)=\"navigateToFeature('documentation')\">\n        <div class=\"feature-icon doc-icon\">\n          <i class=\"material-icons\">description</i>\n        </div>\n        <h3>Complete Documentation</h3>\n        <p>Automatically generate comprehensive documentation for your projects.</p>\n      </div>\n      \n      <div class=\"feature-card clickable\" (click)=\"navigateToFeature('feedback')\">\n        <div class=\"feature-icon feedback-icon\">\n          <i class=\"material-icons\">sync</i>\n        </div>\n        <h3>Real-Time Feedback</h3>\n        <p>See your changes in real-time with integrated code editor and browser preview.</p>\n      </div>\n      \n      <div class=\"feature-card clickable\" (click)=\"navigateToFeature('integrations')\">\n        <div class=\"feature-icon integration-icon\">\n          <i class=\"material-icons\">extension</i>\n        </div>\n        <h3>Multiple AI Integrations</h3>\n        <p>Connect to OpenAI, Ollama, or LM Studio using your own API keys.</p>\n      </div>\n      \n      <div class=\"feature-card clickable\" (click)=\"navigateToFeature('vscode')\">\n        <div class=\"feature-icon vscode-icon\">\n          <i class=\"material-icons\">code</i>\n        </div>\n        <h3>VS Code Integration</h3>\n        <p>Seamlessly integrated with Visual Studio Code for a complete development experience.</p>\n      </div>\n      \n      <div class=\"feature-card clickable\" (click)=\"navigateToFeature('testing')\">\n        <div class=\"feature-icon testing-icon\">\n          <i class=\"material-icons\">check_circle</i>\n        </div>\n        <h3>Autonomous Testing</h3>\n        <p>Automatically test, fix errors, and validate functionality without manual intervention.</p>\n      </div>\n    </div>\n  </div>\n  \n  <div class=\"recent-projects-section\" *ngIf=\"recentProjects.length > 0\">\n    <h2>Recent Projects</h2>\n    \n    <div class=\"projects-list\">\n      <div \n        *ngFor=\"let project of recentProjects\" \n        class=\"project-card\" \n        (click)=\"openProject(project.name)\"\n      >\n        <h3 class=\"project-name\">{{ project.name }}</h3>\n        <div class=\"project-meta\">\n          <span class=\"project-date\">Updated: {{ formatDate(project.updated_at) }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"view-all\">\n      <button class=\"text-btn\" (click)=\"navigateToProjects()\">\n        View All Projects\n      </button>\n    </div>\n  </div>\n  \n  <div class=\"loading-indicator\" *ngIf=\"loading\">\n    <img src=\"assets/images/loading-spinner.svg\" alt=\"Loading\" class=\"spinner-image\">\n    <p>Loading recent projects...</p>\n  </div>\n</div>\n"], "mappings": ";;;;;;;IC4EMA,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAL,UAAA,CAAAM,IAAA,CAAyB;IAAA,EAAC;IAEnCb,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAc,MAAA,GAAkB;IAAAd,EAAA,CAAAe,YAAA,EAAK;IAChDf,EAAA,CAAAC,cAAA,cAA0B;IACGD,EAAA,CAAAc,MAAA,GAA6C;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;;IAFxDf,EAAA,CAAAgB,SAAA,GAAkB;IAAlBhB,EAAA,CAAAiB,iBAAA,CAAAV,UAAA,CAAAM,IAAA,CAAkB;IAEdb,EAAA,CAAAgB,SAAA,GAA6C;IAA7ChB,EAAA,CAAAkB,kBAAA,cAAAC,MAAA,CAAAC,UAAA,CAAAb,UAAA,CAAAc,UAAA,MAA6C;;;;;;IAXhFrB,EAAA,CAAAC,cAAA,cAAuE;IACjED,EAAA,CAAAc,MAAA,sBAAe;IAAAd,EAAA,CAAAe,YAAA,EAAK;IAExBf,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAsB,UAAA,IAAAC,mCAAA,kBASM;IACRvB,EAAA,CAAAe,YAAA,EAAM;IAENf,EAAA,CAAAC,cAAA,cAAsB;IACKD,EAAA,CAAAE,UAAA,mBAAAsB,sDAAA;MAAAxB,EAAA,CAAAK,aAAA,CAAAoB,GAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAe,MAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IACrD3B,EAAA,CAAAc,MAAA,0BACF;IAAAd,EAAA,CAAAe,YAAA,EAAS;;;;IAdaf,EAAA,CAAAgB,SAAA,GAAiB;IAAjBhB,EAAA,CAAA4B,UAAA,YAAAC,MAAA,CAAAC,cAAA,CAAiB;;;;;IAkB3C9B,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAA+B,SAAA,cAAiF;IACjF/B,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAc,MAAA,iCAA0B;IAAAd,EAAA,CAAAe,YAAA,EAAI;;;ADxFrC,OAAM,MAAOiB,aAAa;EAIxBC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAL,cAAc,GAAU,EAAE;IAC1B,KAAAM,OAAO,GAAY,KAAK;EAKpB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,CAACF,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACF,cAAc,CAACK,WAAW,EAAE,CAACC,SAAS,CACxCC,QAAQ,IAAI;MACX,IAAI,CAACX,cAAc,GAAG,CAACW,QAAQ,CAACC,QAAQ,IAAI,EAAE,EAC3CC,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;QACvB,OAAO,IAAIC,IAAI,CAACD,CAAC,CAACxB,UAAU,CAAC,CAAC0B,OAAO,EAAE,GAAG,IAAID,IAAI,CAACF,CAAC,CAACvB,UAAU,CAAC,CAAC0B,OAAO,EAAE;MAC5E,CAAC,CAAC,CACDC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAEd,IAAI,CAACZ,OAAO,GAAG,KAAK;IACtB,CAAC,EACAa,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAACb,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEAT,kBAAkBA,CAAA;IAChB,IAAI,CAACQ,MAAM,CAACgB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACjB,MAAM,CAACgB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEAvC,WAAWA,CAACyC,WAAmB;IAC7B,IAAI,CAAClB,MAAM,CAACgB,QAAQ,CAAC,CAAC,WAAW,EAAEE,WAAW,CAAC,CAAC;EAClD;EAEAC,iBAAiBA,CAACC,OAAe;IAC/B,QAAQA,OAAO;MACb,KAAK,gBAAgB;QACnB,IAAI,CAACpB,MAAM,CAACgB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACnC;MACF,KAAK,eAAe;QAClB,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACnC;MACF,KAAK,UAAU;QACb,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACnC;MACF,KAAK,cAAc;QACjB,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;QACjC;MACF,KAAK,QAAQ;QACX,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC;MACF,KAAK,SAAS;QACZ,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC;MACF;QACE,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B;;EAEN;EAEA/B,UAAUA,CAACoC,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAE1B,MAAMC,IAAI,GAAG,IAAIX,IAAI,CAACU,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACC,cAAc,EAAE;EAC9B;;;uBA5EW1B,aAAa,EAAAhC,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAb/B,aAAa;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT1BtE,EAAA,CAAAC,cAAA,aAA4B;UAGlBD,EAAA,CAAAc,MAAA,+CAAwC;UAAAd,EAAA,CAAAe,YAAA,EAAK;UACjDf,EAAA,CAAAC,cAAA,WAA4B;UAC1BD,EAAA,CAAAc,MAAA,qHACF;UAAAd,EAAA,CAAAe,YAAA,EAAI;UACJf,EAAA,CAAAC,cAAA,aAA0B;UACID,EAAA,CAAAE,UAAA,mBAAAsE,+CAAA;YAAA,OAASD,GAAA,CAAA5C,kBAAA,EAAoB;UAAA,EAAC;UACxD3B,EAAA,CAAAc,MAAA,4BACF;UAAAd,EAAA,CAAAe,YAAA,EAAS;UACTf,EAAA,CAAAC,cAAA,iBAA2D;UAA7BD,EAAA,CAAAE,UAAA,mBAAAuE,gDAAA;YAAA,OAASF,GAAA,CAAAnB,gBAAA,EAAkB;UAAA,EAAC;UACxDpD,EAAA,CAAAc,MAAA,6BACF;UAAAd,EAAA,CAAAe,YAAA,EAAS;UAKff,EAAA,CAAAC,cAAA,cAA8B;UACxBD,EAAA,CAAAc,MAAA,oBAAY;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAErBf,EAAA,CAAAC,cAAA,cAA2B;UACWD,EAAA,CAAAE,UAAA,mBAAAwE,6CAAA;YAAA,OAASH,GAAA,CAAAjB,iBAAA,CAAkB,gBAAgB,CAAC;UAAA,EAAC;UAC/EtD,EAAA,CAAAC,cAAA,eAAkC;UACND,EAAA,CAAAc,MAAA,iBAAS;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAEzCf,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAc,MAAA,8BAAsB;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAC/Bf,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAc,MAAA,+FAAuF;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAGhGf,EAAA,CAAAC,cAAA,cAAiF;UAA7CD,EAAA,CAAAE,UAAA,mBAAAyE,6CAAA;YAAA,OAASJ,GAAA,CAAAjB,iBAAA,CAAkB,eAAe,CAAC;UAAA,EAAC;UAC9EtD,EAAA,CAAAC,cAAA,eAAmC;UACPD,EAAA,CAAAc,MAAA,mBAAW;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAE3Cf,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAc,MAAA,8BAAsB;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAC/Bf,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAc,MAAA,6EAAqE;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAG9Ef,EAAA,CAAAC,cAAA,cAA4E;UAAxCD,EAAA,CAAAE,UAAA,mBAAA0E,6CAAA;YAAA,OAASL,GAAA,CAAAjB,iBAAA,CAAkB,UAAU,CAAC;UAAA,EAAC;UACzEtD,EAAA,CAAAC,cAAA,eAAwC;UACZD,EAAA,CAAAc,MAAA,YAAI;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAEpCf,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAc,MAAA,0BAAkB;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAC3Bf,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAc,MAAA,sFAA8E;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAGvFf,EAAA,CAAAC,cAAA,cAAgF;UAA5CD,EAAA,CAAAE,UAAA,mBAAA2E,6CAAA;YAAA,OAASN,GAAA,CAAAjB,iBAAA,CAAkB,cAAc,CAAC;UAAA,EAAC;UAC7EtD,EAAA,CAAAC,cAAA,eAA2C;UACfD,EAAA,CAAAc,MAAA,iBAAS;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAEzCf,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAc,MAAA,gCAAwB;UAAAd,EAAA,CAAAe,YAAA,EAAK;UACjCf,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAc,MAAA,wEAAgE;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAGzEf,EAAA,CAAAC,cAAA,cAA0E;UAAtCD,EAAA,CAAAE,UAAA,mBAAA4E,6CAAA;YAAA,OAASP,GAAA,CAAAjB,iBAAA,CAAkB,QAAQ,CAAC;UAAA,EAAC;UACvEtD,EAAA,CAAAC,cAAA,eAAsC;UACVD,EAAA,CAAAc,MAAA,YAAI;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAEpCf,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAc,MAAA,2BAAmB;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAC5Bf,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAc,MAAA,4FAAoF;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAG7Ff,EAAA,CAAAC,cAAA,cAA2E;UAAvCD,EAAA,CAAAE,UAAA,mBAAA6E,6CAAA;YAAA,OAASR,GAAA,CAAAjB,iBAAA,CAAkB,SAAS,CAAC;UAAA,EAAC;UACxEtD,EAAA,CAAAC,cAAA,eAAuC;UACXD,EAAA,CAAAc,MAAA,oBAAY;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAE5Cf,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAc,MAAA,0BAAkB;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAC3Bf,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAc,MAAA,+FAAuF;UAAAd,EAAA,CAAAe,YAAA,EAAI;UAKpGf,EAAA,CAAAsB,UAAA,KAAA0D,6BAAA,kBAqBM;UAENhF,EAAA,CAAAsB,UAAA,KAAA2D,6BAAA,kBAGM;UACRjF,EAAA,CAAAe,YAAA,EAAM;;;UA3BkCf,EAAA,CAAAgB,SAAA,IAA+B;UAA/BhB,EAAA,CAAA4B,UAAA,SAAA2C,GAAA,CAAAzC,cAAA,CAAAoD,MAAA,KAA+B;UAuBrClF,EAAA,CAAAgB,SAAA,GAAa;UAAbhB,EAAA,CAAA4B,UAAA,SAAA2C,GAAA,CAAAnC,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}