{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { io } from 'socket.io-client';\nimport * as i0 from \"@angular/core\";\nexport class SocketFactoryService {\n  constructor() {\n    this.socket = io('http://localhost:5000', {\n      transports: ['websocket']\n    });\n    console.log('Socket connection initialized');\n  }\n  connect() {\n    if (!this.socket.connected) {\n      this.socket.connect();\n    }\n  }\n  disconnect() {\n    if (this.socket.connected) {\n      this.socket.disconnect();\n    }\n  }\n  emit(eventName, data) {\n    this.socket.emit(eventName, data);\n  }\n  on(eventName) {\n    return new Observable(observer => {\n      this.socket.on(eventName, data => {\n        observer.next(data);\n      });\n      return () => {\n        this.socket.off(eventName);\n      };\n    });\n  }\n  once(eventName) {\n    return new Observable(observer => {\n      this.socket.once(eventName, data => {\n        observer.next(data);\n        observer.complete();\n      });\n      return () => {\n        this.socket.off(eventName);\n      };\n    });\n  }\n  static {\n    this.ɵfac = function SocketFactoryService_Factory(t) {\n      return new (t || SocketFactoryService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SocketFactoryService,\n      factory: SocketFactoryService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "io", "SocketFactoryService", "constructor", "socket", "transports", "console", "log", "connect", "connected", "disconnect", "emit", "eventName", "data", "on", "observer", "next", "off", "once", "complete", "factory", "ɵfac", "providedIn"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\services\\socket-factory.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { io, Socket } from 'socket.io-client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SocketFactoryService {\r\n  private socket: Socket;\r\n\r\n  constructor() {\r\n    this.socket = io('http://localhost:5000', {\r\n      transports: ['websocket']\r\n    });\r\n    console.log('Socket connection initialized');\r\n  }\r\n\r\n  connect() {\r\n    if (!this.socket.connected) {\r\n      this.socket.connect();\r\n    }\r\n  }\r\n\r\n  disconnect() {\r\n    if (this.socket.connected) {\r\n      this.socket.disconnect();\r\n    }\r\n  }\r\n\r\n  emit(eventName: string, data?: any) {\r\n    this.socket.emit(eventName, data);\r\n  }\r\n\r\n  on(eventName: string): Observable<any> {\r\n    return new Observable(observer => {\r\n      this.socket.on(eventName, (data: any) => {\r\n        observer.next(data);\r\n      });\r\n      \r\n      return () => {\r\n        this.socket.off(eventName);\r\n      };\r\n    });\r\n  }\r\n\r\n  once(eventName: string): Observable<any> {\r\n    return new Observable(observer => {\r\n      this.socket.once(eventName, (data: any) => {\r\n        observer.next(data);\r\n        observer.complete();\r\n      });\r\n      \r\n      return () => {\r\n        this.socket.off(eventName);\r\n      };\r\n    });\r\n  }\r\n} "], "mappings": "AACA,SAASA,UAAU,QAAQ,MAAM;AACjC,SAASC,EAAE,QAAgB,kBAAkB;;AAK7C,OAAM,MAAOC,oBAAoB;EAG/BC,YAAA;IACE,IAAI,CAACC,MAAM,GAAGH,EAAE,CAAC,uBAAuB,EAAE;MACxCI,UAAU,EAAE,CAAC,WAAW;KACzB,CAAC;IACFC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC9C;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACJ,MAAM,CAACK,SAAS,EAAE;MAC1B,IAAI,CAACL,MAAM,CAACI,OAAO,EAAE;;EAEzB;EAEAE,UAAUA,CAAA;IACR,IAAI,IAAI,CAACN,MAAM,CAACK,SAAS,EAAE;MACzB,IAAI,CAACL,MAAM,CAACM,UAAU,EAAE;;EAE5B;EAEAC,IAAIA,CAACC,SAAiB,EAAEC,IAAU;IAChC,IAAI,CAACT,MAAM,CAACO,IAAI,CAACC,SAAS,EAAEC,IAAI,CAAC;EACnC;EAEAC,EAAEA,CAACF,SAAiB;IAClB,OAAO,IAAIZ,UAAU,CAACe,QAAQ,IAAG;MAC/B,IAAI,CAACX,MAAM,CAACU,EAAE,CAACF,SAAS,EAAGC,IAAS,IAAI;QACtCE,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC;MACrB,CAAC,CAAC;MAEF,OAAO,MAAK;QACV,IAAI,CAACT,MAAM,CAACa,GAAG,CAACL,SAAS,CAAC;MAC5B,CAAC;IACH,CAAC,CAAC;EACJ;EAEAM,IAAIA,CAACN,SAAiB;IACpB,OAAO,IAAIZ,UAAU,CAACe,QAAQ,IAAG;MAC/B,IAAI,CAACX,MAAM,CAACc,IAAI,CAACN,SAAS,EAAGC,IAAS,IAAI;QACxCE,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC;QACnBE,QAAQ,CAACI,QAAQ,EAAE;MACrB,CAAC,CAAC;MAEF,OAAO,MAAK;QACV,IAAI,CAACf,MAAM,CAACa,GAAG,CAACL,SAAS,CAAC;MAC5B,CAAC;IACH,CAAC,CAAC;EACJ;;;uBAjDWV,oBAAoB;IAAA;EAAA;;;aAApBA,oBAAoB;MAAAkB,OAAA,EAApBlB,oBAAoB,CAAAmB,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}