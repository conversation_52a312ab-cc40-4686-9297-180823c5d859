{"ast": null, "code": "import { BehaviorSubject } from '../BehaviorSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishBehavior(initialValue) {\n  return source => {\n    const subject = new BehaviorSubject(initialValue);\n    return new ConnectableObservable(source, () => subject);\n  };\n}", "map": {"version": 3, "names": ["BehaviorSubject", "ConnectableObservable", "publish<PERSON>eh<PERSON>or", "initialValue", "source", "subject"], "sources": ["C:/SourceProjects/AutonomousAI/frontend/node_modules/rxjs/dist/esm/internal/operators/publishBehavior.js"], "sourcesContent": ["import { BehaviorSubject } from '../BehaviorSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishBehavior(initialValue) {\n    return (source) => {\n        const subject = new BehaviorSubject(initialValue);\n        return new ConnectableObservable(source, () => subject);\n    };\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,OAAO,SAASC,eAAeA,CAACC,YAAY,EAAE;EAC1C,OAAQC,MAAM,IAAK;IACf,MAAMC,OAAO,GAAG,IAAIL,eAAe,CAACG,YAAY,CAAC;IACjD,OAAO,IAAIF,qBAAqB,CAACG,MAAM,EAAE,MAAMC,OAAO,CAAC;EAC3D,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}