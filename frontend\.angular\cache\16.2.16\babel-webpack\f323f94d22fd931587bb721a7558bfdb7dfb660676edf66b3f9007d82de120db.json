{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api.service\";\nexport class FileService {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  getProjectFiles(projectName) {\n    return this.apiService.getProjectFiles(projectName);\n  }\n  getFileContent(projectName, filePath) {\n    return this.apiService.getFileContent(projectName, filePath);\n  }\n  updateFileContent(projectName, filePath, content) {\n    return this.apiService.updateFileContent(projectName, filePath, content);\n  }\n  deleteFile(projectName, filePath) {\n    return this.apiService.deleteFile(projectName, filePath);\n  }\n  openProjectInExplorer(projectName) {\n    return this.apiService.openProjectInExplorer(projectName);\n  }\n  uploadFile(projectName, formData) {\n    return this.apiService.uploadFile(projectName, formData);\n  }\n  resetProject(projectName) {\n    return this.apiService.resetProject(projectName);\n  }\n  static {\n    this.ɵfac = function FileService_Factory(t) {\n      return new (t || FileService)(i0.ɵɵinject(i1.ApiService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FileService,\n      factory: FileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["FileService", "constructor", "apiService", "getProjectFiles", "projectName", "getFileContent", "filePath", "updateFileContent", "content", "deleteFile", "openProjectInExplorer", "uploadFile", "formData", "resetProject", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\services\\file.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { ApiService } from './api.service';\nimport { Observable } from 'rxjs';\n\nexport interface FileItem {\n  name: string;\n  path: string;\n  isFolder: boolean;\n  children?: FileItem[];\n}\n\nexport interface FileResponse {\n  files: FileItem[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FileService {\n  constructor(private apiService: ApiService) { }\n  \n  getProjectFiles(projectName: string): Observable<FileResponse> {\n    return this.apiService.getProjectFiles(projectName);\n  }\n  \n  getFileContent(projectName: string, filePath: string): Observable<any> {\n    return this.apiService.getFileContent(projectName, filePath);\n  }\n  \n  updateFileContent(projectName: string, filePath: string, content: string): Observable<any> {\n    return this.apiService.updateFileContent(projectName, filePath, content);\n  }\n  \n  deleteFile(projectName: string, filePath: string): Observable<any> {\n    return this.apiService.deleteFile(projectName, filePath);\n  }\n  \n  openProjectInExplorer(projectName: string): Observable<any> {\n    return this.apiService.openProjectInExplorer(projectName);\n  }\n  \n  uploadFile(projectName: string, formData: FormData): Observable<any> {\n    return this.apiService.uploadFile(projectName, formData);\n  }\n  \n  resetProject(projectName: string): Observable<any> {\n    return this.apiService.resetProject(projectName);\n  }\n}\n"], "mappings": ";;AAkBA,OAAM,MAAOA,WAAW;EACtBC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAgB;EAE9CC,eAAeA,CAACC,WAAmB;IACjC,OAAO,IAAI,CAACF,UAAU,CAACC,eAAe,CAACC,WAAW,CAAC;EACrD;EAEAC,cAAcA,CAACD,WAAmB,EAAEE,QAAgB;IAClD,OAAO,IAAI,CAACJ,UAAU,CAACG,cAAc,CAACD,WAAW,EAAEE,QAAQ,CAAC;EAC9D;EAEAC,iBAAiBA,CAACH,WAAmB,EAAEE,QAAgB,EAAEE,OAAe;IACtE,OAAO,IAAI,CAACN,UAAU,CAACK,iBAAiB,CAACH,WAAW,EAAEE,QAAQ,EAAEE,OAAO,CAAC;EAC1E;EAEAC,UAAUA,CAACL,WAAmB,EAAEE,QAAgB;IAC9C,OAAO,IAAI,CAACJ,UAAU,CAACO,UAAU,CAACL,WAAW,EAAEE,QAAQ,CAAC;EAC1D;EAEAI,qBAAqBA,CAACN,WAAmB;IACvC,OAAO,IAAI,CAACF,UAAU,CAACQ,qBAAqB,CAACN,WAAW,CAAC;EAC3D;EAEAO,UAAUA,CAACP,WAAmB,EAAEQ,QAAkB;IAChD,OAAO,IAAI,CAACV,UAAU,CAACS,UAAU,CAACP,WAAW,EAAEQ,QAAQ,CAAC;EAC1D;EAEAC,YAAYA,CAACT,WAAmB;IAC9B,OAAO,IAAI,CAACF,UAAU,CAACW,YAAY,CAACT,WAAW,CAAC;EAClD;;;uBA7BWJ,WAAW,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXjB,WAAW;MAAAkB,OAAA,EAAXlB,WAAW,CAAAmB,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}