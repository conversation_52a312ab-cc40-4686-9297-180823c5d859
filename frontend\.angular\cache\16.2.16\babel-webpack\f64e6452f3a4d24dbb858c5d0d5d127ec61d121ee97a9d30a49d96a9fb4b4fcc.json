{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/file.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"ngx-monaco-editor-v2\";\nfunction CodeEditorComponent_h3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.filePath);\n  }\n}\nfunction CodeEditorComponent_h3_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1, \"No file selected\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CodeEditorComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"div\", 10);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading file...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CodeEditorComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"p\");\n    i0.ɵɵtext(2, \"Select a file to edit\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CodeEditorComponent_ngx_monaco_editor_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-monaco-editor\", 12);\n    i0.ɵɵlistener(\"ngModelChange\", function CodeEditorComponent_ngx_monaco_editor_10_Template_ngx_monaco_editor_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.code = $event);\n    })(\"ngModelChange\", function CodeEditorComponent_ngx_monaco_editor_10_Template_ngx_monaco_editor_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onCodeChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"options\", ctx_r4.editorOptions)(\"ngModel\", ctx_r4.code);\n  }\n}\nexport let CodeEditorComponent = /*#__PURE__*/(() => {\n  class CodeEditorComponent {\n    constructor(fileService) {\n      this.fileService = fileService;\n      this.projectName = '';\n      this.filePath = '';\n      this.fileChangeEvent = new EventEmitter();\n      this.editorOptions = {\n        theme: 'vs-dark',\n        language: 'typescript',\n        automaticLayout: true\n      };\n      this.code = '';\n      this.loading = false;\n    }\n    ngOnInit() {\n      console.log('[CodeEditorComponent] ngOnInit triggered');\n      this.loadFile();\n    }\n    ngOnChanges(changes) {\n      console.log('[CodeEditorComponent] ngOnChanges triggered with changes:', changes);\n      if (changes['filePath'] && this.filePath) {\n        console.log('[CodeEditorComponent] filePath changed. Reloading file.');\n        this.loadFile();\n      }\n    }\n    loadFile() {\n      console.log('[CodeEditorComponent] loadFile called with projectName:', this.projectName, 'filePath:', this.filePath);\n      if (!this.projectName || !this.filePath) {\n        console.warn('[CodeEditorComponent] Missing projectName or filePath. Aborting load.');\n        return;\n      }\n      this.loading = true;\n      console.log('[CodeEditorComponent] Loading file content...');\n      this.fileService.getFileContent(this.projectName, this.filePath).subscribe(response => {\n        console.log('[CodeEditorComponent] File content loaded successfully:', response);\n        this.code = response.content || '';\n        this.setLanguage();\n        this.loading = false;\n      }, error => {\n        console.error('[CodeEditorComponent] ❌ Error loading file:', error);\n        this.loading = false;\n      });\n    }\n    setLanguage() {\n      if (!this.filePath) {\n        console.warn('[CodeEditorComponent] No file path available to set language.');\n        return;\n      }\n      const extension = this.filePath.split('.').pop()?.toLowerCase();\n      console.log('[CodeEditorComponent] Determined file extension:', extension);\n      switch (extension) {\n        case 'ts':\n          this.editorOptions = {\n            ...this.editorOptions,\n            language: 'typescript'\n          };\n          break;\n        case 'js':\n          this.editorOptions = {\n            ...this.editorOptions,\n            language: 'javascript'\n          };\n          break;\n        case 'html':\n          this.editorOptions = {\n            ...this.editorOptions,\n            language: 'html'\n          };\n          break;\n        case 'css':\n        case 'scss':\n          this.editorOptions = {\n            ...this.editorOptions,\n            language: 'css'\n          };\n          break;\n        case 'json':\n          this.editorOptions = {\n            ...this.editorOptions,\n            language: 'json'\n          };\n          break;\n        case 'py':\n          this.editorOptions = {\n            ...this.editorOptions,\n            language: 'python'\n          };\n          break;\n        case 'md':\n          this.editorOptions = {\n            ...this.editorOptions,\n            language: 'markdown'\n          };\n          break;\n        default:\n          this.editorOptions = {\n            ...this.editorOptions,\n            language: 'plaintext'\n          };\n          console.warn('[CodeEditorComponent] Unrecognized file extension. Defaulting to plaintext.');\n      }\n      console.log('[CodeEditorComponent] Editor language set to:', this.editorOptions.language);\n    }\n    onCodeChange(code) {\n      console.log('[CodeEditorComponent] Code changed in editor');\n      this.code = code;\n    }\n    saveFile() {\n      console.log('[CodeEditorComponent] saveFile called');\n      if (!this.projectName || !this.filePath) {\n        console.warn('[CodeEditorComponent] Missing projectName or filePath. Aborting save.');\n        return;\n      }\n      this.loading = true;\n      console.log('[CodeEditorComponent] Saving file content...');\n      this.fileService.updateFileContent(this.projectName, this.filePath, this.code).subscribe(response => {\n        console.log('[CodeEditorComponent] ✅ File saved successfully:', response);\n        this.loading = false;\n        this.fileChangeEvent.emit({\n          type: 'file_saved',\n          filePath: this.filePath\n        });\n      }, error => {\n        console.error('[CodeEditorComponent] ❌ Error saving file:', error);\n        this.loading = false;\n      });\n    }\n    static {\n      this.ɵfac = function CodeEditorComponent_Factory(t) {\n        return new (t || CodeEditorComponent)(i0.ɵɵdirectiveInject(i1.FileService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CodeEditorComponent,\n        selectors: [[\"app-code-editor\"]],\n        inputs: {\n          projectName: \"projectName\",\n          filePath: \"filePath\"\n        },\n        outputs: {\n          fileChangeEvent: \"fileChangeEvent\"\n        },\n        features: [i0.ɵɵNgOnChangesFeature],\n        decls: 11,\n        vars: 6,\n        consts: [[1, \"editor-container\"], [1, \"editor-header\"], [4, \"ngIf\"], [1, \"editor-actions\"], [3, \"disabled\", \"click\"], [1, \"editor-content\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"code-editor\", 3, \"options\", \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [1, \"loading-overlay\"], [1, \"spinner\"], [1, \"empty-state\"], [1, \"code-editor\", 3, \"options\", \"ngModel\", \"ngModelChange\"]],\n        template: function CodeEditorComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵtemplate(2, CodeEditorComponent_h3_2_Template, 2, 1, \"h3\", 2);\n            i0.ɵɵtemplate(3, CodeEditorComponent_h3_3_Template, 2, 0, \"h3\", 2);\n            i0.ɵɵelementStart(4, \"div\", 3)(5, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function CodeEditorComponent_Template_button_click_5_listener() {\n              return ctx.saveFile();\n            });\n            i0.ɵɵtext(6, \" Save \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"div\", 5);\n            i0.ɵɵtemplate(8, CodeEditorComponent_div_8_Template, 4, 0, \"div\", 6);\n            i0.ɵɵtemplate(9, CodeEditorComponent_div_9_Template, 3, 0, \"div\", 7);\n            i0.ɵɵtemplate(10, CodeEditorComponent_ngx_monaco_editor_10_Template, 1, 2, \"ngx-monaco-editor\", 8);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.filePath);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.filePath);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", !ctx.filePath || ctx.loading);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.filePath);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.filePath);\n          }\n        },\n        dependencies: [i2.NgIf, i3.NgControlStatus, i3.NgModel, i4.EditorComponent],\n        styles: [\".editor-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100%;border:1px solid #e0e0e0;border-radius:8px;overflow:hidden}.editor-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:12px 16px;background-color:#f5f5f5;border-bottom:1px solid #e0e0e0}.editor-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:16px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:70%}.editor-header[_ngcontent-%COMP%]   .editor-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.editor-header[_ngcontent-%COMP%]   .editor-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:6px 12px;background-color:#2196f3;color:#fff;border:none;border-radius:4px;font-weight:500;cursor:pointer;transition:background-color .2s}.editor-header[_ngcontent-%COMP%]   .editor-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#1976d2}.editor-header[_ngcontent-%COMP%]   .editor-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{background-color:#bbdefb;cursor:not-allowed}.editor-content[_ngcontent-%COMP%]{flex:1;position:relative}.editor-content[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background-color:#fffc;display:flex;flex-direction:column;justify-content:center;align-items:center;z-index:10}.editor-content[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{width:32px;height:32px;border:3px solid rgba(0,0,0,.1);border-radius:50%;border-top-color:#3498db;animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite;margin-bottom:8px}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}.editor-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:100%;color:#888;font-style:italic}.editor-content[_ngcontent-%COMP%]   .code-editor[_ngcontent-%COMP%]{height:100%;width:100%}\"]\n      });\n    }\n  }\n  return CodeEditorComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}