{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/project.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction ProjectListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"div\", 12);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading projects...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"p\");\n    i0.ɵɵtext(2, \"No projects found. Create a new project to get started.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_div_14_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const project_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openProject(project_r3.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"h3\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 17)(5, \"span\", 18);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 18);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"span\", 20);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 21);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 22)(15, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProjectListComponent_div_14_Template_button_click_15_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const project_r3 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.deleteProject(project_r3.name, $event));\n    });\n    i0.ɵɵtext(16, \" Delete \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r3 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(project_r3.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Created: \", ctx_r2.formatDate(project_r3.created_at), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Updated: \", ctx_r2.formatDate(project_r3.updated_at), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", (project_r3.files == null ? null : project_r3.files.length) || 0, \" files\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", (project_r3.messages == null ? null : project_r3.messages.length) || 0, \" messages\");\n  }\n}\nexport let ProjectListComponent = /*#__PURE__*/(() => {\n  class ProjectListComponent {\n    constructor(projectService, router) {\n      this.projectService = projectService;\n      this.router = router;\n      this.projects = [];\n      this.loading = false;\n      this.newProjectName = '';\n    }\n    ngOnInit() {\n      this.loadProjects();\n    }\n    loadProjects() {\n      this.loading = true;\n      this.projectService.getProjects().subscribe(response => {\n        this.projects = response.projects || [];\n        this.loading = false;\n      }, error => {\n        console.error('Error loading projects:', error);\n        this.loading = false;\n      });\n    }\n    createProject() {\n      if (!this.newProjectName.trim()) return;\n      this.loading = true;\n      this.projectService.createProject(this.newProjectName).subscribe(response => {\n        this.newProjectName = '';\n        this.loadProjects();\n        this.router.navigate(['/projects', response.project.name]);\n      }, error => {\n        console.error('Error creating project:', error);\n        this.loading = false;\n      });\n    }\n    deleteProject(projectName, event) {\n      event.stopPropagation();\n      if (!confirm(`Are you sure you want to delete project \"${projectName}\"?`)) {\n        return;\n      }\n      this.loading = true;\n      this.projectService.deleteProject(projectName).subscribe(() => {\n        this.loadProjects();\n      }, error => {\n        console.error('Error deleting project:', error);\n        this.loading = false;\n      });\n    }\n    openProject(projectName) {\n      this.router.navigate(['/projects', projectName]);\n    }\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleString();\n    }\n    deleteAllProjects() {\n      if (!confirm('Are you sure you want to delete ALL projects? This action cannot be undone.')) {\n        return;\n      }\n      this.loading = true;\n      this.projectService.deleteAllProjects().subscribe(() => {\n        this.loadProjects();\n      }, error => {\n        console.error('Error deleting all projects:', error);\n        this.loading = false;\n      });\n    }\n    static {\n      this.ɵfac = function ProjectListComponent_Factory(t) {\n        return new (t || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.ProjectService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectListComponent,\n        selectors: [[\"app-project-list\"]],\n        decls: 15,\n        vars: 7,\n        consts: [[1, \"projects-container\"], [1, \"projects-header\"], [1, \"create-project\"], [\"type\", \"text\", \"placeholder\", \"New project name\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keyup.enter\"], [3, \"disabled\", \"click\"], [2, \"margin-left\", \"8px\", \"background\", \"#e53935\", \"color\", \"white\", 3, \"disabled\", \"click\"], [1, \"projects-content\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"projects-list\"], [\"class\", \"project-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-indicator\"], [1, \"spinner\"], [1, \"empty-state\"], [1, \"project-card\", 3, \"click\"], [1, \"project-info\"], [1, \"project-name\"], [1, \"project-meta\"], [1, \"project-date\"], [1, \"project-stats\"], [1, \"project-files\"], [1, \"project-messages\"], [1, \"project-actions\"], [1, \"delete-btn\", 3, \"click\"]],\n        template: function ProjectListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"Projects\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 2)(5, \"input\", 3);\n            i0.ɵɵlistener(\"ngModelChange\", function ProjectListComponent_Template_input_ngModelChange_5_listener($event) {\n              return ctx.newProjectName = $event;\n            })(\"keyup.enter\", function ProjectListComponent_Template_input_keyup_enter_5_listener() {\n              return ctx.createProject();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function ProjectListComponent_Template_button_click_6_listener() {\n              return ctx.createProject();\n            });\n            i0.ɵɵtext(7, \" Create Project \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function ProjectListComponent_Template_button_click_8_listener() {\n              return ctx.deleteAllProjects();\n            });\n            i0.ɵɵtext(9, \" Delete All \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(10, \"div\", 6);\n            i0.ɵɵtemplate(11, ProjectListComponent_div_11_Template, 4, 0, \"div\", 7);\n            i0.ɵɵtemplate(12, ProjectListComponent_div_12_Template, 3, 0, \"div\", 8);\n            i0.ɵɵelementStart(13, \"div\", 9);\n            i0.ɵɵtemplate(14, ProjectListComponent_div_14_Template, 17, 5, \"div\", 10);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngModel\", ctx.newProjectName)(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", !ctx.newProjectName.trim() || ctx.loading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.projects.length === 0);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.projects.length === 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.projects);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel],\n        styles: [\".projects-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100%;padding:20px}.projects-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.projects-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:24px;font-weight:500}.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]{display:flex;gap:8px}.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding:8px 12px;border:1px solid #ccc;border-radius:4px;font-size:14px;min-width:200px}.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:disabled{background-color:#f5f5f5;cursor:not-allowed}.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:8px 16px;background-color:#2196f3;color:#fff;border:none;border-radius:4px;font-weight:500;cursor:pointer;transition:background-color .2s}.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#1976d2}.projects-header[_ngcontent-%COMP%]   .create-project[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{background-color:#bbdefb;cursor:not-allowed}.projects-content[_ngcontent-%COMP%]{flex:1}.projects-content[_ngcontent-%COMP%]   .loading-indicator[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin:32px 0}.projects-content[_ngcontent-%COMP%]   .loading-indicator[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{width:32px;height:32px;border:3px solid rgba(0,0,0,.1);border-radius:50%;border-top-color:#3498db;animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite;margin-bottom:8px}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}.projects-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:200px;color:#888;font-style:italic}.projects-list[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:16px}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between;padding:16px;border:1px solid #e0e0e0;border-radius:8px;background-color:#fff;transition:box-shadow .2s,transform .2s;cursor:pointer}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px #0000001a;transform:translateY(-2px)}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-name[_ngcontent-%COMP%]{margin:0 0 8px;font-size:18px;font-weight:500}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-meta[_ngcontent-%COMP%]{display:flex;flex-direction:column;margin-bottom:12px;font-size:12px;color:#666}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-meta[_ngcontent-%COMP%]   .project-date[_ngcontent-%COMP%]{margin-bottom:4px}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]{display:flex;gap:12px;font-size:14px;color:#444}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-files[_ngcontent-%COMP%], .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-messages[_ngcontent-%COMP%]{display:flex;align-items:center}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-files[_ngcontent-%COMP%]:before, .projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-messages[_ngcontent-%COMP%]:before{content:\\\"\\\";display:inline-block;width:16px;height:16px;margin-right:4px;background-color:#2196f3;mask-size:cover;-webkit-mask-size:cover}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-files[_ngcontent-%COMP%]:before{-webkit-mask-image:url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z\\\"/></svg>');mask-image:url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z\\\"/></svg>');-webkit-mask-image:url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z\\\"/></svg>')}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-info[_ngcontent-%COMP%]   .project-stats[_ngcontent-%COMP%]   .project-messages[_ngcontent-%COMP%]:before{-webkit-mask-image:url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10z\\\"/></svg>');mask-image:url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10z\\\"/></svg>');-webkit-mask-image:url('data:image/svg+xml;utf8,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 24 24\\\"><path d=\\\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10z\\\"/></svg>')}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:16px}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]{padding:6px 12px;background-color:#f44336;color:#fff;border:none;border-radius:4px;font-size:12px;font-weight:500;cursor:pointer;transition:background-color .2s}.projects-list[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover{background-color:#d32f2f}\"]\n      });\n    }\n  }\n  return ProjectListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}