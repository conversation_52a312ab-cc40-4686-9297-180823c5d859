{"ast": null, "code": "import { Subject, throwError, of } from 'rxjs';\nimport { debounceTime, catchError, retry, finalize, first } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/project.service\";\nimport * as i3 from \"../../services/socket.service\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../chat/chat.component\";\nimport * as i7 from \"../code-editor/code-editor.component\";\nimport * as i8 from \"../browser-preview/browser-preview.component\";\nimport * as i9 from \"../file-explorer/file-explorer.component\";\nfunction ProjectDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"div\", 11);\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"div\", 13)(4, \"span\", 14);\n    i0.ɵɵtext(5, \"Current Stage: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 15);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 16)(10, \"div\", 17);\n    i0.ɵɵelement(11, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 19);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 20);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 5, ctx_r0.agentStage));\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.agentProgress, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.agentProgress, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.agentStatusMessage);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_13_div_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.toggleFilesMenu());\n    });\n    i0.ɵɵtext(3, \"\\u25C0\\uFE0F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"app-file-explorer\", 32);\n    i0.ɵɵlistener(\"fileSelectEvent\", function ProjectDetailComponent_div_13_div_1_Template_app_file_explorer_fileSelectEvent_4_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onFileSelect($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"projectName\", ctx_r3.projectName);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_13_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.toggleFilesMenu());\n    });\n    i0.ɵɵtext(2, \"\\u25B6\\uFE0F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectDetailComponent_div_13_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-code-editor\", 37);\n    i0.ɵɵlistener(\"fileChangeEvent\", function ProjectDetailComponent_div_13_div_4_ng_container_1_Template_app_code_editor_fileChangeEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r14.onFileChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"projectName\", ctx_r12.projectName)(\"filePath\", ctx_r12.selectedFile == null ? null : ctx_r12.selectedFile.path);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"iframe\", 38);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r13.codeServerUrl, i0.ɵɵsanitizeResourceUrl);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_13_div_4_ng_container_1_Template, 2, 2, \"ng-container\", 36);\n    i0.ɵɵtemplate(2, ProjectDetailComponent_div_13_div_4_ng_container_2_Template, 2, 1, \"ng-container\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.showVSCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showVSCode);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-chat\", 42);\n    i0.ɵɵlistener(\"messageEvent\", function ProjectDetailComponent_div_13_div_7_ng_container_1_Template_app_chat_messageEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.onMessageEvent($event));\n    })(\"chatExpandChange\", function ProjectDetailComponent_div_13_div_7_ng_container_1_Template_app_chat_chatExpandChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.setChatExpanded($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"projectName\", ctx_r16.projectName)(\"messagesLoading\", ctx_r16.chatMessagesLoading)(\"messagesSaving\", ctx_r16.chatMessagesSaving);\n  }\n}\nfunction ProjectDetailComponent_div_13_div_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"span\", 44);\n    i0.ɵɵtext(2, \"Chat with AI Agent\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ProjectDetailComponent_div_13_div_7_ng_template_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.setChatExpanded(true));\n    });\n    i0.ɵɵtext(4, \"\\u25B2\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"collapsed\": a0,\n    \"expanded\": a1\n  };\n};\nfunction ProjectDetailComponent_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_13_div_7_ng_container_1_Template, 2, 3, \"ng-container\", 40);\n    i0.ɵɵtemplate(2, ProjectDetailComponent_div_13_div_7_ng_template_2_Template, 5, 0, \"ng-template\", null, 41, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r17 = i0.ɵɵreference(3);\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c0, !ctx_r6.isChatExpanded, ctx_r6.isChatExpanded));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isChatExpanded)(\"ngIfElse\", _r17);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"files-collapsed\": a0\n  };\n};\nfunction ProjectDetailComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_13_div_1_Template, 5, 1, \"div\", 22);\n    i0.ɵɵtemplate(2, ProjectDetailComponent_div_13_div_2_Template, 3, 0, \"div\", 23);\n    i0.ɵɵelementStart(3, \"div\", 24);\n    i0.ɵɵtemplate(4, ProjectDetailComponent_div_13_div_4_Template, 3, 2, \"div\", 25);\n    i0.ɵɵelementStart(5, \"div\", 26)(6, \"app-browser-preview\", 27);\n    i0.ɵɵlistener(\"expandChange\", function ProjectDetailComponent_div_13_Template_app_browser_preview_expandChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.onBrowserPreviewExpand($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, ProjectDetailComponent_div_13_div_7_Template, 4, 6, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r1.filesCollapsed));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isBrowserPreviewExpanded && !ctx_r1.filesCollapsed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filesCollapsed && !ctx_r1.isBrowserPreviewExpanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isBrowserPreviewExpanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"projectName\", ctx_r1.projectName)(\"url\", ctx_r1.previewUrl)(\"codeServerPort\", ctx_r1.codeServerPort);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isBrowserPreviewExpanded);\n  }\n}\nfunction ProjectDetailComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"div\", 47);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading project...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ProjectDetailComponent = /*#__PURE__*/(() => {\n  class ProjectDetailComponent {\n    constructor(route, projectService, socketService, router, sanitizer) {\n      this.route = route;\n      this.projectService = projectService;\n      this.socketService = socketService;\n      this.router = router;\n      this.sanitizer = sanitizer;\n      this.projectName = '';\n      this.previewUrl = '';\n      this.loading = false;\n      this.isBrowserPreviewExpanded = false;\n      this.filesCollapsed = false;\n      this.isChatExpanded = false;\n      this.showVSCode = false;\n      this.codeServerUrl = '';\n      this.codeServerPort = 8081; // Default code server port\n      // Agent workflow status tracking\n      this.agentActive = false;\n      this.agentStage = '';\n      this.agentProgress = 0;\n      this.agentStatusMessage = '';\n      this.workflowStages = {\n        'setup': {\n          status: 'pending',\n          progress: 0\n        },\n        'research': {\n          status: 'pending',\n          progress: 0\n        },\n        'planning': {\n          status: 'pending',\n          progress: 0\n        },\n        'implementation': {\n          status: 'pending',\n          progress: 0\n        },\n        'testing': {\n          status: 'pending',\n          progress: 0\n        },\n        'validation': {\n          status: 'pending',\n          progress: 0\n        }\n      };\n      // For debounced message saving\n      this.saveMessagesSubject = new Subject();\n      this.subscriptions = [];\n      // Loading states\n      this.chatMessagesLoading = false;\n      this.chatMessagesSaving = false;\n    }\n    ngOnInit() {\n      console.log('[ProjectDetailComponent] ngOnInit called');\n      // Set up debounced message saving (500ms delay)\n      const saveMessagesSub = this.saveMessagesSubject.pipe(debounceTime(500) // Wait for 500ms of quiet time before saving\n      ).subscribe(() => this.saveCurrentMessages());\n      this.subscriptions.push(saveMessagesSub);\n      const routeSub = this.route.params.subscribe(params => {\n        this.projectName = params['name'];\n        console.log('[ProjectDetailComponent] Route param project name:', this.projectName);\n        this.setCodeServerUrl();\n        this.loadProject();\n        this.setupSocketListeners();\n      });\n      this.subscriptions.push(routeSub);\n    }\n    loadProject() {\n      console.log('[ProjectDetailComponent] loadProject called');\n      if (!this.projectName) {\n        console.warn('[ProjectDetailComponent] No project name available. Skipping project load.');\n        return;\n      }\n      this.loading = true;\n      console.log('[ProjectDetailComponent] Fetching project:', this.projectName);\n      this.projectService.getProject(this.projectName).subscribe(response => {\n        console.log('[ProjectDetailComponent] Project fetched successfully:', response);\n        this.project = response.project;\n        this.loading = false;\n        if (this.project.preview_url) {\n          this.previewUrl = this.project.preview_url;\n          console.log('[ProjectDetailComponent] Initial preview URL set:', this.previewUrl);\n        }\n        // Load chat messages and expanded state\n        this.loadChatState();\n      }, error => {\n        console.error('[ProjectDetailComponent] ❌ Error loading project:', error);\n        this.loading = false;\n      });\n    }\n    /**\n     * Load chat messages and expanded state from the backend\n     */\n    loadChatState() {\n      console.log('[ProjectDetailComponent] Loading chat state for:', this.projectName);\n      if (!this.projectName) return;\n      this.chatMessagesLoading = true;\n      this.projectService.getProjectMessages(this.projectName).pipe(retry(1), finalize(() => {\n        this.chatMessagesLoading = false;\n        console.log('[ProjectDetailComponent] Chat state loading completed');\n      })).subscribe(response => {\n        console.log('[ProjectDetailComponent] Chat state loaded:', response);\n        // Set the chat expanded state from the server response\n        if (response.chatExpanded !== undefined) {\n          this.isChatExpanded = response.chatExpanded;\n          console.log('[ProjectDetailComponent] Chat expanded state set to:', this.isChatExpanded);\n        }\n      }, error => {\n        console.error('[ProjectDetailComponent] Error loading chat state:', error);\n      });\n    }\n    setupSocketListeners() {\n      console.log('[ProjectDetailComponent] Setting up socket listeners');\n      // Listen for agent status updates\n      this.socketService.on('agent_status').subscribe(data => {\n        console.log('[ProjectDetailComponent] 🤖 Received agent_status event:', data);\n        if (data.project_name === this.projectName) {\n          this.agentActive = data.active || false;\n          if (data.stage) {\n            this.agentStage = data.stage;\n          }\n          if (data.progress !== undefined) {\n            this.agentProgress = data.progress;\n          }\n          if (data.message) {\n            this.agentStatusMessage = data.message;\n          }\n          // Update specific stage status if provided\n          if (data.stages && typeof data.stages === 'object') {\n            Object.keys(data.stages).forEach(stage => {\n              if (this.workflowStages[stage]) {\n                this.workflowStages[stage] = data.stages[stage];\n              }\n            });\n          }\n        }\n      });\n      this.socketService.on('file_updated').subscribe(data => {\n        console.log('[ProjectDetailComponent] 🔄 Received file_updated event from socket:', data);\n        if (data.project_name === this.projectName) {\n          console.log('[ProjectDetailComponent] file_updated matches current project');\n          if (this.selectedFile && this.selectedFile.path === data.file_path) {\n            console.log('[ProjectDetailComponent] Updating selected file view due to match:', data.file_path);\n            this.onFileSelect({\n              type: 'file_selected',\n              file: this.selectedFile\n            });\n          }\n          if (data.file_path.endsWith('.html')) {\n            console.log('[ProjectDetailComponent] HTML file updated, refreshing preview');\n            this.updatePreview();\n          }\n        }\n      });\n      this.socketService.on('preview_updated').subscribe(data => {\n        console.log('[ProjectDetailComponent] 📺 Received preview_updated event:', data);\n        if (data.project_name === this.projectName) {\n          this.previewUrl = data.preview_url;\n          console.log('[ProjectDetailComponent] Preview URL updated via socket:', this.previewUrl);\n        }\n      });\n      // Listen for project setup completion\n      this.socketService.on('project_setup_complete').subscribe(data => {\n        console.log('[ProjectDetailComponent] 🚀 Received project_setup_complete event:', data);\n        if (data.project_name === this.projectName) {\n          // Update project data with new information\n          if (!this.project) this.project = {};\n          this.project.port = data.port;\n          this.project.actual_dir = data.project_dir;\n          // Update preview URL with the assigned port\n          this.previewUrl = `http://localhost:${data.port}`;\n          console.log('[ProjectDetailComponent] Preview URL updated for new project:', this.previewUrl);\n          // Show VS Code with the correct project directory\n          this.showVSCode = true;\n          this.setCodeServerUrl();\n        }\n      });\n      // Listen for agent_complete to show VS Code/code-server\n      this.socketService.on('agent_complete').subscribe(data => {\n        if (data.project_name === this.projectName) {\n          this.showVSCode = true;\n          // Reset agent workflow status\n          this.agentActive = false;\n          this.agentProgress = 100;\n          this.agentStatusMessage = 'Agent workflow completed successfully';\n          // Set all stages to complete\n          Object.keys(this.workflowStages).forEach(stage => {\n            this.workflowStages[stage].status = 'completed';\n            this.workflowStages[stage].progress = 100;\n          });\n        }\n      });\n    }\n    onFileSelect(event) {\n      console.log('[ProjectDetailComponent] onFileSelect triggered:', event);\n      if (event.type === 'file_selected') {\n        this.selectedFile = event.file;\n        console.log('[ProjectDetailComponent] Selected file set:', this.selectedFile);\n        if (this.selectedFile.path.endsWith('.html')) {\n          console.log('[ProjectDetailComponent] Selected file is HTML. Calling updatePreview.');\n          this.updatePreview();\n        }\n      }\n    }\n    onFileChange(event) {\n      console.log('[ProjectDetailComponent] onFileChange triggered:', event);\n      if (event.type === 'file_saved') {\n        if (event.filePath.endsWith('.html')) {\n          console.log('[ProjectDetailComponent] Saved file is HTML. Updating preview.');\n          this.updatePreview();\n        }\n      }\n    }\n    onMessageEvent(event) {\n      console.log('[ProjectDetailComponent] onMessageEvent triggered:', event);\n      if (event.type === 'message_sent' || event.type === 'message_received') {\n        console.log('[ProjectDetailComponent] Message event received. Triggering debounced save.');\n        // Trigger debounced save\n        this.saveMessagesSubject.next();\n      }\n    }\n    updatePreview() {\n      console.log('[ProjectDetailComponent] updatePreview called');\n      if (this.selectedFile && this.selectedFile.path.endsWith('.html')) {\n        const newPreviewUrl = `http://localhost:5000/projects/${this.projectName}/preview/${this.selectedFile.path}`;\n        console.log('[ProjectDetailComponent] Updating preview URL to:', newPreviewUrl);\n        this.previewUrl = newPreviewUrl;\n      } else {\n        console.warn('[ProjectDetailComponent] No HTML file selected. Skipping preview update.');\n      }\n    }\n    onBrowserPreviewExpand(expanded) {\n      this.isBrowserPreviewExpanded = expanded;\n    }\n    toggleFilesMenu() {\n      this.filesCollapsed = !this.filesCollapsed;\n    }\n    setChatExpanded(expanded) {\n      this.isChatExpanded = expanded;\n      console.log('[ProjectDetailComponent] Chat expanded state changed to:', expanded);\n      // Trigger debounced save to persist expanded state\n      this.saveMessagesSubject.next();\n    }\n    deleteProject() {\n      if (!this.projectName) return;\n      if (!confirm('Are you sure you want to delete this project? This cannot be undone.')) return;\n      this.loading = true;\n      this.projectService.deleteProject(this.projectName).subscribe(() => {\n        this.loading = false;\n        this.router.navigate(['/projects']);\n      }, error => {\n        this.loading = false;\n        alert('Failed to delete project: ' + (error?.error?.detail || error));\n      });\n    }\n    resetProject() {\n      if (!this.projectName) return;\n      if (!confirm('Are you sure you want to reset this project? All files will be deleted, but the folder will remain.')) return;\n      this.loading = true;\n      this.projectService.resetProject(this.projectName).subscribe(() => {\n        this.loading = false;\n        this.loadProject();\n      }, error => {\n        this.loading = false;\n        alert('Failed to reset project: ' + (error?.error?.detail || error));\n      });\n    }\n    toggleVSCode() {\n      this.showVSCode = !this.showVSCode;\n    }\n    setCodeServerUrl() {\n      // Get the port from the project if available, otherwise use default\n      this.codeServerPort = this.project?.port || 8081;\n      // Get the actual project directory path if available\n      const projectPath = this.project?.actual_dir || `/home/<USER>/projects/${this.projectName}`;\n      // Create the code-server URL with the correct port and project path\n      const url = `http://localhost:${this.codeServerPort}/?folder=${encodeURIComponent(projectPath)}`;\n      console.log('[ProjectDetailComponent] Setting code-server URL:', url);\n      this.codeServerUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);\n    }\n    /**\n     * Clean up resources when component is destroyed and save any pending changes\n     */\n    ngOnDestroy() {\n      console.log('[ProjectDetailComponent] ngOnDestroy called, cleaning up subscriptions');\n      // Save any pending changes immediately (don't wait for debounce)\n      this.saveCurrentMessagesImmediately();\n      // Unsubscribe from all subscriptions to prevent memory leaks\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.saveMessagesSubject.complete();\n    }\n    /**\n     * Save the current chat messages immediately without debouncing\n     * This is used when the component is about to be destroyed\n     */\n    saveCurrentMessagesImmediately() {\n      if (!this.projectName || !this.isChatExpanded) return;\n      console.log('[ProjectDetailComponent] Saving chat messages immediately before navigation');\n      // Get the messages and save them with current expanded state\n      this.projectService.getProjectMessages(this.projectName).pipe(\n      // Use first() to complete after getting the first response\n      // so we don't need to unsubscribe from it\n      // This is important for navigation events\n      first(), catchError(error => {\n        console.error('[ProjectDetailComponent] Error fetching messages for immediate save:', error);\n        return of({\n          messages: []\n        });\n      })).subscribe(response => {\n        const messages = response.messages || [];\n        this.projectService.saveProjectMessages(this.projectName, messages, this.isChatExpanded).pipe(first()).subscribe({\n          next: () => console.log('[ProjectDetailComponent] Chat messages saved successfully before navigation'),\n          error: error => console.error('[ProjectDetailComponent] Error saving messages before navigation:', error)\n        });\n      });\n    }\n    /**\n     * Save the current chat messages and expanded state\n     * This is called after debouncing to prevent excessive API calls\n     */\n    saveCurrentMessages() {\n      if (!this.projectName) return;\n      // Set saving state to display UI indicators if needed\n      this.chatMessagesSaving = true;\n      console.log('[ProjectDetailComponent] Saving chat messages (debounced)');\n      this.projectService.getProjectMessages(this.projectName).pipe(\n      // Retry up to 2 times with increasing delays\n      retry(2), catchError(error => {\n        console.error('[ProjectDetailComponent] Error fetching messages to save:', error);\n        // Return empty array instead of error to allow chain to continue\n        return of({\n          messages: [],\n          chatExpanded: this.isChatExpanded\n        });\n      })).subscribe(response => {\n        const messages = response.messages || [];\n        // Save the messages along with the current expanded state\n        this.projectService.saveProjectMessages(this.projectName, messages, this.isChatExpanded).pipe(\n        // Retry saving once if it fails\n        retry(1), catchError(error => {\n          console.error('[ProjectDetailComponent] Error saving chat messages:', error);\n          return throwError(() => new Error('Failed to save chat messages after retry'));\n        }),\n        // Always turn off saving state when operation completes (success or error)\n        finalize(() => {\n          this.chatMessagesSaving = false;\n          console.log('[ProjectDetailComponent] Message save operation finished');\n        })).subscribe({\n          next: () => console.log('[ProjectDetailComponent] Chat messages saved successfully'),\n          error: error => console.error('[ProjectDetailComponent] Final error saving messages:', error)\n        });\n      });\n    }\n    static {\n      this.ɵfac = function ProjectDetailComponent_Factory(t) {\n        return new (t || ProjectDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.SocketService), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i4.DomSanitizer));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectDetailComponent,\n        selectors: [[\"app-project-detail\"]],\n        decls: 15,\n        vars: 7,\n        consts: [[1, \"project-container\"], [1, \"project-header\"], [1, \"project-title-and-status\"], [\"class\", \"agent-workflow-status\", 4, \"ngIf\"], [1, \"project-actions\"], [3, \"disabled\", \"click\"], [2, \"margin-left\", \"8px\", \"color\", \"red\", 3, \"disabled\", \"click\"], [2, \"margin-left\", \"8px\", 3, \"disabled\", \"click\"], [\"class\", \"project-content\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"agent-workflow-status\"], [1, \"status-indicator\", \"pulsing\"], [1, \"status-details\"], [1, \"current-stage\"], [1, \"stage-label\"], [1, \"stage-value\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"status-message\"], [1, \"project-content\", 3, \"ngClass\"], [\"class\", \"sidebar\", 4, \"ngIf\"], [\"class\", \"sidebar-collapsed-bar\", 4, \"ngIf\"], [1, \"main-content\"], [\"class\", \"editor-section\", 4, \"ngIf\"], [1, \"preview-section\"], [3, \"projectName\", \"url\", \"codeServerPort\", \"expandChange\"], [\"class\", \"chat-section\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"sidebar\"], [1, \"sidebar-header\", 2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"flex-end\", \"padding\", \"4px\"], [\"title\", \"Collapse Files Menu\", 3, \"click\"], [3, \"projectName\", \"fileSelectEvent\"], [1, \"sidebar-collapsed-bar\"], [\"title\", \"Expand Files Menu\", 3, \"click\"], [1, \"editor-section\"], [4, \"ngIf\"], [3, \"projectName\", \"filePath\", \"fileChangeEvent\"], [\"title\", \"VS Code (code-server)\", 1, \"code-server-iframe\", 3, \"src\"], [1, \"chat-section\", 3, \"ngClass\"], [4, \"ngIf\", \"ngIfElse\"], [\"collapsedHeader\", \"\"], [3, \"projectName\", \"messagesLoading\", \"messagesSaving\", \"messageEvent\", \"chatExpandChange\"], [1, \"chat-header-collapsed\"], [1, \"chat-label\"], [\"aria-label\", \"Expand Chat\", 1, \"expand-chat-btn\", 3, \"click\"], [1, \"loading-container\"], [1, \"spinner\"]],\n        template: function ProjectDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, ProjectDetailComponent_div_5_Template, 16, 7, \"div\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 4)(7, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function ProjectDetailComponent_Template_button_click_7_listener() {\n              return ctx.loadProject();\n            });\n            i0.ɵɵtext(8, \" Refresh \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function ProjectDetailComponent_Template_button_click_9_listener() {\n              return ctx.deleteProject();\n            });\n            i0.ɵɵtext(10, \"Delete Project\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function ProjectDetailComponent_Template_button_click_11_listener() {\n              return ctx.resetProject();\n            });\n            i0.ɵɵtext(12, \"Reset Project\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(13, ProjectDetailComponent_div_13_Template, 8, 10, \"div\", 8);\n            i0.ɵɵtemplate(14, ProjectDetailComponent_div_14_Template, 4, 0, \"div\", 9);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.projectName);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.agentActive);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.agentActive);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.agentActive);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.agentActive);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgIf, i6.ChatComponent, i7.CodeEditorComponent, i8.BrowserPreviewComponent, i9.FileExplorerComponent, i5.TitleCasePipe],\n        styles: [\".project-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;padding:0;overflow:hidden}.project-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:18px 32px;background:linear-gradient(90deg,#4f8cff 70%,#a0c4ff 100%);color:#fff;border-top-left-radius:18px;border-top-right-radius:18px;box-shadow:0 2px 12px #0000000f;font-size:22px;font-weight:700}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:24px;font-weight:500}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:#ffffff26;border-radius:8px;padding:8px 12px;margin-top:5px;max-width:500px}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%;background-color:#4caf50;margin-right:10px;flex-shrink:0}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-indicator.pulsing[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]{flex:1}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .current-stage[_ngcontent-%COMP%]{font-size:14px;font-weight:500;margin-bottom:4px}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .current-stage[_ngcontent-%COMP%]   .stage-label[_ngcontent-%COMP%]{opacity:.8}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .current-stage[_ngcontent-%COMP%]   .stage-value[_ngcontent-%COMP%]{font-weight:600}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:4px}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{flex:1;height:8px;background-color:#fff3;border-radius:4px;overflow:hidden;margin-right:8px}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{height:100%;background-color:#4caf50;border-radius:4px;transition:width .5s ease-in-out}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{font-size:12px;font-weight:600;min-width:35px}.project-header[_ngcontent-%COMP%]   .project-title-and-status[_ngcontent-%COMP%]   .agent-workflow-status[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   .status-message[_ngcontent-%COMP%]{font-size:12px;opacity:.9;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.project-header[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.project-header[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:8px 18px;background:linear-gradient(90deg,#4f8cff 80%,#a0c4ff 100%);color:#fff;border:none;border-radius:8px;font-size:15px;font-weight:600;cursor:pointer;box-shadow:0 1px 4px #0000000f;margin:0 6px 0 0;transition:background .2s,box-shadow .2s}.project-header[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#4f8cff;box-shadow:0 2px 8px #4f8cff2e}.project-header[_ngcontent-%COMP%]   .project-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{background-color:#bbdefb;cursor:not-allowed}.project-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100%;min-height:0;background:linear-gradient(135deg,#fafdff 80%,#e3f0ff 100%);padding:0;gap:0}.main-content[_ngcontent-%COMP%]{flex:1 1 auto;min-height:0;overflow:auto;display:flex;flex-direction:row;gap:0;padding:24px 24px 0;animation:_ngcontent-%COMP%_fadeInUp .7s}.editor-section[_ngcontent-%COMP%], .preview-section[_ngcontent-%COMP%]{flex:1 1 0;overflow:hidden}.editor-section[_ngcontent-%COMP%]{position:relative;width:100%;height:100%;min-height:400px;display:flex;flex-direction:column}.code-server-iframe[_ngcontent-%COMP%]{width:100%;height:100%;min-height:400px;border:none;border-radius:8px;background:#fff;flex:1 1 auto;display:block}.chat-section[_ngcontent-%COMP%]{flex:0 0 auto;width:100%;background:#fafdff;border-top:1px solid #e0e0e0;box-shadow:0 -2px 12px #4f8cff14;z-index:2;display:flex;flex-direction:column;justify-content:flex-end;border-bottom-left-radius:8px;border-bottom-right-radius:8px;animation:_ngcontent-%COMP%_fadeInChat .8s;margin:0;padding:0}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(40px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_fadeInChat{0%{opacity:0;transform:translateY(60px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #4caf50b3;opacity:1}70%{box-shadow:0 0 0 10px #4caf5000;opacity:.8}to{box-shadow:0 0 #4caf5000;opacity:1}}.project-content.files-collapsed[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]{flex:1 1 100%;width:100%;margin-left:0}.project-content.files-collapsed[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]{display:none!important}.chat-section.collapsed[_ngcontent-%COMP%]{height:40px;min-height:40px;max-height:40px;overflow:hidden;transition:max-height .3s,min-height .3s,height .3s}.chat-section.expanded[_ngcontent-%COMP%]{height:auto;min-height:120px;max-height:600px;transition:max-height .3s,min-height .3s,height .3s}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;height:100%}.loading-container[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{width:50px;height:50px;border:5px solid #f3f3f3;border-top:5px solid #4f8cff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:20px;font-size:16px;color:#666}@media (max-width: 900px){.main-content[_ngcontent-%COMP%]{flex-direction:column;padding:12px 6px 0}.project-header[_ngcontent-%COMP%]{padding:12px 8px;font-size:18px}.chat-section[_ngcontent-%COMP%]{border-bottom-left-radius:6px;border-bottom-right-radius:6px}}.sidebar-collapsed-bar[_ngcontent-%COMP%]{width:20px;min-width:20px;height:100%;display:flex;align-items:center;justify-content:center;background:#f5f5f5;border-right:1px solid #e0e0e0;box-shadow:2px 0 6px #00000008;z-index:2}.sidebar-collapsed-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:#2196f3;color:#fff;border:none;border-radius:4px;padding:4px 6px;font-size:16px;cursor:pointer;transition:background .2s}.sidebar-collapsed-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#1976d2}.chat-header-collapsed[_ngcontent-%COMP%]{position:sticky;bottom:0;left:0;right:0;z-index:10;background:linear-gradient(90deg,#4f8cff 80%,#a0c4ff 100%);color:#fff;display:flex;align-items:center;justify-content:space-between;height:40px;min-height:40px;max-height:40px;padding:0 24px;border-top:1px solid #e0e0e0;border-bottom-left-radius:8px;border-bottom-right-radius:8px;box-shadow:0 -2px 12px #4f8cff14;font-size:16px;font-weight:600}.chat-header-collapsed[_ngcontent-%COMP%]   .expand-chat-btn[_ngcontent-%COMP%]{background:#fff;color:#4f8cff;border:none;border-radius:6px;font-size:18px;font-weight:700;cursor:pointer;padding:2px 12px;margin-left:12px;transition:background .2s,color .2s}.chat-header-collapsed[_ngcontent-%COMP%]   .expand-chat-btn[_ngcontent-%COMP%]:hover{background:#e3f0ff;color:#1976d2}\"]\n      });\n    }\n  }\n  return ProjectDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}