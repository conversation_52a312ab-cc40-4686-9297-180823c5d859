{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/socket.service\";\nimport * as i2 from \"@angular/router\";\nconst _c0 = function () {\n  return [\"/\"];\n};\nconst _c1 = function () {\n  return [\"/projects\"];\n};\nconst _c2 = function () {\n  return [\"/testing\"];\n};\nconst _c3 = function () {\n  return [\"/config\"];\n};\nexport class AppComponent {\n  constructor(socketService) {\n    this.socketService = socketService;\n    this.title = 'Autonomous AI Software Development Agent';\n  }\n  ngOnInit() {\n    this.socketService.connect();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.SocketService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 18,\n      vars: 9,\n      consts: [[1, \"app-container\"], [1, \"app-header\"], [3, \"routerLink\"], [1, \"app-content\"], [1, \"app-footer\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"h1\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"nav\")(5, \"a\", 2);\n          i0.ɵɵtext(6, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"a\", 2);\n          i0.ɵɵtext(8, \"Projects\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 2);\n          i0.ɵɵtext(10, \"Testing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"a\", 2);\n          i0.ɵɵtext(12, \"Configuration\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"main\", 3);\n          i0.ɵɵelement(14, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"footer\", 4)(16, \"p\");\n          i0.ɵɵtext(17, \"Autonomous AI Software Development Agent \\u00A9 2025\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.title);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(8, _c3));\n        }\n      },\n      dependencies: [i2.RouterOutlet, i2.RouterLink],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100vh;\\n}\\n\\n.app-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);\\n  color: white;\\n  padding: 16px 24px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 500;\\n  margin: 0;\\n  display: flex;\\n  align-items: center;\\n}\\n.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:before {\\n  content: \\\"code\\\";\\n  font-family: \\\"Material Icons\\\";\\n  margin-right: 10px;\\n  font-size: 28px;\\n}\\n.app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n}\\n.app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: white;\\n  text-decoration: none;\\n  font-weight: 500;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n}\\n\\n.app-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 20px;\\n  background-color: #f5f7fa;\\n}\\n\\n.app-footer[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  padding: 16px 24px;\\n  text-align: center;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.app-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 14px;\\n}\\n\\n@media (max-width: 768px) {\\n  .app-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: 16px;\\n  }\\n  .app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n  .app-header[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "socketService", "title", "ngOnInit", "connect", "i0", "ɵɵdirectiveInject", "i1", "SocketService", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "_c1", "_c2", "_c3"], "sources": ["C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\app.component.ts", "C:\\SourceProjects\\AutonomousAI\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { SocketService } from './services/socket.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'Autonomous AI Software Development Agent';\n  \n  constructor(private socketService: SocketService) { }\n  \n  ngOnInit(): void {\n    this.socketService.connect();\n  }\n}\n", "<div class=\"app-container\">\n  <header class=\"app-header\">\n    <h1>{{ title }}</h1>\n    <nav>\n      <a [routerLink]=\"['/']\">Home</a>\n      <a [routerLink]=\"['/projects']\">Projects</a>\n      <a [routerLink]=\"['/testing']\">Testing</a>\n      <a [routerLink]=\"['/config']\">Configuration</a>\n\n    </nav>\n  </header>\n  \n  <main class=\"app-content\">\n    <router-outlet></router-outlet>\n  </main>\n  \n  <footer class=\"app-footer\">\n    <p>Autonomous AI Software Development Agent &copy; 2025</p>\n  </footer>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;AAQA,OAAM,MAAOA,YAAY;EAGvBC,YAAoBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;IAFjC,KAAAC,KAAK,GAAG,0CAA0C;EAEE;EAEpDC,QAAQA,CAAA;IACN,IAAI,CAACF,aAAa,CAACG,OAAO,EAAE;EAC9B;;;uBAPWL,YAAY,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAZT,YAAY;MAAAU,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRzBV,EAAA,CAAAY,cAAA,aAA2B;UAEnBZ,EAAA,CAAAa,MAAA,GAAW;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACpBd,EAAA,CAAAY,cAAA,UAAK;UACqBZ,EAAA,CAAAa,MAAA,WAAI;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAChCd,EAAA,CAAAY,cAAA,WAAgC;UAAAZ,EAAA,CAAAa,MAAA,eAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAC5Cd,EAAA,CAAAY,cAAA,WAA+B;UAAAZ,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAC1Cd,EAAA,CAAAY,cAAA,YAA8B;UAAAZ,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAKnDd,EAAA,CAAAY,cAAA,eAA0B;UACxBZ,EAAA,CAAAe,SAAA,qBAA+B;UACjCf,EAAA,CAAAc,YAAA,EAAO;UAEPd,EAAA,CAAAY,cAAA,iBAA2B;UACtBZ,EAAA,CAAAa,MAAA,4DAAoD;UAAAb,EAAA,CAAAc,YAAA,EAAI;;;UAfvDd,EAAA,CAAAgB,SAAA,GAAW;UAAXhB,EAAA,CAAAiB,iBAAA,CAAAN,GAAA,CAAAd,KAAA,CAAW;UAEVG,EAAA,CAAAgB,SAAA,GAAoB;UAApBhB,EAAA,CAAAkB,UAAA,eAAAlB,EAAA,CAAAmB,eAAA,IAAAC,GAAA,EAAoB;UACpBpB,EAAA,CAAAgB,SAAA,GAA4B;UAA5BhB,EAAA,CAAAkB,UAAA,eAAAlB,EAAA,CAAAmB,eAAA,IAAAE,GAAA,EAA4B;UAC5BrB,EAAA,CAAAgB,SAAA,GAA2B;UAA3BhB,EAAA,CAAAkB,UAAA,eAAAlB,EAAA,CAAAmB,eAAA,IAAAG,GAAA,EAA2B;UAC3BtB,EAAA,CAAAgB,SAAA,GAA0B;UAA1BhB,EAAA,CAAAkB,UAAA,eAAAlB,EAAA,CAAAmB,eAAA,IAAAI,GAAA,EAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}