<div class="chat-container" [ngClass]="{'expanded': isChatExpanded}">
  <div class="chat-header">
    <div class="agent-title">
      <span class="agent-icon">🤖</span>
      <h2>Autonomous AI Agent</h2>
      <span class="agent-status" [ngClass]="{'active': loading || longTaskInProgress || agentTyping}">
        {{loading || longTaskInProgress ? 'Working' : agentTyping ? 'Typing...' : 'Ready'}}
      </span>
    </div>
    <div class="model-controls">
      <div class="model-selector cloud-model">
        <label for="modelSelect"><i class="fa fa-cloud"></i> Cloud LLM:</label>
        <select id="modelSelect" [(ngModel)]="selectedModel" (change)="onModelChange(selectedModel)">
          <optgroup label="OpenAI">
            <option *ngFor="let model of getModelsByProvider('openai')" [value]="model.id">{{ model.name }}</option>
          </optgroup>
          <optgroup label="DeepSeek">
            <option *ngFor="let model of getModelsByProvider('deepseek')" [value]="model.id">{{ model.name }}</option>
          </optgroup>
          <option *ngFor="let model of getOtherModels()" [value]="model.id">{{ model.name }}</option>
        </select>
      </div>
      <div class="model-selector local-model">
        <label for="localLlmSelect"><i class="fa fa-desktop"></i> Local LLM:</label>
        <select id="localLlmSelect" [(ngModel)]="selectedLocalLlmModel" (change)="onLocalLlmModelChange(selectedLocalLlmModel)">
          <option *ngFor="let llm of localLlmModels" [value]="llm.id">{{ llm.name }}</option>
        </select>
      </div>
      <div class="streaming-toggle">
        <label for="streamingToggle">
          <input type="checkbox" id="streamingToggle" [(ngModel)]="streamingEnabled"> 
          Streaming
        </label>
      </div>
    </div>
    <div class="chat-actions">
      <button class="mode-toggle-btn" [ngClass]="{'active': autonomousMode}" (click)="toggleAutonomousMode()" title="Toggle between autonomous and assisted mode">
        <i class="fa fa-robot"></i>
        <span>{{autonomousMode ? 'Autonomous' : 'Assisted'}}</span>
      </button>
      <button class="memory-btn" (click)="resetContextMemory()" title="Reset conversation memory">
        <i class="fa fa-brain"></i>
      </button>
      <button class="api-toggle-btn" (click)="toggleApiPayloads()" [ngClass]="{'active': showApiPayloads}" title="Toggle API payloads visibility">
        <i class="fa fa-exchange-alt"></i>
      </button>
      <button class="clear-chat-btn" (click)="clearChat()" title="Clear chat history">
        <i class="fa fa-trash"></i>
      </button>
      <button class="expand-chat-btn" (click)="toggleChatExpand()" [attr.aria-label]="isChatExpanded ? 'Collapse Chat' : 'Expand Chat'">
        <i class="fa" [ngClass]="{'fa-chevron-up': isChatExpanded, 'fa-chevron-down': !isChatExpanded}"></i>
      </button>
    </div>

    <!-- Debug Log Section -->
    <div class="debug-logs" *ngIf="debugLogs.length > 0">
      <div class="debug-header">
        <span>🔍 Debug Logs</span>
        <button (click)="clearDebugLogs()" class="clear-logs-btn">Clear</button>
      </div>
      <div class="debug-content">
        <div *ngFor="let log of debugLogs.slice(-10)" class="debug-log-entry" [ngClass]="'log-' + log.level">
          <span class="log-time">{{log.timestamp | date:'HH:mm:ss'}}</span>
          <span class="log-level">{{log.level.toUpperCase()}}</span>
          <span class="log-message">{{log.message}}</span>
        </div>
      </div>
    </div>
  </div>

  <div class="messages-container" #messagesContainer>
    <div *ngIf="messages.length === 0" class="empty-state">
      <p>No messages yet. Start a conversation with the AI agent.</p>
    </div>
    
    <div *ngFor="let message of messages | reverse" class="message" [ngClass]="{
      'user-message': message.sender === 'user', 
      'agent-message': message.sender === 'agent', 
      'system-message': message.sender === 'system',
      'browser-message': message.messageType === 'browser',
      'openai-message': message.messageType === 'openai',
      'llm-message': message.messageType === 'local_llm',
      'error-message': message.messageType === 'error',
      'api-request-message': message.messageType === 'api_request',
      'api-response-message': message.messageType === 'api_response',
      'streaming': message.sender === 'agent' && !message.isComplete
      }">
      <div class="avatar" [ngClass]="{
        'user-avatar': message.sender === 'user', 
        'ai-avatar': message.sender === 'agent', 
        'system-avatar': message.sender === 'system',
        'browser-avatar': message.messageType === 'browser',
        'openai-avatar': message.messageType === 'openai',
        'llm-avatar': message.messageType === 'local_llm',
        'error-avatar': message.messageType === 'error',
        'api-request-avatar': message.messageType === 'api_request',
        'api-response-avatar': message.messageType === 'api_response'
        }">
        <span *ngIf="message.sender === 'user'">U</span>
        <span *ngIf="message.sender === 'agent' && !message.messageType">AI</span>
        <span *ngIf="message.sender === 'system'">S</span>
        <span *ngIf="message.messageType === 'browser'">🌐</span>
        <span *ngIf="message.messageType === 'openai'">🧠</span>
        <span *ngIf="message.messageType === 'local_llm'">💻</span>
        <span *ngIf="message.messageType === 'error'">⚠️</span>
        <span *ngIf="message.messageType === 'system_notification'">📣</span>
        <span *ngIf="message.messageType === 'api_request'">📤</span>
        <span *ngIf="message.messageType === 'api_response'">📥</span>
      </div>
      <div class="bubble">
        <div class="message-header">
          <span class="sender">
            <span *ngIf="message.sender === 'user'">👤 You</span>
            <span *ngIf="message.sender === 'agent' && !message.messageType">🤖 AI Agent</span>
            <span *ngIf="message.sender === 'system'">⚙️ System</span>
            <span *ngIf="message.messageType === 'browser'">🔍 Browser</span>
            <span *ngIf="message.messageType === 'openai'">🧠 OpenAI</span>
            <span *ngIf="message.messageType === 'lm_studio'">🧠 LM Studio</span>
            <span *ngIf="message.messageType === 'local_llm'">💻 Local LLM</span>
            <span *ngIf="message.messageType === 'terminal'">💻 Terminal</span>
            <span *ngIf="message.messageType === 'llm_openai'">🧠 OpenAI</span>
            <span *ngIf="message.messageType === 'llm_lm_studio'">🧠 LM Studio</span>
            <span *ngIf="message.messageType === 'error'">⚠️ Error</span>
            <span *ngIf="message.messageType === 'plan'">📋 Plan</span>
            <span *ngIf="message.messageType === 'system_notification'">📣 Notification</span>
            <span *ngIf="message.messageType === 'api_request'">📤 API Request</span>
            <span *ngIf="message.messageType === 'api_response'">📥 API Response</span>
          </span>
          <span class="timestamp">{{ message.timestamp | date:'short' }}</span>
          <span class="message-type" *ngIf="message.metadata?.modelId">{{ message.metadata.modelId }}</span>
          
          <!-- Streaming indicator for incomplete messages -->
          <span class="streaming-indicator" *ngIf="message.sender === 'agent' && !message.isComplete">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
          </span>
        </div>
        <div class="message-content" [innerHTML]="message.content"></div>
        <div class="message-metadata" *ngIf="message.metadata && message.metadata.executionTime">
          <span class="execution-time">Execution time: {{ message.metadata.executionTime }}ms</span>
        </div>
        
        <!-- Message reactions -->
        <div class="message-reactions">
          <div class="reaction-buttons">
            <button class="reaction-button" (click)="addReaction(message.id, 'like')" 
                    [class.active]="message.reactions && message.reactions.includes('like')">
              👍 <span *ngIf="message.reactions && message.reactions.includes('like')">1</span>
            </button>
            <button class="reaction-button" (click)="addReaction(message.id, 'dislike')"
                    [class.active]="message.reactions && message.reactions.includes('dislike')">
              👎 <span *ngIf="message.reactions && message.reactions.includes('dislike')">1</span>
            </button>
            <button class="reaction-button" (click)="addReaction(message.id, 'love')"
                    [class.active]="message.reactions && message.reactions.includes('love')">
              ❤️ <span *ngIf="message.reactions && message.reactions.includes('love')">1</span>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Typing indicator outside of messages -->
    <div *ngIf="agentTyping" class="typing-indicator">
      <div class="typing-dot"></div>
      <div class="typing-dot"></div>
      <div class="typing-dot"></div>
    </div>
    
    <div *ngIf="(loading || messagesLoading) && !agentTyping" class="loading-indicator">
      <div class="spinner"></div>
      <p>AI Agent is thinking...</p>
    </div>
  </div>
  
  <!-- Debug/Developer Tools - Agent Thinking Display -->
  <div *ngIf="showAgentThinking && agentThinkingContent" class="agent-thinking-panel">
    <div class="thinking-header">
      <h4>Agent Thinking Process</h4>
      <button (click)="toggleAgentThinking()">Close</button>
    </div>
    <pre class="thinking-content">{{agentThinkingContent}}</pre>
  </div>
  
  <!-- Long-running task banner -->
  <div *ngIf="longTaskInProgress" class="long-task-banner">
    <span class="spinner"></span>
    <span class="banner-text">The AI assistant is working on a complex, long-running task. This may take several minutes. Please do not close this window.</span>
  </div>
  
  <!-- Autonomous Agent Workflow Visualization -->
  <div *ngIf="subtasks && subtasks.length > 0" class="autonomous-workflow">
    <div class="workflow-header">
      <h4>Autonomous Agent Workflow</h4>
    </div>
    
    <!-- Progress Stages as in Screenshot -->
    <div class="progress-stages">
      <div class="stage-icon planning" [class.active]="currentStage >= 1">
        <span>🔍</span>
        <div>Planning</div>
      </div>
      <div class="stage-connector" [class.active]="currentStage >= 1"></div>
      <div class="stage-icon design" [class.active]="currentStage >= 2">
        <span>🎨</span>
        <div>Design</div>
      </div>
      <div class="stage-connector" [class.active]="currentStage >= 2"></div>
      <div class="stage-icon implementation" [class.active]="currentStage >= 3">
        <span>🛠️</span>
        <div>Implementation</div>
      </div>
      <div class="stage-connector" [class.active]="currentStage >= 3"></div>
      <div class="stage-icon testing" [class.active]="currentStage >= 4">
        <span>🧪</span>
        <div>Testing</div>
      </div>
    </div>
    
    <!-- Simple Progress Counter -->
    <div class="progress-counter">
      {{completedSubtasks}}/{{subtasks.length}} steps completed
    </div>
    
    <!-- Subtask Cards Grid (Exactly as in Screenshots) -->
    <div class="subtasks-grid">
      <div *ngFor="let subtask of subtasks; let i = index" class="subtask-card">
        <!-- Card Header -->
        <div class="subtask-header">
          <div class="subtask-number">{{ i + 1 }}</div>
          <div class="subtask-type">{{ subtask.subtask.type || 'COMMAND' }}</div>
          <div class="model-label" *ngIf="subtask.model_type === 'openai'">GPT</div>
          <div class="model-label local" *ngIf="subtask.model_type === 'local'">Local LLM</div>
          <div class="hot-label" *ngIf="subtask.web_research_used">HOT</div>
        </div>
        
        <!-- Task Description -->
        <div class="task-description">
          <div class="bullet">▶</div>
          <div>{{ subtask.subtask.description || '' }}</div>
        </div>
        
        <!-- Output Area -->
        <div class="output-area">
          <div class="output-label">Output:</div>
          <div class="output-content" [class.error]="subtask.error">
            {{ subtask.result || 'Processing...' }}
          </div>
          
          <!-- Additional outputs if available -->
          <div class="web-results" *ngIf="subtask.web_results">Web Search Results</div>
          <div class="file-changes" *ngIf="subtask.file_diff">File Changes</div>
        </div>
        
        <!-- Simple Feedback UI -->
        <div class="feedback-row" *ngIf="subtask.completed">
          <span>Was this helpful?</span>
          <div class="feedback-options">
            <button [class.selected]="subtask.feedback === 'up'" (click)="setSubtaskFeedback(i, 'up')"></button>
            <button [class.selected]="subtask.feedback === 'down'" (click)="setSubtaskFeedback(i, 'down')"></button>
          </div>
        </div>
        
        <!-- Retry Button -->
        <button *ngIf="subtask.error" class="retry-button" (click)="retrySubtask(i)">
          Retry
        </button>
      </div>
    </div>
  </div>
  
  <!-- We've completely hidden the file changes section to avoid display issues -->
  <!-- If you need to see file changes, you can re-enable this section later -->
  
  <form [formGroup]="messageForm" (ngSubmit)="sendMessage()" class="message-form">
    <textarea 
      formControlName="message" 
      placeholder="Type your message here..." 
      [disabled]="loading || messagesLoading || messagesSaving"
      rows="3"
      (keydown.enter)="onEnter($any($event))"
    ></textarea>
    <button type="submit" [disabled]="messageForm.invalid || loading || messagesLoading || messagesSaving">
      <span>Send</span>
    </button>
    <button class="export-chat-btn" type="button" (click)="exportChat()" [disabled]="messages.length === 0 || loading || messagesSaving" title="Export chat history">Export Chat</button>
    <button class="export-chat-btn" type="button" (click)="exportDynamicChat()" [disabled]="messages.length === 0 || loading || messagesSaving" title="Export chat as file (dynamic, no storage)">Export Dynamic Chat</button>
    <button class="copy-chat-btn" type="button" (click)="copyChat()" [disabled]="messages.length === 0 || loading || messagesSaving" title="Copy entire chat to clipboard">Copy Chat</button>
    <div *ngIf="messagesSaving" class="saving-indicator">Saving messages...</div>
  </form>
</div>
