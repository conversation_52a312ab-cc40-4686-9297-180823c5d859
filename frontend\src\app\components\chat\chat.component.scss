// Import autonomous workflow styles
@import './autonomous-workflow.scss';
@import './feedback-styles.scss';
@import './fixed-ui.scss';
@import './message-types.scss'; // Import the new message type styles

.chat-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  background: linear-gradient(135deg, #fafdff 80%, #e3f0ff 100%);
  border-radius: 18px;
  box-shadow: 0 6px 32px rgba(0, 0, 0, 0.10);
  overflow: hidden;
  margin: 0 auto;
  transition: box-shadow 0.3s;
}

.chat-container:focus-within, .chat-container:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 16px;
  background: #3378d1;
  color: white;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  height: 46px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  animation: fadeInDown 0.5s;
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.agent-title {
  display: flex;
  align-items: center;
  gap: 12px;

  h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 700;
    letter-spacing: 0.5px;
    white-space: nowrap;
  }

  .agent-icon {
    font-size: 24px;
    margin-right: 4px;
  }

  .agent-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    
    &.active {
      background-color: #22c55e;
      animation: pulse 1.5s infinite;
    }
  }
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4); }
  70% { box-shadow: 0 0 0 8px rgba(34, 197, 94, 0); }
  100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
}

.model-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255,255,255,0.18);
  padding: 7px 14px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
  transition: background 0.2s;
  
  .autonomous-workflow {
    margin-top: 16px;
    padding: 20px;
    background: linear-gradient(to bottom, #f5f7fa, #ffffff);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.06);
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .workflow-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    
    h4 {
      font-size: 18px;
      font-weight: 600;
      color: #2a5298;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
      
      i {
        font-size: 20px;
        color: #4f8cff;
      }
    }
    
    .workflow-controls {
      display: flex;
      gap: 8px;
    }
    
    .workflow-action-btn {
      background-color: #f0f5ff;
      border: 1px solid #d0e1ff;
      border-radius: 6px;
      padding: 6px 10px;
      color: #4f8cff;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:hover {
        background-color: #e0edff;
        transform: translateY(-1px);
      }
    }
  }
}

.model-selector label {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #2d3a4a;
}

.model-selector select {
  padding: 6px 12px 6px 32px;
  border-radius: 6px;
  border: 1.5px solid #b3d7ff;
  background: url('data:image/svg+xml;utf8,<svg fill="%234f8cff" height="16" viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>') no-repeat 8px center/18px 18px, #fff;
  color: #333;
  font-size: 15px;
  transition: border 0.2s;
  appearance: none;
}

.model-selector select:focus {
  border: 1.5px solid #4f8cff;
  outline: none;
}

.clear-chat-btn {
  background: linear-gradient(90deg, #e57373 60%, #ffb199 100%);
  border: none;
  color: #fff;
  padding: 7px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  margin-left: 10px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  gap: 6px;
}

.clear-chat-btn::before {
  content: '\1F5D1'; /* 🗑️ */
  font-size: 1.1em;
}

.clear-chat-btn:hover {
  background: linear-gradient(90deg, #ffb199 60%, #e57373 100%);
  color: #fffde7;
  box-shadow: 0 2px 8px rgba(229,115,115,0.18);
}

.chat-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mode-toggle-btn {
  padding: 6px 12px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 120px;
  text-align: center;
  white-space: nowrap;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  .fa-robot {
    font-size: 14px;
  }
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  }
  
  &.active {
    background-color: #22c55e;
    border-color: #16a34a;
  }
}

.clear-chat-btn {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 0 5px;
}

.clear-chat-btn:hover {
  background-color: rgba(255, 60, 60, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.expand-chat-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.2s ease;
  margin-left: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.expand-chat-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.messages-container {
  flex: 1 1 auto;
  padding: 0 32px 0 32px;
  overflow-y: auto;
  display: flex;
  flex-direction: column-reverse;
  gap: 18px;
  background: transparent;
  scrollbar-width: thin;
  scrollbar-color: #b3d7ff #fafdff;
  animation: fadeIn 0.6s;
  max-height: 300px;
  min-height: 80px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.messages-container::-webkit-scrollbar {
  width: 8px;
}
.messages-container::-webkit-scrollbar-thumb {
  background: #b3d7ff;
  border-radius: 4px;
}
.messages-container::-webkit-scrollbar-track {
  background: #fafdff;
}

.message {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  opacity: 0;
  animation: slideIn 0.4s forwards;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4f8cff 60%, #a0c4ff 100%);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.08);
  flex-shrink: 0;
  margin-bottom: 2px;
}

.user-message .avatar {
  background: linear-gradient(135deg, #ffb199 60%, #e57373 100%);
}

.system-message .avatar {
  background: linear-gradient(135deg, #ffc107 60%, #ff9800 100%);
}

.bubble {
  padding: 14px 20px;
  border-radius: 16px;
  max-width: 420px;
  min-width: 60px;
  word-break: break-word;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  font-size: 16px;
  line-height: 1.6;
  position: relative;
  transition: background 0.2s;
}

.user-message .bubble {
  background: linear-gradient(90deg, #ffb199 60%, #e57373 100%);
  color: #fff;
  border-bottom-right-radius: 4px;
  align-self: flex-end;
}

.agent-message .bubble {
  background: #fafdff;
  color: #2d3a4a;
  border-bottom-left-radius: 4px;
  align-self: flex-start;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 13px;
  opacity: 0.8;
}

.sender {
  font-weight: bold;
}

.timestamp {
  opacity: 0.7;
}

.message-content {
  line-height: 1.5;
  font-size: 15px;
}

.message-form {
  display: flex;
  gap: 14px;
  padding: 12px 32px 16px 32px;
  background: #fff;
  border-top: 1px solid #e0e0e0;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  box-shadow: 0 -1px 4px rgba(0,0,0,0.03);
  align-items: flex-end;
}

.message-form textarea {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: none;
  font-family: inherit;
  height: 40px;
  font-size: 14px;
  background: white;
}

.message-form textarea:focus {
  border: 1.5px solid #4f8cff;
  outline: none;
}

.message-form button {
  padding: 8px 16px;
  background: #e0e0e0;
  color: #555;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s;
  display: flex;
  align-items: center;
}

.message-form button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.message-form button:not(:disabled):hover {
  background: #d0d0d0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 180px;
  color: #888;
  text-align: center;
  font-size: 17px;
  opacity: 0.8;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  color: #777;
}

.spinner {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top: 3px solid #4f8cff;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 900px) {
  .chat-header, .message-form, .messages-container {
    padding-left: 12px;
    padding-right: 12px;
  }
  .messages-container {
    max-height: 180px;
    min-height: 60px;
  }
}

@media (max-width: 600px) {
  .chat-header, .message-form, .messages-container {
    padding-left: 2px;
    padding-right: 2px;
  }
  .chat-header {
    font-size: 18px;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .message-form button {
    padding: 10px 16px;
    font-size: 15px;
  }
}

/* Main chat container with blue header like screenshots */
.chat-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  transition: all 0.3s ease;
  position: relative;
  min-height: 46px;
  overflow: visible;
}

/* Message form styled to match screenshots exactly and ALWAYS visible */
.message-form {
  display: flex !important;
  gap: 10px;
  padding: 10px;
  background: white;
  position: sticky;
  bottom: 0;
  z-index: 1000;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
  border-top: 1px solid #e0e0e0;
}

/* Control what's visible in collapsed state but ALWAYS show chat form */
.chat-container:not(.expanded) .messages-container,
.chat-container:not(.expanded) .autonomous-workflow {
  display: none;
}

/* Hide file changes container by default - only show when specifically needed */
.file-changes-container {
  display: none !important;
}

/* Fix for expanded mode - simple height change */
.chat-container.expanded {
  height: auto;
  max-height: 600px;
  /* NOT position: fixed - this was causing it to take over the page */
}

/* Messages container - normal and expanded */
.messages-container {
  flex: 1;
  max-height: 150px;
  overflow-y: auto;
  transition: all 0.3s;
}

.chat-container.expanded .messages-container {
  max-height: 400px;
}

/* Keep header styling simple in expanded mode */
.chat-container.expanded .chat-header {
  border-radius: 10px 10px 0 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Ensure model controls remain visible */
.chat-container.expanded .model-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  flex: 1;
  margin: 0 20px;
  
  /* Ensure these remain visible even in expanded mode */
  .model-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    padding: 4px 12px;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    margin-right: 10px;
    
    label {
      font-size: 13px;
      font-weight: 600;
      white-space: nowrap;
    }
    
    select {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 13px;
      min-width: 140px;
      
      option {
        background: #fff;
        color: #333;
      }
    }
  }
}

/* Simple fix for action buttons */
.chat-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Keep expand button visible in all states */
.expand-chat-btn {
  background: linear-gradient(90deg, #4f8cff 80%, #a0c4ff 100%);
  color: white; 
  opacity: 1 !important;
  visibility: visible !important;
}

/* Enhanced styling for file changes to properly format code */
.file-changes-wrapper {
  margin: 10px 0;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  background: #f8f9fa;
  width: 100%;
}

.file-changes {
  background: #f8f9fa;
  width: 100%;
}

.file-changes-header {
  padding: 8px 12px;
  background: #f1f3f4;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  border-bottom: 1px solid #e0e0e0;
}

.file-diff {
  padding: 10px;
  font-family: 'Roboto Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  color: #333;
  border: 1px solid #eee;
  margin: 0;
}

/* Syntax highlighting for diff content */
.diff-added {
  color: #28a745;
  background-color: #e6ffec;
  display: block;
}

.diff-removed {
  color: #d73a49;
  background-color: #ffeef0;
  display: block;
}

.diff-hunk {
  color: #0366d6;
  background-color: #f1f8ff;
  display: block;
  font-weight: bold;
}

/* Expanded state - shows all content while keeping proper layout */
.chat-container.expanded {
  max-height: none;
  height: auto;
  position: relative;
}

.chat-container.expanded .messages-container,
.chat-container.expanded .autonomous-workflow {
  max-height: 400px;
  overflow-y: auto;
}

/* Ensure the chat container has a minimum height to always show the form */
.chat-container {
  position: relative !important;
  z-index: 1;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  min-height: 150px; /* Ensure form is visible */
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Push header to top, form to bottom */
}

.expand-chat-btn {
  background: linear-gradient(90deg, #4f8cff 80%, #a0c4ff 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  margin-left: 10px;
  padding: 6px 14px;
  transition: background 0.2s, box-shadow 0.2s;
  display: flex;
  align-items: center;
}

.expand-chat-btn:hover {
  background: #4f8cff;
  box-shadow: 0 2px 8px rgba(79,140,255,0.18);
}

.export-chat-btn, .copy-chat-btn {
  margin-left: 8px;
  padding: 6px 14px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}
.export-chat-btn:disabled, .copy-chat-btn:disabled {
  background: #bdbdbd;
  cursor: not-allowed;
}
.export-chat-btn:hover:not(:disabled), .copy-chat-btn:hover:not(:disabled) {
  background: #1565c0;
}

/* Typing indicator styles */
.typing-indicator {
  display: flex;
  padding: 10px 20px;
  align-items: center;
  gap: 4px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  background-color: #4f8cff;
  border-radius: 50%;
  animation: typing-animation 1.4s infinite ease-in-out;
  opacity: 0.7;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-animation {
  0%, 100% {
    transform: scale(0.7);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Streaming message style */
.message.streaming .message-content {
  border-right: 2px solid #4f8cff;
  animation: cursor-blink 1s infinite;
}

@keyframes cursor-blink {
  0%, 100% {
    border-color: transparent;
  }
  50% {
    border-color: #4f8cff;
  }
}

/* Reaction buttons */
.message-reactions {
  display: flex;
  justify-content: flex-end;
  padding: 5px 0;
}

.reaction-buttons {
  display: flex;
  gap: 6px;
}

.reaction-button {
  background: none;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #f5f5f5;
    transform: translateY(-1px);
  }
  
  &.active {
    background-color: #e3f2fd;
    border-color: #bbdefb;
    color: #1976d2;
  }
}

/* Agent thinking panel */
.agent-thinking-panel {
  margin: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.thinking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  
  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  button {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    
    &:hover {
      background-color: #e0e0e0;
    }
  }
}

.thinking-content {
  padding: 10px 15px;
  background-color: #fafafa;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  margin: 0;
  white-space: pre-wrap;
}

/* Memory button */
.memory-btn {
  background: linear-gradient(90deg, #4f8cff 0%, #2979ff 100%);
  border: none;
  color: #fff;
  padding: 7px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.2s;
  box-shadow: 0 1px 4px rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: linear-gradient(90deg, #2979ff 0%, #1565c0 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(41, 121, 255, 0.2);
  }
  
  i {
    font-size: 16px;
  }
}

/* Streaming toggle */
.streaming-toggle {
  background: rgba(255,255,255,0.18);
  padding: 5px 10px;
  border-radius: 8px;
  color: white;
  display: flex;
  align-items: center;
  
  label {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    
    input[type="checkbox"] {
      accent-color: #22c55e;
      width: 16px;
      height: 16px;
    }
  }
}

.saving-indicator {
  position: absolute;
  bottom: -20px;
  left: 10px;
  font-size: 12px;
  color: #888;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 2px 6px;
  border-radius: 4px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* API Toggle Button */
.api-toggle-btn {
  background-color: #4a4a4a;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px;
  margin-right: 5px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #666;
  }
  
  &.active {
    background-color: #3498db;
  }
}

/* API Request/Response Message Types */
.message {
  &.api-request-message, &.api-response-message {
    background-color: #f8f9fa;
    border-left: 3px solid #3498db;
    
    .bubble {
      background-color: #f8f9fa;
      border: 1px solid #e0e0e0;
      
      pre {
        background-color: #f1f1f1;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 12px;
        line-height: 1.4;
        max-height: 300px;
        overflow-y: auto;
      }
    }
  }
  
  &.api-request-message {
    border-left-color: #3498db;
    .bubble {
      border-left: 3px solid #3498db;
    }
  }
  
  &.api-response-message {
    border-left-color: #2ecc71;
    .bubble {
      border-left: 3px solid #2ecc71;
    }
  }
}

/* API avatar styles */
.api-request-avatar, .api-response-avatar {
  background-color: #f8f9fa;
  color: #333;
  
  span {
    font-size: 14px;
  }
}

.api-request-avatar {
  background-color: #d4e6f1;
}

.api-response-avatar {
  background-color: #d5f5e3;
}

/* Debug Logs Section */
.debug-logs {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin: 10px 0;
  max-height: 200px;
  overflow-y: auto;

  .debug-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    font-weight: bold;
    font-size: 12px;

    .clear-logs-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 10px;
      cursor: pointer;

      &:hover {
        background: #c82333;
      }
    }
  }

  .debug-content {
    padding: 8px;

    .debug-log-entry {
      display: flex;
      gap: 8px;
      padding: 2px 0;
      font-family: 'Courier New', monospace;
      font-size: 11px;

      .log-time {
        color: #6c757d;
        min-width: 60px;
      }

      .log-level {
        min-width: 50px;
        font-weight: bold;
      }

      &.log-info .log-level {
        color: #0d6efd;
      }

      &.log-error .log-level {
        color: #dc3545;
      }

      &.log-warning .log-level {
        color: #fd7e14;
      }

      .log-message {
        flex: 1;
        color: #212529;
      }
    }
  }
}
