import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SocketService } from '../../services/socket.service';
import { ProjectService } from '../../services/project.service';
import { AgentService } from '../../services/agent.service';

interface MessageData {
  project_name: string;
  message: string;
  message_id?: string;
  message_type?: string;
}

interface CompleteData {
  project_name: string;
}

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss']
})
export class ChatComponent implements OnInit, AfterViewChecked {
  @Input() projectName: string = '';
  @Input() messagesLoading: boolean = false;
  @Input() messagesSaving: boolean = false;
  @Output() messageEvent = new EventEmitter<any>();
  @Output() chatExpandChange = new EventEmitter<boolean>();
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;

  messageForm!: FormGroup;
  messages: any[] = [];
  loading = false;
  models: any[] = [];
  selectedModel: string = 'deepseek/deepseek-coder';
  localLlmModels: any[] = [
    { id: 'mistral-nemo-instruct-2407', name: 'Mistral Nemo Instruct 2407' },
    // Add more local LLM models here if needed
  ];
  selectedLocalLlmModel: string = 'mistral-nemo-instruct-2407';
  isChatExpanded: boolean = false;
  subtasks: any[] = [];
  autonomousMode: boolean = false;
  agentTyping: boolean = false;
  agentThinkingContent: string = '';
  showAgentThinking: boolean = false;
  streamingEnabled: boolean = true;
  showApiPayloads: boolean = true;
  apiRequests: any[] = [];
  apiResponses: any[] = [];
  debugLogs: any[] = [];

  constructor(
    private fb: FormBuilder,
    private socketService: SocketService,
    private projectService: ProjectService,
    private agentService: AgentService
  ) {}

  ngOnInit(): void {
    console.log('[ChatComponent] ngOnInit called');
    this.initForm();
    this.loadMessages();
    this.loadModels();
    this.setupSocketListeners();
  }

  ngAfterViewChecked(): void {
    this.scrollToBottom();
  }

  scrollToBottom(): void {
    try {
      if (this.messagesContainer && this.messagesContainer.nativeElement) {
        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;
      }
    } catch (err) {}
  }

  initForm(): void {
    console.log('[ChatComponent] Initializing form');
    this.messageForm = this.fb.group({
      message: ['', Validators.required]
    });
  }

  loadMessages(): void {
    console.log('[ChatComponent] loadMessages called for project:', this.projectName);
    if (!this.projectName) {
      console.warn('[ChatComponent] No projectName found. Skipping loadMessages.');
      return;
    }

    if (!this.messagesLoading) {
      this.loading = true;
    }

    this.projectService.getProjectMessages(this.projectName).subscribe(
      (response: any) => {
        console.log('[ChatComponent] Project messages loaded:', response);
        this.messages = response.messages || [];
        this.loading = false;
      },
      (error: any) => {
        console.error('[ChatComponent] ❌ Error loading messages:', error);
        this.loading = false;
      }
    );
  }

  loadModels(): void {
    console.log('[ChatComponent] loadModels called');
    this.agentService.getModels().subscribe(
      (response: any) => {
        console.log('[ChatComponent] Models loaded:', response);
        this.models = response.models || [];
      },
      (error: any) => {
        console.error('[ChatComponent] ❌ Error loading models:', error);
      }
    );
  }

  /**
   * Gets models filtered by provider
   */
  getModelsByProvider(provider: string): any[] {
    return this.models.filter(model => model.id.startsWith(`${provider}/`));
  }

  /**
   * Gets models that don't belong to specified providers
   */
  getOtherModels(): any[] {
    const knownProviders = ['openai', 'deepseek'];
    return this.models.filter(model => 
      !knownProviders.some(provider => model.id.startsWith(`${provider}/`))
    );
  }

  setupSocketListeners(): void {
    console.log('[ChatComponent] Setting up socket listeners');

    this.socketService.on('agent_message').subscribe((data: MessageData) => {
      console.log('[ChatComponent] 🔁 Received socket "agent_message":', data);
      this.addDebugLog('info', `Agent message received: ${data.message?.substring(0, 50)}...`);

      if (data.project_name === this.projectName) {
        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);

        // Determine the message type from incoming data
        const messageType = data.message_type || 'agent';

        this.messages.push({
          id: data.message_id || `msg-${Date.now()}`,
          sender: 'agent',
          content: data.message,
          timestamp: new Date(),
          isAgentWorkingPlaceholder: false,
          messageType: messageType,
          reactions: []
        });
        this.loading = false;
        console.log('[ChatComponent] Message added to chat from agent');
      }
    });

    this.socketService.on('agent_typing').subscribe((data: any) => {
      console.log('[ChatComponent] 🔁 Received socket "agent_typing":', data);

      if (data.project_name === this.projectName) {
        this.agentTyping = data.is_typing;
        // If not typing anymore, remove any placeholder messages
        if (!data.is_typing) {
          this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);
        }
      }
    });

    this.socketService.on('agent_stream_token').subscribe((data: any) => {
      console.log('[ChatComponent] 🔁 Received socket "agent_stream_token":', data);

      if (data.project_name === this.projectName) {
        // Find the last agent message or create a new one if none exists
        let lastAgentMessage = this.messages.find(m => m.sender === 'agent' && !m.isComplete);
        
        if (!lastAgentMessage) {
          lastAgentMessage = {
            id: `stream-${Date.now()}`,
            sender: 'agent',
            content: '',
            timestamp: new Date(),
            isComplete: false,
            reactions: []
          };
          this.messages.push(lastAgentMessage);
        }
        
        // Append the token to the message content
        lastAgentMessage.content += data.token;
        this.scrollToBottom();
      }
    });

    this.socketService.on('agent_complete').subscribe((data: CompleteData) => {
      console.log('[ChatComponent] ✅ Received socket "agent_complete":', data);

      if (data.project_name === this.projectName) {
        this.loading = false;
        this.agentTyping = false;
        
        // Mark all agent messages as complete
        this.messages.forEach(message => {
          if (message.sender === 'agent') {
            message.isComplete = true;
          }
        });
        
        // Remove any placeholder messages
        this.messages = this.messages.filter(m => !m.isAgentWorkingPlaceholder);
        console.log('[ChatComponent] Loading state cleared after agent_complete');
      }
    });
    
    this.socketService.on('agent_thinking').subscribe((data: any) => {
      console.log('[ChatComponent] 🧠 Received socket "agent_thinking":', data);
      
      if (data.project_name === this.projectName && this.showAgentThinking) {
        // Display the agent's thought process in a special UI element if debugging is enabled
        this.agentThinkingContent = data.thinking;
      }
    });
    
    this.socketService.on('message_reaction').subscribe((data: any) => {
      console.log('[ChatComponent] 👍 Received socket "message_reaction":', data);
      
      if (data.project_name === this.projectName) {
        // Find the message and add the reaction
        const messageToUpdate = this.messages.find(m => m.id === data.message_id);
        if (messageToUpdate) {
          if (!messageToUpdate.reactions) {
            messageToUpdate.reactions = [];
          }
          if (!messageToUpdate.reactions.includes(data.reaction)) {
            messageToUpdate.reactions.push(data.reaction);
          }
        }
      }
    });

    // Listen for any error events
    this.socketService.on('error').subscribe((data: any) => {
      console.error('[ChatComponent] ❌ Socket error:', data);
      this.addDebugLog('error', `Socket error: ${JSON.stringify(data)}`);
    });

    // Listen for agent errors specifically
    this.socketService.on('agent_error').subscribe((data: any) => {
      console.error('[ChatComponent] ❌ Agent error:', data);
      this.addDebugLog('error', `Agent error: ${data.error || 'Unknown error'}`);

      if (data.project_name === this.projectName) {
        this.loading = false;
        this.agentTyping = false;

        // Add error message to chat
        this.messages.push({
          id: `error-${Date.now()}`,
          sender: 'system',
          messageType: 'error',
          content: `<strong>Agent Error:</strong><br>${data.error || 'Unknown error occurred'}`,
          timestamp: new Date(),
          reactions: []
        });
      }
    });
  }

  sendMessage(): void {
    if (this.messageForm.invalid) {
      return;
    }
    
    const messageContent = this.messageForm.get('message')?.value;
    if (!messageContent || !this.projectName) {
      return;
    }
    
    // Add user message to the chat
    const userMessageId = `msg-${Date.now()}`;
    this.messages.push({
      id: userMessageId,
      sender: 'user',
      content: messageContent,
      timestamp: new Date(),
      reactions: []
    });
    
    // Create request payload
    const requestPayload = {
      project_name: this.projectName,
      message: messageContent,
      model_id: this.selectedModel,
      local_llm_model_id: this.selectedLocalLlmModel,
      streaming_enabled: this.streamingEnabled
    };
    
    // Store request
    const requestEntry = {
      timestamp: new Date(),
      type: 'request',
      endpoint: `/projects/${this.projectName}/messages`,
      payload: requestPayload
    };
    this.apiRequests.push(requestEntry);
    
    // If API payloads are visible, add to messages
    if (this.showApiPayloads) {
      this.messages.push({
        id: `api-req-${Date.now()}`,
        sender: 'system',
        messageType: 'api_request',
        content: `<strong>API Request:</strong><br><pre>${JSON.stringify(requestPayload, null, 2)}</pre>`,
        timestamp: new Date(),
        reactions: []
      });
    }
    
    // Reset the form
    this.messageForm.reset();
    
    // Show loading indicator
    this.loading = true;
    
    // Send to API
    this.addDebugLog('info', `Sending message with model: ${this.selectedModel}`);
    if (this.streamingEnabled) {
      // For streaming, we handle via sockets
      this.addDebugLog('info', 'Using streaming mode via WebSocket');
      this.socketService.sendMessage(this.projectName, messageContent, this.selectedModel);
    } else {
      // For non-streaming, we make a direct API call
      this.agentService.sendMessage(
        this.projectName, 
        messageContent, 
        this.selectedModel,
        this.selectedLocalLlmModel,
        false
      ).subscribe(
        (response: any) => {
          // Store response
          const responseEntry = {
            timestamp: new Date(),
            type: 'response',
            endpoint: `/projects/${this.projectName}/messages`,
            payload: response
          };
          this.apiResponses.push(responseEntry);
          
          // If API payloads are visible, add to messages
          if (this.showApiPayloads) {
            this.messages.push({
              id: `api-res-${Date.now()}`,
              sender: 'system',
              messageType: 'api_response',
              content: `<strong>API Response:</strong><br><pre>${JSON.stringify(response, null, 2)}</pre>`,
              timestamp: new Date(),
              reactions: []
            });
          }
          
          this.loading = false;
        },
        (error: any) => {
          console.error('[ChatComponent] ❌ Error sending message:', error);
          
          // Store error response
          const errorEntry = {
            timestamp: new Date(),
            type: 'error',
            endpoint: `/projects/${this.projectName}/messages`,
            payload: error
          };
          this.apiResponses.push(errorEntry);
          
          // Add error message
          this.messages.push({
            id: `error-${Date.now()}`,
            sender: 'system',
            messageType: 'error',
            content: `<strong>API Error:</strong><br><pre>${JSON.stringify(error, null, 2)}</pre>`,
            timestamp: new Date(),
            reactions: []
          });
          
          this.loading = false;
        }
      );
    }
  }

  onModelChange(modelId: string): void {
    console.log('[ChatComponent] Model changed to:', modelId);
    this.selectedModel = modelId;
  }

  deleteMessage(index: number): void {
    this.messages.splice(index, 1);
  }

  clearChat(): void {
    if (!this.projectName) return;
    // Clear messages in UI
    this.messages = [];
    // Call backend API to delete chat history
    this.projectService.deleteProjectMessages(this.projectName).subscribe(
      () => {
        console.log('[ChatComponent] Chat history deleted on backend');
      },
      (error) => {
        console.error('[ChatComponent] Error deleting chat history:', error);
      }
    );
  }

  toggleChatExpand(): void {
    this.isChatExpanded = !this.isChatExpanded;
    this.chatExpandChange.emit(this.isChatExpanded);
  }

  onEnter(event: KeyboardEvent): void {
    if (event.shiftKey) {
      return; // allow newline
    }
    event.preventDefault();
    this.sendMessage();
  }

  exportChat(): void {
    if (!this.projectName) return;
    this.projectService.exportProjectChat(this.projectName).subscribe(
      (response: any) => {
        alert('Chat exported: ' + (response?.file_path || 'Success'));
      },
      (error: any) => {
        alert('Failed to export chat: ' + (error?.error?.detail || error));
      }
    );
  }

  exportDynamicChat(): void {
    if (!this.messages.length) return;
    // Format chat as text
    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\n');
    const blob = new Blob([chatText], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${this.projectName || 'chat'}-dynamic-${timestamp}.txt`;
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }, 0);
  }

  copyChat(): void {
    const chatText = this.messages.map(m => `[${m.sender === 'user' ? 'You' : 'AI'}] ${m.content}`).join('\n');
    navigator.clipboard.writeText(chatText).then(
      () => alert('Chat copied to clipboard!'),
      () => alert('Failed to copy chat to clipboard.')
    );
  }

  onLocalLlmModelChange(modelId: string): void {
    this.selectedLocalLlmModel = modelId;
    // You can add logic here to notify the backend or update the session if needed
    console.log('[ChatComponent] Local LLM model changed to:', modelId);
  }

  get completedSubtasks(): number {
    if (!this.subtasks) return 0;
    return this.subtasks.filter(subtask => !subtask.error).length;
  }

  retrySubtask(index: number): void {
    // Simulate retry: clear error and result, set to loading, then re-run (in real app, call backend)
    const subtask = this.subtasks[index];
    subtask.error = null;
    subtask.result = 'Retrying...';
    // Simulate async retry (replace with real backend call)
    setTimeout(() => {
      subtask.result = 'Retried result (simulated)';
      subtask.error = null;
    }, 1500);
  }

  setSubtaskFeedback(index: number, feedback: 'up' | 'down'): void {
    this.subtasks[index].feedback = feedback;
  }

  get longTaskInProgress(): boolean {
    if (this.loading) return true;
    if (this.subtasks && this.subtasks.length > 0) {
      return this.subtasks.some(s => !s.result && !s.error);
    }
    return false;
  }

  toggleAutonomousMode(): void {
    this.autonomousMode = !this.autonomousMode;
  }

  /**
   * Formats a unified diff string as HTML with basic syntax highlighting for added, removed, and context lines.
   * @param diff The unified diff string
   * @returns HTML string
   */
  formatFileDiff(diff: string): string {
    if (!diff) return '';
    // Escape HTML
    const escape = (s: string) => s.replace(/[&<>]/g, c => ({'&':'&amp;','<':'&lt;','>':'&gt;'}[c]||c));
    return '<pre>' + diff.split('\n').map(line => {
      if (line.startsWith('+') && !line.startsWith('+++')) {
        return `<span class='diff-added'>${escape(line)}</span>`;
      } else if (line.startsWith('-') && !line.startsWith('---')) {
        return `<span class='diff-removed'>${escape(line)}</span>`;
      } else if (line.startsWith('@@')) {
        return `<span class='diff-hunk'>${escape(line)}</span>`;
      } else {
        return escape(line);
      }
    }).join('\n') + '</pre>';
  }

  /**
   * Returns the current workflow stage for the progress indicator in the chat UI.
   * 1 = Planning, 2 = Design, 3 = Implementation, 4 = Testing
   */
  get currentStage(): number {
    if (!this.subtasks || this.subtasks.length === 0) return 0;
    // If all subtasks are completed, return 4 (Testing)
    if (this.subtasks.every(s => s.completed || s.result || s.error)) return 4;
    // Otherwise, estimate stage based on subtask type or index
    // (You can refine this logic as needed)
    let stage = 1;
    for (const subtask of this.subtasks) {
      if (subtask.subtask?.type === 'design') stage = Math.max(stage, 2);
      else if (subtask.subtask?.type === 'implementation') stage = Math.max(stage, 3);
      else if (subtask.subtask?.type === 'testing') stage = Math.max(stage, 4);
    }
    return stage;
  }

  addReaction(messageId: string, reaction: string): void {
    console.log(`[ChatComponent] Adding reaction ${reaction} to message ${messageId}`);

    // Optimistically update UI
    const messageToUpdate = this.messages.find(m => m.id === messageId);
    if (messageToUpdate) {
      if (!messageToUpdate.reactions) {
        messageToUpdate.reactions = [];
      }
      if (!messageToUpdate.reactions.includes(reaction)) {
        messageToUpdate.reactions.push(reaction);
      }
    }

    // Send to backend
    this.agentService.addMessageReaction(this.projectName, messageId, reaction).subscribe(
      (response) => {
        console.log('[ChatComponent] ✅ Reaction added successfully:', response);
      },
      (error) => {
        console.error('[ChatComponent] ❌ Error adding reaction:', error);
        // Remove the reaction if it failed
        if (messageToUpdate && messageToUpdate.reactions) {
          messageToUpdate.reactions = messageToUpdate.reactions.filter((r: string) => r !== reaction);
        }
      }
    );
  }

  clearDebugLogs(): void {
    this.debugLogs = [];
  }

  addDebugLog(level: string, message: string): void {
    this.debugLogs.push({
      timestamp: new Date(),
      level: level,
      message: message
    });

    // Keep only the last 50 logs to prevent memory issues
    if (this.debugLogs.length > 50) {
      this.debugLogs = this.debugLogs.slice(-50);
    }
  }

  resetContextMemory(): void {
    if (!this.projectName) return;
    this.projectService.resetContextMemory(this.projectName).subscribe(
      () => {
        this.addDebugLog('info', 'Context memory reset successfully');
        console.log('[ChatComponent] Context memory reset');
      },
      (error) => {
        this.addDebugLog('error', `Failed to reset context memory: ${error.message}`);
        console.error('[ChatComponent] Error resetting context memory:', error);
      }
    );
  }

  toggleApiPayloads(): void {
    this.showApiPayloads = !this.showApiPayloads;
    this.addDebugLog('info', `API payloads visibility: ${this.showApiPayloads ? 'enabled' : 'disabled'}`);
  }

  toggleAgentThinking(): void {
    this.showAgentThinking = !this.showAgentThinking;
    this.addDebugLog('info', `Agent thinking display: ${this.showAgentThinking ? 'enabled' : 'disabled'}`);
  }
} 