import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ProjectService {
  private baseDir: string = 'C:/SourceProjects/AutonomousAI/projects';

  constructor(private apiService: ApiService) {
    console.log('[ProjectService] Initializing ProjectService...');
    const platform = navigator.platform.toLowerCase();

    if (platform.includes('linux')) {
      this.baseDir = '/home/<USER>/SourceProjects/AutonomousAI/projects';
      console.log('[ProjectService] Platform: Linux -> baseDir set to', this.baseDir);
    } else if (platform.includes('mac')) {
      this.baseDir = '/Users/<USER>/SourceProjects/AutonomousAI/projects';
      console.log('[ProjectService] Platform: macOS -> baseDir set to', this.baseDir);
    } else {
      console.log('[ProjectService] Platform: Windows/Other -> baseDir remains', this.baseDir);
    }
  }

  getProjects(): Observable<any> {
    console.log('[ProjectService] getProjects called');
    return this.apiService.getProjects();
  }

  getProject(name: string): Observable<any> {
    console.log('[ProjectService] getProject called for:', name);
    return this.apiService.getProject(name);
  }

  createProject(name: string, description?: string): Observable<any> {
    console.log('[ProjectService] createProject called with:', { name, description });
    return this.apiService.createProject(name, description);
  }

  deleteProject(name: string): Observable<any> {
    console.log('[ProjectService] deleteProject called for:', name);
    return this.apiService.deleteProject(name);
  }

  getProjectFiles(projectName: string): Observable<any> {
    console.log('[ProjectService] getProjectFiles called for:', projectName);
    return this.apiService.getProjectFiles(projectName);
  }

  getProjectMessages(projectName: string): Observable<any> {
    console.log('[ProjectService] getProjectMessages called for:', projectName);
    return this.apiService.getProjectMessages(projectName);
  }

  deleteProjectMessages(projectName: string): Observable<any> {
    console.log('[ProjectService] deleteProjectMessages called for:', projectName);
    return this.apiService.deleteProjectMessages(projectName);
  }

  /**
   * Save messages for a project to persist chat history and expanded state
   * @param projectName Project name
   * @param messages Array of messages to save
   * @param chatExpanded Boolean indicating if chat is expanded
   * @returns Observable with save result
   */
  saveProjectMessages(projectName: string, messages: any[], chatExpanded: boolean = false): Observable<any> {
    console.log('[ProjectService] saveProjectMessages called for:', projectName);
    return this.apiService.saveProjectMessages(projectName, messages, chatExpanded);
  }

  getProjectDir(projectName: string): string {
    const path = `${this.baseDir}/${projectName}`;
    console.log('[ProjectService] getProjectDir ->', path);
    return path;
  }

  getBaseDir(): string {
    console.log('[ProjectService] getBaseDir ->', this.baseDir);
    return this.baseDir;
  }

  setBaseDir(dir: string): void {
    console.log('[ProjectService] setBaseDir called. New baseDir:', dir);
    this.baseDir = dir;
  }

  resetProject(name: string): Observable<any> {
    console.log('[ProjectService] resetProject called for:', name);
    return this.apiService.resetProject(name);
  }

  exportProjectChat(name: string): Observable<any> {
    console.log('[ProjectService] exportProjectChat called for:', name);
    return this.apiService.exportProjectChat(name);
  }

  deleteAllProjects(): Observable<any> {
    console.log('[ProjectService] deleteAllProjects called');
    return this.apiService.deleteAllProjects();
  }

  resetContextMemory(projectName: string): Observable<any> {
    console.log('[ProjectService] resetContextMemory called for:', projectName);
    return this.apiService.resetContextMemory(projectName);
  }
}
