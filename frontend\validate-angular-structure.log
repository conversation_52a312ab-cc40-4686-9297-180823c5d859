Angular Structure Validation Log
[2025-06-13T18:44:42.639Z] --- Starting validation ---
[2025-06-13T18:44:42.642Z] angular.json is correct.
[2025-06-13T18:44:42.643Z] tsconfig.json is correct.
[2025-06-13T18:44:44.496Z] Found misplaced src folders: C:\SourceProjects\AutonomousAI\backend\src, C:\SourceProjects\AutonomousAI\desktop-app\backend\src, C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\@eslint\js\src, C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\@humanfs\core\src, C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\@humanfs\node\src, C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\@humanwhocodes\module-importer\src, C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\flat-cache\src, C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\keyv\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\animations\esm2022\browser\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\animations\esm2022\browser\testing\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\animations\esm2022\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\cli\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\http\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\http\testing\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\testing\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\upgrade\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler\esm2022\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler\esm2022\src\template\pipeline\ir\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler\esm2022\src\template\pipeline\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler\esm2022\testing\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\bundles\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\linker\babel\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\linker\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\node_modules\@babel\core\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\common\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\component\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\directive\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\ng_module\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\core\api\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\core\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\cycles\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\diagnostics\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\entry_point\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\file_system\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\imports\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\incremental\semantic_graph\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\incremental\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\indexer\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\logging\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\metadata\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\partial_evaluator\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\perf\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\program_driver\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\reflection\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\resource\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\scope\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\shims\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\sourcemaps\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\transform\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\translator\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\typecheck\diagnostics\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\typecheck\extended\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\typecheck\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\util\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\xi18n\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\core\esm2022\rxjs-interop\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\core\esm2022\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\core\esm2022\src\signals\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\core\esm2022\testing\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\forms\esm2022\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser\esm2022\animations\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser\esm2022\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser\esm2022\testing\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser-dynamic\esm2022\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser-dynamic\esm2022\testing\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\router\esm2022\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\router\esm2022\testing\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\router\esm2022\upgrade\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\architect\node_modules\rxjs\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\architect\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\build-angular\node_modules\rxjs\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\build-angular\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\build-webpack\node_modules\rxjs\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\build-webpack\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\core\node_modules\rxjs\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\core\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\schematics\node_modules\rxjs\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\schematics\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@babel\core\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@babel\plugin-transform-runtime\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@babel\preset-modules\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@discoveryjs\json-ext\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@ngtools\webpack\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@puppeteer\browsers\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\application\files\common-files\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\application\files\module-files\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\application\files\standalone-files\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\e2e\files\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\library\files\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\universal\files\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@webassemblyjs\helper-numbers\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@webassemblyjs\ieee754\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@webassemblyjs\utf8\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\@xtuc\long\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\acorn-import-attributes\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\agent-base\dist\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\agent-base\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\ajv-formats\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\ajv-keywords\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\body-parser\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\compression\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\connect\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\critters\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\engine.io\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\engine.io-client\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\eventemitter-asyncresource\dist\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\eventemitter-asyncresource\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\exponential-backoff\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\express\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\finalhandler\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\guess-parser\dist\guess-parser\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\html-entities\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\human-signals\build\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\icss-utils\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\ip-address\node_modules\sprintf-js\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\ip-address\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\is-what\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\istanbul-lib-instrument\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\istanbul-reports\lib\html-spa\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\jasmine-core\lib\jasmine-core\example\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\karma-jasmine-html-reporter\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\less\node_modules\mime\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\node-gyp\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\nwsapi\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\parse5\node_modules\entities\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\piscina\dist\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\piscina\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\postcss-modules-extract-imports\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\postcss-modules-local-by-default\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\postcss-modules-scope\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\postcss-modules-values\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\puppeteer\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\puppeteer-core\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\rxjs\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\send\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\send\node_modules\mime\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\serve-index\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\socket.io\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\socket.io-adapter\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\socket.io-client\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\socket.io-parser\node_modules\debug\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\sprintf-js\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\typed-assert\src, C:\SourceProjects\AutonomousAI\frontend\node_modules\ua-parser-js\src, C:\SourceProjects\AutonomousAI\node_modules\debug\src, C:\SourceProjects\AutonomousAI\projects\Ludo\Ludo\src, C:\SourceProjects\AutonomousAI\venv\Lib\site-packages\pyhanko-0.29.0.dist-info\licenses\src
[2025-06-13T18:44:44.497Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\backend\src
[2025-06-13T18:44:44.497Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\desktop-app\backend\src
[2025-06-13T18:44:44.498Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\@eslint\js\src
[2025-06-13T18:44:44.498Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\@humanfs\core\src
[2025-06-13T18:44:44.499Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\@humanfs\node\src
[2025-06-13T18:44:44.499Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\@humanwhocodes\module-importer\src
[2025-06-13T18:44:44.500Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\debug\src
[2025-06-13T18:44:44.501Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\flat-cache\src
[2025-06-13T18:44:44.501Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\frontend\node_modules\keyv\src
[2025-06-13T18:44:44.502Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\animations\esm2022\browser\src
[2025-06-13T18:44:44.502Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\animations\esm2022\browser\testing\src
[2025-06-13T18:44:44.503Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\animations\esm2022\src
[2025-06-13T18:44:44.503Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\cli\src
[2025-06-13T18:44:44.504Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\http\src
[2025-06-13T18:44:44.505Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\http\testing\src
[2025-06-13T18:44:44.505Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\src
[2025-06-13T18:44:44.506Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\testing\src
[2025-06-13T18:44:44.506Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\common\esm2022\upgrade\src
[2025-06-13T18:44:44.507Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler\esm2022\src
[2025-06-13T18:44:44.507Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler\esm2022\src\template\pipeline\ir\src
[2025-06-13T18:44:44.508Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler\esm2022\src\template\pipeline\src
[2025-06-13T18:44:44.508Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler\esm2022\testing\src
[2025-06-13T18:44:44.509Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\bundles\src
[2025-06-13T18:44:44.511Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\linker\babel\src
[2025-06-13T18:44:44.512Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\linker\src
[2025-06-13T18:44:44.513Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\node_modules\@babel\core\src
[2025-06-13T18:44:44.513Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src
[2025-06-13T18:44:44.514Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\common\src
[2025-06-13T18:44:44.514Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\component\src
[2025-06-13T18:44:44.515Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\directive\src
[2025-06-13T18:44:44.515Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\ng_module\src
[2025-06-13T18:44:44.516Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\annotations\src
[2025-06-13T18:44:44.516Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\core\api\src
[2025-06-13T18:44:44.517Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\core\src
[2025-06-13T18:44:44.517Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\cycles\src
[2025-06-13T18:44:44.518Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\diagnostics\src
[2025-06-13T18:44:44.518Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\entry_point\src
[2025-06-13T18:44:44.519Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\file_system\src
[2025-06-13T18:44:44.521Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\imports\src
[2025-06-13T18:44:44.522Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\incremental\semantic_graph\src
[2025-06-13T18:44:44.523Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\incremental\src
[2025-06-13T18:44:44.523Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\indexer\src
[2025-06-13T18:44:44.524Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\logging\src
[2025-06-13T18:44:44.530Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\metadata\src
[2025-06-13T18:44:44.531Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\partial_evaluator\src
[2025-06-13T18:44:44.532Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\perf\src
[2025-06-13T18:44:44.532Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\program_driver\src
[2025-06-13T18:44:44.533Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\reflection\src
[2025-06-13T18:44:44.533Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\resource\src
[2025-06-13T18:44:44.534Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\scope\src
[2025-06-13T18:44:44.535Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\shims\src
[2025-06-13T18:44:44.535Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\sourcemaps\src
[2025-06-13T18:44:44.542Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\transform\src
[2025-06-13T18:44:44.545Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\translator\src
[2025-06-13T18:44:44.546Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\typecheck\diagnostics\src
[2025-06-13T18:44:44.546Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\typecheck\extended\src
[2025-06-13T18:44:44.547Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\typecheck\src
[2025-06-13T18:44:44.547Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\util\src
[2025-06-13T18:44:44.548Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\compiler-cli\src\ngtsc\xi18n\src
[2025-06-13T18:44:44.548Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\core\esm2022\rxjs-interop\src
[2025-06-13T18:44:44.549Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\core\esm2022\src
[2025-06-13T18:44:44.549Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\core\esm2022\src\signals\src
[2025-06-13T18:44:44.550Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\core\esm2022\testing\src
[2025-06-13T18:44:44.550Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\forms\esm2022\src
[2025-06-13T18:44:44.551Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser\esm2022\animations\src
[2025-06-13T18:44:44.551Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser\esm2022\src
[2025-06-13T18:44:44.552Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser\esm2022\testing\src
[2025-06-13T18:44:44.552Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser-dynamic\esm2022\src
[2025-06-13T18:44:44.553Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\platform-browser-dynamic\esm2022\testing\src
[2025-06-13T18:44:44.553Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\router\esm2022\src
[2025-06-13T18:44:44.554Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\router\esm2022\testing\src
[2025-06-13T18:44:44.554Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular\router\esm2022\upgrade\src
[2025-06-13T18:44:44.555Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\architect\node_modules\rxjs\src
[2025-06-13T18:44:44.555Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\architect\src
[2025-06-13T18:44:44.556Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\build-angular\node_modules\rxjs\src
[2025-06-13T18:44:44.556Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\build-angular\src
[2025-06-13T18:44:44.557Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\build-webpack\node_modules\rxjs\src
[2025-06-13T18:44:44.557Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\build-webpack\src
[2025-06-13T18:44:44.558Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\core\node_modules\rxjs\src
[2025-06-13T18:44:44.558Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\core\src
[2025-06-13T18:44:44.559Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\schematics\node_modules\rxjs\src
[2025-06-13T18:44:44.560Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@angular-devkit\schematics\src
[2025-06-13T18:44:44.561Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@babel\core\src
[2025-06-13T18:44:44.561Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@babel\plugin-transform-runtime\src
[2025-06-13T18:44:44.562Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@babel\preset-modules\src
[2025-06-13T18:44:44.562Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@discoveryjs\json-ext\src
[2025-06-13T18:44:44.563Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@ngtools\webpack\src
[2025-06-13T18:44:44.563Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@puppeteer\browsers\src
[2025-06-13T18:44:44.564Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\application\files\common-files\src
[2025-06-13T18:44:44.564Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\application\files\module-files\src
[2025-06-13T18:44:44.565Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\application\files\standalone-files\src
[2025-06-13T18:44:44.565Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\e2e\files\src
[2025-06-13T18:44:44.565Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\library\files\src
[2025-06-13T18:44:44.566Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@schematics\angular\universal\files\src
[2025-06-13T18:44:44.566Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@webassemblyjs\helper-numbers\src
[2025-06-13T18:44:44.567Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@webassemblyjs\ieee754\src
[2025-06-13T18:44:44.567Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@webassemblyjs\utf8\src
[2025-06-13T18:44:44.568Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\@xtuc\long\src
[2025-06-13T18:44:44.568Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\acorn-import-attributes\src
[2025-06-13T18:44:44.569Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\agent-base\dist\src
[2025-06-13T18:44:44.570Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\agent-base\src
[2025-06-13T18:44:44.570Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\ajv-formats\src
[2025-06-13T18:44:44.571Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\ajv-keywords\src
[2025-06-13T18:44:44.580Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\body-parser\node_modules\debug\src
[2025-06-13T18:44:44.581Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\compression\node_modules\debug\src
[2025-06-13T18:44:44.581Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\connect\node_modules\debug\src
[2025-06-13T18:44:44.587Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\critters\src
[2025-06-13T18:44:44.588Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\debug\src
[2025-06-13T18:44:44.589Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\engine.io\node_modules\debug\src
[2025-06-13T18:44:44.590Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\engine.io-client\node_modules\debug\src
[2025-06-13T18:44:44.590Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\eventemitter-asyncresource\dist\src
[2025-06-13T18:44:44.591Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\eventemitter-asyncresource\src
[2025-06-13T18:44:44.592Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\exponential-backoff\src
[2025-06-13T18:44:44.593Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\express\node_modules\debug\src
[2025-06-13T18:44:44.594Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\finalhandler\node_modules\debug\src
[2025-06-13T18:44:44.595Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\guess-parser\dist\guess-parser\src
[2025-06-13T18:44:44.596Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\html-entities\src
[2025-06-13T18:44:44.597Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\human-signals\build\src
[2025-06-13T18:44:44.598Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\icss-utils\src
[2025-06-13T18:44:44.600Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\ip-address\node_modules\sprintf-js\src
[2025-06-13T18:44:44.601Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\ip-address\src
[2025-06-13T18:44:44.601Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\is-what\src
[2025-06-13T18:44:44.612Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\istanbul-lib-instrument\src
[2025-06-13T18:44:44.613Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\istanbul-reports\lib\html-spa\src
[2025-06-13T18:44:44.614Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\jasmine-core\lib\jasmine-core\example\src
[2025-06-13T18:44:44.614Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\karma-jasmine-html-reporter\src
[2025-06-13T18:44:44.616Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\less\node_modules\mime\src
[2025-06-13T18:44:44.617Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\node-gyp\src
[2025-06-13T18:44:44.618Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\nwsapi\src
[2025-06-13T18:44:44.618Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\parse5\node_modules\entities\src
[2025-06-13T18:44:44.619Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\piscina\dist\src
[2025-06-13T18:44:44.619Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\piscina\src
[2025-06-13T18:44:44.620Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\postcss-modules-extract-imports\src
[2025-06-13T18:44:44.620Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\postcss-modules-local-by-default\src
[2025-06-13T18:44:44.620Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\postcss-modules-scope\src
[2025-06-13T18:44:44.621Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\postcss-modules-values\src
[2025-06-13T18:44:44.621Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\puppeteer\src
[2025-06-13T18:44:44.622Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\puppeteer-core\src
[2025-06-13T18:44:44.622Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\rxjs\src
[2025-06-13T18:44:44.622Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\send\node_modules\debug\src
[2025-06-13T18:44:44.623Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\send\node_modules\mime\src
[2025-06-13T18:44:44.623Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\serve-index\node_modules\debug\src
[2025-06-13T18:44:44.624Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\socket.io\node_modules\debug\src
[2025-06-13T18:44:44.624Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\socket.io-adapter\node_modules\debug\src
[2025-06-13T18:44:44.624Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\socket.io-client\node_modules\debug\src
[2025-06-13T18:44:44.625Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\socket.io-parser\node_modules\debug\src
[2025-06-13T18:44:44.625Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\sprintf-js\src
[2025-06-13T18:44:44.625Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\typed-assert\src
[2025-06-13T18:44:44.627Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\frontend\node_modules\ua-parser-js\src
[2025-06-13T18:44:44.628Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\node_modules\debug\src
[2025-06-13T18:44:44.629Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\projects\Ludo\Ludo\src
[2025-06-13T18:44:44.630Z] Misplaced src folder not empty: C:\SourceProjects\AutonomousAI\venv\Lib\site-packages\pyhanko-0.29.0.dist-info\licenses\src
[2025-06-13T18:44:44.631Z] --- Validation complete ---
