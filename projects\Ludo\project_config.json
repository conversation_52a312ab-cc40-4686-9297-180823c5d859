{"use_google_search": true, "sqlite_path": "/home/<USER>/implementation/desktop-app/data/agent.db", "log_path": "/home/<USER>/implementation/desktop-app/logs", "vscode_path": "code", "openai": {"api_key": "********************************************************************************************************************************************************************"}, "deepseek": {"api_key": "***********************************", "models": ["deepseek-chat", "deepseek-coder", "deepseek-reasoner"]}, "google": {"api_key": "AIzaSyCWE4vs1WwSmMDgl6oYjpfqEnkCGpJKPY0", "search_engine_id": "753e6a85c0f964d03"}, "gemini": {"api_key": "AIzaSyCNWEUVh734_2ULDA9eM3f5BXrDQJvjjGs", "rate_limit": 2}, "ollama": {"base_url": "http://localhost:11434"}, "lm_studio": {"base_url": "http://localhost:1234/v1"}, "timeout": {"inference": 2400000}, "project_name": "<PERSON><PERSON>"}