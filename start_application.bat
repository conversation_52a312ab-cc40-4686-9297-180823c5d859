@echo off
echo Autonomous AI Software Development Agent - Starting
echo ==================================================

:: Start main backend server (with enhanced agent functionality)
start cmd /k "cd backend\src && ..\..\venv\Scripts\activate.bat && python main.py"

:: Start frontend server
start cmd /k "cd frontend && npm start"

:: Wait for servers to start
echo Waiting for servers to start...
timeout /t 5 /nobreak

:: Open application in browser
echo Opening application in browser...
start http://localhost:4200

:: Launch VS Code with the project
:: echo Launching VS Code with the project...
:: code AutonomousAI.code-workspace

echo Application started successfully!
