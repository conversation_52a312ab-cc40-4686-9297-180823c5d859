#!/usr/bin/env python3
"""
Startup script for the Intelligent Agent System.

This script demonstrates how to use the new intelligent agent
to create projects programmatically.
"""
import asyncio
import os
import sys
import logging

# Add the backend src to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'src'))

from agents.intelligent_agent import IntelligentAgent

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def create_ludo_game():
    """Create a Ludo game using the intelligent agent."""
    print("🎲 Creating Ludo Game with Intelligent Agent")
    print("=" * 50)
    
    # Configuration
    project_name = "ludo-game"
    projects_dir = os.path.join(os.path.dirname(__file__), "data", "projects")
    user_request = """
    Create a complete Ludo board game in Angular with the following features:
    
    1. 4-player game board with colored pieces (<PERSON>, <PERSON>, <PERSON>, Yellow)
    2. Dice rolling mechanism with animation
    3. Piece movement logic following Ludo rules
    4. Player turn management
    5. Win condition detection
    6. Modern, responsive UI with SCSS styling
    7. Game state management
    8. Start/restart game functionality
    
    Make it a complete, playable Ludo game with proper Angular components and services.
    """
    
    # Ensure projects directory exists
    os.makedirs(projects_dir, exist_ok=True)
    
    # Initialize the intelligent agent
    agent = IntelligentAgent(
        project_name=project_name,
        projects_base_dir=projects_dir,
        model_id="deepseek/deepseek-chat"
    )
    
    # Define streaming callback for real-time updates
    async def stream_callback(chunk: str):
        print(chunk, end='', flush=True)
    
    try:
        # Create the project
        result = await agent.create_project(user_request, stream_callback)
        
        print("\n" + "=" * 50)
        print("📊 Project Creation Results")
        print("=" * 50)
        
        if result.get("success", False):
            print("✅ Project created successfully!")
            print(f"📁 Project directory: {result.get('project_dir', 'Unknown')}")
            print(f"📄 Files created: {result.get('files_created', 0)}")
            print(f"📈 Success rate: {result.get('success_rate', 0):.1f}%")
            
            if result.get("build_success", False):
                print("🔨 Build completed successfully!")
            else:
                print("⚠️ Build failed - manual fixes may be needed")
            
            print("\n🚀 Next Steps:")
            print(f"1. cd {result.get('project_dir', '')}")
            print("2. npm start")
            print("3. Open http://localhost:4200 in your browser")
            
        else:
            print("❌ Project creation failed!")
            print(f"Error: {result.get('error', 'Unknown error')}")
            return 1
            
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        logger.error(f"Error in create_ludo_game: {e}", exc_info=True)
        return 1
    
    return 0

async def create_simple_angular_app():
    """Create a simple Angular app for testing."""
    print("🅰️ Creating Simple Angular App")
    print("=" * 50)
    
    project_name = "simple-angular-app"
    projects_dir = os.path.join(os.path.dirname(__file__), "data", "projects")
    user_request = """
    Create a simple Angular application with:
    
    1. Welcome page with navigation
    2. About page
    3. Contact form
    4. Responsive design with Angular Material
    5. Routing between pages
    6. Basic styling with SCSS
    
    Keep it simple but complete and functional.
    """
    
    os.makedirs(projects_dir, exist_ok=True)
    
    agent = IntelligentAgent(
        project_name=project_name,
        projects_base_dir=projects_dir,
        model_id="deepseek/deepseek-chat"
    )
    
    async def stream_callback(chunk: str):
        print(chunk, end='', flush=True)
    
    try:
        result = await agent.create_project(user_request, stream_callback)
        
        print("\n" + "=" * 50)
        print("📊 Simple App Creation Results")
        print("=" * 50)
        
        if result.get("success", False):
            print("✅ Simple Angular app created successfully!")
            print(f"📁 Project directory: {result.get('project_dir', 'Unknown')}")
            return 0
        else:
            print("❌ Simple app creation failed!")
            print(f"Error: {result.get('error', 'Unknown error')}")
            return 1
            
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1

async def main():
    """Main function with menu selection."""
    print("🤖 Intelligent Agent Startup Script")
    print("=" * 50)
    print("Choose what to create:")
    print("1. Ludo Game (Complex Angular Project)")
    print("2. Simple Angular App (Basic Project)")
    print("3. Exit")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            return await create_ludo_game()
        elif choice == "2":
            return await create_simple_angular_app()
        elif choice == "3":
            print("👋 Goodbye!")
            return 0
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
