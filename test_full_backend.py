#!/usr/bin/env python3
"""
Comprehensive test of the backend system with large content.
"""
import asyncio
import sys
import os
import logging

# Add the backend src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

async def test_full_backend_system():
    """Test the complete backend system with large content."""
    try:
        logger.info("🧪 Testing complete backend system with large content...")
        
        # Test 1: Planner with large complex request
        from agents.planner import Planner
        planner = Planner("deepseek/deepseek-coder")
        
        large_request = """Create a comprehensive Snake game in Angular with the following advanced features:
        1. Smooth snake movement with configurable speed settings (slow, medium, fast, extreme)
        2. Multiple food types: regular food (+10 points), golden food (+50 points), power pellets (+25 points)
        3. Power-ups that appear randomly: speed boost (2x speed for 10 seconds), score multiplier (2x points for 15 seconds), invincibility (pass through walls for 5 seconds)
        4. Dynamic obstacle generation that creates walls randomly during gameplay
        5. High score persistence using localStorage with top 10 scores and player names
        6. Responsive design that works perfectly on mobile, tablet, and desktop with touch controls
        7. Sound effects library: eating sounds, power-up sounds, game over sound, background music
        8. Advanced pause/resume functionality with visual overlay and keyboard shortcuts
        9. Multiple difficulty levels: Beginner (slow, no obstacles), Intermediate (medium speed, few obstacles), Advanced (fast, many obstacles), Expert (extreme speed, dynamic obstacles)
        10. Snake growth animation with smooth scaling and visual effects like particle trails
        11. Game statistics tracking: games played, total score, average score, longest snake
        12. Customizable themes: classic green, neon blue, retro arcade, modern dark mode
        13. Multiplayer support preparation with player identification system
        14. Achievement system: first game, score milestones, speed achievements, survival time records
        15. Advanced collision detection with pixel-perfect accuracy
        
        The game should have professional-quality graphics with CSS animations, smooth 60fps gameplay, comprehensive error handling, TypeScript strict mode compliance, and be fully playable immediately after generation. Include all necessary TypeScript interfaces, services for game logic, comprehensive SCSS styling with animations, and complete component architecture."""
        
        logger.info(f"📝 Testing planner with large request ({len(large_request)} characters)")
        
        plan_response = await planner.execute(large_request, "advanced-snake-game")
        logger.info(f"✅ Plan generated successfully: {len(plan_response)} characters")
        
        # Test 2: Parse the plan
        plan_details, automation_steps = planner.parse_response(plan_response)
        logger.info(f"📊 Plan parsed: {len(automation_steps)} automation steps")
        logger.info(f"📋 Plan details keys: {list(plan_details.keys())}")
        
        # Test 3: Full agent execution
        from agents.agent import Agent
        agent = Agent("deepseek/deepseek-coder")
        logger.info("✅ Agent created successfully")
        
        logger.info("🔄 Testing full agent execution with large content...")
        result = await agent.execute(large_request, "advanced-snake-test", streaming=False)
        logger.info(f"✅ Agent execution completed: {type(result)}")
        
        # Test 4: API endpoints
        logger.info("🔄 Testing API endpoints...")
        
        # Test models endpoint
        import requests
        try:
            models_response = requests.get("http://localhost:5000/api/models", timeout=10)
            logger.info(f"✅ Models endpoint: {models_response.status_code}")
            if models_response.status_code == 200:
                models_data = models_response.json()
                logger.info(f"📊 Available models: {len(models_data.get('models', []))}")
        except Exception as e:
            logger.warning(f"⚠️ Models endpoint test failed: {e}")
        
        # Test 5: DeepSeek API directly
        from llm.deepseek_client import DeepSeekClient
        deepseek = DeepSeekClient("deepseek-coder")
        
        logger.info("🔄 Testing DeepSeek API with large prompt...")
        large_prompt = "Create a detailed implementation plan for " + large_request
        
        deepseek_response = await deepseek.generate(large_prompt)
        logger.info(f"✅ DeepSeek API response: {len(deepseek_response)} characters")
        
        logger.info("🎉 All backend tests passed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Backend test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting comprehensive backend tests...")
    
    success = await test_full_backend_system()
    
    if success:
        logger.info("✅ All backend tests passed!")
        sys.exit(0)
    else:
        logger.error("❌ Backend tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
