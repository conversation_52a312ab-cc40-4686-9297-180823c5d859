#!/usr/bin/env python3
"""
Test script for the new Intelligent Agent system.

This script tests the core functionality of the intelligent agent
to ensure it can create Angular projects properly.
"""
import asyncio
import os
import sys
import logging
import tempfile
import shutil

# Add the backend src to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'src'))

from agents.intelligent_agent import IntelligentAgent
from agents.smart_project_executor import SmartProjectExecutor

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_smart_project_executor():
    """Test the SmartProjectExecutor class."""
    print("\n🧪 Testing SmartProjectExecutor...")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        project_name = "test-angular-project"
        
        executor = SmartProjectExecutor(project_name, temp_dir)
        
        # Test file creation
        print("📁 Testing file creation...")
        result = await executor.create_file("test.txt", "Hello, World!")
        
        if result["success"]:
            print("✅ File creation test passed")
        else:
            print(f"❌ File creation test failed: {result['error']}")
            return False
        
        # Test framework detection
        print("🔍 Testing framework detection...")
        framework = executor.detect_framework()
        print(f"Detected framework: {framework}")
        
        print("✅ SmartProjectExecutor tests completed")
        return True

async def test_intelligent_agent():
    """Test the IntelligentAgent class."""
    print("\n🤖 Testing IntelligentAgent...")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        project_name = "test-ludo-game"
        
        agent = IntelligentAgent(
            project_name=project_name,
            projects_base_dir=temp_dir,
            model_id="deepseek/deepseek-chat"
        )
        
        # Test user request analysis
        print("📋 Testing user request analysis...")
        
        test_request = "Create a Ludo board game in Angular with 4 players, dice rolling, and piece movement"
        
        try:
            analysis = await agent._analyze_user_request(test_request)
            
            if analysis["success"]:
                print("✅ User request analysis passed")
                print(f"   Project Type: {analysis['project_type']}")
                print(f"   Complexity: {analysis['complexity']}")
                print(f"   Features: {analysis['main_features']}")
            else:
                print(f"❌ User request analysis failed: {analysis['error']}")
                return False
                
        except Exception as e:
            print(f"❌ User request analysis failed with exception: {e}")
            return False
        
        print("✅ IntelligentAgent tests completed")
        return True

async def test_angular_project_creation():
    """Test Angular project creation (requires Angular CLI)."""
    print("\n🅰️ Testing Angular project creation...")
    
    # Check if Angular CLI is available
    from agents.shell_executor import ShellExecutor
    shell = ShellExecutor()
    
    ng_check = await shell.run_command("ng version", timeout=10)
    if not ng_check["success"]:
        print("⚠️ Angular CLI not found. Skipping Angular project creation test.")
        print("   To run this test, install Angular CLI: npm install -g @angular/cli")
        return True
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        project_name = "test-angular-app"
        
        executor = SmartProjectExecutor(project_name, temp_dir)
        
        print("🏗️ Creating Angular project...")
        result = await executor.create_angular_project()
        
        if result["success"]:
            print("✅ Angular project creation test passed")
            
            # Verify project structure
            project_dir = os.path.join(temp_dir, project_name)
            expected_files = ["package.json", "angular.json", "src/app/app.component.ts"]
            
            for file_path in expected_files:
                full_path = os.path.join(project_dir, file_path)
                if os.path.exists(full_path):
                    print(f"   ✅ Found: {file_path}")
                else:
                    print(f"   ❌ Missing: {file_path}")
                    return False
            
            print("✅ Angular project structure verification passed")
            return True
        else:
            print(f"❌ Angular project creation failed: {result['error']}")
            return False

async def main():
    """Run all tests."""
    print("🚀 Starting Intelligent Agent Tests")
    print("=" * 50)
    
    tests = [
        ("SmartProjectExecutor", test_smart_project_executor),
        ("IntelligentAgent", test_intelligent_agent),
        ("Angular Project Creation", test_angular_project_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Intelligent Agent system is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the output above for details.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
