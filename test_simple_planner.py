#!/usr/bin/env python3
"""
Test the simplified planner with DeepSeek API.
"""
import asyncio
import sys
import os
import logging

# Add the backend src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

async def test_simple_planner():
    """Test the simplified planner."""
    try:
        # Import the planner
        from agents.planner import Planner
        
        logger.info("🧪 Testing simplified planner with DeepSeek...")
        
        # Create planner
        planner = Planner("deepseek/deepseek-coder")
        logger.info(f"✅ Planner created with model: {planner.model_id}")
        
        # Test simple plan generation
        logger.info("🔄 Testing simple plan generation...")
        response = await planner.execute("Create a simple ludo board game", "test-ludo")
        logger.info(f"✅ Plan generated successfully!")
        logger.info(f"📄 Plan preview: {response[:200]}...")
        
        # Test plan parsing
        logger.info("🔄 Testing plan parsing...")
        plan_details, automation_steps = planner.parse_response(response)
        logger.info(f"✅ Plan parsed successfully!")
        logger.info(f"📊 Plan details keys: {list(plan_details.keys())}")
        logger.info(f"🔧 Automation steps count: {len(automation_steps)}")
        
        logger.info("🎉 All simplified planner tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Simplified planner test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    logger.info("🚀 Starting simplified planner tests...")
    
    success = await test_simple_planner()
    
    if success:
        logger.info("✅ All tests passed!")
        sys.exit(0)
    else:
        logger.error("❌ Tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
